{"metadata": {"total_tasks": 20, "generated_at": "2025-08-02T22:48:22.525737", "data_type": "task_node_simulation"}, "tasks": {"task_1": {"task_id": "task_1", "task_type": "general", "cpu_requirement": 3.03, "memory_requirement": 11868.47, "io_requirement": 472.94, "network_requirement": 187.46, "base_runtime": 196.94, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_2": {"task_id": "task_2", "task_type": "memory_intensive", "cpu_requirement": 1.82, "memory_requirement": 2821.41, "io_requirement": 314.14, "network_requirement": 92.19, "base_runtime": 98.19, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_3": {"task_id": "task_3", "task_type": "io_intensive", "cpu_requirement": 2.47, "memory_requirement": 7120.68, "io_requirement": 118.24, "network_requirement": 57.14, "base_runtime": 192.17, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_4": {"task_id": "task_4", "task_type": "general", "cpu_requirement": 1.21, "memory_requirement": 11089.28, "io_requirement": 340.76, "network_requirement": 171.02, "base_runtime": 131.59, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_5": {"task_id": "task_5", "task_type": "network_intensive", "cpu_requirement": 2.29, "memory_requirement": 9516.62, "io_requirement": 269.75, "network_requirement": 24.88, "base_runtime": 257.45, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_6": {"task_id": "task_6", "task_type": "general", "cpu_requirement": 3.19, "memory_requirement": 9336.38, "io_requirement": 68.75, "network_requirement": 155.02, "base_runtime": 149.37, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_7": {"task_id": "task_7", "task_type": "memory_intensive", "cpu_requirement": 3.15, "memory_requirement": 6039.68, "io_requirement": 144.82, "network_requirement": 75.62, "base_runtime": 178.14, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_8": {"task_id": "task_8", "task_type": "cpu_intensive", "cpu_requirement": 1.29, "memory_requirement": 4967.23, "io_requirement": 239.1, "network_requirement": 111.28, "base_runtime": 225.01, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_9": {"task_id": "task_9", "task_type": "network_intensive", "cpu_requirement": 2.88, "memory_requirement": 15284.64, "io_requirement": 321.9, "network_requirement": 188.43, "base_runtime": 236.12, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_10": {"task_id": "task_10", "task_type": "general", "cpu_requirement": 3.95, "memory_requirement": 6242.75, "io_requirement": 341.28, "network_requirement": 140.69, "base_runtime": 93.05, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_11": {"task_id": "task_11", "task_type": "io_intensive", "cpu_requirement": 2.92, "memory_requirement": 15574.59, "io_requirement": 334.29, "network_requirement": 144.59, "base_runtime": 209.03, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_12": {"task_id": "task_12", "task_type": "network_intensive", "cpu_requirement": 1.4, "memory_requirement": 13438.23, "io_requirement": 40.83, "network_requirement": 229.56, "base_runtime": 271.86, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_13": {"task_id": "task_13", "task_type": "general", "cpu_requirement": 3.87, "memory_requirement": 8747.94, "io_requirement": 435.4, "network_requirement": 69.78, "base_runtime": 213.28, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_14": {"task_id": "task_14", "task_type": "cpu_intensive", "cpu_requirement": 2.06, "memory_requirement": 2060.74, "io_requirement": 325.21, "network_requirement": 141.56, "base_runtime": 253.94, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_15": {"task_id": "task_15", "task_type": "network_intensive", "cpu_requirement": 2.63, "memory_requirement": 1073.84, "io_requirement": 131.35, "network_requirement": 189.34, "base_runtime": 198.25, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_16": {"task_id": "task_16", "task_type": "general", "cpu_requirement": 2.38, "memory_requirement": 6706.13, "io_requirement": 46.84, "network_requirement": 215.49, "base_runtime": 198.36, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_17": {"task_id": "task_17", "task_type": "network_intensive", "cpu_requirement": 1.59, "memory_requirement": 9930.41, "io_requirement": 201.66, "network_requirement": 183.4, "base_runtime": 65.32, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_18": {"task_id": "task_18", "task_type": "cpu_intensive", "cpu_requirement": 2.43, "memory_requirement": 9803.15, "io_requirement": 420.73, "network_requirement": 216.21, "base_runtime": 256.0, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_19": {"task_id": "task_19", "task_type": "general", "cpu_requirement": 1.75, "memory_requirement": 3809.29, "io_requirement": 62.12, "network_requirement": 57.33, "base_runtime": 81.26, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}, "task_20": {"task_id": "task_20", "task_type": "io_intensive", "cpu_requirement": 2.09, "memory_requirement": 14810.46, "io_requirement": 44.62, "network_requirement": 174.61, "base_runtime": 156.02, "dependencies": [], "estimated_runtime": 0.0, "start_time": 0.0, "end_time": 0.0, "assigned_node": null}}}