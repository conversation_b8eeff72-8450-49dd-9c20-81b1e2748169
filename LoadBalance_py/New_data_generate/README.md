# 任务和节点数据生成器

## 项目概述
这是一个专门用于生成负载均衡仿真任务和节点数据的工具。基于修订版数据格式，重点考虑工厂调度和甘特图需求。

## 文件结构
```
New_data_generate/
├── task_node_data_format.md          # 数据格式设计文档
├── test_generator.py                  # 简化测试版本（已测试成功）
├── complete_task_node_generator.py    # 完整版本（待调试）
├── task_node_generator.py             # 原始版本（待修复）
└── New_data/                          # 生成的数据目录
    ├── tasks.json                     # 任务数据
    ├── nodes.json                     # 节点数据
    ├── task_node_summary.json         # 数据摘要
    └── README.md                      # 数据说明
```

## 数据格式特点

### 任务数据 (tasks.json)
- **5种任务类型**: CPU密集型、内存密集型、I/O密集型、网络密集型、通用型
- **资源需求**: CPU核心数（消耗量）、内存、I/O带宽、网络带宽
- **执行时间**: 基础执行时间、预估执行时间
- **调度信息**: 开始时间、结束时间、分配节点
- **依赖关系**: 任务间的依赖关系

### 节点数据 (nodes.json)
- **4种节点类型**: CPU密集型、内存密集型、I/O密集型、通用型
- **资源容量**: CPU核心数、内存容量、I/O带宽、网络带宽
- **负载状态**: 当前负载、可用资源
- **执行历史**: 任务执行记录（用于甘特图）
- **性能指标**: makespan

## 使用方法

### 1. 运行测试版本（推荐）
```bash
python test_generator.py
```
- 生成20个任务，5个节点
- 已测试成功，数据格式正确

### 2. 运行完整版本
```bash
python complete_task_node_generator.py
```
- 生成100个任务，20个节点
- 包含完整的统计摘要
- 需要进一步调试

## 数据生成参数

### 任务生成
- **数量**: 20-200个
- **类型分布**: 每种类型20%
- **依赖关系**: 30-50%的任务有依赖
- **资源范围**: 根据任务类型设定

### 节点生成
- **数量**: 5-50个
- **类型分布**: 每种类型25%
- **负载分布**: 0.1-0.8
- **异构程度**: 资源容量20-50%差异

## 关键特性

### 1. 工厂调度支持
- 任务执行时间线记录
- 节点负载跟踪
- Makespan计算

### 2. 甘特图数据
- 任务历史记录
- 资源利用率跟踪
- 时间轴数据

### 3. 负载均衡考虑
- 资源匹配算法
- 性能影响因子计算
- 可用资源管理

## 数据质量

### 验证要点
- ✅ 资源需求合理性（CPU 1-4核心）
- ✅ 节点容量充足（CPU 4-16核心）
- ✅ 依赖关系无循环
- ✅ 数据类型一致性
- ✅ 数值精度（2位小数）

### 统计信息
- 任务类型分布统计
- 资源需求统计（最小值、最大值、平均值、标准差）
- 节点容量统计
- 负载分布统计

## 后续开发

### 计划功能
1. **调度算法集成**: 实现基本的任务分配算法
2. **甘特图生成**: 基于数据生成可视化甘特图
3. **性能分析**: 计算负载均衡指标
4. **数据验证**: 增强数据质量检查
5. **批量生成**: 支持多种规模的数据生成

### 技术改进
1. **错误处理**: 完善异常处理机制
2. **配置管理**: 支持参数化配置
3. **日志记录**: 添加详细的执行日志
4. **单元测试**: 编写测试用例

## 注意事项

1. **数据精度**: 所有数值保留2位小数
2. **依赖关系**: 确保无循环依赖
3. **资源匹配**: 任务需求不超过节点容量
4. **时间一致性**: 开始时间、结束时间、执行时间的一致性

## 联系信息
如有问题或建议，请参考数据格式文档或查看生成的示例数据。 