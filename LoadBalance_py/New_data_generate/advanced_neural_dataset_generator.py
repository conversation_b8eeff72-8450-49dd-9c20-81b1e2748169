"""
高级神经网络数据集生成器
支持多种神经网络架构的负载均衡数据集生成
包括CNN、RNN、Transformer、GNN等架构
"""

import json
import random
import numpy as np
import pandas as pd
from datetime import datetime
import os
from typing import Dict, List, Any, Tuple, Optional, Union
from dataclasses import dataclass
import pickle
from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
from sklearn.model_selection import train_test_split
import networkx as nx
from scipy.sparse import csr_matrix
import torch
from torch_geometric.data import Data as PyGData
import matplotlib.pyplot as plt
import seaborn as sns


@dataclass
class AdvancedDatasetConfig:
    """高级数据集配置"""
    # 数据集规模
    total_samples: int = 50000
    train_ratio: float = 0.7
    val_ratio: float = 0.15
    test_ratio: float = 0.15
    
    # 任务和节点数量范围
    min_tasks: int = 10
    max_tasks: int = 200
    min_nodes: int = 5
    max_nodes: int = 50
    
    # 神经网络架构支持
    support_cnn: bool = True
    support_rnn: bool = True
    support_transformer: bool = True
    support_gnn: bool = True
    support_mlp: bool = True
    
    # 特征工程参数
    normalize_features: bool = True
    add_noise: bool = True
    noise_level: float = 0.05
    feature_augmentation: bool = True
    
    # 图神经网络参数
    max_graph_size: int = 100
    edge_probability: float = 0.3
    
    # 序列长度参数
    max_sequence_length: int = 50
    sequence_padding: bool = True
    
    random_seed: int = 42


class AdvancedNeuralDatasetGenerator:
    """高级神经网络数据集生成器"""
    
    def __init__(self, config: AdvancedDatasetConfig):
        self.config = config
        self.scalers = {}
        self.label_encoders = {}
        
        # 设置随机种子
        random.seed(config.random_seed)
        np.random.seed(config.random_seed)
        torch.manual_seed(config.random_seed)
        
        # 任务类型定义
        self.task_types = {
            'cpu_intensive': {
                'cpu_range': (2.0, 8.0),
                'memory_range': (2048.0, 16384.0),
                'io_range': (64.0, 512.0),
                'network_range': (32.0, 256.0),
                'runtime_range': (60.0, 600.0),
                'priority_range': (1, 5)
            },
            'memory_intensive': {
                'cpu_range': (1.0, 4.0),
                'memory_range': (8192.0, 65536.0),
                'io_range': (128.0, 1024.0),
                'network_range': (32.0, 256.0),
                'runtime_range': (120.0, 1200.0),
                'priority_range': (1, 5)
            },
            'io_intensive': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (1024.0, 8192.0),
                'io_range': (512.0, 4096.0),
                'network_range': (16.0, 128.0),
                'runtime_range': (90.0, 900.0),
                'priority_range': (1, 5)
            },
            'network_intensive': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (1024.0, 8192.0),
                'io_range': (32.0, 256.0),
                'network_range': (256.0, 2048.0),
                'runtime_range': (60.0, 600.0),
                'priority_range': (1, 5)
            },
            'general': {
                'cpu_range': (1.0, 4.0),
                'memory_range': (2048.0, 16384.0),
                'io_range': (64.0, 512.0),
                'network_range': (32.0, 256.0),
                'runtime_range': (90.0, 720.0),
                'priority_range': (1, 5)
            }
        }
        
        # 节点类型定义
        self.node_types = {
            'cpu_intensive': {
                'cpu_range': (8.0, 32.0),
                'memory_range': (16384.0, 131072.0),
                'io_range': (1024.0, 8192.0),
                'network_range': (512.0, 4096.0),
                'reliability_range': (0.95, 0.99)
            },
            'memory_intensive': {
                'cpu_range': (4.0, 16.0),
                'memory_range': (32768.0, 262144.0),
                'io_range': (2048.0, 16384.0),
                'network_range': (256.0, 2048.0),
                'reliability_range': (0.95, 0.99)
            },
            'io_intensive': {
                'cpu_range': (4.0, 16.0),
                'memory_range': (16384.0, 131072.0),
                'io_range': (4096.0, 32768.0),
                'network_range': (256.0, 2048.0),
                'reliability_range': (0.95, 0.99)
            },
            'general': {
                'cpu_range': (6.0, 24.0),
                'memory_range': (24576.0, 196608.0),
                'io_range': (1536.0, 12288.0),
                'network_range': (384.0, 3072.0),
                'reliability_range': (0.95, 0.99)
            }
        }
    
    def generate_task_features(self, task_id: str, task_type: str, task_index: int) -> Dict[str, Any]:
        """生成任务特征"""
        task_config = self.task_types[task_type]
        
        # 基础资源需求
        cpu_req = round(random.uniform(*task_config['cpu_range']), 2)
        memory_req = round(random.uniform(*task_config['memory_range']), 2)
        io_req = round(random.uniform(*task_config['io_range']), 2)
        network_req = round(random.uniform(*task_config['network_range']), 2)
        base_runtime = round(random.uniform(*task_config['runtime_range']), 2)
        priority = random.randint(*task_config['priority_range'])
        
        # 高级特征
        deadline = base_runtime * random.uniform(1.2, 2.0)
        arrival_time = random.uniform(0, 100)
        
        # 依赖关系（更复杂的逻辑）
        dependencies = []
        if random.random() < 0.4:  # 40%的任务有依赖
            num_deps = random.randint(1, min(3, task_index))
            for i in range(num_deps):
                dep_id = f"task_{random.randint(1, max(1, task_index-1))}"
                if dep_id != task_id:
                    dependencies.append(dep_id)
        
        # 任务特征向量
        task_vector = [
            cpu_req, memory_req, io_req, network_req, base_runtime,
            priority, deadline, arrival_time, len(dependencies),
            cpu_req / (cpu_req + memory_req + io_req + network_req),
            memory_req / (cpu_req + memory_req + io_req + network_req),
            io_req / (cpu_req + memory_req + io_req + network_req),
            network_req / (cpu_req + memory_req + io_req + network_req)
        ]
        
        return {
            'task_id': task_id,
            'task_type': task_type,
            'cpu_requirement': cpu_req,
            'memory_requirement': memory_req,
            'io_requirement': io_req,
            'network_requirement': network_req,
            'base_runtime': base_runtime,
            'priority': priority,
            'deadline': deadline,
            'arrival_time': arrival_time,
            'dependencies': dependencies,
            'dependency_count': len(dependencies),
            'total_resource_demand': cpu_req + memory_req + io_req + network_req,
            'task_vector': task_vector,
            'resource_intensity': {
                'cpu_intensity': cpu_req / (cpu_req + memory_req + io_req + network_req),
                'memory_intensity': memory_req / (cpu_req + memory_req + io_req + network_req),
                'io_intensity': io_req / (cpu_req + memory_req + io_req + network_req),
                'network_intensity': network_req / (cpu_req + memory_req + io_req + network_req)
            }
        }
    
    def generate_node_features(self, node_id: str, node_type: str, node_index: int) -> Dict[str, Any]:
        """生成节点特征"""
        node_config = self.node_types[node_type]
        
        # 基础资源容量
        cpu_cap = round(random.uniform(*node_config['cpu_range']), 2)
        memory_cap = round(random.uniform(*node_config['memory_range']), 2)
        io_cap = round(random.uniform(*node_config['io_range']), 2)
        network_cap = round(random.uniform(*node_config['network_range']), 2)
        reliability = round(random.uniform(*node_config['reliability_range']), 3)
        
        # 当前负载和状态
        current_load = round(random.uniform(0.1, 0.8), 2)
        temperature = round(random.uniform(30.0, 80.0), 1)
        power_consumption = round(random.uniform(50.0, 300.0), 1)
        
        # 计算可用资源
        cpu_available = cpu_cap * (1 - current_load)
        memory_available = memory_cap * (1 - current_load)
        io_available = io_cap * (1 - current_load)
        network_available = network_cap * (1 - current_load)
        
        # 节点特征向量
        node_vector = [
            cpu_cap, memory_cap, io_cap, network_cap,
            current_load, temperature, power_consumption, reliability,
            cpu_available, memory_available, io_available, network_available,
            cpu_available / cpu_cap, memory_available / memory_cap,
            io_available / io_cap, network_available / network_cap,
            (cpu_cap + memory_cap + io_cap + network_cap) / 4,  # 平均容量
            (cpu_available + memory_available + io_available + network_available) / 4  # 平均可用
        ]
        
        return {
            'node_id': node_id,
            'node_type': node_type,
            'cpu_capacity': cpu_cap,
            'memory_capacity': memory_cap,
            'io_bandwidth': io_cap,
            'network_bandwidth': network_cap,
            'reliability': reliability,
            'current_load': current_load,
            'temperature': temperature,
            'power_consumption': power_consumption,
            'cpu_available': cpu_available,
            'memory_available': memory_available,
            'io_available': io_available,
            'network_available': network_available,
            'total_capacity': cpu_cap + memory_cap + io_cap + network_cap,
            'total_available': cpu_available + memory_available + io_available + network_available,
            'node_vector': node_vector,
            'resource_efficiency': {
                'cpu_efficiency': cpu_available / cpu_cap,
                'memory_efficiency': memory_available / memory_cap,
                'io_efficiency': io_available / io_cap,
                'network_efficiency': network_available / network_cap
            }
        }
    
    def generate_system_state(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成系统状态特征"""
        total_tasks = len(tasks)
        total_nodes = len(nodes)
        
        # 计算系统级特征
        total_cpu_demand = sum(task['cpu_requirement'] for task in tasks.values())
        total_memory_demand = sum(task['memory_requirement'] for task in tasks.values())
        total_io_demand = sum(task['io_requirement'] for task in tasks.values())
        total_network_demand = sum(task['network_requirement'] for task in tasks.values())
        
        total_cpu_capacity = sum(node['cpu_capacity'] for node in nodes)
        total_memory_capacity = sum(node['memory_capacity'] for node in nodes)
        total_io_capacity = sum(node['io_bandwidth'] for node in nodes)
        total_network_capacity = sum(node['network_bandwidth'] for node in nodes)
        
        # 高级系统特征
        avg_task_priority = np.mean([task['priority'] for task in tasks.values()])
        avg_node_reliability = np.mean([node['reliability'] for node in nodes])
        avg_node_temperature = np.mean([node['temperature'] for node in nodes])
        
        # 任务类型分布
        task_type_counts = {}
        for task in tasks.values():
            task_type = task['task_type']
            task_type_counts[task_type] = task_type_counts.get(task_type, 0) + 1
        
        # 节点类型分布
        node_type_counts = {}
        for node in nodes:
            node_type = node['node_type']
            node_type_counts[node_type] = node_type_counts.get(node_type, 0) + 1
        
        system_vector = [
            total_tasks, total_nodes, total_tasks / total_nodes,
            total_cpu_demand, total_memory_demand, total_io_demand, total_network_demand,
            total_cpu_capacity, total_memory_capacity, total_io_capacity, total_network_capacity,
            total_cpu_demand / total_cpu_capacity, total_memory_demand / total_memory_capacity,
            total_io_demand / total_io_capacity, total_network_demand / total_network_capacity,
            avg_task_priority, avg_node_reliability, avg_node_temperature,
            len(task_type_counts), len(node_type_counts)
        ]
        
        return {
            'total_tasks': total_tasks,
            'total_nodes': total_nodes,
            'task_node_ratio': total_tasks / total_nodes,
            'total_cpu_demand': total_cpu_demand,
            'total_memory_demand': total_memory_demand,
            'total_io_demand': total_io_demand,
            'total_network_demand': total_network_demand,
            'total_cpu_capacity': total_cpu_capacity,
            'total_memory_capacity': total_memory_capacity,
            'total_io_capacity': total_io_capacity,
            'total_network_capacity': total_network_capacity,
            'cpu_utilization_ratio': total_cpu_demand / total_cpu_capacity,
            'memory_utilization_ratio': total_memory_demand / total_memory_capacity,
            'io_utilization_ratio': total_io_demand / total_io_capacity,
            'network_utilization_ratio': total_network_demand / total_network_capacity,
            'avg_task_priority': avg_task_priority,
            'avg_node_reliability': avg_node_reliability,
            'avg_node_temperature': avg_node_temperature,
            'task_type_distribution': task_type_counts,
            'node_type_distribution': node_type_counts,
            'system_vector': system_vector
        }
    
    def generate_optimal_allocation(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成最优分配（使用改进的启发式算法）"""
        task_assignments = {}
        node_loads = {node['node_id']: 0.0 for node in nodes}
        node_task_counts = {node['node_id']: 0 for node in nodes}
        
        # 按优先级和资源需求排序任务
        sorted_tasks = sorted(tasks.items(), 
                            key=lambda x: (x[1]['priority'], x[1]['total_resource_demand']), 
                            reverse=True)
        
        for task_id, task in sorted_tasks:
            best_node = None
            best_score = float('inf')
            
            for node in nodes:
                # 检查资源约束
                if (task['cpu_requirement'] <= node['cpu_available'] and
                    task['memory_requirement'] <= node['memory_available'] and
                    task['io_requirement'] <= node['io_available'] and
                    task['network_requirement'] <= node['network_available']):
                    
                    # 计算综合分配分数
                    load_score = node_loads[node['node_id']]
                    efficiency_score = task['total_resource_demand'] / node['total_capacity']
                    reliability_score = 1.0 - node['reliability']  # 可靠性越高，分数越低
                    temperature_score = node['temperature'] / 100.0  # 温度越高，分数越高
                    
                    # 综合评分
                    score = (load_score * 0.4 + efficiency_score * 0.3 + 
                            reliability_score * 0.2 + temperature_score * 0.1)
                    
                    if score < best_score:
                        best_score = score
                        best_node = node['node_id']
            
            if best_node:
                task_assignments[task_id] = best_node
                node_loads[best_node] += task['total_resource_demand'] / next(n['total_capacity'] for n in nodes if n['node_id'] == best_node)
                node_task_counts[best_node] += 1
        
        return {
            'task_assignments': task_assignments,
            'node_loads': node_loads,
            'node_task_counts': node_task_counts,
            'allocation_quality': self._calculate_allocation_quality(tasks, nodes, task_assignments)
        }
    
    def _calculate_allocation_quality(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]], 
                                   assignments: Dict[str, str]) -> Dict[str, float]:
        """计算分配质量指标"""
        if not assignments:
            return {
                'resource_utilization': 0.0,
                'load_balance': 0.0,
                'makespan': 0.0,
                'objective_value': 0.0,
                'reliability_score': 0.0,
                'energy_efficiency': 0.0
            }
        
        # 计算资源利用率
        total_utilization = 0.0
        node_utilizations = {}
        node_reliabilities = []
        node_energies = []
        
        for node in nodes:
            node_tasks = [task_id for task_id, node_id in assignments.items() if node_id == node['node_id']]
            if node_tasks:
                cpu_used = sum(tasks[task_id]['cpu_requirement'] for task_id in node_tasks)
                memory_used = sum(tasks[task_id]['memory_requirement'] for task_id in node_tasks)
                io_used = sum(tasks[task_id]['io_requirement'] for task_id in node_tasks)
                network_used = sum(tasks[task_id]['network_requirement'] for task_id in node_tasks)
                
                utilization = (cpu_used / node['cpu_capacity'] + 
                             memory_used / node['memory_capacity'] + 
                             io_used / node['io_bandwidth'] + 
                             network_used / node['network_bandwidth']) / 4
                node_utilizations[node['node_id']] = utilization
                total_utilization += utilization
                
                # 收集可靠性和能耗信息
                node_reliabilities.append(node['reliability'])
                node_energies.append(node['power_consumption'])
        
        avg_utilization = total_utilization / len(nodes)
        
        # 计算负载均衡度
        if node_utilizations:
            load_balance = 1.0 - np.std(list(node_utilizations.values()))
        else:
            load_balance = 0.0
        
        # 计算makespan
        makespan = max(node_utilizations.values()) if node_utilizations else 0.0
        
        # 计算可靠性得分
        reliability_score = np.mean(node_reliabilities) if node_reliabilities else 0.0
        
        # 计算能耗效率
        energy_efficiency = 1.0 / (np.mean(node_energies) / 100.0) if node_energies else 0.0
        
        # 计算综合目标函数值
        objective_value = (avg_utilization * 0.25 + load_balance * 0.25 + 
                          (1.0 - makespan) * 0.2 + reliability_score * 0.2 + 
                          energy_efficiency * 0.1)
        
        return {
            'resource_utilization': avg_utilization,
            'load_balance': load_balance,
            'makespan': makespan,
            'reliability_score': reliability_score,
            'energy_efficiency': energy_efficiency,
            'objective_value': objective_value
        }
    
    def generate_mlp_features(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]], 
                             system_state: Dict[str, Any]) -> np.ndarray:
        """生成MLP特征向量"""
        features = []
        
        # 任务特征
        for task in tasks.values():
            features.extend(task['task_vector'])
        
        # 节点特征
        for node in nodes:
            features.extend(node['node_vector'])
        
        # 系统状态特征
        features.extend(system_state['system_vector'])
        
        return np.array(features)
    
    def generate_cnn_features(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]], 
                             system_state: Dict[str, Any]) -> np.ndarray:
        """生成CNN特征矩阵"""
        max_tasks = max(len(tasks), 50)  # 固定大小
        max_nodes = max(len(nodes), 20)
        
        # 创建任务矩阵 (max_tasks, task_features)
        task_matrix = np.zeros((max_tasks, 14))  # 14个任务特征
        for i, (task_id, task) in enumerate(tasks.items()):
            if i < max_tasks:
                task_matrix[i] = task['task_vector']
        
        # 创建节点矩阵 (max_nodes, node_features)
        node_matrix = np.zeros((max_nodes, 18))  # 18个节点特征
        for i, node in enumerate(nodes):
            if i < max_nodes:
                node_matrix[i] = node['node_vector']
        
        # 创建系统状态矩阵 (1, system_features)
        system_matrix = np.array(system_state['system_vector']).reshape(1, -1)
        
        # 组合成CNN输入 (channels, height, width)
        # 使用任务矩阵作为主要特征
        cnn_input = task_matrix.T  # (14, max_tasks)
        
        return cnn_input
    
    def generate_rnn_features(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]], 
                             system_state: Dict[str, Any]) -> np.ndarray:
        """生成RNN特征序列"""
        # 按优先级排序任务
        sorted_tasks = sorted(tasks.items(), key=lambda x: x[1]['priority'], reverse=True)
        
        # 创建序列
        sequence = []
        for task_id, task in sorted_tasks[:self.config.max_sequence_length]:
            # 每个时间步包含任务特征
            step_features = task['task_vector'] + [len(sequence)]  # 添加位置信息
            sequence.append(step_features)
        
        # 填充到固定长度
        if len(sequence) < self.config.max_sequence_length:
            padding = [0.0] * (len(sequence[0]) if sequence else 15)
            for _ in range(self.config.max_sequence_length - len(sequence)):
                sequence.append(padding)
        
        return np.array(sequence)
    
    def generate_transformer_features(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]], 
                                    system_state: Dict[str, Any]) -> Dict[str, np.ndarray]:
        """生成Transformer特征"""
        # 任务序列
        sorted_tasks = sorted(tasks.items(), key=lambda x: x[1]['priority'], reverse=True)
        task_sequence = []
        task_attention_mask = []
        
        for i, (task_id, task) in enumerate(sorted_tasks[:self.config.max_sequence_length]):
            task_sequence.append(task['task_vector'])
            task_attention_mask.append(1)
        
        # 填充
        if len(task_sequence) < self.config.max_sequence_length:
            padding = [0.0] * len(task_sequence[0]) if task_sequence else 14
            for _ in range(self.config.max_sequence_length - len(task_sequence)):
                task_sequence.append(padding)
                task_attention_mask.append(0)
        
        # 节点序列
        node_sequence = []
        node_attention_mask = []
        
        for i, node in enumerate(nodes[:self.config.max_sequence_length]):
            node_sequence.append(node['node_vector'])
            node_attention_mask.append(1)
        
        # 填充
        if len(node_sequence) < self.config.max_sequence_length:
            padding = [0.0] * len(node_sequence[0]) if node_sequence else 18
            for _ in range(self.config.max_sequence_length - len(node_sequence)):
                node_sequence.append(padding)
                node_attention_mask.append(0)
        
        return {
            'task_sequence': np.array(task_sequence),
            'node_sequence': np.array(node_sequence),
            'task_attention_mask': np.array(task_attention_mask),
            'node_attention_mask': np.array(node_attention_mask),
            'system_features': np.array(system_state['system_vector'])
        }
    
    def generate_gnn_features(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]], 
                             system_state: Dict[str, Any]) -> PyGData:
        """生成GNN特征"""
        # 创建图
        G = nx.Graph()
        
        # 添加任务节点
        task_features = []
        for task_id, task in tasks.items():
            G.add_node(task_id, type='task')
            task_features.append(task['task_vector'])
        
        # 添加节点节点
        node_features = []
        for node in nodes:
            G.add_node(node['node_id'], type='node')
            node_features.append(node['node_vector'])
        
        # 添加边（任务-节点连接）
        edges = []
        edge_features = []
        
        for task_id, task in tasks.items():
            for node in nodes:
                # 检查资源约束
                if (task['cpu_requirement'] <= node['cpu_available'] and
                    task['memory_requirement'] <= node['memory_available'] and
                    task['io_requirement'] <= node['io_available'] and
                    task['network_requirement'] <= node['network_available']):
                    
                    G.add_edge(task_id, node['node_id'])
                    edges.append([task_id, node['node_id']])
                    
                    # 边特征：资源匹配度
                    cpu_match = task['cpu_requirement'] / node['cpu_capacity']
                    memory_match = task['memory_requirement'] / node['memory_capacity']
                    io_match = task['io_requirement'] / node['io_bandwidth']
                    network_match = task['network_requirement'] / node['network_bandwidth']
                    
                    edge_feature = [cpu_match, memory_match, io_match, network_match]
                    edge_features.append(edge_feature)
        
        # 转换为PyTorch Geometric格式
        node_mapping = {node: i for i, node in enumerate(G.nodes())}
        
        # 节点特征
        all_features = task_features + node_features
        x = torch.tensor(all_features, dtype=torch.float)
        
        # 边索引
        edge_index = []
        for edge in G.edges():
            edge_index.append([node_mapping[edge[0]], node_mapping[edge[1]]])
            edge_index.append([node_mapping[edge[1]], node_mapping[edge[0]]])  # 无向图
        
        edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
        
        # 边特征
        edge_attr = torch.tensor(edge_features * 2, dtype=torch.float)  # 无向图，边特征重复
        
        return PyGData(x=x, edge_index=edge_index, edge_attr=edge_attr)
    
    def generate_labels(self, allocation_result: Dict[str, Any]) -> np.ndarray:
        """生成标签向量"""
        quality = allocation_result['allocation_quality']
        
        labels = [
            quality['resource_utilization'],
            quality['load_balance'],
            quality['makespan'],
            quality['reliability_score'],
            quality['energy_efficiency'],
            quality['objective_value']
        ]
        
        return np.array(labels)
    
    def generate_single_sample(self) -> Dict[str, Any]:
        """生成单个样本"""
        # 随机生成任务和节点数量
        num_tasks = random.randint(self.config.min_tasks, self.config.max_tasks)
        num_nodes = random.randint(self.config.min_nodes, self.config.max_nodes)
        
        # 生成任务
        tasks = {}
        task_types = list(self.task_types.keys())
        for i in range(num_tasks):
            task_id = f"task_{i+1}"
            task_type = random.choice(task_types)
            tasks[task_id] = self.generate_task_features(task_id, task_type, i+1)
        
        # 生成节点
        nodes = []
        node_types = list(self.node_types.keys())
        for i in range(num_nodes):
            node_id = f"node_{i+1}"
            node_type = random.choice(node_types)
            nodes.append(self.generate_node_features(node_id, node_type, i+1))
        
        # 生成系统状态
        system_state = self.generate_system_state(tasks, nodes)
        
        # 生成最优分配
        allocation_result = self.generate_optimal_allocation(tasks, nodes)
        
        # 生成不同架构的特征
        sample = {
            'mlp_features': self.generate_mlp_features(tasks, nodes, system_state),
            'cnn_features': self.generate_cnn_features(tasks, nodes, system_state),
            'rnn_features': self.generate_rnn_features(tasks, nodes, system_state),
            'transformer_features': self.generate_transformer_features(tasks, nodes, system_state),
            'gnn_features': self.generate_gnn_features(tasks, nodes, system_state),
            'labels': self.generate_labels(allocation_result),
            'metadata': {
                'num_tasks': len(tasks),
                'num_nodes': len(nodes),
                'allocation_result': allocation_result
            }
        }
        
        return sample
    
    def generate_dataset(self) -> Dict[str, List]:
        """生成完整数据集"""
        print(f"生成 {self.config.total_samples} 个样本...")
        
        dataset = {
            'mlp_features': [],
            'cnn_features': [],
            'rnn_features': [],
            'transformer_features': [],
            'gnn_features': [],
            'labels': [],
            'metadata': []
        }
        
        for i in range(self.config.total_samples):
            if (i + 1) % 1000 == 0:
                print(f"已生成 {i + 1} 个样本")
            
            sample = self.generate_single_sample()
            
            for key in dataset.keys():
                if key != 'metadata':
                    dataset[key].append(sample[key])
                else:
                    dataset[key].append(sample['metadata'])
        
        print(f"数据集生成完成！")
        return dataset
    
    def save_dataset(self, dataset: Dict[str, List], output_dir: str = "advanced_neural_dataset"):
        """保存数据集"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 分割数据集
        train_size = int(self.config.total_samples * self.config.train_ratio)
        val_size = int(self.config.total_samples * self.config.val_ratio)
        
        # 保存训练集
        train_dir = os.path.join(output_dir, "train")
        os.makedirs(train_dir, exist_ok=True)
        
        for key in ['mlp_features', 'cnn_features', 'rnn_features', 'transformer_features', 'gnn_features', 'labels']:
            if key == 'transformer_features':
                # 特殊处理transformer特征
                train_data = dataset[key][:train_size]
                np.save(os.path.join(train_dir, f"{key}_task_sequence.npy"), 
                       np.array([data['task_sequence'] for data in train_data]))
                np.save(os.path.join(train_dir, f"{key}_node_sequence.npy"), 
                       np.array([data['node_sequence'] for data in train_data]))
                np.save(os.path.join(train_dir, f"{key}_task_attention_mask.npy"), 
                       np.array([data['task_attention_mask'] for data in train_data]))
                np.save(os.path.join(train_dir, f"{key}_node_attention_mask.npy"), 
                       np.array([data['node_attention_mask'] for data in train_data]))
                np.save(os.path.join(train_dir, f"{key}_system_features.npy"), 
                       np.array([data['system_features'] for data in train_data]))
            elif key == 'gnn_features':
                # 特殊处理GNN特征
                train_data = dataset[key][:train_size]
                torch.save(train_data, os.path.join(train_dir, f"{key}.pt"))
            else:
                train_data = dataset[key][:train_size]
                np.save(os.path.join(train_dir, f"{key}.npy"), np.array(train_data))
        
        # 保存验证集
        val_dir = os.path.join(output_dir, "val")
        os.makedirs(val_dir, exist_ok=True)
        
        for key in ['mlp_features', 'cnn_features', 'rnn_features', 'transformer_features', 'gnn_features', 'labels']:
            if key == 'transformer_features':
                val_data = dataset[key][train_size:train_size+val_size]
                np.save(os.path.join(val_dir, f"{key}_task_sequence.npy"), 
                       np.array([data['task_sequence'] for data in val_data]))
                np.save(os.path.join(val_dir, f"{key}_node_sequence.npy"), 
                       np.array([data['node_sequence'] for data in val_data]))
                np.save(os.path.join(val_dir, f"{key}_task_attention_mask.npy"), 
                       np.array([data['task_attention_mask'] for data in val_data]))
                np.save(os.path.join(val_dir, f"{key}_node_attention_mask.npy"), 
                       np.array([data['node_attention_mask'] for data in val_data]))
                np.save(os.path.join(val_dir, f"{key}_system_features.npy"), 
                       np.array([data['system_features'] for data in val_data]))
            elif key == 'gnn_features':
                val_data = dataset[key][train_size:train_size+val_size]
                torch.save(val_data, os.path.join(val_dir, f"{key}.pt"))
            else:
                val_data = dataset[key][train_size:train_size+val_size]
                np.save(os.path.join(val_dir, f"{key}.npy"), np.array(val_data))
        
        # 保存测试集
        test_dir = os.path.join(output_dir, "test")
        os.makedirs(test_dir, exist_ok=True)
        
        for key in ['mlp_features', 'cnn_features', 'rnn_features', 'transformer_features', 'gnn_features', 'labels']:
            if key == 'transformer_features':
                test_data = dataset[key][train_size+val_size:]
                np.save(os.path.join(test_dir, f"{key}_task_sequence.npy"), 
                       np.array([data['task_sequence'] for data in test_data]))
                np.save(os.path.join(test_dir, f"{key}_node_sequence.npy"), 
                       np.array([data['node_sequence'] for data in test_data]))
                np.save(os.path.join(test_dir, f"{key}_task_attention_mask.npy"), 
                       np.array([data['task_attention_mask'] for data in test_data]))
                np.save(os.path.join(test_dir, f"{key}_node_attention_mask.npy"), 
                       np.array([data['node_attention_mask'] for data in test_data]))
                np.save(os.path.join(test_dir, f"{key}_system_features.npy"), 
                       np.array([data['system_features'] for data in test_data]))
            elif key == 'gnn_features':
                test_data = dataset[key][train_size+val_size:]
                torch.save(test_data, os.path.join(test_dir, f"{key}.pt"))
            else:
                test_data = dataset[key][train_size+val_size:]
                np.save(os.path.join(test_dir, f"{key}.npy"), np.array(test_data))
        
        # 保存数据集信息
        dataset_info = {
            "total_samples": self.config.total_samples,
            "train_samples": train_size,
            "val_samples": val_size,
            "test_samples": self.config.total_samples - train_size - val_size,
            "generated_at": datetime.now().isoformat(),
            "config": self.config.__dict__,
            "feature_shapes": {
                "mlp_features": dataset['mlp_features'][0].shape if dataset['mlp_features'] else None,
                "cnn_features": dataset['cnn_features'][0].shape if dataset['cnn_features'] else None,
                "rnn_features": dataset['rnn_features'][0].shape if dataset['rnn_features'] else None,
                "labels": dataset['labels'][0].shape if dataset['labels'] else None
            }
        }
        
        with open(os.path.join(output_dir, "dataset_info.json"), "w") as f:
            json.dump(dataset_info, f, indent=2)
        
        print(f"数据集已保存到 {output_dir}")
        print(f"训练集: {train_size} 样本")
        print(f"验证集: {val_size} 样本")
        print(f"测试集: {self.config.total_samples - train_size - val_size} 样本")
    
    def generate_complete_dataset(self, output_dir: str = "advanced_neural_dataset"):
        """生成完整的数据集"""
        print("开始生成高级神经网络数据集...")
        
        # 生成数据集
        dataset = self.generate_dataset()
        
        # 保存数据集
        self.save_dataset(dataset, output_dir)
        
        print("高级神经网络数据集生成完成！")


def main():
    """主函数"""
    # 配置数据集生成参数
    config = AdvancedDatasetConfig(
        total_samples=10000,  # 可以根据需要调整
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15,
        min_tasks=10,
        max_tasks=100,
        min_nodes=5,
        max_nodes=30,
        support_cnn=True,
        support_rnn=True,
        support_transformer=True,
        support_gnn=True,
        support_mlp=True,
        normalize_features=True,
        add_noise=True,
        noise_level=0.05,
        feature_augmentation=True,
        max_graph_size=100,
        edge_probability=0.3,
        max_sequence_length=50,
        sequence_padding=True,
        random_seed=42
    )
    
    # 创建数据集生成器
    generator = AdvancedNeuralDatasetGenerator(config)
    
    # 生成数据集
    generator.generate_complete_dataset("advanced_neural_dataset")


if __name__ == "__main__":
    main() 