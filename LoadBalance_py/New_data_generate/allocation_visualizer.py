"""
分配结果可视化模块
用于展示连续时刻任务分配的结果
"""

import json
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from typing import Dict, List, Any
import os


class AllocationVisualizer:
    """分配结果可视化器"""
    
    def __init__(self, results_dir: str = "allocation_results"):
        self.results_dir = results_dir
        self.comparison_data = None
        self.load_comparison_data()
    
    def load_comparison_data(self):
        """加载比较数据"""
        comparison_file = os.path.join(self.results_dir, "algorithm_comparison.json")
        if os.path.exists(comparison_file):
            with open(comparison_file, 'r', encoding='utf-8') as f:
                self.comparison_data = json.load(f)
    
    def plot_algorithm_comparison(self, save_path: str = "visualization"):
        """绘制算法比较图"""
        if not self.comparison_data:
            print("没有找到比较数据")
            return
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('算法性能比较', fontsize=16, fontweight='bold')
        
        algorithms = list(self.comparison_data['comparison_results'].keys())
        metrics = ['resource_utilization', 'response_time', 'makespan', 
                  'load_balance_degree', 'objective_value', 'execution_time']
        metric_names = ['资源利用率 (%)', '响应时间 (秒)', 'Makespan (秒)', 
                       '负载均衡度', '目标函数值', '执行时间 (秒)']
        
        for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            row = i // 3
            col = i % 3
            ax = axes[row, col]
            
            values = [self.comparison_data['comparison_results'][alg][metric] 
                     for alg in algorithms]
            
            # 创建柱状图
            bars = ax.bar(algorithms, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
            ax.set_title(metric_name, fontweight='bold')
            ax.set_ylabel(metric_name)
            
            # 在柱子上添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.2f}', ha='center', va='bottom')
            
            # 旋转x轴标签
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图片
        os.makedirs(save_path, exist_ok=True)
        plt.savefig(os.path.join(save_path, 'algorithm_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_resource_utilization_heatmap(self, algorithm: str, save_path: str = "visualization"):
        """绘制资源利用率热力图"""
        result_file = os.path.join(self.results_dir, f"{algorithm}_result.json")
        if not os.path.exists(result_file):
            print(f"找不到算法 {algorithm} 的结果文件")
            return
        
        with open(result_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建热力图数据
        node_to_tasks = result_data['node_to_tasks']
        nodes = list(node_to_tasks.keys())
        
        # 统计每个节点上的任务数量
        task_counts = [len(tasks) for tasks in node_to_tasks.values()]
        
        # 创建简单的柱状图而不是热力图
        plt.figure(figsize=(10, 6))
        bars = plt.bar(nodes, task_counts, color='skyblue')
        plt.title(f'{algorithm} 算法 - 节点任务分配', fontweight='bold')
        plt.xlabel('节点ID')
        plt.ylabel('任务数量')
        
        # 在柱子上添加数值标签
        for bar, count in zip(bars, task_counts):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{count}', ha='center', va='bottom', fontweight='bold')
        
        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()
        
        # 保存图片
        os.makedirs(save_path, exist_ok=True)
        plt.savefig(os.path.join(save_path, f'{algorithm}_task_distribution.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_makespan_comparison(self, save_path: str = "visualization"):
        """绘制Makespan比较图"""
        if not self.comparison_data:
            print("没有找到比较数据")
            return
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        algorithms = list(self.comparison_data['comparison_results'].keys())
        makespans = [self.comparison_data['comparison_results'][alg]['makespan'] 
                    for alg in algorithms]
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(algorithms, makespans, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        plt.title('各算法Makespan比较', fontweight='bold', fontsize=14)
        plt.ylabel('Makespan (秒)', fontsize=12)
        plt.xlabel('算法', fontsize=12)
        
        # 在柱子上添加数值标签
        for bar, makespan in zip(bars, makespans):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{makespan:.1f}s', ha='center', va='bottom', fontweight='bold')
        
        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()
        
        # 保存图片
        os.makedirs(save_path, exist_ok=True)
        plt.savefig(os.path.join(save_path, 'makespan_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_objective_function_comparison(self, save_path: str = "visualization"):
        """绘制目标函数值比较图"""
        if not self.comparison_data:
            print("没有找到比较数据")
            return
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        algorithms = list(self.comparison_data['comparison_results'].keys())
        objective_values = [self.comparison_data['comparison_results'][alg]['objective_value'] 
                           for alg in algorithms]
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(algorithms, objective_values, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        plt.title('各算法目标函数值比较', fontweight='bold', fontsize=14)
        plt.ylabel('目标函数值', fontsize=12)
        plt.xlabel('算法', fontsize=12)
        
        # 在柱子上添加数值标签
        for bar, value in zip(bars, objective_values):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
        
        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()
        
        # 保存图片
        os.makedirs(save_path, exist_ok=True)
        plt.savefig(os.path.join(save_path, 'objective_function_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_comprehensive_report(self, save_path: str = "visualization"):
        """创建综合报告"""
        print("=" * 60)
        print("分配结果可视化报告")
        print("=" * 60)
        
        if not self.comparison_data:
            print("没有找到比较数据")
            return
        
        # 创建所有可视化图表
        self.plot_algorithm_comparison(save_path)
        self.plot_makespan_comparison(save_path)
        self.plot_objective_function_comparison(save_path)
        
        # 为每个算法创建热力图
        algorithms = list(self.comparison_data['comparison_results'].keys())
        for algorithm in algorithms:
            self.plot_resource_utilization_heatmap(algorithm, save_path)
        
        # 生成文本报告
        self._generate_text_report(save_path)
        
        print(f"\n所有可视化图表已保存到: {save_path}")
        print("可视化报告生成完成！")
    
    def _generate_text_report(self, save_path: str):
        """生成文本报告"""
        report_file = os.path.join(save_path, "allocation_report.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("连续时刻任务分配结果报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"生成时间: {self.comparison_data['generated_at']}\n\n")
            
            f.write("算法性能比较:\n")
            f.write("-" * 40 + "\n")
            
            for algorithm, metrics in self.comparison_data['comparison_results'].items():
                f.write(f"\n{algorithm} 算法:\n")
                f.write(f"  资源利用率: {metrics['resource_utilization']:.2f}%\n")
                f.write(f"  响应时间: {metrics['response_time']:.2f} 秒\n")
                f.write(f"  Makespan: {metrics['makespan']:.2f} 秒\n")
                f.write(f"  负载均衡度: {metrics['load_balance_degree']:.4f}\n")
                f.write(f"  目标函数值: {metrics['objective_value']:.4f}\n")
                f.write(f"  执行时间: {metrics['execution_time']:.4f} 秒\n")
            
            # 找出最佳算法
            best_algorithm = min(self.comparison_data['comparison_results'].items(),
                               key=lambda x: x[1]['objective_value'])
            
            f.write(f"\n最佳算法: {best_algorithm[0]}\n")
            f.write(f"最佳目标函数值: {best_algorithm[1]['objective_value']:.4f}\n")
        
        print(f"文本报告已保存: {report_file}")


def main():
    """主函数"""
    visualizer = AllocationVisualizer()
    visualizer.create_comprehensive_report()


if __name__ == "__main__":
    main() 