"""
连续时刻任务分配系统
结合改进图着色方法和粒子群算法
基于C++代码转换为Python实现
"""

import json
import random
import numpy as np
import time
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import os


class TaskColorType(Enum):
    """任务颜色类型"""
    CPU_INTENSIVE = 1
    MEMORY_INTENSIVE = 2
    NETWORK_INTENSIVE = 3
    GENERAL = 4


class AlgorithmType(Enum):
    """算法类型"""
    ROUND_ROBIN = "round_robin"
    PARTICLE_SWARM = "particle_swarm"
    HYBRID_COLORING_PSO = "hybrid_coloring_pso"


@dataclass
class ObjectiveWeights:
    """目标函数权重"""
    alpha: float = 0.2    # 资源利用率权重
    beta: float = 0.2     # 响应时间权重
    gamma: float = 0.2    # Makespan权重
    epsilon: float = 0.2  # 负载均衡权重
    xi: float = 0.2       # 总执行时间权重


@dataclass
class ResourceWeights:
    """资源权重"""
    cpu_weight: float = 0.4
    memory_weight: float = 0.3
    network_weight: float = 0.3


@dataclass
class AlgorithmParams:
    """算法参数"""
    max_iterations: int = 50
    population_size: int = 50
    inertia_weight: float = 0.7
    c1: float = 1.5  # 个体学习因子
    c2: float = 1.5  # 社会学习因子


@dataclass
class TaskColorInfo:
    """任务颜色信息"""
    task_id: str
    color_type: TaskColorType
    level: int
    dependency_processed: bool
    resource_index: float


@dataclass
class TaskExecutionTime:
    """任务执行时间"""
    cpu_time: float
    memory_time: float
    network_time: float
    total_time: float


@dataclass
class TaskAllocationResult:
    """任务分配结果"""
    node_assignment: List[str]
    resource_utilization: float
    response_time: float
    makespan: float
    load_balance_degree: float
    total_task_time: float
    objective_value: float
    convergence_history: List[float]
    execution_time: float
    node_to_tasks: Dict[str, List[str]]


class TaskColorizer:
    """任务着色器"""
    
    def __init__(self, tasks: Dict[str, Any], res_weights: ResourceWeights):
        self.tasks = tasks
        self.res_weights = res_weights
    
    def calculate_resource_index(self, task: Dict[str, Any]) -> float:
        """计算任务综合资源指数"""
        R_i = (self.res_weights.cpu_weight * task['cpu_requirement'] +
               self.res_weights.memory_weight * task['memory_requirement'] +
               self.res_weights.network_weight * task['network_requirement'])
        return R_i
    
    def determine_task_color_type(self, task: Dict[str, Any]) -> TaskColorType:
        """确定任务颜色类型"""
        R_i = self.calculate_resource_index(task)
        cpu_ratio = (self.res_weights.cpu_weight * task['cpu_requirement']) / R_i
        mem_ratio = (self.res_weights.memory_weight * task['memory_requirement']) / R_i
        net_ratio = (self.res_weights.network_weight * task['network_requirement']) / R_i
        
        # 如果某种资源占比超过综合指数60%，则认为是该类型的任务
        if cpu_ratio > 0.6:
            return TaskColorType.CPU_INTENSIVE
        elif mem_ratio > 0.6:
            return TaskColorType.MEMORY_INTENSIVE
        elif net_ratio > 0.6:
            return TaskColorType.NETWORK_INTENSIVE
        else:
            return TaskColorType.GENERAL
    
    def color_tasks(self) -> List[TaskColorInfo]:
        """贪心着色算法"""
        colored_tasks = []
        
        # 步骤1: 根据资源需求为每个任务分配初始颜色
        for task_id, task in self.tasks.items():
            color_info = TaskColorInfo(
                task_id=task_id,
                color_type=self.determine_task_color_type(task),
                level=0,
                dependency_processed=False,
                resource_index=self.calculate_resource_index(task)
            )
            colored_tasks.append(color_info)
        
        # 步骤2: 构建任务依赖图
        dependency_graph = {}
        indegree = {}
        
        for task_id, task in self.tasks.items():
            indegree[task_id] = len(task.get('dependencies', []))
            
            # 为每个依赖任务添加边
            for dep_task_id in task.get('dependencies', []):
                if dep_task_id not in dependency_graph:
                    dependency_graph[dep_task_id] = []
                dependency_graph[dep_task_id].append(task_id)
        
        # 步骤3: 使用拓扑排序确定任务级别
        zero_indegree = []
        
        # 找出入度为0的任务（没有依赖的任务）
        for task_id, task in self.tasks.items():
            if indegree[task_id] == 0:
                zero_indegree.append(task_id)
        
        current_level = 1
        while zero_indegree:
            size = len(zero_indegree)
            
            for _ in range(size):
                current_task = zero_indegree.pop(0)
                
                # 找到任务在colored_tasks中的索引
                task_index = -1
                for i, colored_task in enumerate(colored_tasks):
                    if colored_task.task_id == current_task:
                        task_index = i
                        break
                
                if task_index != -1:
                    colored_tasks[task_index].level = current_level
                    colored_tasks[task_index].dependency_processed = True
                
                # 更新依赖图
                if current_task in dependency_graph:
                    for dependent_task in dependency_graph[current_task]:
                        indegree[dependent_task] -= 1
                        if indegree[dependent_task] == 0:
                            zero_indegree.append(dependent_task)
            
            current_level += 1
        
        return colored_tasks


class ContinuousTimestepAllocator:
    """连续时刻任务分配器"""
    
    def __init__(self, tasks_data: Dict[str, Any], nodes_data: Dict[str, Any],
                 obj_weights: ObjectiveWeights, res_weights: ResourceWeights,
                 alg_params: AlgorithmParams):
        self.tasks_data = tasks_data
        self.nodes_data = nodes_data
        self.obj_weights = obj_weights
        self.res_weights = res_weights
        self.alg_params = alg_params
        
        self.tasks = tasks_data['tasks']
        self.nodes = nodes_data['nodes']
        self.task_ids = list(self.tasks.keys())
        self.node_ids = [node['node_id'] for node in self.nodes]
        
        # 初始化任务着色器
        self.colorizer = TaskColorizer(self.tasks, self.res_weights)
        self.colored_tasks = self.colorizer.color_tasks()
        
        # 按级别和颜色类型分组任务
        self.task_groups = self._group_tasks_by_level_and_color()
    
    def _group_tasks_by_level_and_color(self) -> Dict[Tuple[int, TaskColorType], List[str]]:
        """按级别和颜色类型分组任务"""
        groups = {}
        for colored_task in self.colored_tasks:
            key = (colored_task.level, colored_task.color_type)
            if key not in groups:
                groups[key] = []
            groups[key].append(colored_task.task_id)
        return groups
    
    def _find_node_index(self, node_id: str) -> int:
        """查找节点索引"""
        for i, node in enumerate(self.nodes):
            if node['node_id'] == node_id:
                return i
        return -1
    
    def _can_node_satisfy_task(self, task: Dict[str, Any], node: Dict[str, Any], 
                              current_allocation: List[str]) -> bool:
        """检查节点是否能够满足任务资源需求"""
        # 检查依赖关系是否满足
        if not self._are_dependencies_satisfied(task, current_allocation):
            return False
        
        # 检查是否会导致循环依赖
        if self._would_create_cyclic_dependency(task, node['node_id'], current_allocation):
            return False
        
        # 计算已分配的资源需求
        allocated_cpu = 0
        allocated_memory = 0
        allocated_network = 0
        
        for i, assigned_node_id in enumerate(current_allocation):
            if assigned_node_id == node['node_id'] and i < len(self.task_ids):
                task_id = self.task_ids[i]
                allocated_cpu += self.tasks[task_id]['cpu_requirement']
                allocated_memory += self.tasks[task_id]['memory_requirement']
                allocated_network += self.tasks[task_id]['network_requirement']
        
        # 检查节点是否有足够的剩余资源
        cpu_satisfied = node['available_resources']['cpu_available'] >= allocated_cpu + task['cpu_requirement']
        memory_satisfied = node['available_resources']['memory_available'] >= allocated_memory + task['memory_requirement']
        network_satisfied = node['available_resources']['network_available'] >= allocated_network + task['network_requirement']
        
        return cpu_satisfied and memory_satisfied and network_satisfied
    
    def _are_dependencies_satisfied(self, task: Dict[str, Any], current_allocation: List[str]) -> bool:
        """检查依赖关系是否满足"""
        dependencies = task.get('dependencies', [])
        if not dependencies:
            return True
        
        # 检查所有依赖任务是否已经分配
        for dep_task_id in dependencies:
            if dep_task_id not in self.task_ids:
                continue
            dep_index = self.task_ids.index(dep_task_id)
            if dep_index >= len(current_allocation) or current_allocation[dep_index] is None:
                return False
        return True
    
    def _would_create_cyclic_dependency(self, task: Dict[str, Any], node_id: str, 
                                       current_allocation: List[str]) -> bool:
        """检查是否会导致循环依赖"""
        # 简化的循环依赖检查
        # 在实际实现中，这里需要更复杂的图算法
        return False
    
    def _calculate_task_execution_time(self, task: Dict[str, Any], node: Dict[str, Any]) -> TaskExecutionTime:
        """计算任务执行时间"""
        cpu_time = task['cpu_requirement'] / node['available_resources']['cpu_available']
        memory_time = task['memory_requirement'] / node['available_resources']['memory_available']
        network_time = task['network_requirement'] / node['available_resources']['network_available']
        
        total_time = max(cpu_time, memory_time, network_time) * task['base_runtime']
        
        return TaskExecutionTime(
            cpu_time=cpu_time,
            memory_time=memory_time,
            network_time=network_time,
            total_time=total_time
        )
    
    def _calculate_objective_value(self, node_assignment: List[str]) -> float:
        """计算目标函数值"""
        if len(node_assignment) != len(self.task_ids):
            return float('inf')
        
        # 计算资源利用率
        node_utilization = {}
        for node_id in self.node_ids:
            node_utilization[node_id] = {'cpu': 0, 'memory': 0, 'network': 0}
        
        for i, node_id in enumerate(node_assignment):
            if node_id is None or i >= len(self.task_ids):
                continue
            task_id = self.task_ids[i]
            task = self.tasks[task_id]
            node_utilization[node_id]['cpu'] += task['cpu_requirement']
            node_utilization[node_id]['memory'] += task['memory_requirement']
            node_utilization[node_id]['network'] += task['network_requirement']
        
        # 计算平均资源利用率
        total_utilization = 0
        for node_id in self.node_ids:
            node = next(n for n in self.nodes if n['node_id'] == node_id)
            cpu_util = node_utilization[node_id]['cpu'] / node['cpu_capacity']
            mem_util = node_utilization[node_id]['memory'] / node['memory_capacity']
            net_util = node_utilization[node_id]['network'] / node['network_bandwidth']
            total_utilization += (cpu_util + mem_util + net_util) / 3
        
        avg_utilization = total_utilization / len(self.node_ids)
        
        # 计算负载均衡度（标准差）
        utilizations = []
        for node_id in self.node_ids:
            node = next(n for n in self.nodes if n['node_id'] == node_id)
            cpu_util = node_utilization[node_id]['cpu'] / node['cpu_capacity']
            mem_util = node_utilization[node_id]['memory'] / node['memory_capacity']
            net_util = node_utilization[node_id]['network'] / node['network_bandwidth']
            utilizations.append((cpu_util + mem_util + net_util) / 3)
        
        load_balance = np.std(utilizations) if len(utilizations) > 1 else 0
        
        # 计算总执行时间
        total_time = 0
        for i, node_id in enumerate(node_assignment):
            if node_id is None or i >= len(self.task_ids):
                continue
            task_id = self.task_ids[i]
            task = self.tasks[task_id]
            node = next(n for n in self.nodes if n['node_id'] == node_id)
            exec_time = self._calculate_task_execution_time(task, node)
            total_time += exec_time.total_time
        
        # 目标函数值（越小越好）
        objective_value = (self.obj_weights.alpha * (1 - avg_utilization) +
                          self.obj_weights.epsilon * load_balance +
                          self.obj_weights.xi * total_time)
        
        return objective_value
    
    def _select_node_based_on_resources(self, task: Dict[str, Any], 
                                       available_nodes: List[Dict[str, Any]]) -> str:
        """基于资源选择节点"""
        best_node_id = None
        best_score = float('inf')
        
        for node in available_nodes:
            if not self._can_node_satisfy_task(task, node, []):
                continue
            
            # 计算资源匹配分数
            cpu_score = task['cpu_requirement'] / node['available_resources']['cpu_available']
            memory_score = task['memory_requirement'] / node['available_resources']['memory_available']
            network_score = task['network_requirement'] / node['available_resources']['network_available']
            
            # 综合分数（越小越好）
            score = (self.res_weights.cpu_weight * cpu_score +
                    self.res_weights.memory_weight * memory_score +
                    self.res_weights.network_weight * network_score)
            
            if score < best_score:
                best_score = score
                best_node_id = node['node_id']
        
        return best_node_id or random.choice(self.node_ids)
    
    def particle_swarm_allocation(self) -> List[str]:
        """粒子群算法分配"""
        particle_count = self.alg_params.population_size
        max_iterations = self.alg_params.max_iterations
        convergence_history = []
        
        # 初始化粒子群
        particles = []
        velocities = []
        fitness = []
        pbest = []
        pbest_fitness = []
        
        for _ in range(particle_count):
            # 随机初始化粒子（使用节点索引而不是节点ID）
            particle = []
            for _ in range(len(self.task_ids)):
                particle.append(random.randint(0, len(self.node_ids) - 1))
            particles.append(particle)
            
            # 初始化速度
            velocity = [random.uniform(-1, 1) for _ in range(len(self.task_ids))]
            velocities.append(velocity)
            
            # 计算适应度
            particle_node_assignment = [self.node_ids[idx] for idx in particle]
            fitness_value = 1.0 / (self._calculate_objective_value(particle_node_assignment) + 1e-10)
            fitness.append(fitness_value)
            
            # 初始化个体最优
            pbest.append(particle.copy())
            pbest_fitness.append(fitness_value)
        
        # 全局最优
        gbest_idx = np.argmax(fitness)
        gbest = particles[gbest_idx].copy()
        convergence_history.append(1.0 / fitness[gbest_idx])
        
        # 迭代优化
        for iteration in range(max_iterations):
            for i in range(particle_count):
                # 更新速度和位置
                for j in range(len(self.task_ids)):
                    r1 = random.random()
                    r2 = random.random()
                    
                    velocities[i][j] = (self.alg_params.inertia_weight * velocities[i][j] +
                                       self.alg_params.c1 * r1 * (pbest[i][j] - particles[i][j]) +
                                       self.alg_params.c2 * r2 * (gbest[j] - particles[i][j]))
                    
                    # 更新位置
                    particles[i][j] += velocities[i][j]
                    
                    # 确保在有效范围内
                    particles[i][j] = max(0, min(particles[i][j], len(self.node_ids) - 1))
                
                # 计算新适应度
                particle_node_assignment = [self.node_ids[int(round(idx))] for idx in particles[i]]
                fitness[i] = 1.0 / (self._calculate_objective_value(particle_node_assignment) + 1e-10)
                
                # 更新个体最优
                if fitness[i] > pbest_fitness[i]:
                    pbest[i] = particles[i].copy()
                    pbest_fitness[i] = fitness[i]
            
            # 更新全局最优
            gbest_idx = np.argmax(fitness)
            gbest = particles[gbest_idx].copy()
            convergence_history.append(1.0 / fitness[gbest_idx])
        
        # 返回最终的节点分配
        final_assignment = [self.node_ids[int(round(idx))] for idx in gbest]
        return final_assignment
    
    def hybrid_coloring_pso_allocation(self) -> List[str]:
        """混合图着色-粒子群算法分配"""
        # 步骤1: 使用图着色进行初始分配
        initial_assignment = self._coloring_based_allocation()
        
        # 步骤2: 使用粒子群算法优化
        optimized_assignment = self.particle_swarm_allocation()
        
        # 步骤3: 比较两种方法，选择更好的结果
        initial_objective = self._calculate_objective_value(initial_assignment)
        optimized_objective = self._calculate_objective_value(optimized_assignment)
        
        if initial_objective < optimized_objective:
            return initial_assignment
        else:
            return optimized_assignment
    
    def _coloring_based_allocation(self) -> List[str]:
        """基于图着色的分配"""
        assignment = [None] * len(self.task_ids)
        
        # 按级别和颜色类型分配任务
        for (level, color_type), task_group in sorted(self.task_groups.items()):
            # 为每个颜色类型选择最适合的节点
            suitable_nodes = self._get_suitable_nodes_for_color(color_type)
            
            for task_id in task_group:
                task = self.tasks[task_id]
                task_index = self.task_ids.index(task_id)
                
                # 选择最适合的节点
                best_node_id = self._select_node_based_on_resources(task, suitable_nodes)
                assignment[task_index] = best_node_id
        
        return assignment
    
    def _get_suitable_nodes_for_color(self, color_type: TaskColorType) -> List[Dict[str, Any]]:
        """获取适合特定颜色类型的节点"""
        suitable_nodes = []
        
        for node in self.nodes:
            if color_type == TaskColorType.CPU_INTENSIVE:
                if node['node_type'] == 'cpu_intensive':
                    suitable_nodes.append(node)
            elif color_type == TaskColorType.MEMORY_INTENSIVE:
                if node['node_type'] == 'memory_intensive':
                    suitable_nodes.append(node)
            elif color_type == TaskColorType.NETWORK_INTENSIVE:
                if node['node_type'] == 'io_intensive':  # 网络密集型任务可以分配给I/O密集型节点
                    suitable_nodes.append(node)
            else:  # GENERAL
                suitable_nodes.append(node)
        
        return suitable_nodes if suitable_nodes else self.nodes
    
    def allocate_tasks(self, algorithm: AlgorithmType) -> TaskAllocationResult:
        """分配任务"""
        start_time = time.time()
        
        # 根据算法类型选择分配方法
        if algorithm == AlgorithmType.ROUND_ROBIN:
            node_assignment = self._round_robin_allocation()
        elif algorithm == AlgorithmType.PARTICLE_SWARM:
            node_assignment = self.particle_swarm_allocation()
        elif algorithm == AlgorithmType.HYBRID_COLORING_PSO:
            node_assignment = self.hybrid_coloring_pso_allocation()
        else:
            raise ValueError(f"不支持的算法类型: {algorithm}")
        
        execution_time = time.time() - start_time
        
        # 计算结果指标
        result = self._calculate_metrics(node_assignment, execution_time)
        
        return result
    
    def _round_robin_allocation(self) -> List[str]:
        """轮询分配"""
        assignment = []
        node_index = 0
        
        for task_id in self.task_ids:
            assignment.append(self.node_ids[node_index])
            node_index = (node_index + 1) % len(self.node_ids)
        
        return assignment
    
    def _calculate_metrics(self, node_assignment: List[str], execution_time: float) -> TaskAllocationResult:
        """计算结果指标"""
        # 计算资源利用率
        node_utilization = {}
        for node_id in self.node_ids:
            node_utilization[node_id] = {'cpu': 0, 'memory': 0, 'network': 0}
        
        node_to_tasks = {node_id: [] for node_id in self.node_ids}
        
        for i, node_id in enumerate(node_assignment):
            if node_id is None or i >= len(self.task_ids):
                continue
            task_id = self.task_ids[i]
            task = self.tasks[task_id]
            node_utilization[node_id]['cpu'] += task['cpu_requirement']
            node_utilization[node_id]['memory'] += task['memory_requirement']
            node_utilization[node_id]['network'] += task['network_requirement']
            node_to_tasks[node_id].append(task_id)
        
        # 计算平均资源利用率
        total_utilization = 0
        for node_id in self.node_ids:
            node = next(n for n in self.nodes if n['node_id'] == node_id)
            cpu_util = node_utilization[node_id]['cpu'] / node['cpu_capacity']
            mem_util = node_utilization[node_id]['memory'] / node['memory_capacity']
            net_util = node_utilization[node_id]['network'] / node['network_bandwidth']
            total_utilization += (cpu_util + mem_util + net_util) / 3
        
        avg_utilization = total_utilization / len(self.node_ids)
        
        # 计算负载均衡度
        utilizations = []
        for node_id in self.node_ids:
            node = next(n for n in self.nodes if n['node_id'] == node_id)
            cpu_util = node_utilization[node_id]['cpu'] / node['cpu_capacity']
            mem_util = node_utilization[node_id]['memory'] / node['memory_capacity']
            net_util = node_utilization[node_id]['network'] / node['network_bandwidth']
            utilizations.append((cpu_util + mem_util + net_util) / 3)
        
        load_balance = np.std(utilizations) if len(utilizations) > 1 else 0
        
        # 计算总执行时间
        total_time = 0
        for i, node_id in enumerate(node_assignment):
            if node_id is None or i >= len(self.task_ids):
                continue
            task_id = self.task_ids[i]
            task = self.tasks[task_id]
            node = next(n for n in self.nodes if n['node_id'] == node_id)
            exec_time = self._calculate_task_execution_time(task, node)
            total_time += exec_time.total_time
        
        # 计算目标函数值
        objective_value = self._calculate_objective_value(node_assignment)
        
        return TaskAllocationResult(
            node_assignment=node_assignment,
            resource_utilization=avg_utilization * 100,  # 转换为百分比
            response_time=total_time / len(self.task_ids),
            makespan=total_time,
            load_balance_degree=load_balance,
            total_task_time=total_time,
            objective_value=objective_value,
            convergence_history=[],  # 简化版本，不记录收敛历史
            execution_time=execution_time,
            node_to_tasks=node_to_tasks
        )


def main():
    """主函数"""
    print("=" * 60)
    print("连续时刻任务分配系统")
    print("=" * 60)
    
    # 加载数据
    try:
        with open("New_data/tasks.json", 'r', encoding='utf-8') as f:
            tasks_data = json.load(f)
        
        with open("New_data/nodes.json", 'r', encoding='utf-8') as f:
            nodes_data = json.load(f)
        
        print(f"加载了 {len(tasks_data['tasks'])} 个任务")
        print(f"加载了 {len(nodes_data['nodes'])} 个节点")
    except FileNotFoundError:
        print("错误：找不到数据文件，请先运行数据生成器")
        return
    
    # 设置参数
    obj_weights = ObjectiveWeights()
    res_weights = ResourceWeights()
    alg_params = AlgorithmParams()
    
    # 创建分配器
    allocator = ContinuousTimestepAllocator(
        tasks_data, nodes_data, obj_weights, res_weights, alg_params
    )
    
    # 测试不同算法
    algorithms = [
        AlgorithmType.ROUND_ROBIN,
        AlgorithmType.PARTICLE_SWARM,
        AlgorithmType.HYBRID_COLORING_PSO
    ]
    
    results = {}
    
    for algorithm in algorithms:
        print(f"\n正在运行 {algorithm.value}...")
        result = allocator.allocate_tasks(algorithm)
        results[algorithm] = result
        
        print(f"算法: {algorithm.value}")
        print(f"资源利用率: {result.resource_utilization:.2f}%")
        print(f"响应时间: {result.response_time:.2f} 秒")
        print(f"Makespan: {result.makespan:.2f} 秒")
        print(f"负载均衡度: {result.load_balance_degree:.4f}")
        print(f"目标函数值: {result.objective_value:.4f}")
        print(f"执行时间: {result.execution_time:.4f} 秒")
    
    # 保存结果
    output_dir = "allocation_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存详细结果
    for algorithm, result in results.items():
        filename = f"{output_dir}/{algorithm.value}_result.json"
        
        result_dict = {
            'algorithm': algorithm.value,
            'node_assignment': result.node_assignment,
            'resource_utilization': result.resource_utilization,
            'response_time': result.response_time,
            'makespan': result.makespan,
            'load_balance_degree': result.load_balance_degree,
            'total_task_time': result.total_task_time,
            'objective_value': result.objective_value,
            'execution_time': result.execution_time,
            'node_to_tasks': result.node_to_tasks,
            'generated_at': datetime.now().isoformat()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result_dict, f, indent=2, ensure_ascii=False)
        
        print(f"结果已保存: {filename}")
    
    # 保存比较结果
    comparison_file = f"{output_dir}/algorithm_comparison.json"
    comparison_data = {
        'comparison_results': {
            algorithm.value: {
                'resource_utilization': result.resource_utilization,
                'response_time': result.response_time,
                'makespan': result.makespan,
                'load_balance_degree': result.load_balance_degree,
                'objective_value': result.objective_value,
                'execution_time': result.execution_time
            }
            for algorithm, result in results.items()
        },
        'generated_at': datetime.now().isoformat()
    }
    
    with open(comparison_file, 'w', encoding='utf-8') as f:
        json.dump(comparison_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n算法比较结果已保存: {comparison_file}")
    print("\n连续时刻任务分配完成！")


if __name__ == "__main__":
    main() 