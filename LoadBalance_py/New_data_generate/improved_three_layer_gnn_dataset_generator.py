"""
改进的Three Layer GNN数据集生成器
使用人工蜂群算法生成最优分配，支持带颜色特征和不带颜色特征两种数据集
"""

import json
import random
import numpy as np
import pandas as pd
from datetime import datetime
import os
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
import pickle
import torch
import networkx as nx
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split


@dataclass
class ImprovedThreeLayerGNNConfig:
    """改进的Three Layer GNN数据集配置"""
    # 数据集规模
    total_samples: int = 10000
    train_ratio: float = 0.7
    val_ratio: float = 0.15
    test_ratio: float = 0.15
    
    # 任务和节点数量范围
    min_tasks: int = 10
    max_tasks: int = 100
    min_nodes: int = 5
    max_nodes: int = 30
    
    # 特征维度（匹配three_layer_gnn模型）
    task_feature_dim: int = 128
    node_feature_dim: int = 32
    resource_dim: int = 2  # CPU和内存约束
    
    # 图结构参数
    max_dag_depth: int = 8
    max_dag_width: int = 15
    dependency_probability: float = 0.3
    
    # 人工蜂群算法参数
    population_size: int = 50
    max_iterations: int = 100
    limit: int = 10  # 停滞限制
    elite_ratio: float = 0.1  # 精英解比例
    scout_ratio: float = 0.2  # 侦查蜂比例
    
    # 图着色参数
    enable_coloring: bool = True  # 是否启用图着色特征
    max_colors: int = 10  # 最大颜色数
    
    # 数据质量参数
    normalize_features: bool = True
    add_noise: bool = True
    noise_level: float = 0.05
    
    random_seed: int = 42


class ArtificialBeeColonyAllocator:
    """人工蜂群算法分配器"""
    
    def __init__(self, population_size: int = 50, max_iterations: int = 100, limit: int = 10):
        self.population_size = population_size
        self.max_iterations = max_iterations
        self.limit = limit
        self.elite_count = int(population_size * 0.1)
        self.scout_count = int(population_size * 0.2)
        self.employed_count = population_size - self.elite_count - self.scout_count
    
    def allocate_tasks(self, tasks: List[Dict], nodes: List[Dict], dag: nx.DiGraph) -> Dict[str, Any]:
        """使用人工蜂群算法分配任务"""
        num_tasks = len(tasks)
        num_nodes = len(nodes)
        
        # 初始化种群
        population = []
        fitness = []
        trials = []
        
        for i in range(self.population_size):
            # 生成随机解（考虑DAG依赖关系）
            solution = self._generate_valid_solution(tasks, nodes, dag)
            population.append(solution)
            fitness.append(self._calculate_fitness(solution, tasks, nodes, dag))
            trials.append(0)
        
        # 记录最佳解
        best_idx = np.argmax(fitness)
        best_solution = population[best_idx].copy()
        best_fitness = fitness[best_idx]
        
        convergence_history = [1.0 / best_fitness]
        
        # 主循环
        for iteration in range(self.max_iterations):
            # 雇佣蜂阶段
            for i in range(self.employed_count):
                # 局部搜索
                new_solution = self._local_search(population[i], tasks, nodes, dag)
                new_fitness = self._calculate_fitness(new_solution, tasks, nodes, dag)
                
                if new_fitness > fitness[i]:
                    population[i] = new_solution
                    fitness[i] = new_fitness
                    trials[i] = 0
                else:
                    trials[i] += 1
            
            # 观察蜂阶段
            for i in range(self.elite_count):
                # 选择解进行改进
                selected_idx = self._roulette_wheel_selection(fitness[:self.employed_count])
                new_solution = self._local_search(population[selected_idx], tasks, nodes, dag)
                new_fitness = self._calculate_fitness(new_solution, tasks, nodes, dag)
                
                if new_fitness > fitness[selected_idx]:
                    population[selected_idx] = new_solution
                    fitness[selected_idx] = new_fitness
                    trials[selected_idx] = 0
                else:
                    trials[selected_idx] += 1
            
            # 侦查蜂阶段
            for i in range(self.scout_count):
                # 重新初始化停滞的解
                if trials[i] > self.limit:
                    population[i] = self._generate_valid_solution(tasks, nodes, dag)
                    fitness[i] = self._calculate_fitness(population[i], tasks, nodes, dag)
                    trials[i] = 0
            
            # 更新最佳解
            current_best_idx = np.argmax(fitness)
            if fitness[current_best_idx] > best_fitness:
                best_solution = population[current_best_idx].copy()
                best_fitness = fitness[current_best_idx]
            
            convergence_history.append(1.0 / best_fitness)
        
        # 计算分配质量
        allocation_quality = self._calculate_allocation_quality(best_solution, tasks, nodes, dag)
        
        return {
            'task_assignments': {f'task_{i}': f'node_{best_solution[i]}' for i in range(num_tasks)},
            'node_loads': self._calculate_node_loads(best_solution, tasks, nodes),
            'allocation_quality': allocation_quality,
            'convergence_history': convergence_history,
            'best_fitness': best_fitness
        }
    
    def _generate_valid_solution(self, tasks: List[Dict], nodes: List[Dict], dag: nx.DiGraph) -> List[int]:
        """生成有效的解（考虑DAG依赖关系）"""
        num_tasks = len(tasks)
        num_nodes = len(nodes)
        solution = [-1] * num_tasks
        
        # 拓扑排序
        try:
            topo_order = list(nx.topological_sort(dag))
        except:
            topo_order = list(range(num_tasks))
        
        # 按拓扑顺序分配任务
        for task_id in topo_order:
            # 检查依赖关系
            valid_nodes = []
            for node_id in range(num_nodes):
                if self._can_assign_task(task_id, node_id, tasks, nodes, solution):
                    valid_nodes.append(node_id)
            
            if valid_nodes:
                # 随机选择一个有效节点
                solution[task_id] = random.choice(valid_nodes)
            else:
                # 如果没有有效节点，选择负载最低的节点
                solution[task_id] = self._select_least_loaded_node(nodes, solution)
        
        return solution
    
    def _can_assign_task(self, task_id: int, node_id: int, tasks: List[Dict], 
                        nodes: List[Dict], current_solution: List[int]) -> bool:
        """检查是否可以将任务分配给节点"""
        task = tasks[task_id]
        node = nodes[node_id]
        
        # 检查资源约束
        if (task['cpu_requirement'] > node['cpu_available'] or 
            task['memory_requirement'] > node['memory_available']):
            return False
        
        # 检查依赖关系（简化版）
        return True
    
    def _select_least_loaded_node(self, nodes: List[Dict], current_solution: List[int]) -> int:
        """选择负载最低的节点"""
        node_loads = [0.0] * len(nodes)
        
        for task_id, node_id in enumerate(current_solution):
            if node_id >= 0:
                node_loads[node_id] += 1.0
        
        return np.argmin(node_loads)
    
    def _local_search(self, solution: List[int], tasks: List[Dict], 
                     nodes: List[Dict], dag: nx.DiGraph) -> List[int]:
        """局部搜索"""
        new_solution = solution.copy()
        num_tasks = len(tasks)
        
        # 随机选择一些任务进行重新分配
        num_changes = max(1, num_tasks // 10)
        tasks_to_change = random.sample(range(num_tasks), num_changes)
        
        for task_id in tasks_to_change:
            # 尝试重新分配任务
            current_node = new_solution[task_id]
            best_node = current_node
            best_fitness = self._calculate_fitness(new_solution, tasks, nodes, dag)
            
            for node_id in range(len(nodes)):
                if node_id != current_node and self._can_assign_task(task_id, node_id, tasks, nodes, new_solution):
                    new_solution[task_id] = node_id
                    new_fitness = self._calculate_fitness(new_solution, tasks, nodes, dag)
                    
                    if new_fitness > best_fitness:
                        best_fitness = new_fitness
                        best_node = node_id
                    else:
                        new_solution[task_id] = current_node
            
            new_solution[task_id] = best_node
        
        return new_solution
    
    def _calculate_fitness(self, solution: List[int], tasks: List[Dict], 
                          nodes: List[Dict], dag: nx.DiGraph) -> float:
        """计算适应度（目标函数值的倒数）"""
        objective_value = self._calculate_objective_value(solution, tasks, nodes, dag)
        return 1.0 / (objective_value + 1e-10)
    
    def _calculate_objective_value(self, solution: List[int], tasks: List[Dict], 
                                 nodes: List[Dict], dag: nx.DiGraph) -> float:
        """计算目标函数值"""
        # 计算资源利用率
        resource_utilization = self._calculate_resource_utilization(solution, tasks, nodes)
        
        # 计算负载均衡度
        load_balance = self._calculate_load_balance(solution, tasks, nodes)
        
        # 计算依赖满足度
        dependency_satisfaction = self._calculate_dependency_satisfaction(solution, dag)
        
        # 综合目标函数
        objective_value = (resource_utilization * 0.4 + 
                          load_balance * 0.3 + 
                          dependency_satisfaction * 0.3)
        
        return objective_value
    
    def _calculate_resource_utilization(self, solution: List[int], tasks: List[Dict], 
                                      nodes: List[Dict]) -> float:
        """计算资源利用率"""
        total_cpu_used = 0.0
        total_memory_used = 0.0
        total_cpu_capacity = 0.0
        total_memory_capacity = 0.0
        
        for node_id, node in enumerate(nodes):
            total_cpu_capacity += node['cpu_capacity']
            total_memory_capacity += node['memory_capacity']
            
            for task_id, assigned_node in enumerate(solution):
                if assigned_node == node_id:
                    total_cpu_used += tasks[task_id]['cpu_requirement']
                    total_memory_used += tasks[task_id]['memory_requirement']
        
        cpu_utilization = total_cpu_used / total_cpu_capacity if total_cpu_capacity > 0 else 0.0
        memory_utilization = total_memory_used / total_memory_capacity if total_memory_capacity > 0 else 0.0
        
        return (cpu_utilization + memory_utilization) / 2.0
    
    def _calculate_load_balance(self, solution: List[int], tasks: List[Dict], 
                              nodes: List[Dict]) -> float:
        """计算负载均衡度"""
        node_loads = [0.0] * len(nodes)
        
        for task_id, node_id in enumerate(solution):
            if node_id >= 0:
                task_load = (tasks[task_id]['cpu_requirement'] + tasks[task_id]['memory_requirement']) / 2.0
                node_loads[node_id] += task_load
        
        if not node_loads:
            return 0.0
        
        mean_load = np.mean(node_loads)
        std_load = np.std(node_loads)
        
        if mean_load == 0:
            return 1.0
        
        # 负载均衡度 = 1 - 变异系数
        return 1.0 - (std_load / mean_load)
    
    def _calculate_dependency_satisfaction(self, solution: List[int], dag: nx.DiGraph) -> float:
        """计算依赖满足度"""
        satisfied_dependencies = 0
        total_dependencies = 0
        
        for edge in dag.edges():
            pred, succ = edge
            total_dependencies += 1
            
            if (solution[pred] == solution[succ] and solution[pred] >= 0):
                satisfied_dependencies += 1
        
        return satisfied_dependencies / total_dependencies if total_dependencies > 0 else 1.0
    
    def _calculate_node_loads(self, solution: List[int], tasks: List[Dict], 
                            nodes: List[Dict]) -> Dict[str, float]:
        """计算节点负载"""
        node_loads = {}
        
        for node_id, node in enumerate(nodes):
            load = 0.0
            for task_id, assigned_node in enumerate(solution):
                if assigned_node == node_id:
                    load += (tasks[task_id]['cpu_requirement'] + tasks[task_id]['memory_requirement']) / 2.0
            
            node_loads[f'node_{node_id}'] = load
        
        return node_loads
    
    def _calculate_allocation_quality(self, solution: List[int], tasks: List[Dict], 
                                    nodes: List[Dict], dag: nx.DiGraph) -> Dict[str, float]:
        """计算分配质量指标"""
        resource_utilization = self._calculate_resource_utilization(solution, tasks, nodes)
        load_balance = self._calculate_load_balance(solution, tasks, nodes)
        dependency_satisfaction = self._calculate_dependency_satisfaction(solution, dag)
        
        # 计算makespan（简化版）
        node_loads = self._calculate_node_loads(solution, tasks, nodes)
        makespan = max(node_loads.values()) if node_loads else 0.0
        
        # 综合目标函数值
        objective_value = (resource_utilization * 0.4 + 
                          load_balance * 0.3 + 
                          dependency_satisfaction * 0.3)
        
        return {
            'resource_utilization': resource_utilization,
            'load_balance': load_balance,
            'makespan': makespan,
            'dependency_satisfaction': dependency_satisfaction,
            'objective_value': objective_value
        }
    
    def _roulette_wheel_selection(self, fitness: List[float]) -> int:
        """轮盘赌选择"""
        total_fitness = sum(fitness)
        if total_fitness == 0:
            return random.randint(0, len(fitness) - 1)
        
        r = random.uniform(0, total_fitness)
        cumulative_fitness = 0
        
        for i, f in enumerate(fitness):
            cumulative_fitness += f
            if cumulative_fitness >= r:
                return i
        
        return len(fitness) - 1


class ImprovedGraphColoring:
    """改进的图着色算法"""
    
    def __init__(self, max_colors: int = 10):
        self.max_colors = max_colors
    
    def color_graph(self, dag: nx.DiGraph) -> Dict[int, int]:
        """为DAG着色"""
        colors = {}
        available_colors = set(range(self.max_colors))
        
        # 拓扑排序
        try:
            topo_order = list(nx.topological_sort(dag))
        except:
            topo_order = list(dag.nodes())
        
        for node in topo_order:
            # 找出已着色的邻居节点
            neighbors = list(dag.predecessors(node)) + list(dag.successors(node))
            used_colors = {colors[neighbor] for neighbor in neighbors if neighbor in colors}
            
            # 选择第一个可用颜色
            available = available_colors - used_colors
            if available:
                colors[node] = min(available)
            else:
                # 如果没有可用颜色，随机选择一个
                colors[node] = random.randint(0, self.max_colors - 1)
        
        return colors
    
    def extract_coloring_features(self, dag: nx.DiGraph, colors: Dict[int, int]) -> Dict[int, List[float]]:
        """提取着色特征"""
        coloring_features = {}
        
        for node in dag.nodes():
            features = []
            
            # 颜色特征
            features.append(colors.get(node, 0) / self.max_colors)  # 归一化颜色
            
            # 邻居颜色特征
            neighbors = list(dag.predecessors(node)) + list(dag.successors(node))
            neighbor_colors = [colors.get(n, 0) for n in neighbors]
            
            if neighbor_colors:
                features.append(np.mean(neighbor_colors) / self.max_colors)
                features.append(np.std(neighbor_colors) / self.max_colors)
                features.append(len(set(neighbor_colors)) / len(neighbor_colors))  # 颜色多样性
            else:
                features.extend([0.0, 0.0, 0.0])
            
            # 着色质量特征
            color_conflicts = sum(1 for n in neighbors if colors.get(n, 0) == colors.get(node, 0))
            features.append(color_conflicts / max(len(neighbors), 1))
            
            # 填充到8维
            while len(features) < 8:
                features.append(0.0)
            
            coloring_features[node] = features[:8]
        
        return coloring_features


class ImprovedThreeLayerGNNDatasetGenerator:
    """改进的Three Layer GNN数据集生成器"""
    
    def __init__(self, config: ImprovedThreeLayerGNNConfig):
        self.config = config
        self.scaler = StandardScaler()
        self.abc_allocator = ArtificialBeeColonyAllocator(
            population_size=config.population_size,
            max_iterations=config.max_iterations,
            limit=config.limit
        )
        self.graph_coloring = ImprovedGraphColoring(config.max_colors)
        
        # 设置随机种子
        random.seed(config.random_seed)
        np.random.seed(config.random_seed)
        torch.manual_seed(config.random_seed)
        
        # 任务类型定义
        self.task_types = {
            'cpu_intensive': {
                'cpu_range': (2.0, 8.0),
                'memory_range': (2048.0, 16384.0),
                'io_range': (64.0, 512.0),
                'network_range': (32.0, 256.0),
                'runtime_range': (60.0, 600.0)
            },
            'memory_intensive': {
                'cpu_range': (1.0, 4.0),
                'memory_range': (8192.0, 65536.0),
                'io_range': (128.0, 1024.0),
                'network_range': (32.0, 256.0),
                'runtime_range': (120.0, 1200.0)
            },
            'io_intensive': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (1024.0, 8192.0),
                'io_range': (512.0, 4096.0),
                'network_range': (16.0, 128.0),
                'runtime_range': (90.0, 900.0)
            },
            'network_intensive': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (1024.0, 8192.0),
                'io_range': (32.0, 256.0),
                'network_range': (256.0, 2048.0),
                'runtime_range': (60.0, 600.0)
            },
            'general': {
                'cpu_range': (1.0, 4.0),
                'memory_range': (2048.0, 16384.0),
                'io_range': (64.0, 512.0),
                'network_range': (32.0, 256.0),
                'runtime_range': (90.0, 720.0)
            }
        }
        
        # 节点类型定义
        self.node_types = {
            'cpu_intensive': {
                'cpu_range': (8.0, 32.0),
                'memory_range': (16384.0, 131072.0),
                'io_range': (1024.0, 8192.0),
                'network_range': (512.0, 4096.0)
            },
            'memory_intensive': {
                'cpu_range': (4.0, 16.0),
                'memory_range': (32768.0, 262144.0),
                'io_range': (2048.0, 16384.0),
                'network_range': (256.0, 2048.0)
            },
            'io_intensive': {
                'cpu_range': (4.0, 16.0),
                'memory_range': (16384.0, 131072.0),
                'io_range': (4096.0, 32768.0),
                'network_range': (256.0, 2048.0)
            },
            'general': {
                'cpu_range': (6.0, 24.0),
                'memory_range': (24576.0, 196608.0),
                'io_range': (1536.0, 12288.0),
                'network_range': (384.0, 3072.0)
            }
        }
    
    def generate_dag_structure(self, num_tasks: int) -> nx.DiGraph:
        """生成DAG结构"""
        dag = nx.DiGraph()
        
        # 添加节点
        for i in range(num_tasks):
            dag.add_node(i)
        
        # 生成依赖关系（确保是DAG）
        for i in range(num_tasks):
            # 为每个任务添加一些依赖
            num_deps = random.randint(0, min(3, i))  # 最多依赖3个前面的任务
            
            for _ in range(num_deps):
                # 随机选择一个前面的任务作为依赖
                dep_task = random.randint(0, i-1)
                
                # 检查是否会形成环
                dag.add_edge(dep_task, i)
                
                # 如果形成环，移除这条边
                if not nx.is_directed_acyclic_graph(dag):
                    dag.remove_edge(dep_task, i)
        
        # 确保图是连通的（添加一些随机边）
        for i in range(1, num_tasks):
            if dag.in_degree(i) == 0:
                # 如果某个节点没有入边，随机添加一个依赖
                dep_task = random.randint(0, i-1)
                dag.add_edge(dep_task, i)
        
        return dag
    
    def generate_task_features(self, task_id: int, task_type: str, dag: nx.DiGraph, 
                             colors: Optional[Dict[int, int]] = None) -> Dict[str, Any]:
        """生成任务特征（支持颜色特征）"""
        task_config = self.task_types[task_type]
        
        # 基础资源需求
        cpu_req = round(random.uniform(*task_config['cpu_range']), 2)
        memory_req = round(random.uniform(*task_config['memory_range']), 2)
        io_req = round(random.uniform(*task_config['io_range']), 2)
        network_req = round(random.uniform(*task_config['network_range']), 2)
        runtime = round(random.uniform(*task_config['runtime_range']), 2)
        
        # 生成128维特征向量
        task_features = np.zeros(self.config.task_feature_dim)
        
        # 基础任务特征 (32维)
        task_features[0] = np.log1p(runtime)
        task_features[1] = runtime / 100.0
        task_features[2] = np.log1p(cpu_req)
        task_features[3] = np.log1p(memory_req)
        task_features[4] = np.log1p(io_req + network_req)
        task_features[5] = (io_req + network_req) / max(cpu_req + memory_req, 1e-6)
        task_features[6] = runtime / max(cpu_req + memory_req + io_req + network_req, 1e-6)
        task_features[7] = (cpu_req + memory_req + io_req + network_req) / max(runtime, 1e-6)
        
        # 资源需求特征 (16维)
        total_demand = cpu_req + memory_req + io_req + network_req
        if total_demand > 0:
            task_features[32] = cpu_req / total_demand
            task_features[33] = memory_req / total_demand
            task_features[34] = io_req / total_demand
            task_features[35] = network_req / total_demand
        
        task_features[36] = np.log1p(cpu_req)
        task_features[37] = np.log1p(memory_req)
        task_features[38] = np.log1p(io_req)
        task_features[39] = np.log1p(network_req)
        
        # DAG结构特征 (24维)
        task_features[56] = dag.in_degree(task_id)
        task_features[57] = dag.out_degree(task_id)
        task_features[58] = dag.degree(task_id)
        
        # 着色特征 (8维) - 根据配置决定是否启用
        if self.config.enable_coloring and colors is not None:
            coloring_features = self.graph_coloring.extract_coloring_features(dag, colors)[task_id]
            task_features[80:88] = coloring_features
        else:
            # 不使用着色特征时，填充随机值
            task_features[80:88] = np.random.normal(0, 0.1, 8)
        
        # 工作流上下文特征 (24维)
        task_features[88] = len(dag.nodes()) / 100.0
        task_features[89] = len(dag.edges()) / max(len(dag.nodes()), 1)
        
        # 统计特征 (24维)
        all_runtimes = [dag.nodes[node].get('runtime', 0) for node in dag.nodes()]
        if all_runtimes:
            task_features[112] = np.mean(all_runtimes)
            task_features[113] = np.std(all_runtimes)
            task_features[114] = (runtime - np.mean(all_runtimes)) / max(np.std(all_runtimes), 1e-6)
        
        return {
            'task_id': task_id,
            'task_type': task_type,
            'cpu_requirement': cpu_req,
            'memory_requirement': memory_req,
            'io_requirement': io_req,
            'network_requirement': network_req,
            'runtime': runtime,
            'task_features': task_features,
            'total_resource_demand': total_demand
        }
    
    def generate_node_features(self, node_id: int, node_type: str) -> Dict[str, Any]:
        """生成节点特征"""
        node_config = self.node_types[node_type]
        
        # 基础资源容量
        cpu_cap = round(random.uniform(*node_config['cpu_range']), 2)
        memory_cap = round(random.uniform(*node_config['memory_range']), 2)
        io_cap = round(random.uniform(*node_config['io_range']), 2)
        network_cap = round(random.uniform(*node_config['network_range']), 2)
        
        # 当前负载
        current_load = round(random.uniform(0.1, 0.8), 2)
        
        # 计算可用资源
        cpu_available = cpu_cap * (1 - current_load)
        memory_available = memory_cap * (1 - current_load)
        io_available = io_cap * (1 - current_load)
        network_available = network_cap * (1 - current_load)
        
        # 生成32维特征向量
        node_features = np.zeros(self.config.node_feature_dim)
        
        # 基础容量特征
        node_features[0] = np.log1p(cpu_cap)
        node_features[1] = np.log1p(memory_cap)
        node_features[2] = np.log1p(io_cap)
        node_features[3] = np.log1p(network_cap)
        
        # 可用资源特征
        node_features[4] = cpu_available
        node_features[5] = memory_available
        node_features[6] = io_available
        node_features[7] = network_available
        
        # 效率特征
        node_features[8] = current_load
        node_features[9] = 1.0 - current_load  # 可用率
        
        # 资源类型特征
        total_capacity = cpu_cap + memory_cap + io_cap + network_cap
        if total_capacity > 0:
            node_features[10] = cpu_cap / total_capacity
            node_features[11] = memory_cap / total_capacity
            node_features[12] = io_cap / total_capacity
            node_features[13] = network_cap / total_capacity
        
        return {
            'node_id': node_id,
            'node_type': node_type,
            'cpu_capacity': cpu_cap,
            'memory_capacity': memory_cap,
            'io_bandwidth': io_cap,
            'network_bandwidth': network_cap,
            'current_load': current_load,
            'cpu_available': cpu_available,
            'memory_available': memory_available,
            'io_available': io_available,
            'network_available': network_available,
            'total_capacity': total_capacity,
            'node_features': node_features
        }
    
    def generate_optimal_allocation(self, tasks: List[Dict], nodes: List[Dict], 
                                  dag: nx.DiGraph) -> Dict[str, Any]:
        """使用人工蜂群算法生成最优分配"""
        return self.abc_allocator.allocate_tasks(tasks, nodes, dag)
    
    def prepare_batch_data(self, tasks: List[Dict], nodes: List[Dict], 
                          dag: nx.DiGraph, allocation_result: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """准备three_layer_gnn模型的批次数据"""
        num_tasks = len(tasks)
        num_nodes = len(nodes)
        
        # 任务特征 [batch_size, max_tasks, input_dim]
        task_features = torch.zeros(1, num_tasks, self.config.task_feature_dim)
        for i, task in enumerate(tasks):
            task_features[0, i] = torch.tensor(task['task_features'], dtype=torch.float32)
        
        # 邻接矩阵 [batch_size, max_tasks, max_tasks]
        adjacency_matrix = torch.zeros(1, num_tasks, num_tasks)
        for edge in dag.edges():
            adjacency_matrix[0, edge[0], edge[1]] = 1.0
        
        # 节点特征 [total_nodes, input_dim]
        node_features = torch.zeros(num_nodes, self.config.node_feature_dim)
        for i, node in enumerate(nodes):
            node_features[i] = torch.tensor(node['node_features'], dtype=torch.float32)
        
        # 任务边索引 [2, num_edges]
        task_edges = list(dag.edges())
        if task_edges:
            task_edge_index = torch.tensor(task_edges, dtype=torch.long).t()
        else:
            task_edge_index = torch.zeros(2, 0, dtype=torch.long)
        
        # 批次索引
        task_batch = torch.zeros(num_tasks, dtype=torch.long)
        node_batch = torch.zeros(num_nodes, dtype=torch.long)
        
        # 资源约束 [batch_size, max_nodes, resource_dim]
        resource_constraints = torch.zeros(1, num_nodes, self.config.resource_dim)
        for i, node in enumerate(nodes):
            resource_constraints[0, i, 0] = node['cpu_available']  # CPU约束
            resource_constraints[0, i, 1] = node['memory_available']  # 内存约束
        
        # 约束数据
        constraint_data = {
            'dag': dag,
            'tasks': tasks,
            'nodes': nodes,
            'allocation_result': allocation_result
        }
        
        return {
            'task_features': task_features,
            'adjacency_matrix': adjacency_matrix,
            'node_features': node_features,
            'task_edge_index': task_edge_index,
            'task_batch': task_batch,
            'node_batch': node_batch,
            'resource_constraints': resource_constraints,
            'constraint_data': constraint_data
        }
    
    def generate_labels(self, allocation_result: Dict[str, Any]) -> torch.Tensor:
        """生成标签"""
        quality = allocation_result['allocation_quality']
        
        # 生成6维标签向量
        labels = torch.tensor([
            quality['resource_utilization'],
            quality['load_balance'],
            quality['makespan'],
            quality['dependency_satisfaction'],
            quality['objective_value'],
            1.0  # 占位符
        ], dtype=torch.float32)
        
        return labels
    
    def generate_single_sample(self) -> Tuple[Dict[str, torch.Tensor], torch.Tensor]:
        """生成单个样本"""
        # 随机生成任务和节点数量
        num_tasks = random.randint(self.config.min_tasks, self.config.max_tasks)
        num_nodes = random.randint(self.config.min_nodes, self.config.max_nodes)
        
        # 生成DAG结构
        dag = self.generate_dag_structure(num_tasks)
        
        # 生成图着色（如果启用）
        colors = None
        if self.config.enable_coloring:
            colors = self.graph_coloring.color_graph(dag)
        
        # 生成任务
        tasks = []
        task_types = list(self.task_types.keys())
        for task_id in range(num_tasks):
            task_type = random.choice(task_types)
            task = self.generate_task_features(task_id, task_type, dag, colors)
            tasks.append(task)
            # 将任务信息添加到DAG节点
            dag.nodes[task_id].update(task)
        
        # 生成节点
        nodes = []
        node_types = list(self.node_types.keys())
        for i in range(num_nodes):
            node_id = i
            node_type = random.choice(node_types)
            nodes.append(self.generate_node_features(node_id, node_type))
        
        # 使用人工蜂群算法生成最优分配
        allocation_result = self.generate_optimal_allocation(tasks, nodes, dag)
        
        # 准备批次数据
        batch_data = self.prepare_batch_data(tasks, nodes, dag, allocation_result)
        
        # 生成标签
        labels = self.generate_labels(allocation_result)
        
        return batch_data, labels
    
    def generate_dataset(self) -> Tuple[List[Dict[str, torch.Tensor]], List[torch.Tensor]]:
        """生成完整数据集"""
        print(f"生成 {self.config.total_samples} 个样本...")
        print(f"图着色特征: {'启用' if self.config.enable_coloring else '禁用'}")
        
        batch_data_list = []
        labels_list = []
        
        for i in range(self.config.total_samples):
            if (i + 1) % 1000 == 0:
                print(f"已生成 {i + 1} 个样本")
            
            batch_data, labels = self.generate_single_sample()
            batch_data_list.append(batch_data)
            labels_list.append(labels)
        
        print(f"数据集生成完成！")
        return batch_data_list, labels_list
    
    def save_dataset(self, batch_data_list: List[Dict[str, torch.Tensor]], 
                    labels_list: List[torch.Tensor], output_dir: str):
        """保存数据集"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 分割数据集
        train_size = int(self.config.total_samples * self.config.train_ratio)
        val_size = int(self.config.total_samples * self.config.val_ratio)
        
        # 保存训练集
        train_dir = os.path.join(output_dir, "train")
        os.makedirs(train_dir, exist_ok=True)
        
        train_data = batch_data_list[:train_size]
        train_labels = labels_list[:train_size]
        
        torch.save(train_data, os.path.join(train_dir, "batch_data.pt"))
        torch.save(train_labels, os.path.join(train_dir, "labels.pt"))
        
        # 保存验证集
        val_dir = os.path.join(output_dir, "val")
        os.makedirs(val_dir, exist_ok=True)
        
        val_data = batch_data_list[train_size:train_size+val_size]
        val_labels = labels_list[train_size:train_size+val_size]
        
        torch.save(val_data, os.path.join(val_dir, "batch_data.pt"))
        torch.save(val_labels, os.path.join(val_dir, "labels.pt"))
        
        # 保存测试集
        test_dir = os.path.join(output_dir, "test")
        os.makedirs(test_dir, exist_ok=True)
        
        test_data = batch_data_list[train_size+val_size:]
        test_labels = labels_list[train_size+val_size:]
        
        torch.save(test_data, os.path.join(test_dir, "batch_data.pt"))
        torch.save(test_labels, os.path.join(test_dir, "labels.pt"))
        
        # 保存数据集信息
        dataset_info = {
            "total_samples": self.config.total_samples,
            "train_samples": train_size,
            "val_samples": val_size,
            "test_samples": self.config.total_samples - train_size - val_size,
            "task_feature_dim": self.config.task_feature_dim,
            "node_feature_dim": self.config.node_feature_dim,
            "resource_dim": self.config.resource_dim,
            "enable_coloring": self.config.enable_coloring,
            "max_colors": self.config.max_colors,
            "algorithm": "Artificial Bee Colony",
            "generated_at": datetime.now().isoformat(),
            "config": self.config.__dict__
        }
        
        with open(os.path.join(output_dir, "dataset_info.json"), "w") as f:
            json.dump(dataset_info, f, indent=2)
        
        print(f"数据集已保存到 {output_dir}")
        print(f"训练集: {train_size} 样本")
        print(f"验证集: {val_size} 样本")
        print(f"测试集: {self.config.total_samples - train_size - val_size} 样本")
    
    def generate_complete_dataset(self, output_dir: str):
        """生成完整的数据集"""
        print("开始生成改进的Three Layer GNN数据集...")
        
        # 生成数据集
        batch_data_list, labels_list = self.generate_dataset()
        
        # 保存数据集
        self.save_dataset(batch_data_list, labels_list, output_dir)
        
        print("改进的Three Layer GNN数据集生成完成！")


def generate_dual_datasets():
    """生成两批数据集：带颜色特征和不带颜色特征"""
    print("=" * 60)
    print("生成双数据集：验证图着色算法效果")
    print("=" * 60)
    
    # 基础配置
    base_config = ImprovedThreeLayerGNNConfig(
        total_samples=1000,  # 可以根据需要调整
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15,
        min_tasks=10,
        max_tasks=50,
        min_nodes=5,
        max_nodes=20,
        task_feature_dim=128,
        node_feature_dim=32,
        resource_dim=2,
        max_dag_depth=6,
        max_dag_width=10,
        dependency_probability=0.3,
        population_size=30,
        max_iterations=50,
        limit=10,
        max_colors=8,
        normalize_features=True,
        add_noise=True,
        noise_level=0.05,
        random_seed=42
    )
    
    # 1. 生成带颜色特征的数据集
    print("\n1. 生成带颜色特征的数据集...")
    config_with_coloring = ImprovedThreeLayerGNNConfig(**base_config.__dict__)
    config_with_coloring.enable_coloring = True
    
    generator_with_coloring = ImprovedThreeLayerGNNDatasetGenerator(config_with_coloring)
    generator_with_coloring.generate_complete_dataset("three_layer_gnn_with_coloring")
    
    # 2. 生成不带颜色特征的数据集
    print("\n2. 生成不带颜色特征的数据集...")
    config_without_coloring = ImprovedThreeLayerGNNConfig(**base_config.__dict__)
    config_without_coloring.enable_coloring = False
    
    generator_without_coloring = ImprovedThreeLayerGNNDatasetGenerator(config_without_coloring)
    generator_without_coloring.generate_complete_dataset("three_layer_gnn_without_coloring")
    
    print("\n双数据集生成完成！")
    print("数据集1: three_layer_gnn_with_coloring (带颜色特征)")
    print("数据集2: three_layer_gnn_without_coloring (不带颜色特征)")
    print("\n可以使用这两个数据集来验证改进图着色算法对神经网络性能的影响。")


def main():
    """主函数"""
    print("改进的Three Layer GNN数据集生成器")
    print("使用人工蜂群算法 + 图着色特征")
    print("=" * 60)
    
    # 生成双数据集
    generate_dual_datasets()


if __name__ == "__main__":
    main() 