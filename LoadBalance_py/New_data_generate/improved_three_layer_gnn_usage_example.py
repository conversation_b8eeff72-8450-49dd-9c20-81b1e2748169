"""
改进的Three Layer GNN数据集使用示例
验证图着色算法对神经网络性能的影响
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import json
import os
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Any, Tuple

# 导入改进的数据集生成器
from improved_three_layer_gnn_dataset_generator import (
    ImprovedThreeLayerGNNDatasetGenerator, 
    ImprovedThreeLayerGNNConfig,
    generate_dual_datasets
)

# 导入模型（假设模型在上级目录）
import sys
sys.path.append('..')
from three_layer_gnn import ThreeLayerGNNScheduler


class ImprovedThreeLayerGNNDataset(Dataset):
    """改进的Three Layer GNN数据集类"""
    
    def __init__(self, batch_data_list: List[Dict[str, torch.Tensor]], 
                 labels_list: List[torch.Tensor]):
        self.batch_data_list = batch_data_list
        self.labels_list = labels_list
    
    def __len__(self):
        return len(self.batch_data_list)
    
    def __getitem__(self, idx):
        return self.batch_data_list[idx], self.labels_list[idx]


def load_dataset(dataset_path: str) -> tuple:
    """加载数据集"""
    print(f"加载数据集: {dataset_path}")
    
    # 加载训练集
    train_dir = os.path.join(dataset_path, "train")
    train_batch_data = torch.load(os.path.join(train_dir, "batch_data.pt"))
    train_labels = torch.load(os.path.join(train_dir, "labels.pt"))
    
    # 加载验证集
    val_dir = os.path.join(dataset_path, "val")
    val_batch_data = torch.load(os.path.join(val_dir, "batch_data.pt"))
    val_labels = torch.load(os.path.join(val_dir, "labels.pt"))
    
    # 加载测试集
    test_dir = os.path.join(dataset_path, "test")
    test_batch_data = torch.load(os.path.join(test_dir, "batch_data.pt"))
    test_labels = torch.load(os.path.join(test_dir, "labels.pt"))
    
    # 加载数据集信息
    with open(os.path.join(dataset_path, "dataset_info.json"), "r") as f:
        dataset_info = json.load(f)
    
    print(f"数据集信息:")
    print(f"  训练集: {len(train_batch_data)} 样本")
    print(f"  验证集: {len(val_batch_data)} 样本")
    print(f"  测试集: {len(test_batch_data)} 样本")
    print(f"  任务特征维度: {dataset_info['task_feature_dim']}")
    print(f"  节点特征维度: {dataset_info['node_feature_dim']}")
    print(f"  资源约束维度: {dataset_info['resource_dim']}")
    print(f"  图着色特征: {'启用' if dataset_info['enable_coloring'] else '禁用'}")
    print(f"  算法: {dataset_info['algorithm']}")
    
    return (train_batch_data, train_labels), (val_batch_data, val_labels), (test_batch_data, test_labels), dataset_info


def create_data_loaders(train_data, val_data, test_data, batch_size=8):
    """创建数据加载器"""
    # 创建数据集
    train_dataset = ImprovedThreeLayerGNNDataset(train_data[0], train_data[1])
    val_dataset = ImprovedThreeLayerGNNDataset(val_data[0], val_data[1])
    test_dataset = ImprovedThreeLayerGNNDataset(test_data[0], test_data[1])
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, val_loader, test_loader


def train_model(model, train_loader, val_loader, num_epochs=50, learning_rate=0.001, 
                model_name="model"):
    """训练模型"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5)
    
    # 训练历史
    train_losses = []
    val_losses = []
    
    print(f"开始训练 {model_name}，设备: {device}")
    print(f"训练轮数: {num_epochs}")
    print(f"学习率: {learning_rate}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, (batch_data, labels) in enumerate(train_loader):
            # 将数据移到设备
            batch_data = {k: v.to(device) for k, v in batch_data.items()}
            labels = labels.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            
            try:
                assignment_probs, debug_info = model(batch_data, debug_mode=False)
                
                # 计算损失（这里需要根据实际输出调整）
                # 假设模型输出的是分配概率，我们需要转换为性能指标
                predicted_quality = torch.mean(assignment_probs, dim=(1, 2))  # 简化的质量指标
                loss = criterion(predicted_quality, labels[:, 4])  # 使用目标函数值作为标签
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                
            except Exception as e:
                print(f"训练批次 {batch_idx} 出错: {e}")
                continue
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for batch_data, labels in val_loader:
                batch_data = {k: v.to(device) for k, v in batch_data.items()}
                labels = labels.to(device)
                
                try:
                    assignment_probs, debug_info = model(batch_data, debug_mode=False)
                    predicted_quality = torch.mean(assignment_probs, dim=(1, 2))
                    loss = criterion(predicted_quality, labels[:, 4])
                    val_loss += loss.item()
                except Exception as e:
                    print(f"验证批次出错: {e}")
                    continue
        
        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 打印进度
        if (epoch + 1) % 5 == 0:
            print(f"Epoch [{epoch+1}/{num_epochs}] - "
                  f"Train Loss: {avg_train_loss:.4f}, "
                  f"Val Loss: {avg_val_loss:.4f}, "
                  f"LR: {optimizer.param_groups[0]['lr']:.6f}")
    
    print(f"{model_name} 训练完成！")
    return train_losses, val_losses


def evaluate_model(model, test_loader, model_name="model"):
    """评估模型"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.eval()
    
    test_loss = 0.0
    predictions = []
    ground_truths = []
    
    with torch.no_grad():
        for batch_data, labels in test_loader:
            batch_data = {k: v.to(device) for k, v in batch_data.items()}
            labels = labels.to(device)
            
            try:
                assignment_probs, debug_info = model(batch_data, debug_mode=False)
                predicted_quality = torch.mean(assignment_probs, dim=(1, 2))
                loss = nn.MSELoss()(predicted_quality, labels[:, 4])
                test_loss += loss.item()
                
                predictions.extend(predicted_quality.cpu().numpy())
                ground_truths.extend(labels[:, 4].cpu().numpy())
                
            except Exception as e:
                print(f"测试批次出错: {e}")
                continue
    
    avg_test_loss = test_loss / len(test_loader)
    
    # 计算评估指标
    predictions = np.array(predictions)
    ground_truths = np.array(ground_truths)
    
    mse = np.mean((predictions - ground_truths) ** 2)
    mae = np.mean(np.abs(predictions - ground_truths))
    r2 = 1 - np.sum((ground_truths - predictions) ** 2) / np.sum((ground_truths - np.mean(ground_truths)) ** 2)
    
    print(f"\n{model_name} 测试结果:")
    print(f"  MSE: {mse:.4f}")
    print(f"  MAE: {mae:.4f}")
    print(f"  R²: {r2:.4f}")
    print(f"  平均测试损失: {avg_test_loss:.4f}")
    
    return {
        'mse': mse,
        'mae': mae,
        'r2': r2,
        'test_loss': avg_test_loss,
        'predictions': predictions,
        'ground_truths': ground_truths
    }


def compare_coloring_effect():
    """比较图着色算法的效果"""
    print("=" * 60)
    print("比较图着色算法对神经网络性能的影响")
    print("=" * 60)
    
    # 加载两个数据集
    print("\n1. 加载数据集...")
    
    # 带颜色特征的数据集
    train_data_with, val_data_with, test_data_with, dataset_info_with = load_dataset("three_layer_gnn_with_coloring")
    
    # 不带颜色特征的数据集
    train_data_without, val_data_without, test_data_without, dataset_info_without = load_dataset("three_layer_gnn_without_coloring")
    
    # 创建数据加载器
    train_loader_with, val_loader_with, test_loader_with = create_data_loaders(
        train_data_with, val_data_with, test_data_with, batch_size=4
    )
    
    train_loader_without, val_loader_without, test_loader_without = create_data_loaders(
        train_data_without, val_data_without, test_data_without, batch_size=4
    )
    
    # 创建模型配置
    model_config = {
        'input_dim': dataset_info_with['task_feature_dim'],
        'hidden_dim': 256,
        'num_heads': 8,
        'num_transformer_layers': 6,
        'num_gat_layers': 3,
        'dropout': 0.1,
        'constraint_weights': {
            'dependency': 1.0,
            'resource': 1.0,
            'temporal': 0.5,
            'communication': 0.5
        }
    }
    
    results = {}
    
    try:
        # 训练带颜色特征的模型
        print("\n2. 训练带颜色特征的模型...")
        model_with_coloring = ThreeLayerGNNScheduler(model_config)
        print(f"带颜色特征模型参数数量: {sum(p.numel() for p in model_with_coloring.parameters()):,}")
        
        train_losses_with, val_losses_with = train_model(
            model_with_coloring, train_loader_with, val_loader_with, 
            num_epochs=20, learning_rate=0.001, model_name="带颜色特征模型"
        )
        
        results_with = evaluate_model(model_with_coloring, test_loader_with, "带颜色特征模型")
        results['with_coloring'] = {
            'train_losses': train_losses_with,
            'val_losses': val_losses_with,
            'test_results': results_with
        }
        
        # 保存模型
        torch.save(model_with_coloring.state_dict(), "three_layer_gnn_with_coloring_model.pth")
        
        # 训练不带颜色特征的模型
        print("\n3. 训练不带颜色特征的模型...")
        model_without_coloring = ThreeLayerGNNScheduler(model_config)
        print(f"不带颜色特征模型参数数量: {sum(p.numel() for p in model_without_coloring.parameters()):,}")
        
        train_losses_without, val_losses_without = train_model(
            model_without_coloring, train_loader_without, val_loader_without, 
            num_epochs=20, learning_rate=0.001, model_name="不带颜色特征模型"
        )
        
        results_without = evaluate_model(model_without_coloring, test_loader_without, "不带颜色特征模型")
        results['without_coloring'] = {
            'train_losses': train_losses_without,
            'val_losses': val_losses_without,
            'test_results': results_without
        }
        
        # 保存模型
        torch.save(model_without_coloring.state_dict(), "three_layer_gnn_without_coloring_model.pth")
        
        # 比较结果
        print("\n4. 比较结果...")
        compare_results(results)
        
        # 可视化比较
        visualize_comparison(results)
        
        return results
        
    except Exception as e:
        print(f"模型训练失败: {e}")
        print("请检查模型依赖和配置")
        return None


def compare_results(results):
    """比较两个模型的结果"""
    print("\n" + "=" * 60)
    print("模型性能比较")
    print("=" * 60)
    
    with_coloring = results['with_coloring']['test_results']
    without_coloring = results['without_coloring']['test_results']
    
    print(f"{'指标':<15} {'带颜色特征':<15} {'不带颜色特征':<15} {'改进':<15}")
    print("-" * 60)
    print(f"{'MSE':<15} {with_coloring['mse']:<15.4f} {without_coloring['mse']:<15.4f} {without_coloring['mse'] - with_coloring['mse']:<15.4f}")
    print(f"{'MAE':<15} {with_coloring['mae']:<15.4f} {without_coloring['mae']:<15.4f} {without_coloring['mae'] - with_coloring['mae']:<15.4f}")
    print(f"{'R²':<15} {with_coloring['r2']:<15.4f} {without_coloring['r2']:<15.4f} {with_coloring['r2'] - without_coloring['r2']:<15.4f}")
    print(f"{'测试损失':<15} {with_coloring['test_loss']:<15.4f} {without_coloring['test_loss']:<15.4f} {without_coloring['test_loss'] - with_coloring['test_loss']:<15.4f}")
    
    # 计算改进百分比
    mse_improvement = (without_coloring['mse'] - with_coloring['mse']) / without_coloring['mse'] * 100
    mae_improvement = (without_coloring['mae'] - with_coloring['mae']) / without_coloring['mae'] * 100
    r2_improvement = (with_coloring['r2'] - without_coloring['r2']) / without_coloring['r2'] * 100
    
    print(f"\n改进百分比:")
    print(f"MSE改进: {mse_improvement:.2f}%")
    print(f"MAE改进: {mae_improvement:.2f}%")
    print(f"R²改进: {r2_improvement:.2f}%")
    
    # 判断图着色算法是否有效
    if with_coloring['r2'] > without_coloring['r2']:
        print(f"\n✅ 图着色算法有效！R²提升了 {r2_improvement:.2f}%")
    else:
        print(f"\n❌ 图着色算法效果不明显。R²下降了 {abs(r2_improvement):.2f}%")


def visualize_comparison(results):
    """可视化比较结果"""
    print("\n5. 生成可视化比较图...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('图着色算法效果比较', fontsize=16, fontweight='bold')
    
    # 1. 训练损失比较
    ax1 = axes[0, 0]
    with_coloring = results['with_coloring']
    without_coloring = results['without_coloring']
    
    epochs = range(1, len(with_coloring['train_losses']) + 1)
    ax1.plot(epochs, with_coloring['train_losses'], 'b-', label='带颜色特征 (训练)', linewidth=2)
    ax1.plot(epochs, with_coloring['val_losses'], 'b--', label='带颜色特征 (验证)', linewidth=2)
    ax1.plot(epochs, without_coloring['train_losses'], 'r-', label='不带颜色特征 (训练)', linewidth=2)
    ax1.plot(epochs, without_coloring['val_losses'], 'r--', label='不带颜色特征 (验证)', linewidth=2)
    
    ax1.set_title('训练和验证损失比较')
    ax1.set_xlabel('训练轮数')
    ax1.set_ylabel('损失值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 预测vs真实值散点图
    ax2 = axes[0, 1]
    with_coloring_results = results['with_coloring']['test_results']
    without_coloring_results = results['without_coloring']['test_results']
    
    ax2.scatter(with_coloring_results['ground_truths'], with_coloring_results['predictions'], 
               alpha=0.6, label=f'带颜色特征 (R²={with_coloring_results["r2"]:.3f})', color='blue')
    ax2.scatter(without_coloring_results['ground_truths'], without_coloring_results['predictions'], 
               alpha=0.6, label=f'不带颜色特征 (R²={without_coloring_results["r2"]:.3f})', color='red')
    
    # 添加对角线
    min_val = min(min(with_coloring_results['ground_truths']), min(without_coloring_results['ground_truths']))
    max_val = max(max(with_coloring_results['ground_truths']), max(without_coloring_results['ground_truths']))
    ax2.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5)
    
    ax2.set_title('预测值 vs 真实值')
    ax2.set_xlabel('真实值')
    ax2.set_ylabel('预测值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 性能指标柱状图
    ax3 = axes[1, 0]
    metrics = ['MSE', 'MAE', 'R²']
    with_coloring_metrics = [with_coloring_results['mse'], with_coloring_results['mae'], with_coloring_results['r2']]
    without_coloring_metrics = [without_coloring_results['mse'], without_coloring_results['mae'], without_coloring_results['r2']]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    ax3.bar(x - width/2, with_coloring_metrics, width, label='带颜色特征', color='blue', alpha=0.7)
    ax3.bar(x + width/2, without_coloring_metrics, width, label='不带颜色特征', color='red', alpha=0.7)
    
    ax3.set_title('性能指标比较')
    ax3.set_xlabel('指标')
    ax3.set_ylabel('值')
    ax3.set_xticks(x)
    ax3.set_xticklabels(metrics)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 改进百分比
    ax4 = axes[1, 1]
    improvements = [
        (without_coloring_results['mse'] - with_coloring_results['mse']) / without_coloring_results['mse'] * 100,
        (without_coloring_results['mae'] - with_coloring_results['mae']) / without_coloring_results['mae'] * 100,
        (with_coloring_results['r2'] - without_coloring_results['r2']) / without_coloring_results['r2'] * 100
    ]
    
    colors = ['green' if imp > 0 else 'red' for imp in improvements]
    bars = ax4.bar(metrics, improvements, color=colors, alpha=0.7)
    
    # 添加数值标签
    for bar, imp in zip(bars, improvements):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height,
                f'{imp:.1f}%', ha='center', va='bottom' if height > 0 else 'top')
    
    ax4.set_title('图着色算法改进百分比')
    ax4.set_xlabel('指标')
    ax4.set_ylabel('改进百分比 (%)')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('coloring_algorithm_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("可视化图表已保存为 'coloring_algorithm_comparison.png'")


def test_single_sample_comparison():
    """测试单个样本的比较"""
    print("=" * 50)
    print("测试单个样本的比较")
    print("=" * 50)
    
    # 创建生成器
    config_with_coloring = ImprovedThreeLayerGNNConfig(
        total_samples=1,
        min_tasks=5,
        max_tasks=10,
        min_nodes=3,
        max_nodes=5,
        task_feature_dim=128,
        node_feature_dim=32,
        resource_dim=2,
        enable_coloring=True,
        max_colors=5,
        random_seed=42
    )
    
    config_without_coloring = ImprovedThreeLayerGNNConfig(
        total_samples=1,
        min_tasks=5,
        max_tasks=10,
        min_nodes=3,
        max_nodes=5,
        task_feature_dim=128,
        node_feature_dim=32,
        resource_dim=2,
        enable_coloring=False,
        max_colors=5,
        random_seed=42
    )
    
    # 生成样本
    generator_with = ImprovedThreeLayerGNNDatasetGenerator(config_with_coloring)
    generator_without = ImprovedThreeLayerGNNDatasetGenerator(config_without_coloring)
    
    batch_data_with, labels_with = generator_with.generate_single_sample()
    batch_data_without, labels_without = generator_without.generate_single_sample()
    
    print(f"样本信息:")
    print(f"  任务特征形状: {batch_data_with['task_features'].shape}")
    print(f"  邻接矩阵形状: {batch_data_with['adjacency_matrix'].shape}")
    print(f"  节点特征形状: {batch_data_with['node_features'].shape}")
    print(f"  资源约束形状: {batch_data_with['resource_constraints'].shape}")
    
    print(f"\n标签值比较:")
    print(f"  带颜色特征: {labels_with.numpy()}")
    print(f"  不带颜色特征: {labels_without.numpy()}")
    
    # 比较特征差异
    task_features_with = batch_data_with['task_features'][0]  # [num_tasks, feature_dim]
    task_features_without = batch_data_without['task_features'][0]
    
    # 检查着色特征区域（80:88）
    coloring_features_with = task_features_with[:, 80:88]
    coloring_features_without = task_features_without[:, 80:88]
    
    print(f"\n着色特征比较:")
    print(f"  带颜色特征 - 着色区域均值: {coloring_features_with.mean():.4f}")
    print(f"  不带颜色特征 - 着色区域均值: {coloring_features_without.mean():.4f}")
    print(f"  着色特征差异: {torch.abs(coloring_features_with - coloring_features_without).mean():.4f}")


def main():
    """主函数"""
    print("改进的Three Layer GNN数据集使用示例")
    print("验证图着色算法对神经网络性能的影响")
    print("=" * 60)
    
    # 1. 生成双数据集
    print("\n步骤1: 生成双数据集...")
    generate_dual_datasets()
    
    # 2. 测试单个样本
    print("\n步骤2: 测试单个样本...")
    test_single_sample_comparison()
    
    # 3. 比较图着色算法效果
    print("\n步骤3: 比较图着色算法效果...")
    results = compare_coloring_effect()
    
    if results:
        print("\n✅ 实验完成！")
        print("可以通过比较结果来验证改进图着色算法对神经网络性能的影响。")
    else:
        print("\n❌ 实验失败，请检查模型依赖和配置。")


if __name__ == "__main__":
    main() 