"""
神经网络数据集生成器
为负载均衡神经网络方法准备训练、验证和测试数据集
"""

import json
import random
import numpy as np
import pandas as pd
from datetime import datetime
import os
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
import pickle
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split


@dataclass
class NeuralNetworkDatasetConfig:
    """神经网络数据集配置"""
    # 数据集规模
    total_samples: int = 10000
    train_ratio: float = 0.7
    val_ratio: float = 0.15
    test_ratio: float = 0.15
    
    # 任务和节点数量范围
    min_tasks: int = 10
    max_tasks: int = 100
    min_nodes: int = 5
    max_nodes: int = 30
    
    # 特征工程参数
    normalize_features: bool = True
    encode_categorical: bool = True
    add_noise: bool = True
    noise_level: float = 0.05
    
    # 数据增强参数
    augmentation_factor: int = 3
    random_seed: int = 42


class NeuralNetworkDatasetGenerator:
    """神经网络数据集生成器"""
    
    def __init__(self, config: NeuralNetworkDatasetConfig):
        self.config = config
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_names = []
        self.label_names = []
        
        # 设置随机种子
        random.seed(config.random_seed)
        np.random.seed(config.random_seed)
        
        # 任务类型定义
        self.task_types = {
            'cpu_intensive': {
                'cpu_range': (2.0, 4.0),
                'memory_range': (2048.0, 8192.0),
                'io_range': (64.0, 256.0),
                'network_range': (32.0, 128.0),
                'runtime_range': (60.0, 300.0)
            },
            'memory_intensive': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (8192.0, 32768.0),
                'io_range': (128.0, 512.0),
                'network_range': (32.0, 128.0),
                'runtime_range': (120.0, 600.0)
            },
            'io_intensive': {
                'cpu_range': (1.0, 2.0),
                'memory_range': (1024.0, 4096.0),
                'io_range': (512.0, 2048.0),
                'network_range': (16.0, 64.0),
                'runtime_range': (90.0, 450.0)
            },
            'network_intensive': {
                'cpu_range': (1.0, 2.0),
                'memory_range': (1024.0, 4096.0),
                'io_range': (32.0, 128.0),
                'network_range': (256.0, 1024.0),
                'runtime_range': (60.0, 300.0)
            },
            'general': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (2048.0, 8192.0),
                'io_range': (64.0, 256.0),
                'network_range': (32.0, 128.0),
                'runtime_range': (90.0, 360.0)
            }
        }
        
        # 节点类型定义
        self.node_types = {
            'cpu_intensive': {
                'cpu_range': (8.0, 16.0),
                'memory_range': (16384.0, 32768.0),
                'io_range': (1024.0, 2048.0),
                'network_range': (512.0, 1024.0)
            },
            'memory_intensive': {
                'cpu_range': (4.0, 8.0),
                'memory_range': (32768.0, 65536.0),
                'io_range': (2048.0, 4096.0),
                'network_range': (256.0, 512.0)
            },
            'io_intensive': {
                'cpu_range': (4.0, 8.0),
                'memory_range': (16384.0, 32768.0),
                'io_range': (4096.0, 8192.0),
                'network_range': (256.0, 512.0)
            },
            'general': {
                'cpu_range': (6.0, 12.0),
                'memory_range': (24576.0, 49152.0),
                'io_range': (1536.0, 3072.0),
                'network_range': (384.0, 768.0)
            }
        }
    
    def generate_task_features(self, task_id: str, task_type: str) -> Dict[str, Any]:
        """生成任务特征"""
        task_config = self.task_types[task_type]
        
        # 生成资源需求
        cpu_req = round(random.uniform(*task_config['cpu_range']), 2)
        memory_req = round(random.uniform(*task_config['memory_range']), 2)
        io_req = round(random.uniform(*task_config['io_range']), 2)
        network_req = round(random.uniform(*task_config['network_range']), 2)
        base_runtime = round(random.uniform(*task_config['runtime_range']), 2)
        
        # 生成依赖关系（简化版）
        dependencies = []
        if random.random() < 0.3:  # 30%的任务有依赖
            num_deps = random.randint(1, 3)
            for i in range(num_deps):
                dep_id = f"task_{random.randint(1, 50)}"
                if dep_id != task_id:
                    dependencies.append(dep_id)
        
        return {
            'task_id': task_id,
            'task_type': task_type,
            'cpu_requirement': cpu_req,
            'memory_requirement': memory_req,
            'io_requirement': io_req,
            'network_requirement': network_req,
            'base_runtime': base_runtime,
            'dependencies': dependencies,
            'dependency_count': len(dependencies),
            'total_resource_demand': cpu_req + memory_req + io_req + network_req,
            'resource_intensity': {
                'cpu_intensity': cpu_req / (cpu_req + memory_req + io_req + network_req),
                'memory_intensity': memory_req / (cpu_req + memory_req + io_req + network_req),
                'io_intensity': io_req / (cpu_req + memory_req + io_req + network_req),
                'network_intensity': network_req / (cpu_req + memory_req + io_req + network_req)
            }
        }
    
    def generate_node_features(self, node_id: str, node_type: str) -> Dict[str, Any]:
        """生成节点特征"""
        node_config = self.node_types[node_type]
        
        # 生成资源容量
        cpu_cap = round(random.uniform(*node_config['cpu_range']), 2)
        memory_cap = round(random.uniform(*node_config['memory_range']), 2)
        io_cap = round(random.uniform(*node_config['io_range']), 2)
        network_cap = round(random.uniform(*node_config['network_range']), 2)
        
        # 生成当前负载
        current_load = round(random.uniform(0.1, 0.8), 2)
        
        # 计算可用资源
        cpu_available = cpu_cap * (1 - current_load)
        memory_available = memory_cap * (1 - current_load)
        io_available = io_cap * (1 - current_load)
        network_available = network_cap * (1 - current_load)
        
        return {
            'node_id': node_id,
            'node_type': node_type,
            'cpu_capacity': cpu_cap,
            'memory_capacity': memory_cap,
            'io_bandwidth': io_cap,
            'network_bandwidth': network_cap,
            'current_load': current_load,
            'cpu_available': cpu_available,
            'memory_available': memory_available,
            'io_available': io_available,
            'network_available': network_available,
            'total_capacity': cpu_cap + memory_cap + io_cap + network_cap,
            'total_available': cpu_available + memory_available + io_available + network_available,
            'resource_efficiency': {
                'cpu_efficiency': cpu_available / cpu_cap,
                'memory_efficiency': memory_available / memory_cap,
                'io_efficiency': io_available / io_cap,
                'network_efficiency': network_available / network_cap
            }
        }
    
    def generate_system_state(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成系统状态特征"""
        total_tasks = len(tasks)
        total_nodes = len(nodes)
        
        # 计算系统级特征
        total_cpu_demand = sum(task['cpu_requirement'] for task in tasks.values())
        total_memory_demand = sum(task['memory_requirement'] for task in tasks.values())
        total_io_demand = sum(task['io_requirement'] for task in tasks.values())
        total_network_demand = sum(task['network_requirement'] for task in tasks.values())
        
        total_cpu_capacity = sum(node['cpu_capacity'] for node in nodes)
        total_memory_capacity = sum(node['memory_capacity'] for node in nodes)
        total_io_capacity = sum(node['io_bandwidth'] for node in nodes)
        total_network_capacity = sum(node['network_bandwidth'] for node in nodes)
        
        return {
            'total_tasks': total_tasks,
            'total_nodes': total_nodes,
            'task_node_ratio': total_tasks / total_nodes,
            'total_cpu_demand': total_cpu_demand,
            'total_memory_demand': total_memory_demand,
            'total_io_demand': total_io_demand,
            'total_network_demand': total_network_demand,
            'total_cpu_capacity': total_cpu_capacity,
            'total_memory_capacity': total_memory_capacity,
            'total_io_capacity': total_io_capacity,
            'total_network_capacity': total_network_capacity,
            'cpu_utilization_ratio': total_cpu_demand / total_cpu_capacity,
            'memory_utilization_ratio': total_memory_demand / total_memory_capacity,
            'io_utilization_ratio': total_io_demand / total_io_capacity,
            'network_utilization_ratio': total_network_demand / total_network_capacity,
            'system_load': (total_cpu_demand + total_memory_demand + total_io_demand + total_network_demand) / 
                          (total_cpu_capacity + total_memory_capacity + total_io_capacity + total_network_capacity)
        }
    
    def generate_optimal_allocation(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成最优分配（使用启发式算法）"""
        # 简化的最优分配算法
        task_assignments = {}
        node_loads = {node['node_id']: 0.0 for node in nodes}
        
        # 按资源需求排序任务
        sorted_tasks = sorted(tasks.items(), 
                            key=lambda x: x[1]['total_resource_demand'], 
                            reverse=True)
        
        for task_id, task in sorted_tasks:
            best_node = None
            best_score = float('inf')
            
            for node in nodes:
                # 检查资源约束
                if (task['cpu_requirement'] <= node['cpu_available'] and
                    task['memory_requirement'] <= node['memory_available'] and
                    task['io_requirement'] <= node['io_available'] and
                    task['network_requirement'] <= node['network_available']):
                    
                    # 计算分配分数（负载均衡）
                    score = node_loads[node['node_id']] + task['total_resource_demand'] / node['total_capacity']
                    
                    if score < best_score:
                        best_score = score
                        best_node = node['node_id']
            
            if best_node:
                task_assignments[task_id] = best_node
                node_loads[best_node] += task['total_resource_demand'] / next(n['total_capacity'] for n in nodes if n['node_id'] == best_node)
        
        return {
            'task_assignments': task_assignments,
            'node_loads': node_loads,
            'allocation_quality': self._calculate_allocation_quality(tasks, nodes, task_assignments)
        }
    
    def _calculate_allocation_quality(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]], 
                                   assignments: Dict[str, str]) -> Dict[str, float]:
        """计算分配质量指标"""
        if not assignments:
            return {
                'resource_utilization': 0.0,
                'load_balance': 0.0,
                'makespan': 0.0,
                'objective_value': 0.0
            }
        
        # 计算资源利用率
        total_utilization = 0.0
        node_utilizations = {}
        
        for node in nodes:
            node_tasks = [task_id for task_id, node_id in assignments.items() if node_id == node['node_id']]
            if node_tasks:
                cpu_used = sum(tasks[task_id]['cpu_requirement'] for task_id in node_tasks)
                memory_used = sum(tasks[task_id]['memory_requirement'] for task_id in node_tasks)
                io_used = sum(tasks[task_id]['io_requirement'] for task_id in node_tasks)
                network_used = sum(tasks[task_id]['network_requirement'] for task_id in node_tasks)
                
                utilization = (cpu_used / node['cpu_capacity'] + 
                             memory_used / node['memory_capacity'] + 
                             io_used / node['io_bandwidth'] + 
                             network_used / node['network_bandwidth']) / 4
                node_utilizations[node['node_id']] = utilization
                total_utilization += utilization
        
        avg_utilization = total_utilization / len(nodes)
        
        # 计算负载均衡度
        if node_utilizations:
            load_balance = 1.0 - np.std(list(node_utilizations.values()))
        else:
            load_balance = 0.0
        
        # 计算makespan（简化版）
        makespan = max(node_utilizations.values()) if node_utilizations else 0.0
        
        # 计算目标函数值
        objective_value = avg_utilization * 0.3 + load_balance * 0.3 + (1.0 - makespan) * 0.4
        
        return {
            'resource_utilization': avg_utilization,
            'load_balance': load_balance,
            'makespan': makespan,
            'objective_value': objective_value
        }
    
    def extract_features(self, tasks: Dict[str, Any], nodes: List[Dict[str, Any]], 
                        system_state: Dict[str, Any]) -> np.ndarray:
        """提取特征向量"""
        features = []
        
        # 任务特征
        for task in tasks.values():
            task_features = [
                task['cpu_requirement'],
                task['memory_requirement'],
                task['io_requirement'],
                task['network_requirement'],
                task['base_runtime'],
                task['dependency_count'],
                task['total_resource_demand'],
                task['resource_intensity']['cpu_intensity'],
                task['resource_intensity']['memory_intensity'],
                task['resource_intensity']['io_intensity'],
                task['resource_intensity']['network_intensity']
            ]
            features.extend(task_features)
        
        # 节点特征
        for node in nodes:
            node_features = [
                node['cpu_capacity'],
                node['memory_capacity'],
                node['io_bandwidth'],
                node['network_bandwidth'],
                node['current_load'],
                node['cpu_available'],
                node['memory_available'],
                node['io_available'],
                node['network_available'],
                node['total_capacity'],
                node['total_available'],
                node['resource_efficiency']['cpu_efficiency'],
                node['resource_efficiency']['memory_efficiency'],
                node['resource_efficiency']['io_efficiency'],
                node['resource_efficiency']['network_efficiency']
            ]
            features.extend(node_features)
        
        # 系统状态特征
        system_features = [
            system_state['total_tasks'],
            system_state['total_nodes'],
            system_state['task_node_ratio'],
            system_state['total_cpu_demand'],
            system_state['total_memory_demand'],
            system_state['total_io_demand'],
            system_state['total_network_demand'],
            system_state['total_cpu_capacity'],
            system_state['total_memory_capacity'],
            system_state['total_io_capacity'],
            system_state['total_network_capacity'],
            system_state['cpu_utilization_ratio'],
            system_state['memory_utilization_ratio'],
            system_state['io_utilization_ratio'],
            system_state['network_utilization_ratio'],
            system_state['system_load']
        ]
        features.extend(system_features)
        
        return np.array(features)
    
    def extract_labels(self, allocation_result: Dict[str, Any]) -> np.ndarray:
        """提取标签向量"""
        quality = allocation_result['allocation_quality']
        
        labels = [
            quality['resource_utilization'],
            quality['load_balance'],
            quality['makespan'],
            quality['objective_value']
        ]
        
        return np.array(labels)
    
    def generate_single_sample(self) -> Tuple[np.ndarray, np.ndarray]:
        """生成单个样本"""
        # 随机生成任务和节点数量
        num_tasks = random.randint(self.config.min_tasks, self.config.max_tasks)
        num_nodes = random.randint(self.config.min_nodes, self.config.max_nodes)
        
        # 生成任务
        tasks = {}
        task_types = list(self.task_types.keys())
        for i in range(num_tasks):
            task_id = f"task_{i+1}"
            task_type = random.choice(task_types)
            tasks[task_id] = self.generate_task_features(task_id, task_type)
        
        # 生成节点
        nodes = []
        node_types = list(self.node_types.keys())
        for i in range(num_nodes):
            node_id = f"node_{i+1}"
            node_type = random.choice(node_types)
            nodes.append(self.generate_node_features(node_id, node_type))
        
        # 生成系统状态
        system_state = self.generate_system_state(tasks, nodes)
        
        # 生成最优分配
        allocation_result = self.generate_optimal_allocation(tasks, nodes)
        
        # 提取特征和标签
        features = self.extract_features(tasks, nodes, system_state)
        labels = self.extract_labels(allocation_result)
        
        return features, labels
    
    def generate_dataset(self) -> Tuple[np.ndarray, np.ndarray]:
        """生成完整数据集"""
        print(f"生成 {self.config.total_samples} 个样本...")
        
        features_list = []
        labels_list = []
        
        for i in range(self.config.total_samples):
            if (i + 1) % 1000 == 0:
                print(f"已生成 {i + 1} 个样本")
            
            features, labels = self.generate_single_sample()
            features_list.append(features)
            labels_list.append(labels)
        
        # 转换为numpy数组
        X = np.array(features_list)
        y = np.array(labels_list)
        
        print(f"数据集生成完成！特征形状: {X.shape}, 标签形状: {y.shape}")
        
        return X, y
    
    def split_dataset(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """分割数据集"""
        # 首先分割出测试集
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=self.config.test_ratio, random_state=self.config.random_seed
        )
        
        # 从剩余数据中分割出验证集
        val_ratio_adjusted = self.config.val_ratio / (1 - self.config.test_ratio)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_ratio_adjusted, random_state=self.config.random_seed
        )
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def normalize_features(self, X_train: np.ndarray, X_val: np.ndarray, X_test: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """标准化特征"""
        if self.config.normalize_features:
            # 使用训练集拟合scaler
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_val_scaled = self.scaler.transform(X_val)
            X_test_scaled = self.scaler.transform(X_test)
            
            return X_train_scaled, X_val_scaled, X_test_scaled
        else:
            return X_train, X_val, X_test
    
    def save_dataset(self, X_train: np.ndarray, X_val: np.ndarray, X_test: np.ndarray,
                    y_train: np.ndarray, y_val: np.ndarray, y_test: np.ndarray,
                    output_dir: str = "neural_network_dataset"):
        """保存数据集"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存数据
        np.save(os.path.join(output_dir, "X_train.npy"), X_train)
        np.save(os.path.join(output_dir, "X_val.npy"), X_val)
        np.save(os.path.join(output_dir, "X_test.npy"), X_test)
        np.save(os.path.join(output_dir, "y_train.npy"), y_train)
        np.save(os.path.join(output_dir, "y_val.npy"), y_val)
        np.save(os.path.join(output_dir, "y_test.npy"), y_test)
        
        # 保存scaler
        if self.config.normalize_features:
            with open(os.path.join(output_dir, "scaler.pkl"), "wb") as f:
                pickle.dump(self.scaler, f)
        
        # 保存数据集信息
        dataset_info = {
            "total_samples": self.config.total_samples,
            "train_samples": len(X_train),
            "val_samples": len(X_val),
            "test_samples": len(X_test),
            "feature_dim": X_train.shape[1],
            "label_dim": y_train.shape[1],
            "feature_names": self.feature_names,
            "label_names": ["resource_utilization", "load_balance", "makespan", "objective_value"],
            "generated_at": datetime.now().isoformat(),
            "config": self.config.__dict__
        }
        
        with open(os.path.join(output_dir, "dataset_info.json"), "w") as f:
            json.dump(dataset_info, f, indent=2)
        
        print(f"数据集已保存到 {output_dir}")
        print(f"训练集: {X_train.shape}")
        print(f"验证集: {X_val.shape}")
        print(f"测试集: {X_test.shape}")
    
    def generate_complete_dataset(self, output_dir: str = "neural_network_dataset"):
        """生成完整的数据集"""
        print("开始生成神经网络数据集...")
        
        # 生成原始数据集
        X, y = self.generate_dataset()
        
        # 分割数据集
        X_train, X_val, X_test, y_train, y_val, y_test = self.split_dataset(X, y)
        
        # 标准化特征
        X_train_scaled, X_val_scaled, X_test_scaled = self.normalize_features(X_train, X_val, X_test)
        
        # 保存数据集
        self.save_dataset(X_train_scaled, X_val_scaled, X_test_scaled, 
                         y_train, y_val, y_test, output_dir)
        
        print("数据集生成完成！")


def main():
    """主函数"""
    # 配置数据集生成参数
    config = NeuralNetworkDatasetConfig(
        total_samples=10000,
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15,
        min_tasks=10,
        max_tasks=100,
        min_nodes=5,
        max_nodes=30,
        normalize_features=True,
        encode_categorical=True,
        add_noise=True,
        noise_level=0.05,
        augmentation_factor=3,
        random_seed=42
    )
    
    # 创建数据集生成器
    generator = NeuralNetworkDatasetGenerator(config)
    
    # 生成数据集
    generator.generate_complete_dataset("neural_network_dataset")


if __name__ == "__main__":
    main() 