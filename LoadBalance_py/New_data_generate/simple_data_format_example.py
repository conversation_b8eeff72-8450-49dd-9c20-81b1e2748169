"""
简单的训练数据格式示例
展示生成的训练数据的具体结构
"""

import torch
import numpy as np
from improved_three_layer_gnn_dataset_generator import (
    ImprovedThreeLayerGNNDatasetGenerator, 
    ImprovedThreeLayerGNNConfig
)


def show_simple_data_format():
    """显示简单的数据格式示例"""
    print("=" * 60)
    print("训练数据格式示例")
    print("=" * 60)
    
    # 创建小规模配置
    config = ImprovedThreeLayerGNNConfig(
        total_samples=1,  # 只生成1个样本
        min_tasks=3,
        max_tasks=5,
        min_nodes=2,
        max_nodes=3,
        task_feature_dim=128,
        node_feature_dim=32,
        resource_dim=2,
        enable_coloring=True,
        max_colors=5,
        random_seed=42
    )
    
    # 创建生成器
    generator = ImprovedThreeLayerGNNDatasetGenerator(config)
    
    # 生成单个样本
    print("\n1. 生成单个样本...")
    batch_data, labels = generator.generate_single_sample()
    
    print(f"\n📊 数据格式详情:")
    print(f"数据类型: {type(batch_data)}")
    print(f"包含的键: {list(batch_data.keys())}")
    
    # 详细分析每个组件
    print(f"\n🔍 各组件详细信息:")
    
    for key, value in batch_data.items():
        if isinstance(value, torch.Tensor):
            print(f"\n  {key}:")
            print(f"    形状: {value.shape}")
            print(f"    数据类型: {value.dtype}")
            print(f"    数值范围: [{value.min().item():.4f}, {value.max().item():.4f}]")
            print(f"    均值: {value.mean().item():.4f}")
            print(f"    标准差: {value.std().item():.4f}")
            
            # 显示具体数值（小规模）
            if value.numel() <= 20:
                print(f"    具体数值: {value.flatten()[:10].numpy()}")
    
    # 分析标签
    print(f"\n🏷️ 标签信息:")
    print(f"  形状: {labels.shape}")
    print(f"  数据类型: {labels.dtype}")
    print(f"  标签值: {labels.numpy()}")
    
    label_names = ['资源利用率', '负载均衡度', 'Makespan', '依赖满足度', '目标函数值', '占位符']
    for i, name in enumerate(label_names):
        print(f"  {name}: {labels[i].item():.4f}")
    
    # 分析任务特征的具体内容
    print(f"\n📋 任务特征详细分析:")
    task_features = batch_data['task_features'][0]  # [num_tasks, 128]
    num_tasks = task_features.shape[0]
    
    print(f"  任务数量: {num_tasks}")
    print(f"  特征维度: {task_features.shape[1]}")
    
    # 显示每个任务的特征摘要
    for task_id in range(min(3, num_tasks)):  # 只显示前3个任务
        task_feat = task_features[task_id]
        print(f"\n  任务 {task_id}:")
        print(f"    基础特征 (0-31): 均值={task_feat[:32].mean():.4f}, 范围=[{task_feat[:32].min():.4f}, {task_feat[:32].max():.4f}]")
        print(f"    资源特征 (32-55): 均值={task_feat[32:56].mean():.4f}, 范围=[{task_feat[32:56].min():.4f}, {task_feat[32:56].max():.4f}]")
        print(f"    结构特征 (56-79): 均值={task_feat[56:80].mean():.4f}, 范围=[{task_feat[56:80].min():.4f}, {task_feat[56:80].max():.4f}]")
        print(f"    着色特征 (80-87): 均值={task_feat[80:88].mean():.4f}, 范围=[{task_feat[80:88].min():.4f}, {task_feat[80:88].max():.4f}]")
        print(f"    上下文特征 (88-111): 均值={task_feat[88:112].mean():.4f}, 范围=[{task_feat[88:112].min():.4f}, {task_feat[88:112].max():.4f}]")
        print(f"    统计特征 (112-127): 均值={task_feat[112:].mean():.4f}, 范围=[{task_feat[112:].min():.4f}, {task_feat[112:].max():.4f}]")
    
    # 分析节点特征
    print(f"\n💻 节点特征详细分析:")
    node_features = batch_data['node_features']  # [num_nodes, 32]
    num_nodes = node_features.shape[0]
    
    print(f"  节点数量: {num_nodes}")
    print(f"  特征维度: {node_features.shape[1]}")
    
    for node_id in range(min(3, num_nodes)):  # 只显示前3个节点
        node_feat = node_features[node_id]
        print(f"\n  节点 {node_id}:")
        print(f"    基础容量 (0-3): 均值={node_feat[:4].mean():.4f}, 范围=[{node_feat[:4].min():.4f}, {node_feat[:4].max():.4f}]")
        print(f"    可用资源 (4-7): 均值={node_feat[4:8].mean():.4f}, 范围=[{node_feat[4:8].min():.4f}, {node_feat[4:8].max():.4f}]")
        print(f"    效率特征 (8-9): 均值={node_feat[8:10].mean():.4f}, 范围=[{node_feat[8:10].min():.4f}, {node_feat[8:10].max():.4f}]")
        print(f"    资源类型 (10-13): 均值={node_feat[10:14].mean():.4f}, 范围=[{node_feat[10:14].min():.4f}, {node_feat[10:14].max():.4f}]")
        print(f"    其他特征 (14-31): 均值={node_feat[14:].mean():.4f}, 范围=[{node_feat[14:].min():.4f}, {node_feat[14:].max():.4f}]")
    
    # 分析邻接矩阵
    print(f"\n🔗 邻接矩阵分析:")
    adjacency = batch_data['adjacency_matrix'][0]  # [num_tasks, num_tasks]
    num_edges = (adjacency > 0).sum().item()
    print(f"  任务数量: {adjacency.shape[0]}")
    print(f"  依赖关系数量: {num_edges}")
    print(f"  邻接矩阵:")
    print(adjacency.numpy())
    
    # 分析资源约束
    print(f"\n⚡ 资源约束分析:")
    resource_constraints = batch_data['resource_constraints'][0]  # [num_nodes, 2]
    print(f"  节点数量: {resource_constraints.shape[0]}")
    print(f"  约束维度: {resource_constraints.shape[1]} (CPU, 内存)")
    print(f"  CPU约束范围: [{resource_constraints[:, 0].min():.2f}, {resource_constraints[:, 0].max():.2f}]")
    print(f"  内存约束范围: [{resource_constraints[:, 1].min():.2f}, {resource_constraints[:, 1].max():.2f}]")
    print(f"  资源约束矩阵:")
    print(resource_constraints.numpy())


def compare_coloring_features():
    """比较着色特征"""
    print("\n" + "=" * 60)
    print("着色特征对比")
    print("=" * 60)
    
    # 生成带颜色特征的样本
    config_with = ImprovedThreeLayerGNNConfig(
        total_samples=1,
        min_tasks=3,
        max_tasks=5,
        min_nodes=2,
        max_nodes=3,
        enable_coloring=True,
        max_colors=5,
        random_seed=42
    )
    
    # 生成不带颜色特征的样本
    config_without = ImprovedThreeLayerGNNConfig(
        total_samples=1,
        min_tasks=3,
        max_tasks=5,
        min_nodes=2,
        max_nodes=3,
        enable_coloring=False,
        max_colors=5,
        random_seed=42
    )
    
    generator_with = ImprovedThreeLayerGNNDatasetGenerator(config_with)
    generator_without = ImprovedThreeLayerGNNDatasetGenerator(config_without)
    
    batch_data_with, labels_with = generator_with.generate_single_sample()
    batch_data_without, labels_without = generator_without.generate_single_sample()
    
    # 提取着色特征
    task_features_with = batch_data_with['task_features'][0]  # [num_tasks, 128]
    task_features_without = batch_data_without['task_features'][0]
    
    coloring_features_with = task_features_with[:, 80:88]  # [num_tasks, 8]
    coloring_features_without = task_features_without[:, 80:88]
    
    print(f"\n🎨 着色特征对比:")
    print(f"  带颜色特征 - 着色区域:")
    print(f"    形状: {coloring_features_with.shape}")
    print(f"    均值: {coloring_features_with.mean():.4f}")
    print(f"    标准差: {coloring_features_with.std():.4f}")
    print(f"    数值范围: [{coloring_features_with.min():.4f}, {coloring_features_with.max():.4f}]")
    print(f"    具体数值:")
    print(coloring_features_with.numpy())
    
    print(f"\n  不带颜色特征 - 着色区域:")
    print(f"    形状: {coloring_features_without.shape}")
    print(f"    均值: {coloring_features_without.mean():.4f}")
    print(f"    标准差: {coloring_features_without.std():.4f}")
    print(f"    数值范围: [{coloring_features_without.min():.4f}, {coloring_features_without.max():.4f}]")
    print(f"    具体数值:")
    print(coloring_features_without.numpy())
    
    print(f"\n  差异分析:")
    diff = torch.abs(coloring_features_with - coloring_features_without)
    print(f"    平均差异: {diff.mean():.4f}")
    print(f"    最大差异: {diff.max():.4f}")
    print(f"    差异标准差: {diff.std():.4f}")


def show_data_usage_example():
    """显示数据使用示例"""
    print("\n" + "=" * 60)
    print("数据使用示例")
    print("=" * 60)
    
    # 生成示例数据
    config = ImprovedThreeLayerGNNConfig(
        total_samples=1,
        min_tasks=3,
        max_tasks=5,
        min_nodes=2,
        max_nodes=3,
        enable_coloring=True,
        random_seed=42
    )
    
    generator = ImprovedThreeLayerGNNDatasetGenerator(config)
    batch_data, labels = generator.generate_single_sample()
    
    print(f"\n📝 数据使用示例:")
    print(f"1. 获取任务特征:")
    print(f"   task_features = batch_data['task_features']  # 形状: {batch_data['task_features'].shape}")
    
    print(f"\n2. 获取节点特征:")
    print(f"   node_features = batch_data['node_features']  # 形状: {batch_data['node_features'].shape}")
    
    print(f"\n3. 获取邻接矩阵:")
    print(f"   adjacency = batch_data['adjacency_matrix']  # 形状: {batch_data['adjacency_matrix'].shape}")
    
    print(f"\n4. 获取资源约束:")
    print(f"   resource_constraints = batch_data['resource_constraints']  # 形状: {batch_data['resource_constraints'].shape}")
    
    print(f"\n5. 获取标签:")
    print(f"   labels = labels  # 形状: {labels.shape}")
    
    print(f"\n6. 提取着色特征:")
    print(f"   coloring_features = task_features[:, 80:88]  # 形状: (num_tasks, 8)")
    
    print(f"\n7. 在模型中使用:")
    print(f"   # 前向传播")
    print(f"   assignment_probs, debug_info = model(batch_data)")
    print(f"   # 计算损失")
    print(f"   loss = criterion(predicted_quality, labels[:, 4])  # 使用目标函数值")


def main():
    """主函数"""
    print("训练数据格式示例")
    print("展示生成的训练数据的具体结构")
    print("=" * 60)
    
    # 1. 显示简单数据格式
    show_simple_data_format()
    
    # 2. 比较着色特征
    compare_coloring_features()
    
    # 3. 显示数据使用示例
    show_data_usage_example()
    
    print(f"\n✅ 数据格式示例展示完成!")
    print("可以通过这些示例了解训练数据的具体结构和用法。")


if __name__ == "__main__":
    main() 