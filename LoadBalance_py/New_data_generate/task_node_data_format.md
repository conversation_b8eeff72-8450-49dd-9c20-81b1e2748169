# 任务和节点数据格式设计（修订版）

## 数据格式概述
基于工作流数据格式，扩展生成模拟的任务和节点数据，用于负载均衡仿真。重点考虑工厂调度和甘特图需求。

## 任务数据格式 (tasks.json)

```json
{
  "metadata": {
    "total_tasks": 50,
    "total_nodes": 10,
    "generated_at": "2024-01-01T12:00:00",
    "data_type": "task_node_simulation"
  },
  "tasks": {
    "task_1": {
      "task_id": "task_1",
      "task_type": "cpu_intensive",  // cpu_intensive, memory_intensive, io_intensive, network_intensive, general
      "cpu_requirement": 2.0,         // CPU核心数需求（消耗量，不是最小值）
      "memory_requirement": 4096.0,   // 内存需求 (MB)
      "io_requirement": 128.0,        // I/O带宽需求 (MB/s)
      "network_requirement": 64.0,    // 网络带宽需求 (MB/s)
      "base_runtime": 120.5,          // 基础执行时间 (秒) - 在标准节点上的执行时间
      "dependencies": ["task_2", "task_3"],  // 依赖任务列表
      "estimated_runtime": 145.2,     // 预估执行时间 (秒) - 考虑节点性能后的实际执行时间
      "start_time": 0.0,              // 任务开始时间 (秒)
      "end_time": 145.2,              // 任务结束时间 (秒)
      "assigned_node": "node_1"       // 分配的节点ID
    },
    "task_2": {
      "task_id": "task_2",
      "task_type": "memory_intensive",
      "cpu_requirement": 1.5,
      "memory_requirement": 8192.0,
      "io_requirement": 256.0,
      "network_requirement": 32.0,
      "base_runtime": 180.0,
      "dependencies": [],
      "estimated_runtime": 195.6,
      "start_time": 0.0,
      "end_time": 195.6,
      "assigned_node": "node_2"
    }
  }
}
```

## 节点数据格式 (nodes.json)

```json
{
  "metadata": {
    "total_nodes": 10,
    "node_types": ["cpu_intensive", "memory_intensive", "io_intensive", "general"],
    "generated_at": "2024-01-01T12:00:00",
    "data_type": "task_node_simulation"
  },
  "nodes": [
    {
      "node_id": "node_1",
      "node_type": "cpu_intensive",   // cpu_intensive, memory_intensive, io_intensive, general
      "cpu_capacity": 8.0,            // CPU核心数容量
      "memory_capacity": 16384.0,     // 内存容量 (MB)
      "io_bandwidth": 1024.0,         // I/O带宽 (MB/s)
      "network_bandwidth": 512.0,     // 网络带宽 (MB/s)
      "current_load": 0.3,            // 当前负载 (0-1)
      "available_resources": {
        "cpu_available": 6.0,         // 可用CPU核心数
        "memory_available": 12288.0,  // 可用内存 (MB)
        "io_available": 768.0,        // 可用I/O带宽 (MB/s)
        "network_available": 384.0    // 可用网络带宽 (MB/s)
      },
      "task_history": [               // 任务执行历史（用于甘特图）
        {
          "task_id": "task_1",
          "start_time": 0.0,
          "end_time": 145.2,
          "cpu_usage": 2.0,
          "memory_usage": 4096.0
        }
      ],
      "makespan": 145.2              // 节点完成所有任务的时间
    },
    {
      "node_id": "node_2",
      "node_type": "memory_intensive",
      "cpu_capacity": 4.0,
      "memory_capacity": 32768.0,
      "io_bandwidth": 2048.0,
      "network_bandwidth": 256.0,
      "current_load": 0.45,
      "available_resources": {
        "cpu_available": 2.5,
        "memory_available": 24576.0,
        "io_available": 1536.0,
        "network_available": 192.0
      },
      "task_history": [],
      "makespan": 0.0
    }
  ]
}
```

## 任务类型定义

### 1. CPU密集型任务 (cpu_intensive)
- **CPU需求**: 2-4核心
- **内存需求**: 2048-8192 MB
- **I/O需求**: 64-256 MB/s
- **网络需求**: 32-128 MB/s
- **基础执行时间**: 60-300秒
- **示例**: 数值计算、图像处理、编译任务

### 2. 内存密集型任务 (memory_intensive)  
- **CPU需求**: 1-3核心
- **内存需求**: 8192-32768 MB
- **I/O需求**: 128-512 MB/s
- **网络需求**: 32-128 MB/s
- **基础执行时间**: 120-600秒
- **示例**: 数据库操作、机器学习训练、大数据分析

### 3. I/O密集型任务 (io_intensive)
- **CPU需求**: 1-2核心
- **内存需求**: 1024-4096 MB
- **I/O需求**: 512-2048 MB/s
- **网络需求**: 16-64 MB/s
- **基础执行时间**: 90-450秒
- **示例**: 日志分析、数据备份、文件处理

### 4. 网络密集型任务 (network_intensive)
- **CPU需求**: 1-2核心
- **内存需求**: 1024-4096 MB
- **I/O需求**: 32-128 MB/s
- **网络需求**: 256-1024 MB/s
- **基础执行时间**: 60-300秒
- **示例**: 网络服务、实时通信、分布式计算

### 5. 通用型任务 (general)
- **CPU需求**: 1-3核心
- **内存需求**: 2048-8192 MB
- **I/O需求**: 64-256 MB/s
- **网络需求**: 32-128 MB/s
- **基础执行时间**: 90-360秒
- **示例**: Web服务、通用计算、批处理任务

## 节点类型定义

### 1. CPU密集型节点 (cpu_intensive)
- **CPU容量**: 8-16核心
- **内存容量**: 16384-32768 MB
- **I/O带宽**: 1024-2048 MB/s
- **网络带宽**: 512-1024 MB/s
- **适合**: 计算密集型任务

### 2. 内存密集型节点 (memory_intensive)
- **CPU容量**: 4-8核心
- **内存容量**: 32768-65536 MB
- **I/O带宽**: 2048-4096 MB/s
- **网络带宽**: 256-512 MB/s
- **适合**: 大数据处理任务

### 3. I/O密集型节点 (io_intensive)
- **CPU容量**: 4-8核心
- **内存容量**: 16384-32768 MB
- **I/O带宽**: 4096-8192 MB/s
- **网络带宽**: 256-512 MB/s
- **适合**: 存储密集型任务

### 4. 通用型节点 (general)
- **CPU容量**: 6-12核心
- **内存容量**: 24576-49152 MB
- **I/O带宽**: 1536-3072 MB/s
- **网络带宽**: 384-768 MB/s
- **适合**: 多种类型任务

## 负载均衡和调度考虑

### 1. 任务分配策略
- **资源匹配**: 根据任务类型和节点类型进行匹配
- **负载均衡**: 考虑节点当前负载和可用资源
- **依赖处理**: 确保依赖任务先完成

### 2. 执行时间计算
```python
# 实际执行时间 = 基础执行时间 * 性能影响因子
performance_factor = max(
    cpu_requirement / cpu_available,
    memory_requirement / memory_available,
    io_requirement / io_available,
    network_requirement / network_available
)
actual_runtime = base_runtime * performance_factor
```

### 3. Makespan计算
```python
# 节点makespan = 该节点上所有任务完成时间的最大值
# 系统makespan = 所有节点makespan的最大值
```

### 4. 甘特图数据
- **任务历史**: 记录每个节点上任务的执行时间线
- **资源利用率**: 跟踪CPU、内存、I/O、网络的使用情况
- **负载变化**: 记录节点负载随时间的变化

## 数据生成参数

### 任务生成参数
- **任务数量**: 50-200个
- **任务类型分布**: 20% CPU, 20% 内存, 20% I/O, 20% 网络, 20% 通用
- **依赖关系**: 30-50%的任务有依赖关系
- **资源需求范围**: 根据任务类型设定合理范围

### 节点生成参数
- **节点数量**: 10-50个
- **节点类型分布**: 25% 每种类型
- **异构程度**: 资源容量有20-50%的差异
- **负载分布**: 当前负载0.1-0.8

## 文件结构
```
New_data/
├── tasks.json              # 任务数据
├── nodes.json              # 节点数据
├── scheduling_result.json   # 调度结果（包含甘特图数据）
├── task_node_summary.json  # 数据摘要
└── README.md               # 数据说明
```

这个修订版格式更符合工厂调度和甘特图的需求，去掉了不必要的属性，明确了资源需求的含义。 