"""
任务和节点数据生成程序
基于修订版数据格式，生成模拟的任务和节点数据
"""

import json
import random
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Tuple
import os


class TaskNodeGenerator:
    """任务和节点数据生成器"""
    
    def __init__(self):
        # 任务类型定义
        self.task_types = {
            'cpu_intensive': {
                'cpu_range': (2.0, 4.0),
                'memory_range': (2048.0, 8192.0),
                'io_range': (64.0, 256.0),
                'network_range': (32.0, 128.0),
                'runtime_range': (60.0, 300.0)
            },
            'memory_intensive': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (8192.0, 32768.0),
                'io_range': (128.0, 512.0),
                'network_range': (32.0, 128.0),
                'runtime_range': (120.0, 600.0)
            },
            'io_intensive': {
                'cpu_range': (1.0, 2.0),
                'memory_range': (1024.0, 4096.0),
                'io_range': (512.0, 2048.0),
                'network_range': (16.0, 64.0),
                'runtime_range': (90.0, 450.0)
            },
            'network_intensive': {
                'cpu_range': (1.0, 2.0),
                'memory_range': (1024.0, 4096.0),
                'io_range': (32.0, 128.0),
                'network_range': (256.0, 1024.0),
                'runtime_range': (60.0, 300.0)
            },
            'general': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (2048.0, 8192.0),
                'io_range': (64.0, 256.0),
                'network_range': (32.0, 128.0),
                'runtime_range': (90.0, 360.0)
            }
        }
        
        # 节点类型定义
        self.node_types = {
            'cpu_intensive': {
                'cpu_range': (8.0, 16.0),
                'memory_range': (16384.0, 32768.0),
                'io_range': (1024.0, 2048.0),
                'network_range': (512.0, 1024.0)
            },
            'memory_intensive': {
                'cpu_range': (4.0, 8.0),
                'memory_range': (32768.0, 65536.0),
                'io_range': (2048.0, 4096.0),
                'network_range': (256.0, 512.0)
            },
            'io_intensive': {
                'cpu_range': (4.0, 8.0),
                'memory_range': (16384.0, 32768.0),
                'io_range': (4096.0, 8192.0),
                'network_range': (256.0, 512.0)
            },
            'general': {
                'cpu_range': (6.0, 12.0),
                'memory_range': (24576.0, 49152.0),
                'io_range': (1536.0, 3072.0),
                'network_range': (384.0, 768.0)
            }
        }
    
    def generate_task(self, task_id: str, task_type: str) -> Dict[str, Any]:
        """生成单个任务"""
        task_config = self.task_types[task_type]
        
        # 生成资源需求
        cpu_req = round(random.uniform(*task_config['cpu_range']), 2)
        memory_req = round(random.uniform(*task_config['memory_range']), 2)
        io_req = round(random.uniform(*task_config['io_range']), 2)
        network_req = round(random.uniform(*task_config['network_range']), 2)
        base_runtime = round(random.uniform(*task_config['runtime_range']), 2)
        
        return {
            'task_id': task_id,
            'task_type': task_type,
            'cpu_requirement': cpu_req,
            'memory_requirement': memory_req,
            'io_requirement': io_req,
            'network_requirement': network_req,
            'base_runtime': base_runtime,
            'dependencies': [],
            'estimated_runtime': base_runtime,
            'start_time': 0.0,
            'end_time': base_runtime,
            'assigned_node': None
        }
    
    def generate_node(self, node_id: str, node_type: str) -> Dict[str, Any]:
        """生成单个节点"""
        node_config = self.node_types[node_type]
        
        # 生成资源容量
        cpu_cap = round(random.uniform(*node_config['cpu_range']), 2)
        memory_cap = round(random.uniform(*node_config['memory_range']), 2)
        io_cap = round(random.uniform(*node_config['io_range']), 2)
        network_cap = round(random.uniform(*node_config['network_range']), 2)
        
        # 生成当前负载
        current_load = round(random.uniform(0.1, 0.8), 2)
        
        # 计算可用资源
        cpu_available = round(cpu_cap * (1 - current_load), 2)
        memory_available = round(memory_cap * (1 - current_load), 2)
        io_available = round(io_cap * (1 - current_load), 2)
        network_available = round(network_cap * (1 - current_load), 2)
        
        return {
            'node_id': node_id,
            'node_type': node_type,
            'cpu_capacity': cpu_cap,
            'memory_capacity': memory_cap,
            'io_bandwidth': io_cap,
            'network_bandwidth': network_cap,
            'current_load': current_load,
            'available_resources': {
                'cpu_available': cpu_available,
                'memory_available': memory_available,
                'io_available': io_available,
                'network_available': network_available
            },
            'task_history': [],
            'makespan': 0.0
        }
    
    def generate_dependencies(self, tasks: Dict[str, Any], dependency_ratio: float = 0.4) -> None:
        """为任务生成依赖关系"""
        task_ids = list(tasks.keys())
        num_dependencies = int(len(task_ids) * dependency_ratio)
        
        # 随机选择一些任务添加依赖
        for i in range(num_dependencies):
            task_id = task_ids[i]
            # 随机选择1-3个依赖任务
            num_deps = random.randint(1, min(3, i))
            if num_deps > 0 and i > 0:  # 确保有前置任务可选
                dependencies = random.sample(task_ids[:i], num_deps)
                tasks[task_id]['dependencies'] = dependencies
    
    def generate_tasks(self, num_tasks: int = 100) -> Dict[str, Any]:
        """生成任务数据"""
        print(f"正在生成 {num_tasks} 个任务...")
        
        tasks = {}
        task_types = list(self.task_types.keys())
        
        # 按比例分配任务类型
        type_counts = {}
        for task_type in task_types:
            type_counts[task_type] = num_tasks // len(task_types)
        
        # 分配剩余任务
        remaining = num_tasks % len(task_types)
        for i in range(remaining):
            task_type = task_types[i]
            type_counts[task_type] += 1
        
        # 生成任务
        task_id_counter = 1
        for task_type, count in type_counts.items():
            for _ in range(count):
                task_id = f"task_{task_id_counter}"
                tasks[task_id] = self.generate_task(task_id, task_type)
                task_id_counter += 1
        
        # 生成依赖关系
        self.generate_dependencies(tasks)
        
        # 创建元数据
        metadata = {
            'total_tasks': num_tasks,
            'generated_at': datetime.now().isoformat(),
            'data_type': 'task_node_simulation'
        }
        
        return {
            'metadata': metadata,
            'tasks': tasks
        }
    
    def generate_nodes(self, num_nodes: int = 20) -> Dict[str, Any]:
        """生成节点数据"""
        print(f"正在生成 {num_nodes} 个节点...")
        
        nodes = []
        node_types = list(self.node_types.keys())
        
        # 按比例分配节点类型
        type_counts = {}
        for node_type in node_types:
            type_counts[node_type] = num_nodes // len(node_types)
        
        # 分配剩余节点
        remaining = num_nodes % len(node_types)
        for i in range(remaining):
            node_type = node_types[i]
            type_counts[node_type] += 1
        
        # 生成节点
        node_id_counter = 1
        for node_type, count in type_counts.items():
            for _ in range(count):
                node_id = f"node_{node_id_counter}"
                node = self.generate_node(node_id, node_type)
                nodes.append(node)
                node_id_counter += 1
        
        # 创建元数据
        metadata = {
            'total_nodes': num_nodes,
            'node_types': node_types,
            'generated_at': datetime.now().isoformat(),
            'data_type': 'task_node_simulation'
        }
        
        return {
            'metadata': metadata,
            'nodes': nodes
        }
    
    def calculate_task_runtime(self, task: Dict[str, Any], node: Dict[str, Any]) -> float:
        """计算任务在指定节点上的实际执行时间"""
        # 计算性能影响因子
        cpu_factor = task['cpu_requirement'] / node['available_resources']['cpu_available']
        memory_factor = task['memory_requirement'] / node['available_resources']['memory_available']
        io_factor = task['io_requirement'] / node['available_resources']['io_available']
        network_factor = task['network_requirement'] / node['available_resources']['network_available']
        
        # 取最大值作为性能影响因子
        performance_factor = max(cpu_factor, memory_factor, io_factor, network_factor, 1.0)
        
        # 计算实际执行时间
        actual_runtime = task['base_runtime'] * performance_factor
        return round(actual_runtime, 2)
    
    def generate_summary(self, tasks_data: Dict[str, Any], nodes_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成数据摘要"""
        tasks = tasks_data['tasks']
        nodes = nodes_data['nodes']
        
        # 统计任务类型分布
        task_type_distribution = {}
        for task in tasks.values():
            task_type = task['task_type']
            task_type_distribution[task_type] = task_type_distribution.get(task_type, 0) + 1
        
        # 统计节点类型分布
        node_type_distribution = {}
        for node in nodes:
            node_type = node['node_type']
            node_type_distribution[node_type] = node_type_distribution.get(node_type, 0) + 1
        
        # 计算资源需求统计
        cpu_reqs = [task['cpu_requirement'] for task in tasks.values()]
        memory_reqs = [task['memory_requirement'] for task in tasks.values()]
        io_reqs = [task['io_requirement'] for task in tasks.values()]
        network_reqs = [task['network_requirement'] for task in tasks.values()]
        
        # 计算节点容量统计
        cpu_caps = [node['cpu_capacity'] for node in nodes]
        memory_caps = [node['memory_capacity'] for node in nodes]
        io_caps = [node['io_bandwidth'] for node in nodes]
        network_caps = [node['network_bandwidth'] for node in nodes]
        
        summary = {
            'generated_at': datetime.now().isoformat(),
            'task_statistics': {
                'total_tasks': len(tasks),
                'task_type_distribution': task_type_distribution,
                'resource_requirements': {
                    'cpu': {
                        'min': round(min(cpu_reqs), 2),
                        'max': round(max(cpu_reqs), 2),
                        'mean': round(np.mean(cpu_reqs), 2),
                        'std': round(np.std(cpu_reqs), 2)
                    },
                    'memory': {
                        'min': round(min(memory_reqs), 2),
                        'max': round(max(memory_reqs), 2),
                        'mean': round(np.mean(memory_reqs), 2),
                        'std': round(np.std(memory_reqs), 2)
                    },
                    'io': {
                        'min': round(min(io_reqs), 2),
                        'max': round(max(io_reqs), 2),
                        'mean': round(np.mean(io_reqs), 2),
                        'std': round(np.std(io_reqs), 2)
                    },
                    'network': {
                        'min': round(min(network_reqs), 2),
                        'max': round(max(network_reqs), 2),
                        'mean': round(np.mean(network_reqs), 2),
                        'std': round(np.std(network_reqs), 2)
                    }
                }
            },
            'node_statistics': {
                'total_nodes': len(nodes),
                'node_type_distribution': node_type_distribution,
                'resource_capacities': {
                    'cpu': {
                        'min': round(min(cpu_caps), 2),
                        'max': round(max(cpu_caps), 2),
                        'mean': round(np.mean(cpu_caps), 2),
                        'std': round(np.std(cpu_caps), 2)
                    },
                    'memory': {
                        'min': round(min(memory_caps), 2),
                        'max': round(max(memory_caps), 2),
                        'mean': round(np.mean(memory_caps), 2),
                        'std': round(np.std(memory_caps), 2)
                    },
                    'io': {
                        'min': round(min(io_caps), 2),
                        'max': round(max(io_caps), 2),
                        'mean': round(np.mean(io_caps), 2),
                        'std': round(np.std(io_caps), 2)
                    },
                    'network': {
                        'min': round(min(network_caps), 2),
                        'max': round(max(network_caps), 2),
                        'mean': round(np.mean(network_caps), 2),
                        'std': round(np.std(network_caps), 2)
                    }
                }
            }
        }
        
        return summary
    
    def save_data(self, tasks_data: Dict[str, Any], nodes_data: Dict[str, Any], 
                  summary_data: Dict[str, Any], output_dir: str = "New_data") -> None:
        """保存数据到文件"""
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存任务数据
        tasks_file = os.path.join(output_dir, "tasks.json")
        with open(tasks_file, 'w', encoding='utf-8') as f:
            json.dump(tasks_data, f, indent=2, ensure_ascii=False)
        print(f"任务数据已保存: {tasks_file}")
        
        # 保存节点数据
        nodes_file = os.path.join(output_dir, "nodes.json")
        with open(nodes_file, 'w', encoding='utf-8') as f:
            json.dump(nodes_data, f, indent=2, ensure_ascii=False)
        print(f"节点数据已保存: {nodes_file}")
        
        # 保存摘要数据
        summary_file = os.path.join(output_dir, "task_node_summary.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False)
        print(f"数据摘要已保存: {summary_file}")
        
        # 创建README文件
        readme_content = f"""# 任务和节点数据说明

## 数据概述
- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 任务数量: {tasks_data['metadata']['total_tasks']}
- 节点数量: {nodes_data['metadata']['total_nodes']}

## 文件说明
- `tasks.json`: 任务数据，包含资源需求、依赖关系等
- `nodes.json`: 节点数据，包含资源容量、当前负载等
- `task_node_summary.json`: 数据统计摘要

## 数据格式
详细格式说明请参考 `task_node_data_format.md`

## 使用说明
此数据可用于负载均衡仿真、调度算法测试、甘特图生成等。
"""
        
        readme_file = os.path.join(output_dir, "README.md")
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"说明文档已保存: {readme_file}")
    
    def generate_all_data(self, num_tasks: int = 100, num_nodes: int = 20, 
                         output_dir: str = "New_data") -> None:
        """生成所有数据"""
        print("=" * 60)
        print("任务和节点数据生成器")
        print("=" * 60)
        
        # 生成任务数据
        tasks_data = self.generate_tasks(num_tasks)
        
        # 生成节点数据
        nodes_data = self.generate_nodes(num_nodes)
        
        # 生成摘要数据
        summary_data = self.generate_summary(tasks_data, nodes_data)
        
        # 保存数据
        self.save_data(tasks_data, nodes_data, summary_data, output_dir)
        
        print("=" * 60)
        print("数据生成完成！")
        print("=" * 60)


def main():
    """主函数"""
    generator = TaskNodeGenerator()
    
    # 生成数据
    generator.generate_all_data(
        num_tasks=100,    # 100个任务
        num_nodes=20,     # 20个节点
        output_dir="New_data"
    )


if __name__ == "__main__":
    main() 