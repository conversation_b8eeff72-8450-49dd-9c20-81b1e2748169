"""
测试数据集生成器
验证基础神经网络数据集生成器的功能
"""

import sys
import os
import numpy as np
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from neural_network_dataset_generator import NeuralNetworkDatasetGenerator, NeuralNetworkDatasetConfig


def test_basic_generator():
    """测试基础数据集生成器"""
    print("=" * 50)
    print("测试基础神经网络数据集生成器")
    print("=" * 50)
    
    # 创建小规模配置用于测试
    config = NeuralNetworkDatasetConfig(
        total_samples=100,        # 小规模测试
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15,
        min_tasks=5,
        max_tasks=20,
        min_nodes=3,
        max_nodes=10,
        normalize_features=True,
        random_seed=42
    )
    
    # 创建生成器
    generator = NeuralNetworkDatasetGenerator(config)
    
    # 生成单个样本进行测试
    print("\n1. 测试单个样本生成...")
    features, labels = generator.generate_single_sample()
    print(f"特征形状: {features.shape}")
    print(f"标签形状: {labels.shape}")
    print(f"特征范围: [{features.min():.3f}, {features.max():.3f}]")
    print(f"标签范围: [{labels.min():.3f}, {labels.max():.3f}]")
    
    # 生成小规模数据集
    print("\n2. 生成小规模数据集...")
    X, y = generator.generate_dataset()
    print(f"数据集特征形状: {X.shape}")
    print(f"数据集标签形状: {y.shape}")
    
    # 测试数据分割
    print("\n3. 测试数据分割...")
    X_train, X_val, X_test, y_train, y_val, y_test = generator.split_dataset(X, y)
    print(f"训练集: {X_train.shape}")
    print(f"验证集: {X_val.shape}")
    print(f"测试集: {X_test.shape}")
    
    # 测试特征标准化
    print("\n4. 测试特征标准化...")
    X_train_scaled, X_val_scaled, X_test_scaled = generator.normalize_features(X_train, X_val, X_test)
    print(f"标准化后训练集范围: [{X_train_scaled.min():.3f}, {X_train_scaled.max():.3f}]")
    
    # 测试数据集保存
    print("\n5. 测试数据集保存...")
    test_output_dir = "test_neural_dataset"
    generator.save_dataset(X_train_scaled, X_val_scaled, X_test_scaled, 
                         y_train, y_val, y_test, test_output_dir)
    
    # 验证保存的数据
    print("\n6. 验证保存的数据...")
    X_train_loaded = np.load(os.path.join(test_output_dir, "X_train.npy"))
    y_train_loaded = np.load(os.path.join(test_output_dir, "y_train.npy"))
    print(f"加载的训练集特征形状: {X_train_loaded.shape}")
    print(f"加载的训练集标签形状: {y_train_loaded.shape}")
    
    # 检查数据集信息文件
    with open(os.path.join(test_output_dir, "dataset_info.json"), "r") as f:
        dataset_info = json.load(f)
    print(f"\n数据集信息:")
    print(f"总样本数: {dataset_info['total_samples']}")
    print(f"训练样本数: {dataset_info['train_samples']}")
    print(f"验证样本数: {dataset_info['val_samples']}")
    print(f"测试样本数: {dataset_info['test_samples']}")
    print(f"特征维度: {dataset_info['feature_dim']}")
    print(f"标签维度: {dataset_info['label_dim']}")
    
    print("\n✅ 基础数据集生成器测试完成！")


def test_advanced_generator():
    """测试高级数据集生成器"""
    print("\n" + "=" * 50)
    print("测试高级神经网络数据集生成器")
    print("=" * 50)
    
    try:
        from advanced_neural_dataset_generator import AdvancedNeuralDatasetGenerator, AdvancedDatasetConfig
        
        # 创建小规模配置用于测试
        config = AdvancedDatasetConfig(
            total_samples=50,             # 小规模测试
            train_ratio=0.7,
            val_ratio=0.15,
            test_ratio=0.15,
            min_tasks=5,
            max_tasks=15,
            min_nodes=3,
            max_nodes=8,
            support_cnn=True,
            support_rnn=True,
            support_transformer=True,
            support_gnn=False,  # 暂时禁用GNN以避免依赖问题
            support_mlp=True,
            normalize_features=True,
            add_noise=True,
            noise_level=0.05,
            feature_augmentation=True,
            max_graph_size=50,
            edge_probability=0.3,
            max_sequence_length=20,
            sequence_padding=True,
            random_seed=42
        )
        
        # 创建生成器
        generator = AdvancedNeuralDatasetGenerator(config)
        
        # 生成单个样本进行测试
        print("\n1. 测试单个样本生成...")
        sample = generator.generate_single_sample()
        
        print(f"MLP特征形状: {sample['mlp_features'].shape}")
        print(f"CNN特征形状: {sample['cnn_features'].shape}")
        print(f"RNN特征形状: {sample['rnn_features'].shape}")
        print(f"Transformer任务序列形状: {sample['transformer_features']['task_sequence'].shape}")
        print(f"Transformer节点序列形状: {sample['transformer_features']['node_sequence'].shape}")
        print(f"标签形状: {sample['labels'].shape}")
        
        # 生成小规模数据集
        print("\n2. 生成小规模数据集...")
        dataset = generator.generate_dataset()
        
        print(f"MLP特征数量: {len(dataset['mlp_features'])}")
        print(f"CNN特征数量: {len(dataset['cnn_features'])}")
        print(f"RNN特征数量: {len(dataset['rnn_features'])}")
        print(f"Transformer特征数量: {len(dataset['transformer_features'])}")
        print(f"标签数量: {len(dataset['labels'])}")
        
        # 测试数据集保存
        print("\n3. 测试数据集保存...")
        test_output_dir = "test_advanced_neural_dataset"
        generator.save_dataset(dataset, test_output_dir)
        
        # 验证保存的数据
        print("\n4. 验证保存的数据...")
        train_dir = os.path.join(test_output_dir, "train")
        
        mlp_features = np.load(os.path.join(train_dir, "mlp_features.npy"))
        cnn_features = np.load(os.path.join(train_dir, "cnn_features.npy"))
        rnn_features = np.load(os.path.join(train_dir, "rnn_features.npy"))
        labels = np.load(os.path.join(train_dir, "labels.npy"))
        
        print(f"加载的MLP特征形状: {mlp_features.shape}")
        print(f"加载的CNN特征形状: {cnn_features.shape}")
        print(f"加载的RNN特征形状: {rnn_features.shape}")
        print(f"加载的标签形状: {labels.shape}")
        
        # 检查Transformer数据
        task_sequence = np.load(os.path.join(train_dir, "transformer_features_task_sequence.npy"))
        node_sequence = np.load(os.path.join(train_dir, "transformer_features_node_sequence.npy"))
        print(f"加载的任务序列形状: {task_sequence.shape}")
        print(f"加载的节点序列形状: {node_sequence.shape}")
        
        print("\n✅ 高级数据集生成器测试完成！")
        
    except ImportError as e:
        print(f"⚠️ 高级数据集生成器测试跳过: {e}")
        print("请安装必要的依赖包: torch, torch-geometric, networkx")


def test_data_quality():
    """测试数据质量"""
    print("\n" + "=" * 50)
    print("测试数据质量")
    print("=" * 50)
    
    # 创建配置
    config = NeuralNetworkDatasetConfig(
        total_samples=1000,
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15,
        min_tasks=10,
        max_tasks=50,
        min_nodes=5,
        max_nodes=20,
        normalize_features=True,
        random_seed=42
    )
    
    # 创建生成器
    generator = NeuralNetworkDatasetGenerator(config)
    
    # 生成数据集
    print("生成数据集进行质量测试...")
    X, y = generator.generate_dataset()
    
    # 数据质量检查
    print(f"\n数据质量检查:")
    print(f"特征形状: {X.shape}")
    print(f"标签形状: {y.shape}")
    print(f"特征范围: [{X.min():.3f}, {X.max():.3f}]")
    print(f"标签范围: [{y.min():.3f}, {y.max():.3f}]")
    print(f"特征均值: {X.mean():.3f}")
    print(f"特征标准差: {X.std():.3f}")
    print(f"是否有NaN: {np.isnan(X).any()}")
    print(f"是否有无穷值: {np.isinf(X).any()}")
    print(f"标签是否有NaN: {np.isnan(y).any()}")
    print(f"标签是否有无穷值: {np.isinf(y).any()}")
    
    # 检查标签分布
    print(f"\n标签分布:")
    for i in range(y.shape[1]):
        label_name = ["资源利用率", "负载均衡度", "Makespan", "目标函数值"][i]
        print(f"{label_name}: 均值={y[:, i].mean():.3f}, 标准差={y[:, i].std():.3f}")
    
    print("\n✅ 数据质量测试完成！")


def main():
    """主函数"""
    print("开始测试神经网络数据集生成器...")
    print(f"测试时间: {datetime.now()}")
    
    # 测试基础生成器
    test_basic_generator()
    
    # 测试高级生成器
    test_advanced_generator()
    
    # 测试数据质量
    test_data_quality()
    
    print("\n" + "=" * 50)
    print("所有测试完成！")
    print("=" * 50)
    
    # 清理测试文件
    import shutil
    test_dirs = ["test_neural_dataset", "test_advanced_neural_dataset"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"已清理测试目录: {test_dir}")


if __name__ == "__main__":
    main() 