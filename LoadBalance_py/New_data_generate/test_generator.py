"""
简化的任务和节点数据生成器测试版
"""

import json
import random
import numpy as np
from datetime import datetime
import os


def generate_simple_task(task_id, task_type):
    """生成简单任务"""
    return {
        'task_id': task_id,
        'task_type': task_type,
        'cpu_requirement': round(random.uniform(1.0, 4.0), 2),
        'memory_requirement': round(random.uniform(1024.0, 16384.0), 2),
        'io_requirement': round(random.uniform(32.0, 512.0), 2),
        'network_requirement': round(random.uniform(16.0, 256.0), 2),
        'base_runtime': round(random.uniform(60.0, 300.0), 2),
        'dependencies': [],
        'estimated_runtime': 0.0,
        'start_time': 0.0,
        'end_time': 0.0,
        'assigned_node': None
    }


def generate_simple_node(node_id, node_type):
    """生成简单节点"""
    return {
        'node_id': node_id,
        'node_type': node_type,
        'cpu_capacity': round(random.uniform(4.0, 16.0), 2),
        'memory_capacity': round(random.uniform(8192.0, 32768.0), 2),
        'io_bandwidth': round(random.uniform(512.0, 2048.0), 2),
        'network_bandwidth': round(random.uniform(256.0, 1024.0), 2),
        'current_load': round(random.uniform(0.1, 0.8), 2),
        'available_resources': {
            'cpu_available': 0.0,
            'memory_available': 0.0,
            'io_available': 0.0,
            'network_available': 0.0
        },
        'task_history': [],
        'makespan': 0.0
    }


def main():
    """主函数"""
    print("开始生成测试数据...")
    
    # 生成任务
    tasks = {}
    task_types = ['cpu_intensive', 'memory_intensive', 'io_intensive', 'network_intensive', 'general']
    
    for i in range(20):  # 生成20个任务
        task_id = f"task_{i+1}"
        task_type = random.choice(task_types)
        tasks[task_id] = generate_simple_task(task_id, task_type)
    
    # 生成节点
    nodes = []
    node_types = ['cpu_intensive', 'memory_intensive', 'io_intensive', 'general']
    
    for i in range(5):  # 生成5个节点
        node_id = f"node_{i+1}"
        node_type = random.choice(node_types)
        node = generate_simple_node(node_id, node_type)
        
        # 计算可用资源
        current_load = node['current_load']
        node['available_resources']['cpu_available'] = round(node['cpu_capacity'] * (1 - current_load), 2)
        node['available_resources']['memory_available'] = round(node['memory_capacity'] * (1 - current_load), 2)
        node['available_resources']['io_available'] = round(node['io_bandwidth'] * (1 - current_load), 2)
        node['available_resources']['network_available'] = round(node['network_bandwidth'] * (1 - current_load), 2)
        
        nodes.append(node)
    
    # 创建数据结构
    tasks_data = {
        'metadata': {
            'total_tasks': len(tasks),
            'generated_at': datetime.now().isoformat(),
            'data_type': 'task_node_simulation'
        },
        'tasks': tasks
    }
    
    nodes_data = {
        'metadata': {
            'total_nodes': len(nodes),
            'node_types': node_types,
            'generated_at': datetime.now().isoformat(),
            'data_type': 'task_node_simulation'
        },
        'nodes': nodes
    }
    
    # 保存数据
    os.makedirs("New_data", exist_ok=True)
    
    with open("New_data/tasks.json", 'w', encoding='utf-8') as f:
        json.dump(tasks_data, f, indent=2, ensure_ascii=False)
    
    with open("New_data/nodes.json", 'w', encoding='utf-8') as f:
        json.dump(nodes_data, f, indent=2, ensure_ascii=False)
    
    print("测试数据生成完成！")
    print(f"生成了 {len(tasks)} 个任务")
    print(f"生成了 {len(nodes)} 个节点")


if __name__ == "__main__":
    main() 