"""
Three Layer GNN数据集生成器
专门为ThreeLayerGNNScheduler模型生成训练数据
"""

import json
import random
import numpy as np
import pandas as pd
from datetime import datetime
import os
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
import pickle
import torch
import networkx as nx
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split


@dataclass
class ThreeLayerGNNConfig:
    """Three Layer GNN数据集配置"""
    # 数据集规模
    total_samples: int = 10000
    train_ratio: float = 0.7
    val_ratio: float = 0.15
    test_ratio: float = 0.15
    
    # 任务和节点数量范围
    min_tasks: int = 10
    max_tasks: int = 100
    min_nodes: int = 5
    max_nodes: int = 30
    
    # 特征维度（匹配three_layer_gnn模型）
    task_feature_dim: int = 128
    node_feature_dim: int = 32
    resource_dim: int = 2  # CPU和内存约束
    
    # 图结构参数
    max_dag_depth: int = 8
    max_dag_width: int = 15
    dependency_probability: float = 0.3
    
    # 数据质量参数
    normalize_features: bool = True
    add_noise: bool = True
    noise_level: float = 0.05
    
    random_seed: int = 42


class ThreeLayerGNNDatasetGenerator:
    """Three Layer GNN数据集生成器"""
    
    def __init__(self, config: ThreeLayerGNNConfig):
        self.config = config
        self.scaler = StandardScaler()
        
        # 设置随机种子
        random.seed(config.random_seed)
        np.random.seed(config.random_seed)
        torch.manual_seed(config.random_seed)
        
        # 任务类型定义
        self.task_types = {
            'cpu_intensive': {
                'cpu_range': (2.0, 8.0),
                'memory_range': (2048.0, 16384.0),
                'io_range': (64.0, 512.0),
                'network_range': (32.0, 256.0),
                'runtime_range': (60.0, 600.0)
            },
            'memory_intensive': {
                'cpu_range': (1.0, 4.0),
                'memory_range': (8192.0, 65536.0),
                'io_range': (128.0, 1024.0),
                'network_range': (32.0, 256.0),
                'runtime_range': (120.0, 1200.0)
            },
            'io_intensive': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (1024.0, 8192.0),
                'io_range': (512.0, 4096.0),
                'network_range': (16.0, 128.0),
                'runtime_range': (90.0, 900.0)
            },
            'network_intensive': {
                'cpu_range': (1.0, 3.0),
                'memory_range': (1024.0, 8192.0),
                'io_range': (32.0, 256.0),
                'network_range': (256.0, 2048.0),
                'runtime_range': (60.0, 600.0)
            },
            'general': {
                'cpu_range': (1.0, 4.0),
                'memory_range': (2048.0, 16384.0),
                'io_range': (64.0, 512.0),
                'network_range': (32.0, 256.0),
                'runtime_range': (90.0, 720.0)
            }
        }
        
        # 节点类型定义
        self.node_types = {
            'cpu_intensive': {
                'cpu_range': (8.0, 32.0),
                'memory_range': (16384.0, 131072.0),
                'io_range': (1024.0, 8192.0),
                'network_range': (512.0, 4096.0)
            },
            'memory_intensive': {
                'cpu_range': (4.0, 16.0),
                'memory_range': (32768.0, 262144.0),
                'io_range': (2048.0, 16384.0),
                'network_range': (256.0, 2048.0)
            },
            'io_intensive': {
                'cpu_range': (4.0, 16.0),
                'memory_range': (16384.0, 131072.0),
                'io_range': (4096.0, 32768.0),
                'network_range': (256.0, 2048.0)
            },
            'general': {
                'cpu_range': (6.0, 24.0),
                'memory_range': (24576.0, 196608.0),
                'io_range': (1536.0, 12288.0),
                'network_range': (384.0, 3072.0)
            }
        }
    
    def generate_dag_structure(self, num_tasks: int) -> nx.DiGraph:
        """生成DAG结构"""
        dag = nx.DiGraph()
        
        # 添加节点
        for i in range(num_tasks):
            dag.add_node(i)
        
        # 生成依赖关系（确保是DAG）
        for i in range(num_tasks):
            # 为每个任务添加一些依赖
            num_deps = random.randint(0, min(3, i))  # 最多依赖3个前面的任务
            
            for _ in range(num_deps):
                # 随机选择一个前面的任务作为依赖
                dep_task = random.randint(0, i-1)
                
                # 检查是否会形成环
                dag.add_edge(dep_task, i)
                
                # 如果形成环，移除这条边
                if not nx.is_directed_acyclic_graph(dag):
                    dag.remove_edge(dep_task, i)
        
        # 确保图是连通的（添加一些随机边）
        for i in range(1, num_tasks):
            if dag.in_degree(i) == 0:
                # 如果某个节点没有入边，随机添加一个依赖
                dep_task = random.randint(0, i-1)
                dag.add_edge(dep_task, i)
        
        return dag
    
    def generate_task_features(self, task_id: int, task_type: str, dag: nx.DiGraph) -> Dict[str, Any]:
        """生成任务特征（匹配three_layer_gnn的128维特征）"""
        task_config = self.task_types[task_type]
        
        # 基础资源需求
        cpu_req = round(random.uniform(*task_config['cpu_range']), 2)
        memory_req = round(random.uniform(*task_config['memory_range']), 2)
        io_req = round(random.uniform(*task_config['io_range']), 2)
        network_req = round(random.uniform(*task_config['network_range']), 2)
        runtime = round(random.uniform(*task_config['runtime_range']), 2)
        
        # 生成128维特征向量（匹配WorkflowFeatureExtractor）
        task_features = np.zeros(self.config.task_feature_dim)
        
        # 基础任务特征 (32维)
        task_features[0] = np.log1p(runtime)
        task_features[1] = runtime / 100.0
        task_features[2] = np.log1p(cpu_req)
        task_features[3] = np.log1p(memory_req)
        task_features[4] = np.log1p(io_req + network_req)
        task_features[5] = (io_req + network_req) / max(cpu_req + memory_req, 1e-6)
        task_features[6] = runtime / max(cpu_req + memory_req + io_req + network_req, 1e-6)
        task_features[7] = (cpu_req + memory_req + io_req + network_req) / max(runtime, 1e-6)
        
        # 资源需求特征 (16维)
        total_demand = cpu_req + memory_req + io_req + network_req
        if total_demand > 0:
            task_features[32] = cpu_req / total_demand
            task_features[33] = memory_req / total_demand
            task_features[34] = io_req / total_demand
            task_features[35] = network_req / total_demand
        
        task_features[36] = np.log1p(cpu_req)
        task_features[37] = np.log1p(memory_req)
        task_features[38] = np.log1p(io_req)
        task_features[39] = np.log1p(network_req)
        
        # DAG结构特征 (24维)
        task_features[56] = dag.in_degree(task_id)
        task_features[57] = dag.out_degree(task_id)
        task_features[58] = dag.degree(task_id)
        
        # 着色特征 (8维) - 简化版
        task_features[80] = random.random()  # 颜色
        task_features[81] = random.choice([0, 1, 2, 3, 4])  # 资源类型
        
        # 工作流上下文特征 (24维)
        task_features[88] = len(dag.nodes()) / 100.0
        task_features[89] = len(dag.edges()) / max(len(dag.nodes()), 1)
        
        # 统计特征 (24维)
        all_runtimes = [dag.nodes[node].get('runtime', 0) for node in dag.nodes()]
        if all_runtimes:
            task_features[112] = np.mean(all_runtimes)
            task_features[113] = np.std(all_runtimes)
            task_features[114] = (runtime - np.mean(all_runtimes)) / max(np.std(all_runtimes), 1e-6)
        
        return {
            'task_id': task_id,
            'task_type': task_type,
            'cpu_requirement': cpu_req,
            'memory_requirement': memory_req,
            'io_requirement': io_req,
            'network_requirement': network_req,
            'runtime': runtime,
            'task_features': task_features,
            'total_resource_demand': total_demand
        }
    
    def generate_node_features(self, node_id: int, node_type: str) -> Dict[str, Any]:
        """生成节点特征（匹配three_layer_gnn的32维特征）"""
        node_config = self.node_types[node_type]
        
        # 基础资源容量
        cpu_cap = round(random.uniform(*node_config['cpu_range']), 2)
        memory_cap = round(random.uniform(*node_config['memory_range']), 2)
        io_cap = round(random.uniform(*node_config['io_range']), 2)
        network_cap = round(random.uniform(*node_config['network_range']), 2)
        
        # 当前负载
        current_load = round(random.uniform(0.1, 0.8), 2)
        
        # 计算可用资源
        cpu_available = cpu_cap * (1 - current_load)
        memory_available = memory_cap * (1 - current_load)
        io_available = io_cap * (1 - current_load)
        network_available = network_cap * (1 - current_load)
        
        # 生成32维特征向量
        node_features = np.zeros(self.config.node_feature_dim)
        
        # 基础容量特征
        node_features[0] = np.log1p(cpu_cap)
        node_features[1] = np.log1p(memory_cap)
        node_features[2] = np.log1p(io_cap)
        node_features[3] = np.log1p(network_cap)
        
        # 可用资源特征
        node_features[4] = cpu_available
        node_features[5] = memory_available
        node_features[6] = io_available
        node_features[7] = network_available
        
        # 效率特征
        node_features[8] = current_load
        node_features[9] = 1.0 - current_load  # 可用率
        
        # 资源类型特征
        total_capacity = cpu_cap + memory_cap + io_cap + network_cap
        if total_capacity > 0:
            node_features[10] = cpu_cap / total_capacity
            node_features[11] = memory_cap / total_capacity
            node_features[12] = io_cap / total_capacity
            node_features[13] = network_cap / total_capacity
        
        return {
            'node_id': node_id,
            'node_type': node_type,
            'cpu_capacity': cpu_cap,
            'memory_capacity': memory_cap,
            'io_bandwidth': io_cap,
            'network_bandwidth': network_cap,
            'current_load': current_load,
            'cpu_available': cpu_available,
            'memory_available': memory_available,
            'io_available': io_available,
            'network_available': network_available,
            'total_capacity': total_capacity,
            'node_features': node_features
        }
    
    def generate_optimal_allocation(self, tasks: Dict[int, Any], nodes: List[Dict[str, Any]], 
                                  dag: nx.DiGraph) -> Dict[str, Any]:
        """生成最优分配（考虑DAG依赖关系）"""
        task_assignments = {}
        node_loads = {node['node_id']: 0.0 for node in nodes}
        
        # 拓扑排序确保依赖关系
        try:
            topo_order = list(nx.topological_sort(dag))
        except:
            # 如果有环，使用节点ID顺序
            topo_order = list(tasks.keys())
        
        # 按拓扑顺序分配任务
        for task_id in topo_order:
            task = tasks[task_id]
            best_node = None
            best_score = float('inf')
            
            for node in nodes:
                # 检查资源约束
                if (task['cpu_requirement'] <= node['cpu_available'] and
                    task['memory_requirement'] <= node['memory_available']):
                    
                    # 计算综合评分
                    load_score = node_loads[node['node_id']]
                    resource_match = (task['cpu_requirement'] / node['cpu_capacity'] + 
                                    task['memory_requirement'] / node['memory_capacity']) / 2
                    
                    # 考虑依赖关系（优先分配到相同节点）
                    dependency_bonus = 0.0
                    for pred in dag.predecessors(task_id):
                        if pred in task_assignments and task_assignments[pred] == node['node_id']:
                            dependency_bonus = -0.1  # 奖励分配到相同节点
                    
                    score = load_score + resource_match + dependency_bonus
                    
                    if score < best_score:
                        best_score = score
                        best_node = node['node_id']
            
            if best_node:
                task_assignments[task_id] = best_node
                node_loads[best_node] += task['total_resource_demand'] / next(n['total_capacity'] for n in nodes if n['node_id'] == best_node)
        
        return {
            'task_assignments': task_assignments,
            'node_loads': node_loads,
            'allocation_quality': self._calculate_allocation_quality(tasks, nodes, task_assignments, dag)
        }
    
    def _calculate_allocation_quality(self, tasks: Dict[int, Any], nodes: List[Dict[str, Any]], 
                                   assignments: Dict[int, int], dag: nx.DiGraph) -> Dict[str, float]:
        """计算分配质量指标"""
        if not assignments:
            return {
                'resource_utilization': 0.0,
                'load_balance': 0.0,
                'makespan': 0.0,
                'dependency_satisfaction': 0.0,
                'objective_value': 0.0
            }
        
        # 计算资源利用率
        total_utilization = 0.0
        node_utilizations = {}
        
        for node in nodes:
            node_tasks = [task_id for task_id, node_id in assignments.items() if node_id == node['node_id']]
            if node_tasks:
                cpu_used = sum(tasks[task_id]['cpu_requirement'] for task_id in node_tasks)
                memory_used = sum(tasks[task_id]['memory_requirement'] for task_id in node_tasks)
                
                utilization = (cpu_used / node['cpu_capacity'] + memory_used / node['memory_capacity']) / 2
                node_utilizations[node['node_id']] = utilization
                total_utilization += utilization
        
        avg_utilization = total_utilization / len(nodes)
        
        # 计算负载均衡度
        if node_utilizations:
            load_balance = 1.0 - np.std(list(node_utilizations.values()))
        else:
            load_balance = 0.0
        
        # 计算makespan（简化版）
        makespan = max(node_utilizations.values()) if node_utilizations else 0.0
        
        # 计算依赖满足度
        dependency_satisfaction = 0.0
        total_dependencies = 0
        
        for edge in dag.edges():
            pred, succ = edge
            if pred in assignments and succ in assignments:
                total_dependencies += 1
                if assignments[pred] == assignments[succ]:
                    dependency_satisfaction += 1
        
        if total_dependencies > 0:
            dependency_satisfaction /= total_dependencies
        
        # 计算综合目标函数值
        objective_value = (avg_utilization * 0.3 + load_balance * 0.3 + 
                          (1.0 - makespan) * 0.2 + dependency_satisfaction * 0.2)
        
        return {
            'resource_utilization': avg_utilization,
            'load_balance': load_balance,
            'makespan': makespan,
            'dependency_satisfaction': dependency_satisfaction,
            'objective_value': objective_value
        }
    
    def prepare_batch_data(self, tasks: Dict[int, Any], nodes: List[Dict[str, Any]], 
                          dag: nx.DiGraph, allocation_result: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """准备three_layer_gnn模型的批次数据"""
        num_tasks = len(tasks)
        num_nodes = len(nodes)
        
        # 任务特征 [batch_size, max_tasks, input_dim]
        task_features = torch.zeros(1, num_tasks, self.config.task_feature_dim)
        for task_id, task in tasks.items():
            task_features[0, task_id] = torch.tensor(task['task_features'], dtype=torch.float32)
        
        # 邻接矩阵 [batch_size, max_tasks, max_tasks]
        adjacency_matrix = torch.zeros(1, num_tasks, num_tasks)
        for edge in dag.edges():
            adjacency_matrix[0, edge[0], edge[1]] = 1.0
        
        # 节点特征 [total_nodes, input_dim]
        node_features = torch.zeros(num_nodes, self.config.node_feature_dim)
        for i, node in enumerate(nodes):
            node_features[i] = torch.tensor(node['node_features'], dtype=torch.float32)
        
        # 任务边索引 [2, num_edges]
        task_edges = list(dag.edges())
        if task_edges:
            task_edge_index = torch.tensor(task_edges, dtype=torch.long).t()
        else:
            task_edge_index = torch.zeros(2, 0, dtype=torch.long)
        
        # 批次索引
        task_batch = torch.zeros(num_tasks, dtype=torch.long)
        node_batch = torch.zeros(num_nodes, dtype=torch.long)
        
        # 资源约束 [batch_size, max_nodes, resource_dim]
        resource_constraints = torch.zeros(1, num_nodes, self.config.resource_dim)
        for i, node in enumerate(nodes):
            resource_constraints[0, i, 0] = node['cpu_available']  # CPU约束
            resource_constraints[0, i, 1] = node['memory_available']  # 内存约束
        
        # 约束数据
        constraint_data = {
            'dag': dag,
            'tasks': tasks,
            'nodes': nodes,
            'allocation_result': allocation_result
        }
        
        return {
            'task_features': task_features,
            'adjacency_matrix': adjacency_matrix,
            'node_features': node_features,
            'task_edge_index': task_edge_index,
            'task_batch': task_batch,
            'node_batch': node_batch,
            'resource_constraints': resource_constraints,
            'constraint_data': constraint_data
        }
    
    def generate_labels(self, allocation_result: Dict[str, Any]) -> torch.Tensor:
        """生成标签（分配概率矩阵）"""
        quality = allocation_result['allocation_quality']
        
        # 生成6维标签向量
        labels = torch.tensor([
            quality['resource_utilization'],
            quality['load_balance'],
            quality['makespan'],
            quality['dependency_satisfaction'],
            quality['objective_value'],
            1.0  # 占位符
        ], dtype=torch.float32)
        
        return labels
    
    def generate_single_sample(self) -> Tuple[Dict[str, torch.Tensor], torch.Tensor]:
        """生成单个样本"""
        # 随机生成任务和节点数量
        num_tasks = random.randint(self.config.min_tasks, self.config.max_tasks)
        num_nodes = random.randint(self.config.min_nodes, self.config.max_nodes)
        
        # 生成DAG结构
        dag = self.generate_dag_structure(num_tasks)
        
        # 生成任务
        tasks = {}
        task_types = list(self.task_types.keys())
        for task_id in range(num_tasks):
            task_type = random.choice(task_types)
            tasks[task_id] = self.generate_task_features(task_id, task_type, dag)
            # 将任务信息添加到DAG节点
            dag.nodes[task_id].update(tasks[task_id])
        
        # 生成节点
        nodes = []
        node_types = list(self.node_types.keys())
        for i in range(num_nodes):
            node_id = i
            node_type = random.choice(node_types)
            nodes.append(self.generate_node_features(node_id, node_type))
        
        # 生成最优分配
        allocation_result = self.generate_optimal_allocation(tasks, nodes, dag)
        
        # 准备批次数据
        batch_data = self.prepare_batch_data(tasks, nodes, dag, allocation_result)
        
        # 生成标签
        labels = self.generate_labels(allocation_result)
        
        return batch_data, labels
    
    def generate_dataset(self) -> Tuple[List[Dict[str, torch.Tensor]], List[torch.Tensor]]:
        """生成完整数据集"""
        print(f"生成 {self.config.total_samples} 个样本...")
        
        batch_data_list = []
        labels_list = []
        
        for i in range(self.config.total_samples):
            if (i + 1) % 1000 == 0:
                print(f"已生成 {i + 1} 个样本")
            
            batch_data, labels = self.generate_single_sample()
            batch_data_list.append(batch_data)
            labels_list.append(labels)
        
        print(f"数据集生成完成！")
        return batch_data_list, labels_list
    
    def save_dataset(self, batch_data_list: List[Dict[str, torch.Tensor]], 
                    labels_list: List[torch.Tensor], output_dir: str = "three_layer_gnn_dataset"):
        """保存数据集"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 分割数据集
        train_size = int(self.config.total_samples * self.config.train_ratio)
        val_size = int(self.config.total_samples * self.config.val_ratio)
        
        # 保存训练集
        train_dir = os.path.join(output_dir, "train")
        os.makedirs(train_dir, exist_ok=True)
        
        train_data = batch_data_list[:train_size]
        train_labels = labels_list[:train_size]
        
        torch.save(train_data, os.path.join(train_dir, "batch_data.pt"))
        torch.save(train_labels, os.path.join(train_dir, "labels.pt"))
        
        # 保存验证集
        val_dir = os.path.join(output_dir, "val")
        os.makedirs(val_dir, exist_ok=True)
        
        val_data = batch_data_list[train_size:train_size+val_size]
        val_labels = labels_list[train_size:train_size+val_size]
        
        torch.save(val_data, os.path.join(val_dir, "batch_data.pt"))
        torch.save(val_labels, os.path.join(val_dir, "labels.pt"))
        
        # 保存测试集
        test_dir = os.path.join(output_dir, "test")
        os.makedirs(test_dir, exist_ok=True)
        
        test_data = batch_data_list[train_size+val_size:]
        test_labels = labels_list[train_size+val_size:]
        
        torch.save(test_data, os.path.join(test_dir, "batch_data.pt"))
        torch.save(test_labels, os.path.join(test_dir, "labels.pt"))
        
        # 保存数据集信息
        dataset_info = {
            "total_samples": self.config.total_samples,
            "train_samples": train_size,
            "val_samples": val_size,
            "test_samples": self.config.total_samples - train_size - val_size,
            "task_feature_dim": self.config.task_feature_dim,
            "node_feature_dim": self.config.node_feature_dim,
            "resource_dim": self.config.resource_dim,
            "generated_at": datetime.now().isoformat(),
            "config": self.config.__dict__
        }
        
        with open(os.path.join(output_dir, "dataset_info.json"), "w") as f:
            json.dump(dataset_info, f, indent=2)
        
        print(f"数据集已保存到 {output_dir}")
        print(f"训练集: {train_size} 样本")
        print(f"验证集: {val_size} 样本")
        print(f"测试集: {self.config.total_samples - train_size - val_size} 样本")
    
    def generate_complete_dataset(self, output_dir: str = "three_layer_gnn_dataset"):
        """生成完整的数据集"""
        print("开始生成Three Layer GNN数据集...")
        
        # 生成数据集
        batch_data_list, labels_list = self.generate_dataset()
        
        # 保存数据集
        self.save_dataset(batch_data_list, labels_list, output_dir)
        
        print("Three Layer GNN数据集生成完成！")


def main():
    """主函数"""
    # 配置数据集生成参数
    config = ThreeLayerGNNConfig(
        total_samples=1000,  # 可以根据需要调整
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15,
        min_tasks=10,
        max_tasks=50,
        min_nodes=5,
        max_nodes=20,
        task_feature_dim=128,
        node_feature_dim=32,
        resource_dim=2,
        max_dag_depth=6,
        max_dag_width=10,
        dependency_probability=0.3,
        normalize_features=True,
        add_noise=True,
        noise_level=0.05,
        random_seed=42
    )
    
    # 创建数据集生成器
    generator = ThreeLayerGNNDatasetGenerator(config)
    
    # 生成数据集
    generator.generate_complete_dataset("three_layer_gnn_dataset")


if __name__ == "__main__":
    main() 