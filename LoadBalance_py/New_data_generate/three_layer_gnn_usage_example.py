"""
Three Layer GNN数据集使用示例
展示如何使用专门的数据生成器和模型
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import json
import os
from typing import List, Dict, Any

# 导入数据集生成器
from three_layer_gnn_dataset_generator import ThreeLayerGNNDatasetGenerator, ThreeLayerGNNConfig

# 导入模型（假设模型在上级目录）
import sys
sys.path.append('..')
from three_layer_gnn import ThreeLayerGNNScheduler


class ThreeLayerGNNDataset(Dataset):
    """Three Layer GNN数据集类"""
    
    def __init__(self, batch_data_list: List[Dict[str, torch.Tensor]], 
                 labels_list: List[torch.Tensor]):
        self.batch_data_list = batch_data_list
        self.labels_list = labels_list
    
    def __len__(self):
        return len(self.batch_data_list)
    
    def __getitem__(self, idx):
        return self.batch_data_list[idx], self.labels_list[idx]


def load_dataset(dataset_path: str) -> tuple:
    """加载数据集"""
    print(f"加载数据集: {dataset_path}")
    
    # 加载训练集
    train_dir = os.path.join(dataset_path, "train")
    train_batch_data = torch.load(os.path.join(train_dir, "batch_data.pt"))
    train_labels = torch.load(os.path.join(train_dir, "labels.pt"))
    
    # 加载验证集
    val_dir = os.path.join(dataset_path, "val")
    val_batch_data = torch.load(os.path.join(val_dir, "batch_data.pt"))
    val_labels = torch.load(os.path.join(val_dir, "labels.pt"))
    
    # 加载测试集
    test_dir = os.path.join(dataset_path, "test")
    test_batch_data = torch.load(os.path.join(test_dir, "batch_data.pt"))
    test_labels = torch.load(os.path.join(test_dir, "labels.pt"))
    
    # 加载数据集信息
    with open(os.path.join(dataset_path, "dataset_info.json"), "r") as f:
        dataset_info = json.load(f)
    
    print(f"数据集信息:")
    print(f"  训练集: {len(train_batch_data)} 样本")
    print(f"  验证集: {len(val_batch_data)} 样本")
    print(f"  测试集: {len(test_batch_data)} 样本")
    print(f"  任务特征维度: {dataset_info['task_feature_dim']}")
    print(f"  节点特征维度: {dataset_info['node_feature_dim']}")
    print(f"  资源约束维度: {dataset_info['resource_dim']}")
    
    return (train_batch_data, train_labels), (val_batch_data, val_labels), (test_batch_data, test_labels), dataset_info


def create_data_loaders(train_data, val_data, test_data, batch_size=8):
    """创建数据加载器"""
    # 创建数据集
    train_dataset = ThreeLayerGNNDataset(train_data[0], train_data[1])
    val_dataset = ThreeLayerGNNDataset(val_data[0], val_data[1])
    test_dataset = ThreeLayerGNNDataset(test_data[0], test_data[1])
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, val_loader, test_loader


def train_model(model, train_loader, val_loader, num_epochs=50, learning_rate=0.001):
    """训练模型"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5)
    
    # 训练历史
    train_losses = []
    val_losses = []
    
    print(f"开始训练，设备: {device}")
    print(f"训练轮数: {num_epochs}")
    print(f"学习率: {learning_rate}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, (batch_data, labels) in enumerate(train_loader):
            # 将数据移到设备
            batch_data = {k: v.to(device) for k, v in batch_data.items()}
            labels = labels.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            
            try:
                assignment_probs, debug_info = model(batch_data, debug_mode=False)
                
                # 计算损失（这里需要根据实际输出调整）
                # 假设模型输出的是分配概率，我们需要转换为性能指标
                predicted_quality = torch.mean(assignment_probs, dim=(1, 2))  # 简化的质量指标
                loss = criterion(predicted_quality, labels[:, 4])  # 使用目标函数值作为标签
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                
            except Exception as e:
                print(f"训练批次 {batch_idx} 出错: {e}")
                continue
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for batch_data, labels in val_loader:
                batch_data = {k: v.to(device) for k, v in batch_data.items()}
                labels = labels.to(device)
                
                try:
                    assignment_probs, debug_info = model(batch_data, debug_mode=False)
                    predicted_quality = torch.mean(assignment_probs, dim=(1, 2))
                    loss = criterion(predicted_quality, labels[:, 4])
                    val_loss += loss.item()
                except Exception as e:
                    print(f"验证批次出错: {e}")
                    continue
        
        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        # 打印进度
        if (epoch + 1) % 5 == 0:
            print(f"Epoch [{epoch+1}/{num_epochs}] - "
                  f"Train Loss: {avg_train_loss:.4f}, "
                  f"Val Loss: {avg_val_loss:.4f}, "
                  f"LR: {optimizer.param_groups[0]['lr']:.6f}")
    
    print("训练完成！")
    return train_losses, val_losses


def evaluate_model(model, test_loader):
    """评估模型"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.eval()
    
    test_loss = 0.0
    predictions = []
    ground_truths = []
    
    with torch.no_grad():
        for batch_data, labels in test_loader:
            batch_data = {k: v.to(device) for k, v in batch_data.items()}
            labels = labels.to(device)
            
            try:
                assignment_probs, debug_info = model(batch_data, debug_mode=False)
                predicted_quality = torch.mean(assignment_probs, dim=(1, 2))
                loss = nn.MSELoss()(predicted_quality, labels[:, 4])
                test_loss += loss.item()
                
                predictions.extend(predicted_quality.cpu().numpy())
                ground_truths.extend(labels[:, 4].cpu().numpy())
                
            except Exception as e:
                print(f"测试批次出错: {e}")
                continue
    
    avg_test_loss = test_loss / len(test_loader)
    
    # 计算评估指标
    predictions = np.array(predictions)
    ground_truths = np.array(ground_truths)
    
    mse = np.mean((predictions - ground_truths) ** 2)
    mae = np.mean(np.abs(predictions - ground_truths))
    r2 = 1 - np.sum((ground_truths - predictions) ** 2) / np.sum((ground_truths - np.mean(ground_truths)) ** 2)
    
    print(f"\n测试结果:")
    print(f"  MSE: {mse:.4f}")
    print(f"  MAE: {mae:.4f}")
    print(f"  R²: {r2:.4f}")
    print(f"  平均测试损失: {avg_test_loss:.4f}")
    
    return {
        'mse': mse,
        'mae': mae,
        'r2': r2,
        'test_loss': avg_test_loss,
        'predictions': predictions,
        'ground_truths': ground_truths
    }


def generate_dataset_example():
    """生成数据集示例"""
    print("=" * 50)
    print("生成Three Layer GNN数据集")
    print("=" * 50)
    
    # 配置参数
    config = ThreeLayerGNNConfig(
        total_samples=100,  # 小规模示例
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15,
        min_tasks=5,
        max_tasks=15,
        min_nodes=3,
        max_nodes=8,
        task_feature_dim=128,
        node_feature_dim=32,
        resource_dim=2,
        max_dag_depth=4,
        max_dag_width=8,
        dependency_probability=0.3,
        normalize_features=True,
        add_noise=True,
        noise_level=0.05,
        random_seed=42
    )
    
    # 创建生成器
    generator = ThreeLayerGNNDatasetGenerator(config)
    
    # 生成数据集
    generator.generate_complete_dataset("three_layer_gnn_dataset")
    
    print("数据集生成完成！")


def train_model_example():
    """训练模型示例"""
    print("=" * 50)
    print("训练Three Layer GNN模型")
    print("=" * 50)
    
    # 加载数据集
    train_data, val_data, test_data, dataset_info = load_dataset("three_layer_gnn_dataset")
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_data_loaders(
        train_data, val_data, test_data, batch_size=4
    )
    
    # 创建模型
    model_config = {
        'input_dim': dataset_info['task_feature_dim'],
        'hidden_dim': 256,
        'num_heads': 8,
        'num_transformer_layers': 6,
        'num_gat_layers': 3,
        'dropout': 0.1,
        'constraint_weights': {
            'dependency': 1.0,
            'resource': 1.0,
            'temporal': 0.5,
            'communication': 0.5
        }
    }
    
    try:
        model = ThreeLayerGNNScheduler(model_config)
        print(f"模型创建成功！")
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 训练模型
        train_losses, val_losses = train_model(
            model, train_loader, val_loader, 
            num_epochs=20, learning_rate=0.001
        )
        
        # 评估模型
        results = evaluate_model(model, test_loader)
        
        # 保存模型
        torch.save(model.state_dict(), "three_layer_gnn_model.pth")
        print("模型已保存到 three_layer_gnn_model.pth")
        
        return model, results
        
    except Exception as e:
        print(f"模型创建或训练失败: {e}")
        print("请检查模型依赖和配置")
        return None, None


def test_single_sample():
    """测试单个样本"""
    print("=" * 50)
    print("测试单个样本")
    print("=" * 50)
    
    # 创建生成器
    config = ThreeLayerGNNConfig(
        total_samples=1,
        min_tasks=3,
        max_tasks=5,
        min_nodes=2,
        max_nodes=3,
        task_feature_dim=128,
        node_feature_dim=32,
        resource_dim=2,
        random_seed=42
    )
    
    generator = ThreeLayerGNNDatasetGenerator(config)
    
    # 生成单个样本
    batch_data, labels = generator.generate_single_sample()
    
    print(f"样本信息:")
    print(f"  任务特征形状: {batch_data['task_features'].shape}")
    print(f"  邻接矩阵形状: {batch_data['adjacency_matrix'].shape}")
    print(f"  节点特征形状: {batch_data['node_features'].shape}")
    print(f"  任务边索引形状: {batch_data['task_edge_index'].shape}")
    print(f"  资源约束形状: {batch_data['resource_constraints'].shape}")
    print(f"  标签形状: {labels.shape}")
    
    print(f"\n标签值: {labels.numpy()}")
    
    # 尝试运行模型（如果可用）
    try:
        model_config = {
            'input_dim': 128,
            'hidden_dim': 256,
            'num_heads': 8,
            'num_transformer_layers': 6,
            'num_gat_layers': 3,
            'dropout': 0.1,
            'constraint_weights': {
                'dependency': 1.0,
                'resource': 1.0,
                'temporal': 0.5,
                'communication': 0.5
            }
        }
        
        model = ThreeLayerGNNScheduler(model_config)
        
        # 前向传播
        with torch.no_grad():
            assignment_probs, debug_info = model(batch_data, debug_mode=True)
        
        print(f"\n模型输出:")
        print(f"  分配概率形状: {assignment_probs.shape}")
        print(f"  分配概率和: {assignment_probs.sum(dim=-1)}")
        print(f"  调试信息键: {list(debug_info.keys())}")
        
    except Exception as e:
        print(f"模型运行失败: {e}")


def main():
    """主函数"""
    print("Three Layer GNN数据集和模型使用示例")
    print("=" * 60)
    
    # 1. 生成数据集
    generate_dataset_example()
    
    # 2. 测试单个样本
    test_single_sample()
    
    # 3. 训练模型（可选）
    print("\n是否要训练模型？(y/n): ", end="")
    choice = input().lower()
    
    if choice == 'y':
        model, results = train_model_example()
        
        if model is not None:
            print("\n训练和评估完成！")
            print(f"最终R²分数: {results['r2']:.4f}")
    else:
        print("跳过模型训练")


if __name__ == "__main__":
    main() 