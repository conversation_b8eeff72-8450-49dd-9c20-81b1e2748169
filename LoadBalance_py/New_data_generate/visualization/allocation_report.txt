============================================================
连续时刻任务分配结果报告
============================================================

生成时间: 2025-08-02T23:14:33.584507

算法性能比较:
----------------------------------------

round_robin 算法:
  资源利用率: 162.46%
  响应时间: 370.31 秒
  Makespan: 7406.15 秒
  负载均衡度: 0.4756
  目标函数值: 1481.2012
  执行时间: 0.0000 秒

particle_swarm 算法:
  资源利用率: 133.58%
  响应时间: 154.80 秒
  Makespan: 3095.96 秒
  负载均衡度: 1.5159
  目标函数值: 619.4288
  执行时间: 0.2455 秒

hybrid_coloring_pso 算法:
  资源利用率: 132.90%
  响应时间: 169.23 秒
  Makespan: 3384.60 秒
  负载均衡度: 1.1003
  目标函数值: 677.0737
  执行时间: 0.2622 秒

最佳算法: particle_swarm
最佳目标函数值: 619.4288
