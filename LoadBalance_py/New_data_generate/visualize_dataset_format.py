"""
查看和可视化生成的训练数据格式
展示带颜色特征和不带颜色特征的数据集结构
"""

import torch
import numpy as np
import json
import os
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, Tuple
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def load_and_analyze_dataset(dataset_path: str, dataset_name: str):
    """加载并分析数据集"""
    print(f"\n{'='*60}")
    print(f"分析数据集: {dataset_name}")
    print(f"路径: {dataset_path}")
    print(f"{'='*60}")
    
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return None
    
    # 加载数据集信息
    info_path = os.path.join(dataset_path, "dataset_info.json")
    if os.path.exists(info_path):
        with open(info_path, "r") as f:
            dataset_info = json.load(f)
        print(f"数据集信息:")
        for key, value in dataset_info.items():
            if key != "config":
                print(f"  {key}: {value}")
    
    # 加载训练数据
    train_dir = os.path.join(dataset_path, "train")
    if os.path.exists(train_dir):
        batch_data_path = os.path.join(train_dir, "batch_data.pt")
        labels_path = os.path.join(train_dir, "labels.pt")
        
        if os.path.exists(batch_data_path) and os.path.exists(labels_path):
            batch_data_list = torch.load(batch_data_path)
            labels_list = torch.load(labels_path)
            
            print(f"\n数据加载成功!")
            print(f"训练样本数量: {len(batch_data_list)}")
            print(f"标签数量: {len(labels_list)}")
            
            return analyze_batch_data(batch_data_list, labels_list, dataset_name)
        else:
            print(f"❌ 数据文件不存在")
            return None
    else:
        print(f"❌ 训练目录不存在")
        return None


def analyze_batch_data(batch_data_list: list, labels_list: list, dataset_name: str):
    """分析批次数据格式"""
    print(f"\n📊 数据格式分析:")
    
    if len(batch_data_list) == 0:
        print("❌ 数据列表为空")
        return None
    
    # 分析第一个样本
    first_batch = batch_data_list[0]
    first_labels = labels_list[0]
    
    print(f"\n第一个样本的结构:")
    print(f"  数据类型: {type(first_batch)}")
    print(f"  键值: {list(first_batch.keys())}")
    
    # 详细分析每个组件
    analysis_results = {}
    
    for key, value in first_batch.items():
        if isinstance(value, torch.Tensor):
            print(f"\n  {key}:")
            print(f"    形状: {value.shape}")
            print(f"    数据类型: {value.dtype}")
            print(f"    数值范围: [{value.min().item():.4f}, {value.max().item():.4f}]")
            print(f"    均值: {value.mean().item():.4f}")
            print(f"    标准差: {value.std().item():.4f}")
            
            analysis_results[key] = {
                'shape': value.shape,
                'dtype': value.dtype,
                'min': value.min().item(),
                'max': value.max().item(),
                'mean': value.mean().item(),
                'std': value.std().item(),
                'tensor': value
            }
    
    # 分析标签
    print(f"\n  标签:")
    print(f"    形状: {first_labels.shape}")
    print(f"    数据类型: {first_labels.dtype}")
    print(f"    数值范围: [{first_labels.min().item():.4f}, {first_labels.max().item():.4f}]")
    print(f"    标签值: {first_labels.numpy()}")
    
    # 分析多个样本的统计信息
    print(f"\n📈 多样本统计信息:")
    
    # 收集所有样本的统计信息
    all_task_features = []
    all_node_features = []
    all_labels = []
    
    for i, (batch_data, labels) in enumerate(zip(batch_data_list, labels_list)):
        all_task_features.append(batch_data['task_features'])
        all_node_features.append(batch_data['node_features'])
        all_labels.append(labels)
        
        if i >= 9:  # 只分析前10个样本
            break
    
    # 任务特征统计
    task_features_concat = torch.cat(all_task_features, dim=0)
    print(f"  任务特征 (前10个样本):")
    print(f"    总形状: {task_features_concat.shape}")
    print(f"    数值范围: [{task_features_concat.min().item():.4f}, {task_features_concat.max().item():.4f}]")
    print(f"    均值: {task_features_concat.mean().item():.4f}")
    print(f"    标准差: {task_features_concat.std().item():.4f}")
    
    # 节点特征统计
    node_features_concat = torch.cat(all_node_features, dim=0)
    print(f"  节点特征 (前10个样本):")
    print(f"    总形状: {node_features_concat.shape}")
    print(f"    数值范围: [{node_features_concat.min().item():.4f}, {node_features_concat.max().item():.4f}]")
    print(f"    均值: {node_features_concat.mean().item():.4f}")
    print(f"    标准差: {node_features_concat.std().item():.4f}")
    
    # 标签统计
    labels_concat = torch.stack(all_labels)
    print(f"  标签 (前10个样本):")
    print(f"    总形状: {labels_concat.shape}")
    print(f"    数值范围: [{labels_concat.min().item():.4f}, {labels_concat.max().item():.4f}]")
    print(f"    均值: {labels_concat.mean().item():.4f}")
    print(f"    标准差: {labels_concat.std().item():.4f}")
    
    return {
        'first_sample': first_batch,
        'first_labels': first_labels,
        'analysis_results': analysis_results,
        'task_features_concat': task_features_concat,
        'node_features_concat': node_features_concat,
        'labels_concat': labels_concat
    }


def visualize_data_format(with_coloring_data, without_coloring_data):
    """可视化数据格式对比"""
    print(f"\n{'='*60}")
    print("可视化数据格式对比")
    print(f"{'='*60}")
    
    if with_coloring_data is None or without_coloring_data is None:
        print("❌ 数据加载失败，无法进行可视化")
        return
    
    # 创建可视化图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('训练数据格式可视化对比', fontsize=16, fontweight='bold')
    
    # 1. 任务特征分布对比
    ax1 = axes[0, 0]
    task_features_with = with_coloring_data['task_features_concat']
    task_features_without = without_coloring_data['task_features_concat']
    
    # 分析着色特征区域（80:88）
    coloring_features_with = task_features_with[:, 80:88].flatten()
    coloring_features_without = task_features_without[:, 80:88].flatten()
    
    ax1.hist(coloring_features_with.numpy(), bins=30, alpha=0.7, label='带颜色特征', color='blue')
    ax1.hist(coloring_features_without.numpy(), bins=30, alpha=0.7, label='不带颜色特征', color='red')
    ax1.set_title('着色特征分布对比 (位置80:88)')
    ax1.set_xlabel('特征值')
    ax1.set_ylabel('频次')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 任务特征整体分布
    ax2 = axes[0, 1]
    ax2.hist(task_features_with.flatten().numpy(), bins=50, alpha=0.7, label='带颜色特征', color='blue')
    ax2.hist(task_features_without.flatten().numpy(), bins=50, alpha=0.7, label='不带颜色特征', color='red')
    ax2.set_title('任务特征整体分布')
    ax2.set_xlabel('特征值')
    ax2.set_ylabel('频次')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 节点特征分布
    ax3 = axes[0, 2]
    node_features_with = with_coloring_data['node_features_concat']
    node_features_without = without_coloring_data['node_features_concat']
    
    ax3.hist(node_features_with.flatten().numpy(), bins=50, alpha=0.7, label='带颜色特征', color='blue')
    ax3.hist(node_features_without.flatten().numpy(), bins=50, alpha=0.7, label='不带颜色特征', color='red')
    ax3.set_title('节点特征分布')
    ax3.set_xlabel('特征值')
    ax3.set_ylabel('频次')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 标签分布对比
    ax4 = axes[1, 0]
    labels_with = with_coloring_data['labels_concat']
    labels_without = without_coloring_data['labels_concat']
    
    # 绘制每个标签维度的分布
    label_names = ['资源利用率', '负载均衡度', 'Makespan', '依赖满足度', '目标函数值', '占位符']
    for i in range(6):
        ax4.hist(labels_with[:, i].numpy(), bins=20, alpha=0.6, label=f'{label_names[i]} (带颜色)', color=f'C{i}')
        ax4.hist(labels_without[:, i].numpy(), bins=20, alpha=0.6, label=f'{label_names[i]} (不带颜色)', color=f'C{i}', linestyle='--')
    
    ax4.set_title('标签分布对比')
    ax4.set_xlabel('标签值')
    ax4.set_ylabel('频次')
    ax4.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax4.grid(True, alpha=0.3)
    
    # 5. 特征维度对比
    ax5 = axes[1, 1]
    feature_dims = list(range(128))
    task_features_mean_with = task_features_with.mean(dim=0)
    task_features_mean_without = task_features_without.mean(dim=0)
    
    ax5.plot(feature_dims, task_features_mean_with.numpy(), 'b-', label='带颜色特征', linewidth=2)
    ax5.plot(feature_dims, task_features_mean_without.numpy(), 'r-', label='不带颜色特征', linewidth=2)
    ax5.axvspan(80, 88, alpha=0.3, color='yellow', label='着色特征区域')
    ax5.set_title('任务特征维度均值对比')
    ax5.set_xlabel('特征维度')
    ax5.set_ylabel('均值')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 数据形状信息
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    # 创建数据形状信息表格
    shape_info = [
        ['组件', '带颜色特征', '不带颜色特征'],
        ['任务特征', f"{task_features_with.shape}", f"{task_features_without.shape}"],
        ['节点特征', f"{node_features_with.shape}", f"{node_features_without.shape}"],
        ['标签', f"{labels_with.shape}", f"{labels_without.shape}"],
        ['着色特征', f"{coloring_features_with.shape}", f"{coloring_features_without.shape}"],
        ['样本数量', f"{len(with_coloring_data['task_features_concat'])}", f"{len(without_coloring_data['task_features_concat'])}"]
    ]
    
    table = ax6.table(cellText=shape_info[1:], colLabels=shape_info[0], 
                      cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    
    ax6.set_title('数据形状信息', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('dataset_format_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("可视化图表已保存为 'dataset_format_visualization.png'")


def show_detailed_sample_info(with_coloring_data, without_coloring_data):
    """显示详细的样本信息"""
    print(f"\n{'='*60}")
    print("详细样本信息")
    print(f"{'='*60}")
    
    if with_coloring_data is None or without_coloring_data is None:
        print("❌ 数据加载失败")
        return
    
    # 第一个样本的详细信息
    first_with = with_coloring_data['first_sample']
    first_without = without_coloring_data['first_sample']
    
    print(f"\n📋 第一个样本详细信息:")
    print(f"  任务数量: {first_with['task_features'].shape[1]}")
    print(f"  节点数量: {first_with['node_features'].shape[0]}")
    print(f"  任务特征维度: {first_with['task_features'].shape[2]}")
    print(f"  节点特征维度: {first_with['node_features'].shape[1]}")
    print(f"  资源约束维度: {first_with['resource_constraints'].shape[2]}")
    
    # 邻接矩阵信息
    adjacency = first_with['adjacency_matrix']
    num_edges = (adjacency > 0).sum().item()
    print(f"  依赖关系数量: {num_edges}")
    
    # 着色特征对比
    task_features_with = first_with['task_features'][0]  # [num_tasks, feature_dim]
    task_features_without = first_without['task_features'][0]
    
    coloring_features_with = task_features_with[:, 80:88]
    coloring_features_without = task_features_without[:, 80:88]
    
    print(f"\n🎨 着色特征对比:")
    print(f"  带颜色特征 - 着色区域均值: {coloring_features_with.mean():.4f}")
    print(f"  不带颜色特征 - 着色区域均值: {coloring_features_without.mean():.4f}")
    print(f"  着色特征差异: {torch.abs(coloring_features_with - coloring_features_without).mean():.4f}")
    
    # 标签对比
    labels_with = with_coloring_data['first_labels']
    labels_without = without_coloring_data['first_labels']
    
    label_names = ['资源利用率', '负载均衡度', 'Makespan', '依赖满足度', '目标函数值', '占位符']
    print(f"\n🏷️ 标签对比:")
    for i, name in enumerate(label_names):
        print(f"  {name}: {labels_with[i].item():.4f} vs {labels_without[i].item():.4f}")
    
    # 资源约束信息
    resource_constraints = first_with['resource_constraints']
    print(f"\n💻 资源约束信息:")
    print(f"  CPU约束范围: [{resource_constraints[0, :, 0].min():.2f}, {resource_constraints[0, :, 0].max():.2f}]")
    print(f"  内存约束范围: [{resource_constraints[0, :, 1].min():.2f}, {resource_constraints[0, :, 1].max():.2f}]")


def create_data_format_summary():
    """创建数据格式总结"""
    print(f"\n{'='*60}")
    print("数据格式总结")
    print(f"{'='*60}")
    
    summary = """
📊 训练数据格式总结:

1. 数据结构:
   - batch_data_list: List[Dict[str, torch.Tensor]] - 批次数据列表
   - labels_list: List[torch.Tensor] - 标签列表

2. 每个样本包含的组件:
   - task_features: [batch_size, num_tasks, 128] - 任务特征
   - adjacency_matrix: [batch_size, num_tasks, num_tasks] - 邻接矩阵
   - node_features: [num_nodes, 32] - 节点特征
   - task_edge_index: [2, num_edges] - 任务边索引
   - task_batch: [num_tasks] - 任务批次索引
   - node_batch: [num_nodes] - 节点批次索引
   - resource_constraints: [batch_size, num_nodes, 2] - 资源约束

3. 任务特征维度 (128维):
   - 0-31: 基础任务特征 (32维)
   - 32-55: 资源需求特征 (24维)
   - 56-79: DAG结构特征 (24维)
   - 80-87: 着色特征 (8维) - 仅带颜色特征数据集
   - 88-111: 工作流上下文特征 (24维)
   - 112-127: 统计特征 (16维)

4. 节点特征维度 (32维):
   - 0-3: 基础容量特征 (4维)
   - 4-7: 可用资源特征 (4维)
   - 8-9: 效率特征 (2维)
   - 10-13: 资源类型特征 (4维)
   - 14-31: 其他特征 (18维)

5. 标签维度 (6维):
   - 0: 资源利用率
   - 1: 负载均衡度
   - 2: Makespan
   - 3: 依赖满足度
   - 4: 目标函数值
   - 5: 占位符

6. 数据集差异:
   - 带颜色特征: 着色特征区域(80:88)包含真实的图着色信息
   - 不带颜色特征: 着色特征区域(80:88)填充随机值
   - 其他特征完全相同
    """
    
    print(summary)


def main():
    """主函数"""
    print("训练数据格式查看器")
    print("分析带颜色特征和不带颜色特征的数据集")
    print("=" * 60)
    
    # 数据集路径
    with_coloring_path = "three_layer_gnn_with_coloring"
    without_coloring_path = "three_layer_gnn_without_coloring"
    
    # 加载并分析数据集
    print("\n1. 加载带颜色特征的数据集...")
    with_coloring_data = load_and_analyze_dataset(with_coloring_path, "带颜色特征数据集")
    
    print("\n2. 加载不带颜色特征的数据集...")
    without_coloring_data = load_and_analyze_dataset(without_coloring_path, "不带颜色特征数据集")
    
    # 显示详细样本信息
    print("\n3. 显示详细样本信息...")
    show_detailed_sample_info(with_coloring_data, without_coloring_data)
    
    # 可视化数据格式对比
    print("\n4. 可视化数据格式对比...")
    visualize_data_format(with_coloring_data, without_coloring_data)
    
    # 创建数据格式总结
    print("\n5. 创建数据格式总结...")
    create_data_format_summary()
    
    print(f"\n✅ 数据格式分析完成!")
    print("可以通过可视化图表和详细信息了解训练数据的结构。")


if __name__ == "__main__":
    main() 