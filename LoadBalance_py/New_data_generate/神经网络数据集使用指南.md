# 神经网络数据集使用指南

## 📋 概述

本指南详细介绍了如何为负载均衡神经网络方法准备和使用数据集。我们提供了两个数据集生成器：

1. **基础神经网络数据集生成器** (`neural_network_dataset_generator.py`)
2. **高级神经网络数据集生成器** (`advanced_neural_dataset_generator.py`)

## 🎯 数据集特点

### 输入特征 (Input Features)

#### 任务特征
- **CPU需求**: 任务所需的CPU核心数
- **内存需求**: 任务所需的内存大小 (MB)
- **I/O需求**: 任务所需的I/O带宽 (MB/s)
- **网络需求**: 任务所需的网络带宽 (MB/s)
- **基础执行时间**: 在标准节点上的执行时间 (秒)
- **优先级**: 任务的优先级等级 (1-5)
- **截止时间**: 任务的截止时间
- **到达时间**: 任务的到达时间
- **依赖关系**: 任务间的依赖关系
- **资源强度**: 各资源类型的相对需求比例

#### 节点特征
- **CPU容量**: 节点的CPU核心数
- **内存容量**: 节点的内存大小 (MB)
- **I/O带宽**: 节点的I/O带宽 (MB/s)
- **网络带宽**: 节点的网络带宽 (MB/s)
- **可靠性**: 节点的可靠性评分 (0.95-0.99)
- **当前负载**: 节点的当前负载水平 (0.1-0.8)
- **温度**: 节点的运行温度
- **功耗**: 节点的功耗水平
- **可用资源**: 各资源类型的可用量
- **资源效率**: 各资源类型的利用效率

#### 系统状态特征
- **任务数量**: 系统中的总任务数
- **节点数量**: 系统中的总节点数
- **任务节点比例**: 任务数与节点数的比值
- **总资源需求**: 所有任务的资源需求总和
- **总资源容量**: 所有节点的资源容量总和
- **资源利用率**: 各资源类型的利用率
- **平均任务优先级**: 所有任务的平均优先级
- **平均节点可靠性**: 所有节点的平均可靠性
- **平均节点温度**: 所有节点的平均温度

### 输出标签 (Output Labels)

#### 性能指标
- **资源利用率**: 系统整体资源利用效率 (0-1)
- **负载均衡度**: 节点间负载分布的均衡程度 (0-1)
- **Makespan**: 系统完成所有任务的时间
- **可靠性得分**: 系统整体的可靠性评分 (0-1)
- **能耗效率**: 系统的能耗效率评分 (0-1)
- **目标函数值**: 综合性能指标

## 🚀 使用方法

### 1. 基础数据集生成

```python
# 导入基础数据集生成器
from neural_network_dataset_generator import NeuralNetworkDatasetGenerator, NeuralNetworkDatasetConfig

# 配置参数
config = NeuralNetworkDatasetConfig(
    total_samples=10000,      # 总样本数
    train_ratio=0.7,          # 训练集比例
    val_ratio=0.15,           # 验证集比例
    test_ratio=0.15,          # 测试集比例
    min_tasks=10,             # 最小任务数
    max_tasks=100,            # 最大任务数
    min_nodes=5,              # 最小节点数
    max_nodes=30,             # 最大节点数
    normalize_features=True,   # 是否标准化特征
    random_seed=42            # 随机种子
)

# 创建生成器
generator = NeuralNetworkDatasetGenerator(config)

# 生成数据集
generator.generate_complete_dataset("neural_network_dataset")
```

### 2. 高级数据集生成

```python
# 导入高级数据集生成器
from advanced_neural_dataset_generator import AdvancedNeuralDatasetGenerator, AdvancedDatasetConfig

# 配置参数
config = AdvancedDatasetConfig(
    total_samples=50000,           # 总样本数
    train_ratio=0.7,              # 训练集比例
    val_ratio=0.15,               # 验证集比例
    test_ratio=0.15,              # 测试集比例
    min_tasks=10,                 # 最小任务数
    max_tasks=200,                # 最大任务数
    min_nodes=5,                  # 最小节点数
    max_nodes=50,                 # 最大节点数
    support_cnn=True,             # 支持CNN
    support_rnn=True,             # 支持RNN
    support_transformer=True,     # 支持Transformer
    support_gnn=True,             # 支持GNN
    support_mlp=True,             # 支持MLP
    normalize_features=True,       # 标准化特征
    add_noise=True,               # 添加噪声
    noise_level=0.05,             # 噪声水平
    feature_augmentation=True,    # 特征增强
    max_graph_size=100,           # 最大图大小
    edge_probability=0.3,         # 边概率
    max_sequence_length=50,       # 最大序列长度
    sequence_padding=True,        # 序列填充
    random_seed=42                # 随机种子
)

# 创建生成器
generator = AdvancedNeuralDatasetGenerator(config)

# 生成数据集
generator.generate_complete_dataset("advanced_neural_dataset")
```

## 📊 数据集格式

### 基础数据集格式

```
neural_network_dataset/
├── X_train.npy              # 训练集特征
├── X_val.npy                # 验证集特征
├── X_test.npy               # 测试集特征
├── y_train.npy              # 训练集标签
├── y_val.npy                # 验证集标签
├── y_test.npy               # 测试集标签
├── scaler.pkl               # 特征标准化器
└── dataset_info.json        # 数据集信息
```

### 高级数据集格式

```
advanced_neural_dataset/
├── train/                   # 训练集
│   ├── mlp_features.npy     # MLP特征
│   ├── cnn_features.npy     # CNN特征
│   ├── rnn_features.npy     # RNN特征
│   ├── transformer_features_task_sequence.npy    # Transformer任务序列
│   ├── transformer_features_node_sequence.npy    # Transformer节点序列
│   ├── transformer_features_task_attention_mask.npy  # 任务注意力掩码
│   ├── transformer_features_node_attention_mask.npy  # 节点注意力掩码
│   ├── transformer_features_system_features.npy  # 系统特征
│   ├── gnn_features.pt      # GNN特征
│   └── labels.npy           # 标签
├── val/                     # 验证集 (格式同训练集)
├── test/                    # 测试集 (格式同训练集)
└── dataset_info.json        # 数据集信息
```

## 🧠 神经网络架构支持

### 1. MLP (多层感知机)

适用于：
- 特征向量输入
- 回归任务
- 性能预测

```python
# 加载MLP数据
X_train = np.load("advanced_neural_dataset/train/mlp_features.npy")
y_train = np.load("advanced_neural_dataset/train/labels.npy")

# 数据形状
print(f"训练集特征形状: {X_train.shape}")  # (7000, feature_dim)
print(f"训练集标签形状: {y_train.shape}")  # (7000, 6)
```

### 2. CNN (卷积神经网络)

适用于：
- 矩阵形式输入
- 特征提取
- 模式识别

```python
# 加载CNN数据
X_train = np.load("advanced_neural_dataset/train/cnn_features.npy")

# 数据形状
print(f"CNN特征形状: {X_train.shape}")  # (7000, 14, 50) - (channels, height, width)
```

### 3. RNN (循环神经网络)

适用于：
- 序列数据
- 时序依赖
- 动态调度

```python
# 加载RNN数据
X_train = np.load("advanced_neural_dataset/train/rnn_features.npy")

# 数据形状
print(f"RNN特征形状: {X_train.shape}")  # (7000, 50, 15) - (batch, sequence_length, features)
```

### 4. Transformer

适用于：
- 长序列依赖
- 注意力机制
- 复杂关系建模

```python
# 加载Transformer数据
task_sequence = np.load("advanced_neural_dataset/train/transformer_features_task_sequence.npy")
node_sequence = np.load("advanced_neural_dataset/train/transformer_features_node_sequence.npy")
task_attention_mask = np.load("advanced_neural_dataset/train/transformer_features_task_attention_mask.npy")
node_attention_mask = np.load("advanced_neural_dataset/train/transformer_features_node_attention_mask.npy")
system_features = np.load("advanced_neural_dataset/train/transformer_features_system_features.npy")

# 数据形状
print(f"任务序列形状: {task_sequence.shape}")  # (7000, 50, 14)
print(f"节点序列形状: {node_sequence.shape}")  # (7000, 50, 18)
print(f"任务注意力掩码形状: {task_attention_mask.shape}")  # (7000, 50)
print(f"节点注意力掩码形状: {node_attention_mask.shape}")  # (7000, 50)
print(f"系统特征形状: {system_features.shape}")  # (7000, 19)
```

### 5. GNN (图神经网络)

适用于：
- 图结构数据
- 节点关系建模
- 任务-节点匹配

```python
# 加载GNN数据
import torch
from torch_geometric.data import DataLoader

gnn_data = torch.load("advanced_neural_dataset/train/gnn_features.pt")

# 数据形状
print(f"GNN数据数量: {len(gnn_data)}")  # 7000个图
print(f"第一个图的节点数: {gnn_data[0].x.shape[0]}")
print(f"第一个图的边数: {gnn_data[0].edge_index.shape[1]}")
```

## 🔧 数据预处理

### 1. 特征标准化

```python
from sklearn.preprocessing import StandardScaler

# 加载标准化器
with open("neural_network_dataset/scaler.pkl", "rb") as f:
    scaler = pickle.load(f)

# 标准化新数据
X_new_scaled = scaler.transform(X_new)
```

### 2. 数据增强

```python
# 添加噪声
def add_noise(X, noise_level=0.05):
    noise = np.random.normal(0, noise_level, X.shape)
    return X + noise

# 特征缩放
def scale_features(X, scale_factor=1.2):
    return X * scale_factor
```

### 3. 数据验证

```python
# 检查数据质量
def validate_data(X, y):
    print(f"特征形状: {X.shape}")
    print(f"标签形状: {y.shape}")
    print(f"特征范围: [{X.min():.3f}, {X.max():.3f}]")
    print(f"标签范围: [{y.min():.3f}, {y.max():.3f}]")
    print(f"是否有NaN: {np.isnan(X).any() or np.isnan(y).any()}")
    print(f"是否有无穷值: {np.isinf(X).any() or np.isinf(y).any()}")
```

## 📈 模型训练示例

### MLP模型

```python
import torch
import torch.nn as nn
import torch.optim as optim

class LoadBalancingMLP(nn.Module):
    def __init__(self, input_dim, hidden_dims, output_dim):
        super(LoadBalancingMLP, self).__init__()
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, output_dim))
        self.network = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.network(x)

# 训练模型
def train_mlp_model(X_train, y_train, X_val, y_val):
    model = LoadBalancingMLP(
        input_dim=X_train.shape[1],
        hidden_dims=[512, 256, 128],
        output_dim=y_train.shape[1]
    )
    
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    # 训练循环
    for epoch in range(100):
        optimizer.zero_grad()
        outputs = model(torch.FloatTensor(X_train))
        loss = criterion(outputs, torch.FloatTensor(y_train))
        loss.backward()
        optimizer.step()
        
        if epoch % 10 == 0:
            val_outputs = model(torch.FloatTensor(X_val))
            val_loss = criterion(val_outputs, torch.FloatTensor(y_val))
            print(f"Epoch {epoch}: Train Loss = {loss.item():.4f}, Val Loss = {val_loss.item():.4f}")
    
    return model
```

### CNN模型

```python
class LoadBalancingCNN(nn.Module):
    def __init__(self, input_channels, output_dim):
        super(LoadBalancingCNN, self).__init__()
        self.conv1 = nn.Conv1d(input_channels, 64, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(64, 128, kernel_size=3, padding=1)
        self.conv3 = nn.Conv1d(128, 256, kernel_size=3, padding=1)
        
        self.pool = nn.AdaptiveAvgPool1d(1)
        self.dropout = nn.Dropout(0.3)
        self.fc = nn.Linear(256, output_dim)
    
    def forward(self, x):
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = torch.relu(self.conv3(x))
        x = self.pool(x)
        x = x.view(x.size(0), -1)
        x = self.dropout(x)
        x = self.fc(x)
        return x
```

## 📊 性能评估

### 评估指标

```python
def evaluate_model(model, X_test, y_test):
    model.eval()
    with torch.no_grad():
        predictions = model(torch.FloatTensor(X_test))
        predictions = predictions.numpy()
    
    # 计算各种指标
    mse = np.mean((predictions - y_test) ** 2)
    mae = np.mean(np.abs(predictions - y_test))
    r2 = 1 - np.sum((y_test - predictions) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
    
    print(f"MSE: {mse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"R²: {r2:.4f}")
    
    return predictions
```

## 🎯 最佳实践

### 1. 数据质量保证
- 检查数据完整性
- 验证特征范围合理性
- 确保标签质量

### 2. 模型选择
- **MLP**: 适用于简单特征向量
- **CNN**: 适用于矩阵形式特征
- **RNN**: 适用于序列数据
- **Transformer**: 适用于复杂依赖关系
- **GNN**: 适用于图结构数据

### 3. 超参数调优
- 使用网格搜索或贝叶斯优化
- 交叉验证评估
- 早停防止过拟合

### 4. 模型集成
- 结合多种架构
- 使用投票或平均
- 考虑不同特征表示

## 🔄 持续改进

### 1. 数据更新
- 定期生成新数据
- 更新数据分布
- 添加新的特征

### 2. 模型优化
- 尝试新的架构
- 优化损失函数
- 改进训练策略

### 3. 部署监控
- 实时性能监控
- 模型漂移检测
- 自动重训练

## 📝 总结

通过使用这些数据集生成器，你可以：

1. **快速生成大量训练数据**
2. **支持多种神经网络架构**
3. **保证数据质量和一致性**
4. **灵活配置数据参数**
5. **便于模型训练和评估**

选择合适的生成器和配置参数，根据你的具体需求调整数据规模和特征，就能为神经网络方法准备高质量的数据集。 