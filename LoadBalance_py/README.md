# 负载均衡仿真系统 - Python版本

## 项目简介

这是负载均衡仿真系统的Python版本实现，主要提供数据生成功能。该版本与C++版本保持数据结构的一致性，便于后续的算法实现和性能比较。

## 功能特性

- **数据生成**: 生成节点和任务数据集
- **参数配置**: 灵活的参数配置系统
- **依赖关系**: 支持任务依赖关系生成
- **类型分类**: 自动进行任务和节点类型分类
- **文件输出**: 生成标准格式的数据文件

## 文件结构

```
LoadBalance_py/
├── data_structures.py      # 数据结构定义
├── main_data_generator.py  # 主数据生成程序
├── requirements.txt        # Python依赖
├── README.md              # 说明文档
└── examples/              # 示例文件
    ├── sample_nodes.txt   # 示例节点数据
    └── sample_tasks.txt   # 示例任务数据
```

## 安装和运行

### 1. 环境要求

- Python 3.7+
- 推荐使用虚拟环境

### 2. 安装依赖

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 运行程序

```bash
# 运行数据生成程序
python main_data_generator.py
```

## 数据结构

### 节点结构 (Node)

```python
@dataclass
class Node:
    id: int                 # 节点ID
    time_step: int          # 时间步
    resource: NodeResource  # 节点资源
    node_type: NodeType     # 节点类型
```

### 任务结构 (Task)

```python
@dataclass
class Task:
    id: int                              # 任务ID
    time_step: int                       # 时间步
    resource_demand: TaskResourceDemand  # 资源需求
    dependencies: Set[int]               # 依赖关系
    task_type: TaskType                  # 任务类型
```

## 参数配置

### 节点生成参数

```python
@dataclass
class NodeGenerationParams:
    num_nodes: int = 20                     # 节点数量
    min_cpu_capacity: int = 10000           # 最小CPU容量
    max_cpu_capacity: int = 30000           # 最大CPU容量
    min_memory_capacity: int = 10240        # 最小内存容量
    max_memory_capacity: int = 30720        # 最大内存容量
    min_bandwidth_capacity: int = 10240     # 最小带宽容量
    max_bandwidth_capacity: int = 30720     # 最大带宽容量
    current_resource_ratio_min: float = 0.1 # 当前资源最小比例
    current_resource_ratio_max: float = 0.2 # 当前资源最大比例
```

### 任务生成参数

```python
@dataclass
class TaskGenerationParams:
    time_steps: int = 3                    # 时间步数
    tasks_per_time_step: int = 50          # 每步任务数
    min_cpu_demand: int = 100              # 最小CPU需求
    max_cpu_demand: int = 1000             # 最大CPU需求
    min_memory_demand: int = 100           # 最小内存需求
    max_memory_demand: int = 1000          # 最大内存需求
    min_bandwidth_demand: int = 100        # 最小带宽需求
    max_bandwidth_demand: int = 1000       # 最大带宽需求
    dependency_probability: float = 0.3     # 依赖概率
    max_dependencies: int = 3              # 最大依赖数
    max_dependency_layers: int = 5         # 最大依赖层数
```

## 输出文件格式

### 节点文件格式

```
节点ID	时间步	CPU容量	内存容量	带宽容量	当前CPU	当前内存	当前带宽	节点类型
1	0	15000	20480	15360	2250	3072	2304	1
2	0	25000	25600	20480	3750	3840	3072	2
...
```

### 任务文件格式

```
任务ID	时间步	CPU需求	内存需求	带宽需求	依赖关系;任务类型
1	1	450	320	280	;1
2	1	380	420	200	1;2
3	1	520	180	300	1 2;1
...
```

## 类型分类

### 节点类型

- **CPU密集型** (CPU_INTENSIVE = 1): CPU资源占比较高
- **内存密集型** (MEMORY_INTENSIVE = 2): 内存资源占比较高
- **通信密集型** (NETWORK_INTENSIVE = 3): 网络资源占比较高
- **通用型** (GENERAL = 4): 资源分布相对均衡

### 任务类型

- **CPU密集型任务** (TASK_CPU_INTENSIVE = 1): CPU需求占比较高
- **内存密集型任务** (TASK_MEMORY_INTENSIVE = 2): 内存需求占比较高
- **通信密集型任务** (TASK_NETWORK_INTENSIVE = 3): 带宽需求占比较高
- **通用任务** (TASK_GENERAL = 4): 资源需求分布相对均衡

## 使用示例

### 基本使用

```python
from data_structures import DataGenerator, TaskGenerationParams, NodeGenerationParams

# 设置参数
node_params = NodeGenerationParams(num_nodes=20)
task_params = TaskGenerationParams(time_steps=3, tasks_per_time_step=50)

# 生成数据
DataGenerator.generate_node_dataset("nodes.txt", node_params)
DataGenerator.generate_task_dataset("tasks.txt", task_params)
```

### 自定义参数

```python
# 自定义节点参数
node_params = NodeGenerationParams(
    num_nodes=100,
    min_cpu_capacity=5000,
    max_cpu_capacity=20000,
    current_resource_ratio_min=0.05,
    current_resource_ratio_max=0.15
)

# 自定义任务参数
task_params = TaskGenerationParams(
    time_steps=5,
    tasks_per_time_step=100,
    dependency_probability=0.5,
    max_dependencies=5
)
```

## 与C++版本的关系

Python版本与C++版本保持以下一致性：

1. **数据结构**: 完全对应的数据结构定义
2. **参数配置**: 相同的参数配置选项
3. **文件格式**: 相同的输出文件格式
4. **类型分类**: 相同的分类逻辑和标准

这样可以确保两个版本生成的数据可以互相兼容，便于后续的算法实现和性能比较。

## 扩展计划

- [ ] 添加数据可视化功能
- [ ] 实现数据验证和统计功能
- [ ] 添加配置文件支持
- [ ] 实现批量数据生成
- [ ] 添加数据质量评估

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。 