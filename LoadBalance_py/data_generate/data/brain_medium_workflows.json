{"workflow_type": "brain", "tasks": {"task_0": {"task_type": "statistics", "cpu_requirement": 1.32, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 234.99, "dependencies": []}, "task_1": {"task_type": "preprocessing", "cpu_requirement": 3.25, "memory_requirement": 4936.17, "io_requirement": 30.32, "network_requirement": 11.35, "base_runtime": 366.67, "dependencies": ["task_0"]}, "task_2": {"task_type": "normalization", "cpu_requirement": 3.11, "memory_requirement": 781.24, "io_requirement": 10.48, "network_requirement": 5, "base_runtime": 406.9, "dependencies": ["task_0"]}, "task_3": {"task_type": "statistics", "cpu_requirement": 1.18, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 206.08, "dependencies": ["task_2", "task_0"]}, "task_4": {"task_type": "normalization", "cpu_requirement": 1.59, "memory_requirement": 531.29, "io_requirement": 10, "network_requirement": 5, "base_runtime": 194.47, "dependencies": ["task_2", "task_0", "task_1", "task_3"]}, "task_5": {"task_type": "normalization", "cpu_requirement": 2.13, "memory_requirement": 1341.93, "io_requirement": 13.06, "network_requirement": 5, "base_runtime": 258.03, "dependencies": ["task_4", "task_0"]}, "task_6": {"task_type": "visualization", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10.67, "network_requirement": 5, "base_runtime": 112.84, "dependencies": ["task_3"]}, "task_7": {"task_type": "registration", "cpu_requirement": 6.57, "memory_requirement": 2035.54, "io_requirement": 30.33, "network_requirement": 10.77, "base_runtime": 572.91, "dependencies": ["task_5", "task_6", "task_1", "task_3"]}, "task_8": {"task_type": "statistics", "cpu_requirement": 1.29, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 246.14, "dependencies": ["task_2"]}, "task_9": {"task_type": "segmentation", "cpu_requirement": 4.88, "memory_requirement": 2556.02, "io_requirement": 15.51, "network_requirement": 5, "base_runtime": 378.71, "dependencies": ["task_4"]}, "task_10": {"task_type": "statistics", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 156.68, "dependencies": ["task_5", "task_2", "task_9"]}, "task_11": {"task_type": "visualization", "cpu_requirement": 1.0, "memory_requirement": 892.22, "io_requirement": 17.64, "network_requirement": 5, "base_runtime": 176.83, "dependencies": ["task_5", "task_7", "task_0"]}, "task_12": {"task_type": "normalization", "cpu_requirement": 2.96, "memory_requirement": 1045.83, "io_requirement": 12.57, "network_requirement": 5, "base_runtime": 373.98, "dependencies": ["task_7", "task_9"]}, "task_13": {"task_type": "normalization", "cpu_requirement": 3.51, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 398.62, "dependencies": ["task_11", "task_5"]}, "task_14": {"task_type": "registration", "cpu_requirement": 9.21, "memory_requirement": 2324.83, "io_requirement": 37.8, "network_requirement": 12.13, "base_runtime": 710.21, "dependencies": ["task_8", "task_12", "task_1"]}, "task_15": {"task_type": "normalization", "cpu_requirement": 2.71, "memory_requirement": 738.78, "io_requirement": 10, "network_requirement": 5, "base_runtime": 311.96, "dependencies": ["task_7", "task_9", "task_11", "task_1"]}, "task_16": {"task_type": "segmentation", "cpu_requirement": 6.31, "memory_requirement": 2066.08, "io_requirement": 18.9, "network_requirement": 5.85, "base_runtime": 508.67, "dependencies": ["task_13", "task_15", "task_6"]}, "task_17": {"task_type": "normalization", "cpu_requirement": 1.93, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 231.38, "dependencies": ["task_16"]}, "task_18": {"task_type": "registration", "cpu_requirement": 6.81, "memory_requirement": 1464.15, "io_requirement": 29.82, "network_requirement": 13.17, "base_runtime": 589.32, "dependencies": ["task_1", "task_3"]}, "task_19": {"task_type": "visualization", "cpu_requirement": 1.0, "memory_requirement": 545.44, "io_requirement": 11.98, "network_requirement": 5, "base_runtime": 161.13, "dependencies": ["task_15", "task_2"]}, "task_20": {"task_type": "visualization", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 133.45, "dependencies": ["task_19", "task_8", "task_7", "task_18"]}, "task_21": {"task_type": "registration", "cpu_requirement": 7.33, "memory_requirement": 1492.82, "io_requirement": 17.31, "network_requirement": 6.28, "base_runtime": 592.99, "dependencies": ["task_8"]}, "task_22": {"task_type": "statistics", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 148.12, "dependencies": ["task_6", "task_19"]}, "task_23": {"task_type": "segmentation", "cpu_requirement": 6.76, "memory_requirement": 1637.17, "io_requirement": 14.46, "network_requirement": 5.13, "base_runtime": 519.87, "dependencies": ["task_13", "task_4", "task_6", "task_5"]}, "task_24": {"task_type": "registration", "cpu_requirement": 4.55, "memory_requirement": 2503.72, "io_requirement": 17.62, "network_requirement": 8.43, "base_runtime": 405.08, "dependencies": ["task_2"]}}, "nodes": [{"node_id": "node_0", "cpu_capacity": 15, "memory_capacity": 122447.89495349216, "io_capacity": 2707.6518881181746, "network_capacity": 928.5735875935078, "energy_efficiency": 0.7646367931389123, "cost_per_hour": 0.1973308957299058, "node_type": "memory_intensive"}, {"node_id": "node_1", "cpu_capacity": 9, "memory_capacity": 108794.75340625625, "io_capacity": 2611.990848694606, "network_capacity": 1464.1761043884403, "energy_efficiency": 0.8134297188781014, "cost_per_hour": 0.2262652154145281, "node_type": "memory_intensive"}, {"node_id": "node_2", "cpu_capacity": 16, "memory_capacity": 97530.8011631283, "io_capacity": 2189.8468057238924, "network_capacity": 1496.846532550168, "energy_efficiency": 0.7906074565010811, "cost_per_hour": 0.2388232003032725, "node_type": "memory_intensive"}, {"node_id": "node_3", "cpu_capacity": 11, "memory_capacity": 127321.44536394047, "io_capacity": 2035.5636138305658, "network_capacity": 1149.6603902472934, "energy_efficiency": 0.7653374564526004, "cost_per_hour": 0.1749973551604037, "node_type": "memory_intensive"}, {"node_id": "node_4", "cpu_capacity": 16, "memory_capacity": 71280.41833163158, "io_capacity": 2628.8020985675607, "network_capacity": 1360.2526783324897, "energy_efficiency": 0.7876694630451324, "cost_per_hour": 0.1784290541153597, "node_type": "memory_intensive"}, {"node_id": "node_5", "cpu_capacity": 9, "memory_capacity": 71979.15747691144, "io_capacity": 2297.625069227297, "network_capacity": 1272.056644262926, "energy_efficiency": 0.7244730322496771, "cost_per_hour": 0.20683560122144018, "node_type": "memory_intensive"}, {"node_id": "node_6", "cpu_capacity": 10, "memory_capacity": 86978.7281315371, "io_capacity": 1896.0421724064959, "network_capacity": 1373.8149486216512, "energy_efficiency": 0.8409865056098859, "cost_per_hour": 0.21960405729051002, "node_type": "memory_intensive"}, {"node_id": "node_7", "cpu_capacity": 9, "memory_capacity": 100543.55961038769, "io_capacity": 2859.541086530894, "network_capacity": 1123.8313750216976, "energy_efficiency": 0.7312822547764332, "cost_per_hour": 0.21519469306958722, "node_type": "memory_intensive"}], "edges": [["task_0", "task_1", {"data_size": 160.04907668389225, "transfer_time": 0}], ["task_0", "task_2", {"data_size": 139.20809450727157, "transfer_time": 0}], ["task_2", "task_3", {"data_size": 190.1366513895145, "transfer_time": 0}], ["task_0", "task_3", {"data_size": 122.72601172870205, "transfer_time": 0}], ["task_2", "task_4", {"data_size": 250.46048936052262, "transfer_time": 0}], ["task_0", "task_4", {"data_size": 165.7712175664514, "transfer_time": 0}], ["task_1", "task_4", {"data_size": 646.1104945797492, "transfer_time": 0}], ["task_3", "task_4", {"data_size": 372.8572702288349, "transfer_time": 0}], ["task_4", "task_5", {"data_size": 263.8282119869432, "transfer_time": 0}], ["task_0", "task_5", {"data_size": 164.47391303342545, "transfer_time": 0}], ["task_3", "task_6", {"data_size": 348.90852004192817, "transfer_time": 0}], ["task_5", "task_7", {"data_size": 735.9098401032021, "transfer_time": 0}], ["task_6", "task_7", {"data_size": 759.8455285474113, "transfer_time": 0}], ["task_1", "task_7", {"data_size": 708.5931391602486, "transfer_time": 0}], ["task_3", "task_7", {"data_size": 395.64835581958494, "transfer_time": 0}], ["task_2", "task_8", {"data_size": 424.7370680789898, "transfer_time": 0}], ["task_4", "task_9", {"data_size": 248.94058718741462, "transfer_time": 0}], ["task_5", "task_10", {"data_size": 359.6558161130909, "transfer_time": 0}], ["task_2", "task_10", {"data_size": 458.9717098902426, "transfer_time": 0}], ["task_9", "task_10", {"data_size": 255.29821322628095, "transfer_time": 0}], ["task_5", "task_11", {"data_size": 752.3221490505931, "transfer_time": 0}], ["task_7", "task_11", {"data_size": 1628.2786339704733, "transfer_time": 0}], ["task_0", "task_11", {"data_size": 146.72583985645412, "transfer_time": 0}], ["task_7", "task_12", {"data_size": 1322.3556086467577, "transfer_time": 0}], ["task_9", "task_12", {"data_size": 382.4474196658127, "transfer_time": 0}], ["task_11", "task_13", {"data_size": 676.0235008818016, "transfer_time": 0}], ["task_5", "task_13", {"data_size": 710.6419290233906, "transfer_time": 0}], ["task_8", "task_14", {"data_size": 134.30268092663994, "transfer_time": 0}], ["task_12", "task_14", {"data_size": 226.89259678826235, "transfer_time": 0}], ["task_1", "task_14", {"data_size": 560.2134707052855, "transfer_time": 0}], ["task_7", "task_15", {"data_size": 898.5478172206623, "transfer_time": 0}], ["task_9", "task_15", {"data_size": 248.348476595918, "transfer_time": 0}], ["task_11", "task_15", {"data_size": 698.1363903137741, "transfer_time": 0}], ["task_1", "task_15", {"data_size": 569.6905556676493, "transfer_time": 0}], ["task_13", "task_16", {"data_size": 520.4404923021619, "transfer_time": 0}], ["task_15", "task_16", {"data_size": 552.0460042939314, "transfer_time": 0}], ["task_6", "task_16", {"data_size": 608.5231648928368, "transfer_time": 0}], ["task_16", "task_17", {"data_size": 354.716006335302, "transfer_time": 0}], ["task_1", "task_18", {"data_size": 416.9316349032458, "transfer_time": 0}], ["task_3", "task_18", {"data_size": 301.2072247015726, "transfer_time": 0}], ["task_15", "task_19", {"data_size": 384.81549326129283, "transfer_time": 0}], ["task_2", "task_19", {"data_size": 391.6281101264194, "transfer_time": 0}], ["task_19", "task_20", {"data_size": 611.7855653709148, "transfer_time": 0}], ["task_8", "task_20", {"data_size": 138.6555192241741, "transfer_time": 0}], ["task_7", "task_20", {"data_size": 1386.0906171177687, "transfer_time": 0}], ["task_18", "task_20", {"data_size": 1356.1783427067303, "transfer_time": 0}], ["task_8", "task_21", {"data_size": 107.53503994095344, "transfer_time": 0}], ["task_6", "task_22", {"data_size": 712.453839419423, "transfer_time": 0}], ["task_19", "task_22", {"data_size": 284.30058002360425, "transfer_time": 0}], ["task_13", "task_23", {"data_size": 320.609471367959, "transfer_time": 0}], ["task_4", "task_23", {"data_size": 318.0757660936774, "transfer_time": 0}], ["task_6", "task_23", {"data_size": 447.41674775281734, "transfer_time": 0}], ["task_5", "task_23", {"data_size": 399.21651638310783, "transfer_time": 0}], ["task_2", "task_24", {"data_size": 416.5307297549981, "transfer_time": 0}]], "metadata": {"total_tasks": 25, "total_base_runtime": 8396.03, "total_cpu_requirement": 83.41, "total_memory_requirement": 31501.24, "critical_path_length": 2953.89, "parallelism_degree": 8, "total_edges": 54, "average_base_runtime": 335.84, "average_cpu_requirement": 3.34, "average_memory_requirement": 1260.05, "workflow_type": "brain"}}