{"workflow_type": "cybershake", "tasks": {"task_0": {"task_type": "syn1d", "cpu_requirement": 16.0, "memory_requirement": 741.41, "io_requirement": 16.37, "network_requirement": 7.17, "base_runtime": 1537.11, "dependencies": []}, "task_1": {"task_type": "post_processing", "cpu_requirement": 3.43, "memory_requirement": 1221.95, "io_requirement": 20.65, "network_requirement": 8.13, "base_runtime": 432.28, "dependencies": ["task_0"]}, "task_2": {"task_type": "srfgen", "cpu_requirement": 3.65, "memory_requirement": 584.5, "io_requirement": 10, "network_requirement": 5, "base_runtime": 451.36, "dependencies": ["task_1"]}, "task_3": {"task_type": "post_processing", "cpu_requirement": 3.99, "memory_requirement": 772.18, "io_requirement": 26.38, "network_requirement": 6.79, "base_runtime": 472.23, "dependencies": ["task_0", "task_2", "task_1"]}, "task_4": {"task_type": "post_processing", "cpu_requirement": 5.16, "memory_requirement": 512, "io_requirement": 16.11, "network_requirement": 6.43, "base_runtime": 682.29, "dependencies": ["task_1"]}, "task_5": {"task_type": "syn2d", "cpu_requirement": 16.0, "memory_requirement": 2409.32, "io_requirement": 25.2, "network_requirement": 15.77, "base_runtime": 1875.41, "dependencies": ["task_2"]}, "task_6": {"task_type": "srfgen", "cpu_requirement": 6.33, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 667.17, "dependencies": ["task_2"]}, "task_7": {"task_type": "syn1d", "cpu_requirement": 14.91, "memory_requirement": 1491.29, "io_requirement": 21.81, "network_requirement": 12.2, "base_runtime": 1146.05, "dependencies": ["task_5", "task_6", "task_1"]}, "task_8": {"task_type": "syn1d", "cpu_requirement": 13.52, "memory_requirement": 1091.79, "io_requirement": 16.62, "network_requirement": 10.38, "base_runtime": 1030.68, "dependencies": ["task_0", "task_1", "task_5"]}, "task_9": {"task_type": "velocity_model", "cpu_requirement": 11.02, "memory_requirement": 1462.24, "io_requirement": 18.17, "network_requirement": 9.97, "base_runtime": 904.16, "dependencies": ["task_5", "task_4", "task_0"]}, "task_10": {"task_type": "post_processing", "cpu_requirement": 5.01, "memory_requirement": 1236.75, "io_requirement": 20.03, "network_requirement": 5, "base_runtime": 576.51, "dependencies": ["task_3", "task_5"]}, "task_11": {"task_type": "syn2d", "cpu_requirement": 16.0, "memory_requirement": 1475.85, "io_requirement": 37.59, "network_requirement": 16.92, "base_runtime": 1725.48, "dependencies": ["task_2", "task_0"]}, "task_12": {"task_type": "rupture_generator", "cpu_requirement": 2.47, "memory_requirement": 548.11, "io_requirement": 10, "network_requirement": 7.12, "base_runtime": 305.2, "dependencies": ["task_6"]}, "task_13": {"task_type": "syn2d", "cpu_requirement": 16.0, "memory_requirement": 3223.74, "io_requirement": 39.08, "network_requirement": 29.39, "base_runtime": 1456.37, "dependencies": ["task_5"]}, "task_14": {"task_type": "syn2d", "cpu_requirement": 16.0, "memory_requirement": 1084.26, "io_requirement": 12.97, "network_requirement": 9.49, "base_runtime": 1900.9, "dependencies": ["task_11"]}, "task_15": {"task_type": "velocity_model", "cpu_requirement": 8.73, "memory_requirement": 1056.65, "io_requirement": 10, "network_requirement": 5.92, "base_runtime": 781.94, "dependencies": ["task_3", "task_14", "task_8", "task_5"]}, "task_16": {"task_type": "velocity_model", "cpu_requirement": 10.74, "memory_requirement": 856.22, "io_requirement": 10, "network_requirement": 5, "base_runtime": 944.91, "dependencies": ["task_6"]}, "task_17": {"task_type": "rupture_generator", "cpu_requirement": 4.73, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 586.59, "dependencies": ["task_6", "task_11"]}, "task_18": {"task_type": "velocity_model", "cpu_requirement": 11.33, "memory_requirement": 1649.8, "io_requirement": 14.37, "network_requirement": 11.49, "base_runtime": 1072.76, "dependencies": ["task_14"]}, "task_19": {"task_type": "syn2d", "cpu_requirement": 16.0, "memory_requirement": 3355.63, "io_requirement": 35.31, "network_requirement": 25.83, "base_runtime": 1820.84, "dependencies": ["task_7", "task_3", "task_0"]}, "task_20": {"task_type": "syn2d", "cpu_requirement": 16.0, "memory_requirement": 2118.48, "io_requirement": 22.42, "network_requirement": 14.57, "base_runtime": 2124.44, "dependencies": ["task_2", "task_0"]}, "task_21": {"task_type": "srfgen", "cpu_requirement": 6.61, "memory_requirement": 593.91, "io_requirement": 10, "network_requirement": 5, "base_runtime": 720.46, "dependencies": ["task_9", "task_10", "task_16", "task_13"]}, "task_22": {"task_type": "post_processing", "cpu_requirement": 3.69, "memory_requirement": 512, "io_requirement": 14.34, "network_requirement": 5.28, "base_runtime": 491.63, "dependencies": ["task_20", "task_7"]}, "task_23": {"task_type": "post_processing", "cpu_requirement": 6.51, "memory_requirement": 800.98, "io_requirement": 22.26, "network_requirement": 5.17, "base_runtime": 829.54, "dependencies": ["task_13"]}, "task_24": {"task_type": "syn3d", "cpu_requirement": 16.0, "memory_requirement": 4918.59, "io_requirement": 39.71, "network_requirement": 17.44, "base_runtime": 3059.61, "dependencies": ["task_5", "task_16", "task_0", "task_13"]}}, "nodes": [{"node_id": "node_0", "cpu_capacity": 19, "memory_capacity": 31547.21562052808, "io_capacity": 2153.529402863021, "network_capacity": 8931.646494803017, "energy_efficiency": 0.7498207666635968, "cost_per_hour": 0.34197825518353997, "node_type": "network_intensive"}, {"node_id": "node_1", "cpu_capacity": 16, "memory_capacity": 31209.55454815874, "io_capacity": 2159.583340594596, "network_capacity": 6383.939340326539, "energy_efficiency": 0.796272062203421, "cost_per_hour": 0.33434511412904694, "node_type": "network_intensive"}, {"node_id": "node_2", "cpu_capacity": 20, "memory_capacity": 27840.1498506238, "io_capacity": 1159.9807936894867, "network_capacity": 5119.652183767688, "energy_efficiency": 0.8232764907599927, "cost_per_hour": 0.223105085419339, "node_type": "network_intensive"}, {"node_id": "node_3", "cpu_capacity": 8, "memory_capacity": 17534.37678397396, "io_capacity": 2819.699051780503, "network_capacity": 9755.532406974506, "energy_efficiency": 0.7121384814610099, "cost_per_hour": 0.267592203900476, "node_type": "network_intensive"}, {"node_id": "node_4", "cpu_capacity": 16, "memory_capacity": 19414.22636958886, "io_capacity": 2442.6294166005, "network_capacity": 9262.991817374437, "energy_efficiency": 0.7945213254179613, "cost_per_hour": 0.2921995299778721, "node_type": "network_intensive"}, {"node_id": "node_5", "cpu_capacity": 16, "memory_capacity": 21973.610659618847, "io_capacity": 1941.7981491010491, "network_capacity": 5382.174440339546, "energy_efficiency": 0.7496741829847123, "cost_per_hour": 0.18214807618136714, "node_type": "network_intensive"}, {"node_id": "node_6", "cpu_capacity": 13, "memory_capacity": 28072.32210442421, "io_capacity": 1108.5166673545789, "network_capacity": 7951.5752929276605, "energy_efficiency": 0.7020790510004402, "cost_per_hour": 0.2949224940444855, "node_type": "network_intensive"}, {"node_id": "node_7", "cpu_capacity": 19, "memory_capacity": 24627.578259992883, "io_capacity": 1931.4735526119798, "network_capacity": 9090.283088552493, "energy_efficiency": 0.8451464950141, "cost_per_hour": 0.2044744692994437, "node_type": "network_intensive"}], "edges": [["task_0", "task_1", {"data_size": 574.7778720735594, "transfer_time": 0}], ["task_1", "task_2", {"data_size": 905.8845547868882, "transfer_time": 0}], ["task_0", "task_3", {"data_size": 699.9228842250055, "transfer_time": 0}], ["task_2", "task_3", {"data_size": 281.9919018910044, "transfer_time": 0}], ["task_1", "task_3", {"data_size": 951.691902909278, "transfer_time": 0}], ["task_1", "task_4", {"data_size": 1087.1560248121584, "transfer_time": 0}], ["task_2", "task_5", {"data_size": 342.6787954596596, "transfer_time": 0}], ["task_2", "task_6", {"data_size": 509.86032642054573, "transfer_time": 0}], ["task_5", "task_7", {"data_size": 324.99311689795906, "transfer_time": 0}], ["task_6", "task_7", {"data_size": 91.8629658343671, "transfer_time": 0}], ["task_1", "task_7", {"data_size": 1260.7617941942488, "transfer_time": 0}], ["task_0", "task_8", {"data_size": 824.3150107241503, "transfer_time": 0}], ["task_1", "task_8", {"data_size": 972.3029438265887, "transfer_time": 0}], ["task_5", "task_8", {"data_size": 460.56331523288895, "transfer_time": 0}], ["task_5", "task_9", {"data_size": 404.9224099980552, "transfer_time": 0}], ["task_4", "task_9", {"data_size": 931.9731908135379, "transfer_time": 0}], ["task_0", "task_9", {"data_size": 971.4466322919599, "transfer_time": 0}], ["task_3", "task_10", {"data_size": 1215.028331926535, "transfer_time": 0}], ["task_5", "task_10", {"data_size": 356.0389326639747, "transfer_time": 0}], ["task_2", "task_11", {"data_size": 327.1430364658515, "transfer_time": 0}], ["task_0", "task_11", {"data_size": 629.8494836446426, "transfer_time": 0}], ["task_6", "task_12", {"data_size": 117.599950930246, "transfer_time": 0}], ["task_5", "task_13", {"data_size": 425.53768648999346, "transfer_time": 0}], ["task_11", "task_14", {"data_size": 2506.3698178701716, "transfer_time": 0}], ["task_3", "task_15", {"data_size": 1174.9394641875538, "transfer_time": 0}], ["task_14", "task_15", {"data_size": 317.58992346087535, "transfer_time": 0}], ["task_8", "task_15", {"data_size": 492.9015992056393, "transfer_time": 0}], ["task_5", "task_15", {"data_size": 602.7787113810915, "transfer_time": 0}], ["task_6", "task_16", {"data_size": 77.77618388524425, "transfer_time": 0}], ["task_6", "task_17", {"data_size": 107.43828452795536, "transfer_time": 0}], ["task_11", "task_17", {"data_size": 2349.0445917655943, "transfer_time": 0}], ["task_14", "task_18", {"data_size": 585.7103511325151, "transfer_time": 0}], ["task_7", "task_19", {"data_size": 1119.2007597773732, "transfer_time": 0}], ["task_3", "task_19", {"data_size": 1038.1320341964301, "transfer_time": 0}], ["task_0", "task_19", {"data_size": 666.3904238264606, "transfer_time": 0}], ["task_2", "task_20", {"data_size": 477.76530135443596, "transfer_time": 0}], ["task_0", "task_20", {"data_size": 1231.8482284803501, "transfer_time": 0}], ["task_9", "task_21", {"data_size": 751.5522035845859, "transfer_time": 0}], ["task_10", "task_21", {"data_size": 498.1161111712038, "transfer_time": 0}], ["task_16", "task_21", {"data_size": 260.80539875996897, "transfer_time": 0}], ["task_13", "task_21", {"data_size": 1307.148434773514, "transfer_time": 0}], ["task_20", "task_22", {"data_size": 449.5072698877242, "transfer_time": 0}], ["task_7", "task_22", {"data_size": 1061.3786515316258, "transfer_time": 0}], ["task_13", "task_23", {"data_size": 1302.8204244540586, "transfer_time": 0}], ["task_5", "task_24", {"data_size": 306.21754093290156, "transfer_time": 0}], ["task_16", "task_24", {"data_size": 338.2093145368069, "transfer_time": 0}], ["task_0", "task_24", {"data_size": 1026.097097806167, "transfer_time": 0}], ["task_13", "task_24", {"data_size": 1071.630437944741, "transfer_time": 0}]], "metadata": {"total_tasks": 25, "total_base_runtime": 27595.93, "total_cpu_requirement": 249.83, "total_memory_requirement": 34741.63, "critical_path_length": 7275.02, "parallelism_degree": 8, "total_edges": 48, "average_base_runtime": 1103.84, "average_cpu_requirement": 9.99, "average_memory_requirement": 1389.67, "workflow_type": "cybershake"}}