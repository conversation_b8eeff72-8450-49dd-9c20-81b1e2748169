{"workflow_type": "epigenomics", "tasks": {"task_0": {"task_type": "macs2", "cpu_requirement": 9.23, "memory_requirement": 3706.52, "io_requirement": 49.89, "network_requirement": 14.27, "base_runtime": 734.1, "dependencies": []}, "task_1": {"task_type": "bwa", "cpu_requirement": 16.0, "memory_requirement": 6141.24, "io_requirement": 159.77, "network_requirement": 30.56, "base_runtime": 1367.37, "dependencies": ["task_0"]}, "task_2": {"task_type": "samtools", "cpu_requirement": 2.92, "memory_requirement": 1018.04, "io_requirement": 32.52, "network_requirement": 5.92, "base_runtime": 354.68, "dependencies": ["task_1"]}, "task_3": {"task_type": "macs2", "cpu_requirement": 16.0, "memory_requirement": 2865.87, "io_requirement": 35.09, "network_requirement": 9.68, "base_runtime": 1534.0, "dependencies": ["task_2"]}, "task_4": {"task_type": "homer", "cpu_requirement": 16.0, "memory_requirement": 7138.18, "io_requirement": 101.11, "network_requirement": 24.91, "base_runtime": 1394.87, "dependencies": ["task_1"]}, "task_5": {"task_type": "homer", "cpu_requirement": 15.58, "memory_requirement": 8637.6, "io_requirement": 73.92, "network_requirement": 25.85, "base_runtime": 1072.67, "dependencies": ["task_4", "task_3", "task_2", "task_0"]}, "task_6": {"task_type": "bedtools", "cpu_requirement": 3.11, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 435.81, "dependencies": ["task_0", "task_1", "task_2"]}, "task_7": {"task_type": "samtools", "cpu_requirement": 6.61, "memory_requirement": 1913.62, "io_requirement": 32.17, "network_requirement": 8.15, "base_runtime": 828.5, "dependencies": ["task_6", "task_0", "task_5", "task_3", "task_2"]}, "task_8": {"task_type": "igv", "cpu_requirement": 1.96, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 290.85, "dependencies": ["task_4"]}, "task_9": {"task_type": "igv", "cpu_requirement": 1.0, "memory_requirement": 540.97, "io_requirement": 10, "network_requirement": 5, "base_runtime": 139.07, "dependencies": ["task_1", "task_6", "task_7"]}, "task_10": {"task_type": "homer", "cpu_requirement": 16.0, "memory_requirement": 6921.45, "io_requirement": 82.38, "network_requirement": 18.88, "base_runtime": 2272.15, "dependencies": ["task_3"]}, "task_11": {"task_type": "igv", "cpu_requirement": 1.0, "memory_requirement": 586.78, "io_requirement": 10, "network_requirement": 5, "base_runtime": 141.22, "dependencies": ["task_9", "task_7", "task_5", "task_6"]}, "task_12": {"task_type": "bedtools", "cpu_requirement": 2.33, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 370.86, "dependencies": ["task_3", "task_5", "task_4", "task_10"]}, "task_13": {"task_type": "bwa", "cpu_requirement": 16.0, "memory_requirement": 6144.48, "io_requirement": 119.5, "network_requirement": 25.27, "base_runtime": 1339.18, "dependencies": ["task_7", "task_11", "task_10", "task_0", "task_2"]}, "task_14": {"task_type": "igv", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 134.24, "dependencies": ["task_9", "task_5", "task_12", "task_6"]}, "task_15": {"task_type": "fastqc", "cpu_requirement": 1.0, "memory_requirement": 558.54, "io_requirement": 13.02, "network_requirement": 5, "base_runtime": 129.63, "dependencies": ["task_2", "task_3"]}, "task_16": {"task_type": "macs2", "cpu_requirement": 16.0, "memory_requirement": 4933.07, "io_requirement": 58.98, "network_requirement": 15.02, "base_runtime": 1439.77, "dependencies": ["task_15"]}, "task_17": {"task_type": "igv", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 157.71, "dependencies": ["task_9"]}, "task_18": {"task_type": "bwa", "cpu_requirement": 16.0, "memory_requirement": 4193.51, "io_requirement": 123.61, "network_requirement": 32.36, "base_runtime": 1923.28, "dependencies": ["task_8", "task_5", "task_16", "task_7"]}, "task_19": {"task_type": "bedtools", "cpu_requirement": 2.4, "memory_requirement": 525.77, "io_requirement": 15.21, "network_requirement": 5, "base_runtime": 372.73, "dependencies": ["task_18", "task_8", "task_12", "task_4", "task_15"]}, "task_20": {"task_type": "bwa", "cpu_requirement": 16.0, "memory_requirement": 6368.09, "io_requirement": 169.09, "network_requirement": 43.63, "base_runtime": 1445.18, "dependencies": ["task_12", "task_16", "task_14", "task_15"]}, "task_21": {"task_type": "macs2", "cpu_requirement": 15.74, "memory_requirement": 1675.26, "io_requirement": 33.49, "network_requirement": 10.48, "base_runtime": 1424.36, "dependencies": ["task_10", "task_20", "task_13", "task_11", "task_16"]}, "task_22": {"task_type": "fastqc", "cpu_requirement": 1.37, "memory_requirement": 2146.91, "io_requirement": 37.15, "network_requirement": 6.75, "base_runtime": 263.99, "dependencies": ["task_21", "task_9", "task_13"]}, "task_23": {"task_type": "macs2", "cpu_requirement": 11.6, "memory_requirement": 3653.06, "io_requirement": 42.76, "network_requirement": 8.88, "base_runtime": 942.97, "dependencies": ["task_4", "task_2", "task_18", "task_8", "task_6"]}, "task_24": {"task_type": "bwa", "cpu_requirement": 16.0, "memory_requirement": 10695.1, "io_requirement": 144.95, "network_requirement": 42.79, "base_runtime": 2028.7, "dependencies": ["task_2", "task_3"]}}, "nodes": [{"node_id": "node_0", "cpu_capacity": 10, "memory_capacity": 9895.220051451168, "io_capacity": 5156.517907687499, "network_capacity": 4129.879580524517, "energy_efficiency": 0.6854848991986883, "cost_per_hour": 0.15456081009253894, "node_type": "data_intensive"}, {"node_id": "node_1", "cpu_capacity": 4, "memory_capacity": 15483.538943480662, "io_capacity": 7219.542945787196, "network_capacity": 3925.198232393778, "energy_efficiency": 0.6582299803817802, "cost_per_hour": 0.17642744519319367, "node_type": "data_intensive"}, {"node_id": "node_2", "cpu_capacity": 7, "memory_capacity": 9401.573412336054, "io_capacity": 9478.133621721532, "network_capacity": 3739.817890534754, "energy_efficiency": 0.6542916475067487, "cost_per_hour": 0.10331502869058584, "node_type": "data_intensive"}, {"node_id": "node_3", "cpu_capacity": 12, "memory_capacity": 11883.556393964609, "io_capacity": 6749.074367513415, "network_capacity": 2107.2482559573004, "energy_efficiency": 0.6538879794089341, "cost_per_hour": 0.19897917829628162, "node_type": "data_intensive"}, {"node_id": "node_4", "cpu_capacity": 7, "memory_capacity": 11270.027185569088, "io_capacity": 6747.076901048978, "network_capacity": 2884.2061025400417, "energy_efficiency": 0.7308971466055116, "cost_per_hour": 0.13169594153384825, "node_type": "data_intensive"}, {"node_id": "node_5", "cpu_capacity": 11, "memory_capacity": 14449.39575522906, "io_capacity": 6217.628327554876, "network_capacity": 2505.097819149318, "energy_efficiency": 0.7337664662583722, "cost_per_hour": 0.1624959277827867, "node_type": "data_intensive"}, {"node_id": "node_6", "cpu_capacity": 8, "memory_capacity": 14546.593138358205, "io_capacity": 7896.558931500279, "network_capacity": 4513.849397971714, "energy_efficiency": 0.6998824649518851, "cost_per_hour": 0.12018479930704384, "node_type": "data_intensive"}, {"node_id": "node_7", "cpu_capacity": 5, "memory_capacity": 9093.503310003056, "io_capacity": 5106.677420596174, "network_capacity": 3997.700349421624, "energy_efficiency": 0.6861486163217395, "cost_per_hour": 0.1665926337722517, "node_type": "data_intensive"}], "edges": [["task_0", "task_1", {"data_size": 2167.7544903011735, "transfer_time": 0}], ["task_1", "task_2", {"data_size": 3664.533951108011, "transfer_time": 0}], ["task_2", "task_3", {"data_size": 1268.8844984856164, "transfer_time": 0}], ["task_1", "task_4", {"data_size": 3884.598510818463, "transfer_time": 0}], ["task_4", "task_5", {"data_size": 1666.386439438995, "transfer_time": 0}], ["task_3", "task_5", {"data_size": 1608.4200126253297, "transfer_time": 0}], ["task_2", "task_5", {"data_size": 2167.6475261960713, "transfer_time": 0}], ["task_0", "task_5", {"data_size": 1264.9007627353583, "transfer_time": 0}], ["task_0", "task_6", {"data_size": 1765.326555634318, "transfer_time": 0}], ["task_1", "task_6", {"data_size": 6226.605051336288, "transfer_time": 0}], ["task_2", "task_6", {"data_size": 1380.3325691169541, "transfer_time": 0}], ["task_6", "task_7", {"data_size": 548.0807072130765, "transfer_time": 0}], ["task_0", "task_7", {"data_size": 1908.454192808521, "transfer_time": 0}], ["task_5", "task_7", {"data_size": 1890.1156981332238, "transfer_time": 0}], ["task_3", "task_7", {"data_size": 936.7149452734885, "transfer_time": 0}], ["task_2", "task_7", {"data_size": 1635.207649112329, "transfer_time": 0}], ["task_4", "task_8", {"data_size": 3672.2558560294638, "transfer_time": 0}], ["task_1", "task_9", {"data_size": 5301.755161686985, "transfer_time": 0}], ["task_6", "task_9", {"data_size": 590.9787273132596, "transfer_time": 0}], ["task_7", "task_9", {"data_size": 725.6131030967591, "transfer_time": 0}], ["task_3", "task_10", {"data_size": 816.0987949908975, "transfer_time": 0}], ["task_9", "task_11", {"data_size": 707.6960195819405, "transfer_time": 0}], ["task_7", "task_11", {"data_size": 1416.5337931525974, "transfer_time": 0}], ["task_5", "task_11", {"data_size": 1767.5249761621822, "transfer_time": 0}], ["task_6", "task_11", {"data_size": 287.88290554689706, "transfer_time": 0}], ["task_3", "task_12", {"data_size": 729.7064057652306, "transfer_time": 0}], ["task_5", "task_12", {"data_size": 804.5690553432681, "transfer_time": 0}], ["task_4", "task_12", {"data_size": 1982.9478592980347, "transfer_time": 0}], ["task_10", "task_12", {"data_size": 3136.197078492353, "transfer_time": 0}], ["task_7", "task_13", {"data_size": 1779.7741962850791, "transfer_time": 0}], ["task_11", "task_13", {"data_size": 71.57207461138103, "transfer_time": 0}], ["task_10", "task_13", {"data_size": 1745.8543651630778, "transfer_time": 0}], ["task_0", "task_13", {"data_size": 2030.395879150121, "transfer_time": 0}], ["task_2", "task_13", {"data_size": 821.6737861735439, "transfer_time": 0}], ["task_9", "task_14", {"data_size": 448.10093015430994, "transfer_time": 0}], ["task_5", "task_14", {"data_size": 1408.096707910001, "transfer_time": 0}], ["task_12", "task_14", {"data_size": 199.45459624858873, "transfer_time": 0}], ["task_6", "task_14", {"data_size": 517.8773791465352, "transfer_time": 0}], ["task_2", "task_15", {"data_size": 1116.2992656166857, "transfer_time": 0}], ["task_3", "task_15", {"data_size": 1150.1503732087947, "transfer_time": 0}], ["task_15", "task_16", {"data_size": 86.89091098345102, "transfer_time": 0}], ["task_9", "task_17", {"data_size": 244.82054670794992, "transfer_time": 0}], ["task_8", "task_18", {"data_size": 255.51259110102956, "transfer_time": 0}], ["task_5", "task_18", {"data_size": 736.5390948784593, "transfer_time": 0}], ["task_16", "task_18", {"data_size": 1727.5588087992933, "transfer_time": 0}], ["task_7", "task_18", {"data_size": 1840.3058853982147, "transfer_time": 0}], ["task_18", "task_19", {"data_size": 3021.897441247352, "transfer_time": 0}], ["task_8", "task_19", {"data_size": 108.6010468453582, "transfer_time": 0}], ["task_12", "task_19", {"data_size": 287.99467499167287, "transfer_time": 0}], ["task_4", "task_19", {"data_size": 2996.0537921634223, "transfer_time": 0}], ["task_15", "task_19", {"data_size": 54.00213439201968, "transfer_time": 0}], ["task_12", "task_20", {"data_size": 158.56676705086096, "transfer_time": 0}], ["task_16", "task_20", {"data_size": 3367.379430920207, "transfer_time": 0}], ["task_14", "task_20", {"data_size": 211.7582371207136, "transfer_time": 0}], ["task_15", "task_20", {"data_size": 92.66154674000687, "transfer_time": 0}], ["task_10", "task_21", {"data_size": 3613.5238123662466, "transfer_time": 0}], ["task_20", "task_21", {"data_size": 5805.8585168477275, "transfer_time": 0}], ["task_13", "task_21", {"data_size": 3563.022793436208, "transfer_time": 0}], ["task_11", "task_21", {"data_size": 109.52804337644126, "transfer_time": 0}], ["task_16", "task_21", {"data_size": 1371.014840391746, "transfer_time": 0}], ["task_21", "task_22", {"data_size": 1614.120051758253, "transfer_time": 0}], ["task_9", "task_22", {"data_size": 607.3727931010682, "transfer_time": 0}], ["task_13", "task_22", {"data_size": 2703.005158375476, "transfer_time": 0}], ["task_4", "task_23", {"data_size": 3447.6654231264947, "transfer_time": 0}], ["task_2", "task_23", {"data_size": 1990.413891944792, "transfer_time": 0}], ["task_18", "task_23", {"data_size": 4445.6561242678, "transfer_time": 0}], ["task_8", "task_23", {"data_size": 235.16893638081513, "transfer_time": 0}], ["task_6", "task_23", {"data_size": 549.211016386004, "transfer_time": 0}], ["task_2", "task_24", {"data_size": 1834.0255602434706, "transfer_time": 0}], ["task_3", "task_24", {"data_size": 1166.589880632524, "transfer_time": 0}]], "metadata": {"total_tasks": 25, "total_base_runtime": 22537.9, "total_cpu_requirement": 221.85, "total_memory_requirement": 82924.05, "critical_path_length": 9166.83, "parallelism_degree": 8, "total_edges": 70, "average_base_runtime": 901.52, "average_cpu_requirement": 8.87, "average_memory_requirement": 3316.96, "workflow_type": "epigenomics"}}