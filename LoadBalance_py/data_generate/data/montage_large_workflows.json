{"workflow_type": "montage", "tasks": {"task_0": {"task_type": "mDiffFit", "cpu_requirement": 1.85, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 198.28, "dependencies": []}, "task_1": {"task_type": "mAdd", "cpu_requirement": 5.92, "memory_requirement": 1409.4, "io_requirement": 14.93, "network_requirement": 5.45, "base_runtime": 467.71, "dependencies": ["task_0"]}, "task_2": {"task_type": "mDiffFit", "cpu_requirement": 2.75, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 284.45, "dependencies": ["task_0"]}, "task_3": {"task_type": "mProject", "cpu_requirement": 6.28, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 481.37, "dependencies": ["task_0", "task_2"]}, "task_4": {"task_type": "mBackground", "cpu_requirement": 3.02, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 252.1, "dependencies": ["task_0"]}, "task_5": {"task_type": "mBackground", "cpu_requirement": 2.89, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 262.35, "dependencies": ["task_2"]}, "task_6": {"task_type": "mAdd", "cpu_requirement": 6.4, "memory_requirement": 1242.01, "io_requirement": 11.85, "network_requirement": 5, "base_runtime": 421.37, "dependencies": ["task_3"]}, "task_7": {"task_type": "mProject", "cpu_requirement": 2.34, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 182.75, "dependencies": ["task_6", "task_4"]}, "task_8": {"task_type": "mProject", "cpu_requirement": 4.81, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 357.2, "dependencies": ["task_1", "task_6", "task_4"]}, "task_9": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 176.69, "dependencies": ["task_2"]}, "task_10": {"task_type": "mBackground", "cpu_requirement": 3.95, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 348.84, "dependencies": ["task_1"]}, "task_11": {"task_type": "mBgModel", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 118.63, "dependencies": ["task_2", "task_6", "task_8"]}, "task_12": {"task_type": "mProject", "cpu_requirement": 3.62, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 281.76, "dependencies": ["task_11"]}, "task_13": {"task_type": "mProject", "cpu_requirement": 5.02, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 401.3, "dependencies": ["task_0"]}, "task_14": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 99.89, "dependencies": ["task_7"]}, "task_15": {"task_type": "mProject", "cpu_requirement": 6.24, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 498.75, "dependencies": ["task_3"]}, "task_16": {"task_type": "mBackground", "cpu_requirement": 4.12, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 353.83, "dependencies": ["task_13", "task_0"]}, "task_17": {"task_type": "mProject", "cpu_requirement": 3.72, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 318.94, "dependencies": ["task_10", "task_1", "task_5"]}, "task_18": {"task_type": "mDiffFit", "cpu_requirement": 1.87, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 212.0, "dependencies": ["task_1", "task_15"]}, "task_19": {"task_type": "mBackground", "cpu_requirement": 2.71, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 248.56, "dependencies": ["task_18", "task_17"]}, "task_20": {"task_type": "mBackground", "cpu_requirement": 2.7, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 245.08, "dependencies": ["task_7", "task_0"]}, "task_21": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 141.89, "dependencies": ["task_14"]}, "task_22": {"task_type": "mDiffFit", "cpu_requirement": 2.13, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 258.51, "dependencies": ["task_3", "task_19"]}, "task_23": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 130.31, "dependencies": ["task_5", "task_14", "task_22"]}, "task_24": {"task_type": "mBgModel", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 134.31, "dependencies": ["task_21", "task_0"]}, "task_25": {"task_type": "mDiffFit", "cpu_requirement": 2.29, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 286.99, "dependencies": ["task_16", "task_15"]}, "task_26": {"task_type": "mDiffFit", "cpu_requirement": 2.32, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 283.23, "dependencies": ["task_25", "task_18"]}, "task_27": {"task_type": "mDiffFit", "cpu_requirement": 1.27, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 147.58, "dependencies": ["task_18", "task_17", "task_3"]}, "task_28": {"task_type": "mBgModel", "cpu_requirement": 1.53, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 226.99, "dependencies": ["task_6"]}, "task_29": {"task_type": "mBackground", "cpu_requirement": 2.58, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 211.62, "dependencies": ["task_14"]}, "task_30": {"task_type": "mBackground", "cpu_requirement": 2.47, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 239.05, "dependencies": ["task_19", "task_22"]}, "task_31": {"task_type": "mDiffFit", "cpu_requirement": 1.81, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 226.62, "dependencies": ["task_23", "task_11"]}, "task_32": {"task_type": "mDiffFit", "cpu_requirement": 1.38, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 164.91, "dependencies": ["task_27", "task_3"]}, "task_33": {"task_type": "mProject", "cpu_requirement": 6.2, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 512.67, "dependencies": ["task_27", "task_13"]}, "task_34": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 124.32, "dependencies": ["task_9", "task_23", "task_31"]}, "task_35": {"task_type": "mProject", "cpu_requirement": 6.98, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 593.57, "dependencies": ["task_5"]}, "task_36": {"task_type": "mProject", "cpu_requirement": 5.33, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 407.29, "dependencies": ["task_4", "task_9"]}, "task_37": {"task_type": "mProject", "cpu_requirement": 2.59, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 201.83, "dependencies": ["task_12", "task_25"]}, "task_38": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 102.65, "dependencies": ["task_32", "task_11"]}, "task_39": {"task_type": "mProject", "cpu_requirement": 5.66, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 469.44, "dependencies": ["task_26", "task_28"]}}, "nodes": [{"node_id": "node_0", "cpu_capacity": 26, "memory_capacity": 34440.15732564087, "io_capacity": 3512.942159082103, "network_capacity": 1743.1816682215044, "energy_efficiency": 0.7925475858128885, "cost_per_hour": 0.15001196929313765, "node_type": "cpu_intensive"}, {"node_id": "node_1", "cpu_capacity": 22, "memory_capacity": 61835.128560149, "io_capacity": 4500.965969661434, "network_capacity": 1881.6043410276957, "energy_efficiency": 0.8894870445859067, "cost_per_hour": 0.2840646618720062, "node_type": "cpu_intensive"}, {"node_id": "node_2", "cpu_capacity": 27, "memory_capacity": 62329.863146718795, "io_capacity": 3620.276694427837, "network_capacity": 1077.610711531471, "energy_efficiency": 0.7954993646708264, "cost_per_hour": 0.2608406091600207, "node_type": "cpu_intensive"}, {"node_id": "node_3", "cpu_capacity": 19, "memory_capacity": 59788.39691776152, "io_capacity": 4967.061113675516, "network_capacity": 1958.8299313386306, "energy_efficiency": 0.7667106179924236, "cost_per_hour": 0.2582479709322456, "node_type": "cpu_intensive"}, {"node_id": "node_4", "cpu_capacity": 30, "memory_capacity": 43425.765024685184, "io_capacity": 3736.7493526573685, "network_capacity": 1967.1403476399187, "energy_efficiency": 0.8700377567837108, "cost_per_hour": 0.2479386165386705, "node_type": "cpu_intensive"}, {"node_id": "node_5", "cpu_capacity": 20, "memory_capacity": 53867.27000937553, "io_capacity": 4235.273059308518, "network_capacity": 1915.4474354033055, "energy_efficiency": 0.8294637304425198, "cost_per_hour": 0.16544468750263394, "node_type": "cpu_intensive"}, {"node_id": "node_6", "cpu_capacity": 23, "memory_capacity": 33549.87203998788, "io_capacity": 4462.256797918568, "network_capacity": 1383.3672367675574, "energy_efficiency": 0.83951453987917, "cost_per_hour": 0.22284755040073334, "node_type": "cpu_intensive"}, {"node_id": "node_7", "cpu_capacity": 21, "memory_capacity": 55678.533358992034, "io_capacity": 4458.270909182615, "network_capacity": 1534.2367360334374, "energy_efficiency": 0.8683262563170004, "cost_per_hour": 0.1739679736008671, "node_type": "cpu_intensive"}, {"node_id": "node_8", "cpu_capacity": 27, "memory_capacity": 53522.57568090204, "io_capacity": 4074.679000554855, "network_capacity": 1819.742093290595, "energy_efficiency": 0.7651027690111604, "cost_per_hour": 0.24027938898074463, "node_type": "cpu_intensive"}, {"node_id": "node_9", "cpu_capacity": 26, "memory_capacity": 48650.11579226941, "io_capacity": 4300.806723752042, "network_capacity": 1189.1463562811641, "energy_efficiency": 0.8227424608518796, "cost_per_hour": 0.29226689992946575, "node_type": "cpu_intensive"}, {"node_id": "node_10", "cpu_capacity": 22, "memory_capacity": 63297.862302903086, "io_capacity": 2570.1017889338395, "network_capacity": 1885.4379573978144, "energy_efficiency": 0.8470226361679303, "cost_per_hour": 0.2512549545126281, "node_type": "cpu_intensive"}, {"node_id": "node_11", "cpu_capacity": 27, "memory_capacity": 43374.86504460621, "io_capacity": 3789.490375760761, "network_capacity": 1090.5429197830474, "energy_efficiency": 0.8958037555933847, "cost_per_hour": 0.1643310783233715, "node_type": "cpu_intensive"}], "edges": [["task_0", "task_1", {"data_size": 109.46202161149236, "transfer_time": 0}], ["task_0", "task_2", {"data_size": 57.337130043071234, "transfer_time": 0}], ["task_0", "task_3", {"data_size": 149.7849484741053, "transfer_time": 0}], ["task_2", "task_3", {"data_size": 86.70946657962662, "transfer_time": 0}], ["task_0", "task_4", {"data_size": 160.7018107290498, "transfer_time": 0}], ["task_2", "task_5", {"data_size": 96.24810571001137, "transfer_time": 0}], ["task_3", "task_6", {"data_size": 119.53816740388254, "transfer_time": 0}], ["task_6", "task_7", {"data_size": 343.897125995349, "transfer_time": 0}], ["task_4", "task_7", {"data_size": 97.35500129337956, "transfer_time": 0}], ["task_1", "task_8", {"data_size": 326.0443318013725, "transfer_time": 0}], ["task_6", "task_8", {"data_size": 182.78496326867545, "transfer_time": 0}], ["task_4", "task_8", {"data_size": 132.12435892066026, "transfer_time": 0}], ["task_2", "task_9", {"data_size": 73.03190197590934, "transfer_time": 0}], ["task_1", "task_10", {"data_size": 315.99871001788557, "transfer_time": 0}], ["task_2", "task_11", {"data_size": 63.170554039109014, "transfer_time": 0}], ["task_6", "task_11", {"data_size": 380.9178750279866, "transfer_time": 0}], ["task_8", "task_11", {"data_size": 144.3179407973692, "transfer_time": 0}], ["task_11", "task_12", {"data_size": 52.074654139767894, "transfer_time": 0}], ["task_0", "task_13", {"data_size": 159.50000313840707, "transfer_time": 0}], ["task_7", "task_14", {"data_size": 231.14908749953986, "transfer_time": 0}], ["task_3", "task_15", {"data_size": 130.5053242239659, "transfer_time": 0}], ["task_13", "task_16", {"data_size": 148.0595306744654, "transfer_time": 0}], ["task_0", "task_16", {"data_size": 158.36468371029352, "transfer_time": 0}], ["task_10", "task_17", {"data_size": 166.1759722014937, "transfer_time": 0}], ["task_1", "task_17", {"data_size": 190.13807111148336, "transfer_time": 0}], ["task_5", "task_17", {"data_size": 199.41548780812263, "transfer_time": 0}], ["task_1", "task_18", {"data_size": 231.56561060208503, "transfer_time": 0}], ["task_15", "task_18", {"data_size": 264.8737900621836, "transfer_time": 0}], ["task_18", "task_19", {"data_size": 110.64598799673458, "transfer_time": 0}], ["task_17", "task_19", {"data_size": 121.80013312487107, "transfer_time": 0}], ["task_7", "task_20", {"data_size": 150.3776963426186, "transfer_time": 0}], ["task_0", "task_20", {"data_size": 74.29925196558573, "transfer_time": 0}], ["task_14", "task_21", {"data_size": 37.859476518656066, "transfer_time": 0}], ["task_3", "task_22", {"data_size": 136.5588715611849, "transfer_time": 0}], ["task_19", "task_22", {"data_size": 95.75733353477777, "transfer_time": 0}], ["task_5", "task_23", {"data_size": 233.1320051787382, "transfer_time": 0}], ["task_14", "task_23", {"data_size": 29.204942011954117, "transfer_time": 0}], ["task_22", "task_23", {"data_size": 84.49510198409348, "transfer_time": 0}], ["task_21", "task_24", {"data_size": 34.48031459750863, "transfer_time": 0}], ["task_0", "task_24", {"data_size": 56.69132945528236, "transfer_time": 0}], ["task_16", "task_25", {"data_size": 191.13917962410446, "transfer_time": 0}], ["task_15", "task_25", {"data_size": 182.72021655105522, "transfer_time": 0}], ["task_25", "task_26", {"data_size": 122.08916241284889, "transfer_time": 0}], ["task_18", "task_26", {"data_size": 107.36647954315198, "transfer_time": 0}], ["task_18", "task_27", {"data_size": 66.35726225630202, "transfer_time": 0}], ["task_17", "task_27", {"data_size": 240.00337111086918, "transfer_time": 0}], ["task_3", "task_27", {"data_size": 133.91701920896645, "transfer_time": 0}], ["task_6", "task_28", {"data_size": 175.11439677322122, "transfer_time": 0}], ["task_14", "task_29", {"data_size": 17.90128608141363, "transfer_time": 0}], ["task_19", "task_30", {"data_size": 87.36415870195643, "transfer_time": 0}], ["task_22", "task_30", {"data_size": 204.71844830553493, "transfer_time": 0}], ["task_23", "task_31", {"data_size": 45.58426892217257, "transfer_time": 0}], ["task_11", "task_31", {"data_size": 44.688548291271395, "transfer_time": 0}], ["task_27", "task_32", {"data_size": 43.544236976908046, "transfer_time": 0}], ["task_3", "task_32", {"data_size": 189.57595802102927, "transfer_time": 0}], ["task_27", "task_33", {"data_size": 59.922065539095115, "transfer_time": 0}], ["task_13", "task_33", {"data_size": 189.33747272996334, "transfer_time": 0}], ["task_9", "task_34", {"data_size": 44.951310061196764, "transfer_time": 0}], ["task_23", "task_34", {"data_size": 35.91735477784484, "transfer_time": 0}], ["task_31", "task_34", {"data_size": 50.904501255613845, "transfer_time": 0}], ["task_5", "task_35", {"data_size": 97.01645715765055, "transfer_time": 0}], ["task_4", "task_36", {"data_size": 85.42841303499473, "transfer_time": 0}], ["task_9", "task_36", {"data_size": 37.58233988991142, "transfer_time": 0}], ["task_12", "task_37", {"data_size": 231.05502661422685, "transfer_time": 0}], ["task_25", "task_37", {"data_size": 117.23403469716526, "transfer_time": 0}], ["task_32", "task_38", {"data_size": 82.81502023045113, "transfer_time": 0}], ["task_11", "task_38", {"data_size": 96.68456570290064, "transfer_time": 0}], ["task_26", "task_39", {"data_size": 26.989904560234073, "transfer_time": 0}], ["task_28", "task_39", {"data_size": 70.26287771151985, "transfer_time": 0}]], "metadata": {"total_tasks": 40, "total_base_runtime": 11075.64, "total_cpu_requirement": 122.76, "total_memory_requirement": 22107.41, "critical_path_length": 2464.89, "parallelism_degree": 13, "total_edges": 69, "average_base_runtime": 276.89, "average_cpu_requirement": 3.07, "average_memory_requirement": 552.69, "workflow_type": "montage"}}