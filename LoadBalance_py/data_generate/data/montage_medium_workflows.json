{"workflow_type": "montage", "tasks": {"task_0": {"task_type": "mBgModel", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 134.79, "dependencies": []}, "task_1": {"task_type": "mAdd", "cpu_requirement": 5.24, "memory_requirement": 1234.92, "io_requirement": 23.02, "network_requirement": 5, "base_runtime": 360.36, "dependencies": ["task_0"]}, "task_2": {"task_type": "mBackground", "cpu_requirement": 1.91, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 155.96, "dependencies": ["task_1", "task_0"]}, "task_3": {"task_type": "mProject", "cpu_requirement": 4.62, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 354.99, "dependencies": ["task_1"]}, "task_4": {"task_type": "mBackground", "cpu_requirement": 3.11, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 255.19, "dependencies": ["task_0", "task_2", "task_1"]}, "task_5": {"task_type": "mBackground", "cpu_requirement": 3.19, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 302.49, "dependencies": ["task_0", "task_4"]}, "task_6": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 128.75, "dependencies": ["task_1", "task_5"]}, "task_7": {"task_type": "mAdd", "cpu_requirement": 3.87, "memory_requirement": 974.24, "io_requirement": 15.97, "network_requirement": 5, "base_runtime": 306.11, "dependencies": ["task_6", "task_3", "task_0"]}, "task_8": {"task_type": "mBgModel", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 140.5, "dependencies": ["task_4"]}, "task_9": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 85.57, "dependencies": ["task_8", "task_7", "task_3"]}, "task_10": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 157.41, "dependencies": ["task_1"]}, "task_11": {"task_type": "mDiffFit", "cpu_requirement": 2.35, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 252.15, "dependencies": ["task_8", "task_9", "task_2"]}, "task_12": {"task_type": "mProject", "cpu_requirement": 3.67, "memory_requirement": 560.38, "io_requirement": 10, "network_requirement": 5, "base_runtime": 299.04, "dependencies": ["task_11", "task_4", "task_9"]}, "task_13": {"task_type": "mBackground", "cpu_requirement": 4.14, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 356.66, "dependencies": ["task_8"]}, "task_14": {"task_type": "mAdd", "cpu_requirement": 3.34, "memory_requirement": 1616.09, "io_requirement": 15.54, "network_requirement": 5, "base_runtime": 251.92, "dependencies": ["task_8", "task_4", "task_2"]}, "task_15": {"task_type": "mBgModel", "cpu_requirement": 1.3, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 174.3, "dependencies": ["task_14"]}, "task_16": {"task_type": "mDiffFit", "cpu_requirement": 2.13, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 237.1, "dependencies": ["task_13", "task_3"]}, "task_17": {"task_type": "mBackground", "cpu_requirement": 1.73, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 169.62, "dependencies": ["task_7", "task_5", "task_14"]}, "task_18": {"task_type": "mBgModel", "cpu_requirement": 1.56, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 196.01, "dependencies": ["task_15", "task_14"]}, "task_19": {"task_type": "mAdd", "cpu_requirement": 6.39, "memory_requirement": 539.5, "io_requirement": 10, "network_requirement": 5, "base_runtime": 475.18, "dependencies": ["task_14"]}, "task_20": {"task_type": "mProject", "cpu_requirement": 7.36, "memory_requirement": 694.48, "io_requirement": 11.97, "network_requirement": 5, "base_runtime": 543.27, "dependencies": ["task_12", "task_14"]}, "task_21": {"task_type": "mBgModel", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 120.59, "dependencies": ["task_2", "task_8", "task_13"]}, "task_22": {"task_type": "mBackground", "cpu_requirement": 2.3, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 218.38, "dependencies": ["task_8", "task_12", "task_15"]}, "task_23": {"task_type": "mProject", "cpu_requirement": 6.35, "memory_requirement": 595.01, "io_requirement": 10, "network_requirement": 5, "base_runtime": 465.18, "dependencies": ["task_10", "task_12", "task_6"]}, "task_24": {"task_type": "mAdd", "cpu_requirement": 5.91, "memory_requirement": 1325.99, "io_requirement": 16.66, "network_requirement": 5.06, "base_runtime": 413.24, "dependencies": ["task_22", "task_12", "task_18"]}}, "nodes": [{"node_id": "node_0", "cpu_capacity": 25, "memory_capacity": 39466.17929978053, "io_capacity": 3838.892106300402, "network_capacity": 1792.355796271509, "energy_efficiency": 0.8133992530580035, "cost_per_hour": 0.18543624050474633, "node_type": "cpu_intensive"}, {"node_id": "node_1", "cpu_capacity": 25, "memory_capacity": 49122.775433028786, "io_capacity": 2595.772834986976, "network_capacity": 1377.0686306986797, "energy_efficiency": 0.874443826003624, "cost_per_hour": 0.17787933400859465, "node_type": "cpu_intensive"}, {"node_id": "node_2", "cpu_capacity": 19, "memory_capacity": 61370.63134267376, "io_capacity": 3068.536559790812, "network_capacity": 1705.3412071888606, "energy_efficiency": 0.8177237250760192, "cost_per_hour": 0.1763086452809168, "node_type": "cpu_intensive"}, {"node_id": "node_3", "cpu_capacity": 30, "memory_capacity": 61368.230643709685, "io_capacity": 2151.020812986438, "network_capacity": 1334.1068893383672, "energy_efficiency": 0.776022247061103, "cost_per_hour": 0.2340802083562421, "node_type": "cpu_intensive"}, {"node_id": "node_4", "cpu_capacity": 31, "memory_capacity": 33830.124724262496, "io_capacity": 2284.728056876808, "network_capacity": 1069.0087519240733, "energy_efficiency": 0.8148825701105382, "cost_per_hour": 0.21759277711926794, "node_type": "cpu_intensive"}, {"node_id": "node_5", "cpu_capacity": 22, "memory_capacity": 35303.52650128903, "io_capacity": 3837.7884589163987, "network_capacity": 1852.3424716343125, "energy_efficiency": 0.7877020986944205, "cost_per_hour": 0.17311419872028092, "node_type": "cpu_intensive"}, {"node_id": "node_6", "cpu_capacity": 21, "memory_capacity": 61282.077458853724, "io_capacity": 3796.2593602056895, "network_capacity": 1736.7322211628002, "energy_efficiency": 0.7576812105162387, "cost_per_hour": 0.1927990646525692, "node_type": "cpu_intensive"}, {"node_id": "node_7", "cpu_capacity": 27, "memory_capacity": 46931.79438607143, "io_capacity": 2074.8678303563725, "network_capacity": 1478.7844403560662, "energy_efficiency": 0.8258348297461092, "cost_per_hour": 0.24466363681079312, "node_type": "cpu_intensive"}], "edges": [["task_0", "task_1", {"data_size": 92.85815679334453, "transfer_time": 0}], ["task_1", "task_2", {"data_size": 386.15964817210636, "transfer_time": 0}], ["task_0", "task_2", {"data_size": 100.59943145855252, "transfer_time": 0}], ["task_1", "task_3", {"data_size": 727.0837962495959, "transfer_time": 0}], ["task_0", "task_4", {"data_size": 48.350376891957545, "transfer_time": 0}], ["task_2", "task_4", {"data_size": 183.10007674251898, "transfer_time": 0}], ["task_1", "task_4", {"data_size": 687.3372661859066, "transfer_time": 0}], ["task_0", "task_5", {"data_size": 39.99046741713859, "transfer_time": 0}], ["task_4", "task_5", {"data_size": 82.72993798166051, "transfer_time": 0}], ["task_1", "task_6", {"data_size": 384.85046827714325, "transfer_time": 0}], ["task_5", "task_6", {"data_size": 156.99236658476116, "transfer_time": 0}], ["task_6", "task_7", {"data_size": 77.58587767577372, "transfer_time": 0}], ["task_3", "task_7", {"data_size": 427.389789206323, "transfer_time": 0}], ["task_0", "task_7", {"data_size": 85.45678296765149, "transfer_time": 0}], ["task_4", "task_8", {"data_size": 105.44662423875378, "transfer_time": 0}], ["task_8", "task_9", {"data_size": 68.43574324105377, "transfer_time": 0}], ["task_7", "task_9", {"data_size": 307.52901768099736, "transfer_time": 0}], ["task_3", "task_9", {"data_size": 430.0977883563462, "transfer_time": 0}], ["task_1", "task_10", {"data_size": 581.1432977054836, "transfer_time": 0}], ["task_8", "task_11", {"data_size": 151.9956932987345, "transfer_time": 0}], ["task_9", "task_11", {"data_size": 64.40931798879143, "transfer_time": 0}], ["task_2", "task_11", {"data_size": 187.13597845749757, "transfer_time": 0}], ["task_11", "task_12", {"data_size": 73.02993597071287, "transfer_time": 0}], ["task_4", "task_12", {"data_size": 183.37334393118124, "transfer_time": 0}], ["task_9", "task_12", {"data_size": 27.95183208572055, "transfer_time": 0}], ["task_8", "task_13", {"data_size": 103.68318887931767, "transfer_time": 0}], ["task_8", "task_14", {"data_size": 108.17730880371329, "transfer_time": 0}], ["task_4", "task_14", {"data_size": 185.70599221332358, "transfer_time": 0}], ["task_2", "task_14", {"data_size": 168.76101569095903, "transfer_time": 0}], ["task_14", "task_15", {"data_size": 234.76637081999272, "transfer_time": 0}], ["task_13", "task_16", {"data_size": 85.37672082286969, "transfer_time": 0}], ["task_3", "task_16", {"data_size": 163.48112195757108, "transfer_time": 0}], ["task_7", "task_17", {"data_size": 447.7435110461107, "transfer_time": 0}], ["task_5", "task_17", {"data_size": 152.02820667309254, "transfer_time": 0}], ["task_14", "task_17", {"data_size": 221.2080429671171, "transfer_time": 0}], ["task_15", "task_18", {"data_size": 119.1278262039111, "transfer_time": 0}], ["task_14", "task_18", {"data_size": 118.93041396399843, "transfer_time": 0}], ["task_14", "task_19", {"data_size": 222.53391742940613, "transfer_time": 0}], ["task_12", "task_20", {"data_size": 130.49326132799672, "transfer_time": 0}], ["task_14", "task_20", {"data_size": 124.07342907363207, "transfer_time": 0}], ["task_2", "task_21", {"data_size": 156.5581991393715, "transfer_time": 0}], ["task_8", "task_21", {"data_size": 159.36014748096787, "transfer_time": 0}], ["task_13", "task_21", {"data_size": 58.47307741058215, "transfer_time": 0}], ["task_8", "task_22", {"data_size": 135.90167099098443, "transfer_time": 0}], ["task_12", "task_22", {"data_size": 104.94123247073095, "transfer_time": 0}], ["task_15", "task_22", {"data_size": 104.40460267468512, "transfer_time": 0}], ["task_10", "task_23", {"data_size": 39.98448903639238, "transfer_time": 0}], ["task_12", "task_23", {"data_size": 110.43150654676437, "transfer_time": 0}], ["task_6", "task_23", {"data_size": 76.9914532970248, "transfer_time": 0}], ["task_22", "task_24", {"data_size": 283.20185507062536, "transfer_time": 0}], ["task_12", "task_24", {"data_size": 138.54076481902567, "transfer_time": 0}], ["task_18", "task_24", {"data_size": 32.406642830780704, "transfer_time": 0}]], "metadata": {"total_tasks": 25, "total_base_runtime": 6554.77, "total_cpu_requirement": 76.47, "total_memory_requirement": 16244.62, "critical_path_length": 2777.24, "parallelism_degree": 8, "total_edges": 52, "average_base_runtime": 262.19, "average_cpu_requirement": 3.06, "average_memory_requirement": 649.78, "workflow_type": "montage"}}