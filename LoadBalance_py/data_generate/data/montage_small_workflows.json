{"workflow_type": "montage", "tasks": {"task_0": {"task_type": "mAdd", "cpu_requirement": 4.94, "memory_requirement": 1253.59, "io_requirement": 13.98, "network_requirement": 5, "base_runtime": 377.09, "dependencies": []}, "task_1": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 81.24, "dependencies": ["task_0"]}, "task_2": {"task_type": "mBgModel", "cpu_requirement": 1.41, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 190.44, "dependencies": ["task_1", "task_0"]}, "task_3": {"task_type": "mBackground", "cpu_requirement": 3.75, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 312.49, "dependencies": ["task_1", "task_2"]}, "task_4": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 86.74, "dependencies": ["task_2"]}, "task_5": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 124.01, "dependencies": ["task_4", "task_3"]}, "task_6": {"task_type": "mProject", "cpu_requirement": 2.86, "memory_requirement": 598.23, "io_requirement": 10, "network_requirement": 5, "base_runtime": 233.3, "dependencies": ["task_3"]}, "task_7": {"task_type": "mAdd", "cpu_requirement": 4.15, "memory_requirement": 576.16, "io_requirement": 13.39, "network_requirement": 5, "base_runtime": 307.8, "dependencies": ["task_5"]}, "task_8": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 145.38, "dependencies": ["task_4", "task_3"]}, "task_9": {"task_type": "mBackground", "cpu_requirement": 3.36, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 297.71, "dependencies": ["task_8"]}, "task_10": {"task_type": "mBgModel", "cpu_requirement": 1.09, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 155.85, "dependencies": ["task_5"]}, "task_11": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 60.54, "dependencies": ["task_1", "task_0"]}, "task_12": {"task_type": "mConcatFit", "cpu_requirement": 1.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 163.32, "dependencies": ["task_1", "task_4"]}, "task_13": {"task_type": "mBackground", "cpu_requirement": 2.4, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 211.61, "dependencies": ["task_2", "task_10", "task_4"]}, "task_14": {"task_type": "mProject", "cpu_requirement": 2.38, "memory_requirement": 680.76, "io_requirement": 10, "network_requirement": 5, "base_runtime": 202.94, "dependencies": ["task_8"]}}, "nodes": [{"node_id": "node_0", "cpu_capacity": 18, "memory_capacity": 61891.561511383334, "io_capacity": 3233.615695353519, "network_capacity": 1559.5733249660552, "energy_efficiency": 0.8700753514646318, "cost_per_hour": 0.16169636315715963, "node_type": "cpu_intensive"}, {"node_id": "node_1", "cpu_capacity": 27, "memory_capacity": 52919.21526900875, "io_capacity": 4582.397283030461, "network_capacity": 1711.6095472075167, "energy_efficiency": 0.8484007510441433, "cost_per_hour": 0.24110123378411585, "node_type": "cpu_intensive"}, {"node_id": "node_2", "cpu_capacity": 19, "memory_capacity": 56151.91591311208, "io_capacity": 3313.6498230532743, "network_capacity": 1102.9840600901955, "energy_efficiency": 0.7902191590132587, "cost_per_hour": 0.16679442161850758, "node_type": "cpu_intensive"}, {"node_id": "node_3", "cpu_capacity": 28, "memory_capacity": 62061.66915339499, "io_capacity": 2719.427580786802, "network_capacity": 1919.9229356010883, "energy_efficiency": 0.7758085926305229, "cost_per_hour": 0.20568572840321697, "node_type": "cpu_intensive"}, {"node_id": "node_4", "cpu_capacity": 20, "memory_capacity": 64448.60137280502, "io_capacity": 3591.635693632578, "network_capacity": 1599.7003714049506, "energy_efficiency": 0.8244089040764301, "cost_per_hour": 0.21134585534487685, "node_type": "cpu_intensive"}, {"node_id": "node_5", "cpu_capacity": 17, "memory_capacity": 40112.42583564392, "io_capacity": 2025.8092486375906, "network_capacity": 1503.195716338755, "energy_efficiency": 0.7814382039021874, "cost_per_hour": 0.22492001386434182, "node_type": "cpu_intensive"}], "edges": [["task_0", "task_1", {"data_size": 241.41569163837605, "transfer_time": 0}], ["task_1", "task_2", {"data_size": 14.733806550909007, "transfer_time": 0}], ["task_0", "task_2", {"data_size": 210.38776406609603, "transfer_time": 0}], ["task_1", "task_3", {"data_size": 21.53042962912853, "transfer_time": 0}], ["task_2", "task_3", {"data_size": 36.70243553722395, "transfer_time": 0}], ["task_2", "task_4", {"data_size": 42.847457884230764, "transfer_time": 0}], ["task_4", "task_5", {"data_size": 32.27704773447718, "transfer_time": 0}], ["task_3", "task_5", {"data_size": 92.2697100525573, "transfer_time": 0}], ["task_3", "task_6", {"data_size": 138.15362684801988, "transfer_time": 0}], ["task_5", "task_7", {"data_size": 22.975758331377385, "transfer_time": 0}], ["task_4", "task_8", {"data_size": 20.871049842667965, "transfer_time": 0}], ["task_3", "task_8", {"data_size": 90.65545251836873, "transfer_time": 0}], ["task_8", "task_9", {"data_size": 49.19453011411002, "transfer_time": 0}], ["task_5", "task_10", {"data_size": 13.100869492001516, "transfer_time": 0}], ["task_1", "task_11", {"data_size": 17.367604896745615, "transfer_time": 0}], ["task_0", "task_11", {"data_size": 254.30594748841318, "transfer_time": 0}], ["task_1", "task_12", {"data_size": 36.143727270660214, "transfer_time": 0}], ["task_4", "task_12", {"data_size": 30.51830465208158, "transfer_time": 0}], ["task_2", "task_13", {"data_size": 71.87161794085345, "transfer_time": 0}], ["task_10", "task_13", {"data_size": 39.71646334339907, "transfer_time": 0}], ["task_4", "task_13", {"data_size": 30.169347318686594, "transfer_time": 0}], ["task_8", "task_14", {"data_size": 31.620977723584733, "transfer_time": 0}]], "metadata": {"total_tasks": 15, "total_base_runtime": 2950.47, "total_cpu_requirement": 32.35, "total_memory_requirement": 8740.74, "critical_path_length": 1075.65, "parallelism_degree": 5, "total_edges": 22, "average_base_runtime": 196.7, "average_cpu_requirement": 2.16, "average_memory_requirement": 582.72, "workflow_type": "montage"}}