{"workflow_type": "sipht", "tasks": {"task_0": {"task_type": "cmsearch", "cpu_requirement": 7.52, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 714.34, "dependencies": []}, "task_1": {"task_type": "rnafold", "cpu_requirement": 5.61, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 598.89, "dependencies": ["task_0"]}, "task_2": {"task_type": "rnafold", "cpu_requirement": 5.67, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 531.78, "dependencies": ["task_0"]}, "task_3": {"task_type": "infernal", "cpu_requirement": 10.47, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 830.22, "dependencies": ["task_1", "task_0", "task_2"]}, "task_4": {"task_type": "infernal", "cpu_requirement": 11.69, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 886.89, "dependencies": ["task_3", "task_2"]}, "task_5": {"task_type": "blast", "cpu_requirement": 16.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 2080.58, "dependencies": ["task_2"]}, "task_6": {"task_type": "clus<PERSON>w", "cpu_requirement": 10.93, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 971.2, "dependencies": ["task_1", "task_3"]}, "task_7": {"task_type": "rnafold", "cpu_requirement": 4.19, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 383.3, "dependencies": ["task_4", "task_1"]}, "task_8": {"task_type": "clus<PERSON>w", "cpu_requirement": 8.57, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 783.16, "dependencies": ["task_7"]}, "task_9": {"task_type": "infernal", "cpu_requirement": 8.46, "memory_requirement": 562.22, "io_requirement": 10, "network_requirement": 5, "base_runtime": 678.23, "dependencies": ["task_6"]}, "task_10": {"task_type": "cmsearch", "cpu_requirement": 6.88, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 663.49, "dependencies": ["task_0", "task_2"]}, "task_11": {"task_type": "infernal", "cpu_requirement": 16.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 1387.87, "dependencies": ["task_10", "task_5"]}, "task_12": {"task_type": "rnafold", "cpu_requirement": 7.76, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 762.99, "dependencies": ["task_5", "task_4"]}, "task_13": {"task_type": "blast", "cpu_requirement": 16.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 2012.55, "dependencies": ["task_2"]}, "task_14": {"task_type": "infernal", "cpu_requirement": 16.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 1285.84, "dependencies": ["task_13", "task_8", "task_2"]}, "task_15": {"task_type": "clus<PERSON>w", "cpu_requirement": 11.12, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 1092.56, "dependencies": ["task_4", "task_9", "task_7"]}, "task_16": {"task_type": "clus<PERSON>w", "cpu_requirement": 7.83, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 701.89, "dependencies": ["task_13", "task_0", "task_15"]}, "task_17": {"task_type": "genome_scan", "cpu_requirement": 16.0, "memory_requirement": 4386.42, "io_requirement": 30.22, "network_requirement": 10.45, "base_runtime": 2856.87, "dependencies": ["task_5", "task_16"]}, "task_18": {"task_type": "infernal", "cpu_requirement": 16.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 1473.59, "dependencies": ["task_0", "task_6", "task_14"]}, "task_19": {"task_type": "infernal", "cpu_requirement": 14.65, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 1191.76, "dependencies": ["task_2", "task_11", "task_17"]}, "task_20": {"task_type": "infernal", "cpu_requirement": 16.0, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 1484.42, "dependencies": ["task_0"]}, "task_21": {"task_type": "infernal", "cpu_requirement": 10.33, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 836.18, "dependencies": ["task_10", "task_5"]}, "task_22": {"task_type": "cmsearch", "cpu_requirement": 4.98, "memory_requirement": 512, "io_requirement": 10, "network_requirement": 5, "base_runtime": 470.49, "dependencies": ["task_12", "task_0"]}, "task_23": {"task_type": "blast", "cpu_requirement": 16.0, "memory_requirement": 1082.02, "io_requirement": 12.78, "network_requirement": 5, "base_runtime": 1559.39, "dependencies": ["task_22"]}, "task_24": {"task_type": "genome_scan", "cpu_requirement": 16.0, "memory_requirement": 2505.33, "io_requirement": 32.34, "network_requirement": 13.76, "base_runtime": 2586.96, "dependencies": ["task_10", "task_0"]}}, "nodes": [{"node_id": "node_0", "cpu_capacity": 43, "memory_capacity": 24948.475524258094, "io_capacity": 1479.402114291514, "network_capacity": 975.697085636114, "energy_efficiency": 0.9197776237733827, "cost_per_hour": 0.2252613954273151, "node_type": "compute_intensive"}, {"node_id": "node_1", "cpu_capacity": 34, "memory_capacity": 26800.133829459133, "io_capacity": 2382.9389017190506, "network_capacity": 839.8571175732018, "energy_efficiency": 0.8507100465358199, "cost_per_hour": 0.203917359922521, "node_type": "compute_intensive"}, {"node_id": "node_2", "cpu_capacity": 56, "memory_capacity": 19423.487908447263, "io_capacity": 1225.0642635773734, "network_capacity": 900.1293273335684, "energy_efficiency": 0.8984382225510561, "cost_per_hour": 0.3473317921498448, "node_type": "compute_intensive"}, {"node_id": "node_3", "cpu_capacity": 60, "memory_capacity": 28414.18632850689, "io_capacity": 2215.0870693601637, "network_capacity": 756.953449355392, "energy_efficiency": 0.8816721098654935, "cost_per_hour": 0.2026342098629254, "node_type": "compute_intensive"}, {"node_id": "node_4", "cpu_capacity": 64, "memory_capacity": 30958.107785899505, "io_capacity": 2213.9325984906613, "network_capacity": 668.0161836651364, "energy_efficiency": 0.8829119193815773, "cost_per_hour": 0.25441153382186166, "node_type": "compute_intensive"}, {"node_id": "node_5", "cpu_capacity": 61, "memory_capacity": 27809.819910056143, "io_capacity": 1602.6594878774745, "network_capacity": 610.155739049754, "energy_efficiency": 0.8604127977309521, "cost_per_hour": 0.3371367897027926, "node_type": "compute_intensive"}, {"node_id": "node_6", "cpu_capacity": 56, "memory_capacity": 21092.923045032556, "io_capacity": 1068.5612517452187, "network_capacity": 512.7602508849297, "energy_efficiency": 0.8846672099758432, "cost_per_hour": 0.3180047796378203, "node_type": "compute_intensive"}, {"node_id": "node_7", "cpu_capacity": 47, "memory_capacity": 25781.395662821127, "io_capacity": 1764.9814823629233, "network_capacity": 793.5964303446037, "energy_efficiency": 0.9027166627723557, "cost_per_hour": 0.22044559311727924, "node_type": "compute_intensive"}], "edges": [["task_0", "task_1", {"data_size": 80.95468408179859, "transfer_time": 0}], ["task_0", "task_2", {"data_size": 59.51443870384669, "transfer_time": 0}], ["task_1", "task_3", {"data_size": 155.39272561585165, "transfer_time": 0}], ["task_0", "task_3", {"data_size": 76.90061340835733, "transfer_time": 0}], ["task_2", "task_3", {"data_size": 146.25545993909466, "transfer_time": 0}], ["task_3", "task_4", {"data_size": 220.86931020859038, "transfer_time": 0}], ["task_2", "task_4", {"data_size": 213.35867602980753, "transfer_time": 0}], ["task_2", "task_5", {"data_size": 127.29369271391451, "transfer_time": 0}], ["task_1", "task_6", {"data_size": 99.04410181917547, "transfer_time": 0}], ["task_3", "task_6", {"data_size": 142.6426089969777, "transfer_time": 0}], ["task_4", "task_7", {"data_size": 88.95358665829991, "transfer_time": 0}], ["task_1", "task_7", {"data_size": 105.84094724626878, "transfer_time": 0}], ["task_7", "task_8", {"data_size": 216.88981316954576, "transfer_time": 0}], ["task_6", "task_9", {"data_size": 303.10158288679656, "transfer_time": 0}], ["task_0", "task_10", {"data_size": 137.82880233161913, "transfer_time": 0}], ["task_2", "task_10", {"data_size": 172.26455748167893, "transfer_time": 0}], ["task_10", "task_11", {"data_size": 124.24998517758317, "transfer_time": 0}], ["task_5", "task_11", {"data_size": 222.5304134023961, "transfer_time": 0}], ["task_5", "task_12", {"data_size": 319.04211052848586, "transfer_time": 0}], ["task_4", "task_12", {"data_size": 93.87788153474919, "transfer_time": 0}], ["task_2", "task_13", {"data_size": 142.46524362460883, "transfer_time": 0}], ["task_13", "task_14", {"data_size": 812.7507853211331, "transfer_time": 0}], ["task_8", "task_14", {"data_size": 223.83144267532646, "transfer_time": 0}], ["task_2", "task_14", {"data_size": 85.1520882903305, "transfer_time": 0}], ["task_4", "task_15", {"data_size": 70.47136288472495, "transfer_time": 0}], ["task_9", "task_15", {"data_size": 79.37703066603024, "transfer_time": 0}], ["task_7", "task_15", {"data_size": 79.34616924955277, "transfer_time": 0}], ["task_13", "task_16", {"data_size": 426.66185312902604, "transfer_time": 0}], ["task_0", "task_16", {"data_size": 85.68603289272941, "transfer_time": 0}], ["task_15", "task_16", {"data_size": 189.12496391547464, "transfer_time": 0}], ["task_5", "task_17", {"data_size": 219.7767073455968, "transfer_time": 0}], ["task_16", "task_17", {"data_size": 120.586205220288, "transfer_time": 0}], ["task_0", "task_18", {"data_size": 137.5621736751218, "transfer_time": 0}], ["task_6", "task_18", {"data_size": 334.7675428328719, "transfer_time": 0}], ["task_14", "task_18", {"data_size": 179.90131565685786, "transfer_time": 0}], ["task_2", "task_19", {"data_size": 176.0365961770472, "transfer_time": 0}], ["task_11", "task_19", {"data_size": 90.70166273323105, "transfer_time": 0}], ["task_17", "task_19", {"data_size": 501.5948930867445, "transfer_time": 0}], ["task_0", "task_20", {"data_size": 96.32845062875911, "transfer_time": 0}], ["task_10", "task_21", {"data_size": 101.02603260944768, "transfer_time": 0}], ["task_5", "task_21", {"data_size": 328.8162089345219, "transfer_time": 0}], ["task_12", "task_22", {"data_size": 213.38041783531398, "transfer_time": 0}], ["task_0", "task_22", {"data_size": 75.20658561270346, "transfer_time": 0}], ["task_22", "task_23", {"data_size": 98.40971833890852, "transfer_time": 0}], ["task_10", "task_24", {"data_size": 221.25521826905808, "transfer_time": 0}], ["task_0", "task_24", {"data_size": 155.28758145630547, "transfer_time": 0}]], "metadata": {"total_tasks": 25, "total_base_runtime": 28825.44, "total_cpu_requirement": 280.65, "total_memory_requirement": 19287.99, "critical_path_length": 8921.61, "parallelism_degree": 8, "total_edges": 46, "average_base_runtime": 1153.02, "average_cpu_requirement": 11.23, "average_memory_requirement": 771.52, "workflow_type": "sipht"}}