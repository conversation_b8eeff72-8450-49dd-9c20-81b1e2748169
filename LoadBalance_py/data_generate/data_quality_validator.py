"""
负载均衡仿真系统 - 数据质量验证器
验证生成数据的真实性和可靠性，生成详细的质量报告
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Tuple
from datetime import datetime
import pandas as pd
from realistic_data_model import RealisticDataModel


class DataQualityValidator:
    """数据质量验证器"""
    
    def __init__(self):
        self.realistic_model = RealisticDataModel()
        self.validation_results = {}
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def load_workflow_data(self, filename: str) -> Dict[str, Any]:
        """加载工作流数据"""
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def validate_workflow_data(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证工作流数据的真实性和可靠性"""
        validation_results = {
            'workflow_info': {},
            'task_analysis': {},
            'node_analysis': {},
            'compatibility_analysis': {},
            'resource_utilization': {},
            'execution_time_analysis': {},
            'data_quality_metrics': {},
            'recommendations': []
        }
        
        # 基本信息
        validation_results['workflow_info'] = {
            'workflow_type': workflow_data['workflow_type'],
            'total_tasks': len(workflow_data['tasks']),
            'total_nodes': len(workflow_data['nodes']),
            'total_edges': len(workflow_data['edges']),
            'generated_at': workflow_data.get('generated_at', 'Unknown')
        }
        
        # 任务分析
        tasks = workflow_data['tasks']
        task_analysis = {
            'task_types': {},
            'resource_distributions': {},
            'runtime_statistics': {},
            'resource_correlations': {}
        }
        
        # 统计任务类型
        task_type_counts = {}
        for task in tasks.values():
            task_type = task['task_type']
            task_type_counts[task_type] = task_type_counts.get(task_type, 0) + 1
        
        task_analysis['task_types'] = task_type_counts
        
        # 资源分布统计
        cpu_reqs = [task['cpu_requirement'] for task in tasks.values()]
        memory_reqs = [task['memory_requirement'] for task in tasks.values()]
        io_reqs = [task['io_requirement'] for task in tasks.values()]
        network_reqs = [task['network_requirement'] for task in tasks.values()]
        runtimes = [task['base_runtime'] for task in tasks.values()]
        
        task_analysis['resource_distributions'] = {
            'cpu_requirement': {
                'mean': np.mean(cpu_reqs),
                'std': np.std(cpu_reqs),
                'min': np.min(cpu_reqs),
                'max': np.max(cpu_reqs),
                'median': np.median(cpu_reqs)
            },
            'memory_requirement': {
                'mean': np.mean(memory_reqs),
                'std': np.std(memory_reqs),
                'min': np.min(memory_reqs),
                'max': np.max(memory_reqs),
                'median': np.median(memory_reqs)
            },
            'io_requirement': {
                'mean': np.mean(io_reqs),
                'std': np.std(io_reqs),
                'min': np.min(io_reqs),
                'max': np.max(io_reqs),
                'median': np.median(io_reqs)
            },
            'network_requirement': {
                'mean': np.mean(network_reqs),
                'std': np.std(network_reqs),
                'min': np.min(network_reqs),
                'max': np.max(network_reqs),
                'median': np.median(network_reqs)
            },
            'base_runtime': {
                'mean': np.mean(runtimes),
                'std': np.std(runtimes),
                'min': np.min(runtimes),
                'max': np.max(runtimes),
                'median': np.median(runtimes)
            }
        }
        
        # 资源相关性分析
        task_analysis['resource_correlations'] = {
            'cpu_memory_correlation': np.corrcoef(cpu_reqs, memory_reqs)[0, 1],
            'cpu_io_correlation': np.corrcoef(cpu_reqs, io_reqs)[0, 1],
            'cpu_network_correlation': np.corrcoef(cpu_reqs, network_reqs)[0, 1],
            'memory_io_correlation': np.corrcoef(memory_reqs, io_reqs)[0, 1],
            'memory_network_correlation': np.corrcoef(memory_reqs, network_reqs)[0, 1],
            'io_network_correlation': np.corrcoef(io_reqs, network_reqs)[0, 1]
        }
        
        validation_results['task_analysis'] = task_analysis
        
        # 节点分析
        nodes = workflow_data['nodes']
        node_analysis = {
            'node_types': {},
            'capacity_distributions': {},
            'capacity_estimates': {}
        }
        
        # 统计节点类型
        node_type_counts = {}
        for node in nodes:
            node_type = node['node_type']
            node_type_counts[node_type] = node_type_counts.get(node_type, 0) + 1
        
        node_analysis['node_types'] = node_type_counts
        
        # 容量分布统计
        cpu_caps = [node['cpu_capacity'] for node in nodes]
        memory_caps = [node['memory_capacity'] for node in nodes]
        io_caps = [node['io_capacity'] for node in nodes]
        network_caps = [node['network_capacity'] for node in nodes]
        
        node_analysis['capacity_distributions'] = {
            'cpu_capacity': {
                'mean': np.mean(cpu_caps),
                'std': np.std(cpu_caps),
                'min': np.min(cpu_caps),
                'max': np.max(cpu_caps),
                'median': np.median(cpu_caps)
            },
            'memory_capacity': {
                'mean': np.mean(memory_caps),
                'std': np.std(memory_caps),
                'min': np.min(memory_caps),
                'max': np.max(memory_caps),
                'median': np.median(memory_caps)
            },
            'io_capacity': {
                'mean': np.mean(io_caps),
                'std': np.std(io_caps),
                'min': np.min(io_caps),
                'max': np.max(io_caps),
                'median': np.median(io_caps)
            },
            'network_capacity': {
                'mean': np.mean(network_caps),
                'std': np.std(network_caps),
                'min': np.min(network_caps),
                'max': np.max(network_caps),
                'median': np.median(network_caps)
            }
        }
        
        # 估算节点容量
        for node in nodes:
            node_dict = {
                'node_id': node['node_id'],
                'cpu_capacity': node['cpu_capacity'],
                'memory_capacity': node['memory_capacity'],
                'io_capacity': node['io_capacity'],
                'network_capacity': node['network_capacity'],
                'node_type': node['node_type']
            }
            capacity_estimate = self.realistic_model.estimate_node_capacity(node_dict)
            node_analysis['capacity_estimates'][node['node_id']] = capacity_estimate
        
        validation_results['node_analysis'] = node_analysis
        
        # 兼容性分析
        compatibility_analysis = {
            'task_node_compatibility': {},
            'overall_compatibility': {}
        }
        
        compatible_tasks = 0
        total_compatible_pairs = 0
        
        for task_id, task in tasks.items():
            compatible_nodes = 0
            for node in nodes:
                task_dict = {
                    'task_id': task['task_id'],
                    'base_runtime': task['base_runtime'],
                    'cpu_requirement': task['cpu_requirement'],
                    'memory_requirement': task['memory_requirement'],
                    'io_requirement': task['io_requirement'],
                    'network_requirement': task['network_requirement'],
                    'task_type': task['task_type']
                }
                
                node_dict = {
                    'node_id': node['node_id'],
                    'cpu_capacity': node['cpu_capacity'],
                    'memory_capacity': node['memory_capacity'],
                    'io_capacity': node['io_capacity'],
                    'network_capacity': node['network_capacity'],
                    'node_type': node['node_type']
                }
                
                if self.realistic_model.validate_task_node_compatibility(task_dict, node_dict):
                    compatible_nodes += 1
                    total_compatible_pairs += 1
            
            if compatible_nodes > 0:
                compatible_tasks += 1
            
            compatibility_analysis['task_node_compatibility'][task_id] = {
                'compatible_nodes': compatible_nodes,
                'total_nodes': len(nodes),
                'compatibility_ratio': compatible_nodes / len(nodes)
            }
        
        compatibility_analysis['overall_compatibility'] = {
            'compatible_tasks': compatible_tasks,
            'total_tasks': len(tasks),
            'task_compatibility_ratio': compatible_tasks / len(tasks),
            'total_compatible_pairs': total_compatible_pairs,
            'total_possible_pairs': len(tasks) * len(nodes),
            'pair_compatibility_ratio': total_compatible_pairs / (len(tasks) * len(nodes))
        }
        
        validation_results['compatibility_analysis'] = compatibility_analysis
        
        # 资源利用率分析
        total_cpu_capacity = sum(node['cpu_capacity'] for node in nodes)
        total_memory_capacity = sum(node['memory_capacity'] for node in nodes)
        total_io_capacity = sum(node['io_capacity'] for node in nodes)
        total_network_capacity = sum(node['network_capacity'] for node in nodes)
        
        total_cpu_requirement = sum(task['cpu_requirement'] for task in tasks.values())
        total_memory_requirement = sum(task['memory_requirement'] for task in tasks.values())
        total_io_requirement = sum(task['io_requirement'] for task in tasks.values())
        total_network_requirement = sum(task['network_requirement'] for task in tasks.values())
        
        validation_results['resource_utilization'] = {
            'cpu_utilization': total_cpu_requirement / total_cpu_capacity if total_cpu_capacity > 0 else 0,
            'memory_utilization': total_memory_requirement / total_memory_capacity if total_memory_capacity > 0 else 0,
            'io_utilization': total_io_requirement / total_io_capacity if total_io_capacity > 0 else 0,
            'network_utilization': total_network_requirement / total_network_capacity if total_network_capacity > 0 else 0,
            'total_cpu_capacity': total_cpu_capacity,
            'total_memory_capacity': total_memory_capacity,
            'total_io_capacity': total_io_capacity,
            'total_network_capacity': total_network_capacity,
            'total_cpu_requirement': total_cpu_requirement,
            'total_memory_requirement': total_memory_requirement,
            'total_io_requirement': total_io_requirement,
            'total_network_requirement': total_network_requirement
        }
        
        # 执行时间分析
        execution_times = []
        for task in tasks.values():
            task_dict = {
                'task_id': task['task_id'],
                'base_runtime': task['base_runtime'],
                'cpu_requirement': task['cpu_requirement'],
                'memory_requirement': task['memory_requirement'],
                'io_requirement': task['io_requirement'],
                'network_requirement': task['network_requirement'],
                'task_type': task['task_type']
            }
            
            for node in nodes:
                node_dict = {
                    'node_id': node['node_id'],
                    'cpu_capacity': node['cpu_capacity'],
                    'memory_capacity': node['memory_capacity'],
                    'io_capacity': node['io_capacity'],
                    'network_capacity': node['network_capacity'],
                    'node_type': node['node_type']
                }
                
                if self.realistic_model.validate_task_node_compatibility(task_dict, node_dict):
                    exec_time = self.realistic_model.calculate_realistic_execution_time(task_dict, node_dict)
                    execution_times.append(exec_time)
        
        if execution_times:
            validation_results['execution_time_analysis'] = {
                'min_execution_time': min(execution_times),
                'max_execution_time': max(execution_times),
                'avg_execution_time': np.mean(execution_times),
                'std_execution_time': np.std(execution_times),
                'total_execution_times': len(execution_times),
                'execution_time_distribution': {
                    'percentiles': {
                        '25%': np.percentile(execution_times, 25),
                        '50%': np.percentile(execution_times, 50),
                        '75%': np.percentile(execution_times, 75),
                        '90%': np.percentile(execution_times, 90),
                        '95%': np.percentile(execution_times, 95)
                    }
                }
            }
        
        # 数据质量指标
        compatibility_ratio = compatibility_analysis['overall_compatibility']['task_compatibility_ratio']
        resource_utilization = validation_results['resource_utilization']
        avg_resource_utilization = np.mean([
            resource_utilization['cpu_utilization'],
            resource_utilization['memory_utilization'],
            resource_utilization['io_utilization'],
            resource_utilization['network_utilization']
        ])
        
        validation_results['data_quality_metrics'] = {
            'compatibility_score': compatibility_ratio,
            'resource_utilization_score': avg_resource_utilization,
            'overall_quality_score': (compatibility_ratio + avg_resource_utilization) / 2,
            'data_realism_score': self._calculate_realism_score(validation_results),
            'data_reliability_score': self._calculate_reliability_score(validation_results)
        }
        
        # 生成建议
        validation_results['recommendations'] = self._generate_recommendations(validation_results)
        
        return validation_results
    
    def _calculate_realism_score(self, validation_results: Dict[str, Any]) -> float:
        """计算数据真实性分数"""
        task_analysis = validation_results['task_analysis']
        node_analysis = validation_results['node_analysis']
        
        # 检查资源分布是否合理
        cpu_req_mean = task_analysis['resource_distributions']['cpu_requirement']['mean']
        memory_req_mean = task_analysis['resource_distributions']['memory_requirement']['mean']
        io_req_mean = task_analysis['resource_distributions']['io_requirement']['mean']
        
        cpu_cap_mean = node_analysis['capacity_distributions']['cpu_capacity']['mean']
        memory_cap_mean = node_analysis['capacity_distributions']['memory_capacity']['mean']
        io_cap_mean = node_analysis['capacity_distributions']['io_capacity']['mean']
        
        # 计算资源比例合理性
        cpu_ratio = cpu_req_mean / cpu_cap_mean if cpu_cap_mean > 0 else 0
        memory_ratio = memory_req_mean / memory_cap_mean if memory_cap_mean > 0 else 0
        io_ratio = io_req_mean / io_cap_mean if io_cap_mean > 0 else 0
        
        # 理想比例应该在0.1-0.8之间
        cpu_realism = 1.0 if 0.1 <= cpu_ratio <= 0.8 else max(0, 1 - abs(cpu_ratio - 0.45) / 0.45)
        memory_realism = 1.0 if 0.1 <= memory_ratio <= 0.8 else max(0, 1 - abs(memory_ratio - 0.45) / 0.45)
        io_realism = 1.0 if 0.1 <= io_ratio <= 0.8 else max(0, 1 - abs(io_ratio - 0.45) / 0.45)
        
        return (cpu_realism + memory_realism + io_realism) / 3
    
    def _calculate_reliability_score(self, validation_results: Dict[str, Any]) -> float:
        """计算数据可靠性分数"""
        compatibility_analysis = validation_results['compatibility_analysis']
        resource_utilization = validation_results['resource_utilization']
        
        # 兼容性分数
        compatibility_score = compatibility_analysis['overall_compatibility']['task_compatibility_ratio']
        
        # 资源利用率分数（适中的利用率更可靠）
        avg_utilization = np.mean([
            resource_utilization['cpu_utilization'],
            resource_utilization['memory_utilization'],
            resource_utilization['io_utilization'],
            resource_utilization['network_utilization']
        ])
        
        # 理想的资源利用率应该在0.3-0.8之间
        utilization_score = 1.0 if 0.3 <= avg_utilization <= 0.8 else max(0, 1 - abs(avg_utilization - 0.55) / 0.55)
        
        return (compatibility_score + utilization_score) / 2
    
    def _generate_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """生成数据质量改进建议"""
        recommendations = []
        
        compatibility_analysis = validation_results['compatibility_analysis']
        resource_utilization = validation_results['resource_utilization']
        quality_metrics = validation_results['data_quality_metrics']
        
        compatibility_ratio = compatibility_analysis['overall_compatibility']['task_compatibility_ratio']
        avg_resource_utilization = np.mean([
            resource_utilization['cpu_utilization'],
            resource_utilization['memory_utilization'],
            resource_utilization['io_utilization'],
            resource_utilization['network_utilization']
        ])
        
        if compatibility_ratio < 0.8:
            recommendations.append("任务节点兼容性较低，建议调整任务资源需求或增加节点容量")
        
        if resource_utilization['cpu_utilization'] > 0.9:
            recommendations.append("CPU利用率过高，建议增加CPU密集型节点或减少任务CPU需求")
        
        if resource_utilization['memory_utilization'] > 0.9:
            recommendations.append("内存利用率过高，建议增加内存密集型节点或减少任务内存需求")
        
        if resource_utilization['io_utilization'] > 0.9:
            recommendations.append("I/O利用率过高，建议增加数据密集型节点或减少任务I/O需求")
        
        if resource_utilization['network_utilization'] > 0.9:
            recommendations.append("网络利用率过高，建议增加网络密集型节点或减少任务网络需求")
        
        if avg_resource_utilization < 0.3:
            recommendations.append("资源利用率过低，建议减少节点容量或增加任务资源需求")
        
        if quality_metrics['data_realism_score'] < 0.7:
            recommendations.append("数据真实性较低，建议调整任务和节点的资源比例")
        
        if quality_metrics['data_reliability_score'] < 0.7:
            recommendations.append("数据可靠性较低，建议优化任务节点匹配和资源分配")
        
        if not recommendations:
            recommendations.append("数据质量良好，适合用于负载均衡仿真实验")
        
        return recommendations
    
    def generate_quality_report(self, validation_results: Dict[str, Any], output_filename: str = None) -> str:
        """生成数据质量报告"""
        if not output_filename:
            workflow_type = validation_results['workflow_info']['workflow_type']
            output_filename = f"{workflow_type}_data_quality_report.txt"
        
        with open(output_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("负载均衡仿真系统 - 数据质量验证报告\n")
            f.write("=" * 80 + "\n\n")
            
            # 基本信息
            workflow_info = validation_results['workflow_info']
            f.write("1. 工作流基本信息\n")
            f.write("-" * 40 + "\n")
            f.write(f"工作流类型: {workflow_info['workflow_type']}\n")
            f.write(f"任务数量: {workflow_info['total_tasks']}\n")
            f.write(f"节点数量: {workflow_info['total_nodes']}\n")
            f.write(f"依赖关系数量: {workflow_info['total_edges']}\n")
            f.write(f"生成时间: {workflow_info['generated_at']}\n\n")
            
            # 数据质量指标
            quality_metrics = validation_results['data_quality_metrics']
            f.write("2. 数据质量指标\n")
            f.write("-" * 40 + "\n")
            f.write(f"兼容性分数: {quality_metrics['compatibility_score']:.3f}\n")
            f.write(f"资源利用率分数: {quality_metrics['resource_utilization_score']:.3f}\n")
            f.write(f"整体质量分数: {quality_metrics['overall_quality_score']:.3f}\n")
            f.write(f"数据真实性分数: {quality_metrics['data_realism_score']:.3f}\n")
            f.write(f"数据可靠性分数: {quality_metrics['data_reliability_score']:.3f}\n\n")
            
            # 兼容性分析
            compatibility_analysis = validation_results['compatibility_analysis']
            overall_compatibility = compatibility_analysis['overall_compatibility']
            f.write("3. 任务节点兼容性分析\n")
            f.write("-" * 40 + "\n")
            f.write(f"兼容任务数: {overall_compatibility['compatible_tasks']}\n")
            f.write(f"总任务数: {overall_compatibility['total_tasks']}\n")
            f.write(f"任务兼容性比例: {overall_compatibility['task_compatibility_ratio']:.3f}\n")
            f.write(f"兼容任务节点对数: {overall_compatibility['total_compatible_pairs']}\n")
            f.write(f"总可能对数: {overall_compatibility['total_possible_pairs']}\n")
            f.write(f"配对兼容性比例: {overall_compatibility['pair_compatibility_ratio']:.3f}\n\n")
            
            # 资源利用率分析
            resource_utilization = validation_results['resource_utilization']
            f.write("4. 资源利用率分析\n")
            f.write("-" * 40 + "\n")
            f.write(f"CPU利用率: {resource_utilization['cpu_utilization']:.3f}\n")
            f.write(f"内存利用率: {resource_utilization['memory_utilization']:.3f}\n")
            f.write(f"I/O利用率: {resource_utilization['io_utilization']:.3f}\n")
            f.write(f"网络利用率: {resource_utilization['network_utilization']:.3f}\n")
            f.write(f"平均资源利用率: {np.mean([resource_utilization['cpu_utilization'], resource_utilization['memory_utilization'], resource_utilization['io_utilization'], resource_utilization['network_utilization']]):.3f}\n\n")
            
            # 执行时间分析
            if 'execution_time_analysis' in validation_results:
                exec_time_analysis = validation_results['execution_time_analysis']
                f.write("5. 执行时间分析\n")
                f.write("-" * 40 + "\n")
                f.write(f"最小执行时间: {exec_time_analysis['min_execution_time']:.2f}秒\n")
                f.write(f"最大执行时间: {exec_time_analysis['max_execution_time']:.2f}秒\n")
                f.write(f"平均执行时间: {exec_time_analysis['avg_execution_time']:.2f}秒\n")
                f.write(f"执行时间标准差: {exec_time_analysis['std_execution_time']:.2f}秒\n")
                f.write(f"总执行时间样本数: {exec_time_analysis['total_execution_times']}\n\n")
            
            # 改进建议
            recommendations = validation_results['recommendations']
            f.write("6. 数据质量改进建议\n")
            f.write("-" * 40 + "\n")
            for i, rec in enumerate(recommendations, 1):
                f.write(f"{i}. {rec}\n")
            f.write("\n")
            
            # 总结
            f.write("7. 总结\n")
            f.write("-" * 40 + "\n")
            overall_score = quality_metrics['overall_quality_score']
            if overall_score >= 0.8:
                f.write("数据质量优秀，非常适合用于负载均衡仿真实验。\n")
            elif overall_score >= 0.6:
                f.write("数据质量良好，适合用于负载均衡仿真实验，但建议根据上述建议进行优化。\n")
            elif overall_score >= 0.4:
                f.write("数据质量一般，建议根据上述建议进行改进后再用于仿真实验。\n")
            else:
                f.write("数据质量较差，建议重新生成数据或大幅调整参数。\n")
            
            f.write(f"整体质量分数: {overall_score:.3f}\n")
            f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"数据质量报告已保存: {output_filename}")
        return output_filename
    
    def plot_quality_analysis(self, validation_results: Dict[str, Any], save_plots: bool = True, filename_prefix: str = None):
        """绘制数据质量分析图表"""
        if not filename_prefix:
            workflow_type = validation_results['workflow_info']['workflow_type']
            filename_prefix = workflow_type
        
        # 1. 资源分布对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'{workflow_type.upper()} 工作流数据质量分析', fontsize=16, fontweight='bold')
        
        task_analysis = validation_results['task_analysis']
        node_analysis = validation_results['node_analysis']
        
        # CPU需求 vs 容量
        cpu_reqs = [task['cpu_requirement'] for task in validation_results['workflow_data']['tasks'].values()]
        cpu_caps = [node['cpu_capacity'] for node in validation_results['workflow_data']['nodes']]
        
        axes[0, 0].hist(cpu_reqs, bins=20, alpha=0.7, label='任务CPU需求', color='red')
        axes[0, 0].hist(cpu_caps, bins=20, alpha=0.7, label='节点CPU容量', color='blue')
        axes[0, 0].set_title('CPU需求 vs 容量分布')
        axes[0, 0].set_xlabel('CPU (核数)')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 内存需求 vs 容量
        memory_reqs = [task['memory_requirement'] for task in validation_results['workflow_data']['tasks'].values()]
        memory_caps = [node['memory_capacity'] for node in validation_results['workflow_data']['nodes']]
        
        axes[0, 1].hist(memory_reqs, bins=20, alpha=0.7, label='任务内存需求', color='red')
        axes[0, 1].hist(memory_caps, bins=20, alpha=0.7, label='节点内存容量', color='blue')
        axes[0, 1].set_title('内存需求 vs 容量分布')
        axes[0, 1].set_xlabel('内存 (MB)')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 资源利用率
        resource_utilization = validation_results['resource_utilization']
        resources = ['CPU', '内存', 'I/O', '网络']
        utilizations = [
            resource_utilization['cpu_utilization'],
            resource_utilization['memory_utilization'],
            resource_utilization['io_utilization'],
            resource_utilization['network_utilization']
        ]
        
        bars = axes[1, 0].bar(resources, utilizations, color=['red', 'blue', 'green', 'orange'])
        axes[1, 0].set_title('资源利用率')
        axes[1, 0].set_ylabel('利用率')
        axes[1, 0].set_ylim(0, 1)
        for bar, util in zip(bars, utilizations):
            axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                           f'{util:.3f}', ha='center', va='bottom')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 数据质量指标
        quality_metrics = validation_results['data_quality_metrics']
        metrics = ['兼容性', '资源利用率', '整体质量', '真实性', '可靠性']
        scores = [
            quality_metrics['compatibility_score'],
            quality_metrics['resource_utilization_score'],
            quality_metrics['overall_quality_score'],
            quality_metrics['data_realism_score'],
            quality_metrics['data_reliability_score']
        ]
        
        bars = axes[1, 1].bar(metrics, scores, color=['purple', 'orange', 'green', 'red', 'blue'])
        axes[1, 1].set_title('数据质量指标')
        axes[1, 1].set_ylabel('分数')
        axes[1, 1].set_ylim(0, 1)
        for bar, score in zip(bars, scores):
            axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                           f'{score:.3f}', ha='center', va='bottom')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plots:
            plot_filename = f"{filename_prefix}_quality_analysis.png"
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            print(f"质量分析图已保存: {plot_filename}")
        
        plt.show()


def main():
    """测试数据质量验证器"""
    validator = DataQualityValidator()
    
    # 测试真实数据模型
    model = RealisticDataModel()
    
    # 生成测试数据
    test_task = model.generate_realistic_task('blast', 'test_task_0')
    test_node = model.generate_realistic_node('compute_intensive', 'test_node_0')
    
    # 创建测试工作流数据
    test_workflow_data = {
        'workflow_type': 'test',
        'tasks': {'test_task_0': test_task},
        'nodes': [test_node],
        'edges': [],
        'generated_at': datetime.now().isoformat()
    }
    
    # 验证数据质量
    validation_results = validator.validate_workflow_data(test_workflow_data)
    
    # 生成报告
    validator.generate_quality_report(validation_results)
    
    print("数据质量验证完成！")


if __name__ == "__main__":
    main() 