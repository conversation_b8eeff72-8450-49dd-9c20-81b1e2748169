"""
负载均衡仿真系统 - 数据结构定义
Python版本的数据结构，对应C++版本的data.h
"""

from enum import Enum
from typing import Set, List, Dict, Any
from dataclasses import dataclass
import random


class NodeType(Enum):
    """节点类型枚举"""
    CPU_INTENSIVE = 1    # CPU密集型
    MEMORY_INTENSIVE = 2  # 内存密集型
    NETWORK_INTENSIVE = 3 # 通信密集型
    GENERAL = 4          # 通用型


class TaskType(Enum):
    """任务类型枚举"""
    TASK_CPU_INTENSIVE = 1    # CPU密集型任务
    TASK_MEMORY_INTENSIVE = 2 # 内存密集型任务
    TASK_NETWORK_INTENSIVE = 3 # 通信密集型任务
    TASK_GENERAL = 4          # 通用任务


@dataclass
class TaskResourceDemand:
    """任务资源需求结构"""
    cpu_demand: int      # CPU需求(100-1000)
    memory_demand: int   # 内存需求(100-1000)
    bandwidth_demand: int # 带宽需求(100-1000)


@dataclass
class Task:
    """任务结构"""
    id: int                              # 任务ID
    time_step: int                       # 时间步
    resource_demand: TaskResourceDemand  # 资源需求
    dependencies: Set[int]               # 依赖关系集合
    task_type: TaskType                  # 任务类型
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = set()


@dataclass
class NodeResource:
    """节点资源结构"""
    cpu_capacity: int        # CPU标准容量
    memory_capacity: int     # 内存标准容量
    bandwidth_capacity: int  # 带宽标准容量
    current_cpu: int         # 当前CPU使用量
    current_memory: int      # 当前内存使用量
    current_bandwidth: int   # 当前带宽使用量


@dataclass
class Node:
    """节点结构"""
    id: int                 # 节点ID
    time_step: int          # 时间步(初始为0)
    resource: NodeResource  # 节点资源
    node_type: NodeType     # 节点类型/特性


@dataclass
class TaskGenerationParams:
    """任务生成参数"""
    time_steps: int = 3                    # 时间步数
    tasks_per_time_step: int = 50          # 每个时间步的任务数量
    min_cpu_demand: int = 100              # 最小CPU需求
    max_cpu_demand: int = 1000             # 最大CPU需求
    min_memory_demand: int = 100           # 最小内存需求
    max_memory_demand: int = 1000          # 最大内存需求
    min_bandwidth_demand: int = 100        # 最小带宽需求
    max_bandwidth_demand: int = 1000       # 最大带宽需求
    dependency_probability: float = 0.3     # 依赖概率
    max_dependencies: int = 3              # 最大依赖数量
    max_dependency_layers: int = 5         # 最大依赖层数
    
    # 任务类型权重参数
    cpu_weight: float = 0.4         # CPU权重 
    memory_weight: float = 0.3      # 内存权重
    network_weight: float = 0.3     # 网络权重


@dataclass
class NodeGenerationParams:
    """节点生成参数"""
    num_nodes: int = 20                     # 节点数量
    min_cpu_capacity: int = 10000           # 最小CPU标准容量
    max_cpu_capacity: int = 30000           # 最大CPU标准容量
    min_memory_capacity: int = 10240        # 最小内存标准容量
    max_memory_capacity: int = 30720        # 最大内存标准容量
    min_bandwidth_capacity: int = 10240     # 最小带宽标准容量
    max_bandwidth_capacity: int = 30720     # 最大带宽标准容量
    current_resource_ratio_min: float = 0.1 # 当前资源最小比例
    current_resource_ratio_max: float = 0.2 # 当前资源最大比例
    
    # 节点类型权重参数
    cpu_weight: float = 0.4         # CPU权重
    memory_weight: float = 0.3      # 内存权重
    network_weight: float = 0.3     # 网络权重


class DataGenerator:
    """数据生成器类"""
    
    @staticmethod
    def generate_task_dataset(filename: str, params: TaskGenerationParams) -> bool:
        """
        生成任务数据集
        
        Args:
            filename: 输出文件名
            params: 任务生成参数
            
        Returns:
            bool: 是否成功生成
        """
        try:
            tasks = []
            task_id = 1
            
            for time_step in range(1, params.time_steps + 1):
                for _ in range(params.tasks_per_time_step):
                    # 生成资源需求
                    cpu_demand = random.randint(params.min_cpu_demand, params.max_cpu_demand)
                    memory_demand = random.randint(params.min_memory_demand, params.max_memory_demand)
                    bandwidth_demand = random.randint(params.min_bandwidth_demand, params.max_bandwidth_demand)
                    
                    # 确定任务类型
                    task_type = DataGenerator._determine_task_type(cpu_demand, memory_demand, bandwidth_demand, params)
                    
                    # 生成依赖关系
                    dependencies = DataGenerator._generate_dependencies(task_id, tasks, params)
                    
                    # 创建任务
                    task = Task(
                        id=task_id,
                        time_step=time_step,
                        resource_demand=TaskResourceDemand(
                            cpu_demand=cpu_demand,
                            memory_demand=memory_demand,
                            bandwidth_demand=bandwidth_demand
                        ),
                        dependencies=dependencies,
                        task_type=task_type
                    )
                    
                    tasks.append(task)
                    task_id += 1
            
            # 写入文件
            DataGenerator._write_tasks_to_file(filename, tasks)
            return True
            
        except Exception as e:
            print(f"生成任务数据集失败: {e}")
            return False
    
    @staticmethod
    def generate_node_dataset(filename: str, params: NodeGenerationParams) -> bool:
        """
        生成节点数据集
        
        Args:
            filename: 输出文件名
            params: 节点生成参数
            
        Returns:
            bool: 是否成功生成
        """
        try:
            nodes = []
            
            for i in range(params.num_nodes):
                # 生成标准容量
                cpu_capacity = random.randint(params.min_cpu_capacity, params.max_cpu_capacity)
                memory_capacity = random.randint(params.min_memory_capacity, params.max_memory_capacity)
                bandwidth_capacity = random.randint(params.min_bandwidth_capacity, params.max_bandwidth_capacity)
                
                # 生成当前使用量
                current_cpu = int(cpu_capacity * random.uniform(params.current_resource_ratio_min, params.current_resource_ratio_max))
                current_memory = int(memory_capacity * random.uniform(params.current_resource_ratio_min, params.current_resource_ratio_max))
                current_bandwidth = int(bandwidth_capacity * random.uniform(params.current_resource_ratio_min, params.current_resource_ratio_max))
                
                # 确定节点类型
                node_type = DataGenerator._determine_node_type(cpu_capacity, memory_capacity, bandwidth_capacity, params)
                
                # 创建节点
                node = Node(
                    id=i + 1,
                    time_step=0,
                    resource=NodeResource(
                        cpu_capacity=cpu_capacity,
                        memory_capacity=memory_capacity,
                        bandwidth_capacity=bandwidth_capacity,
                        current_cpu=current_cpu,
                        current_memory=current_memory,
                        current_bandwidth=current_bandwidth
                    ),
                    node_type=node_type
                )
                
                nodes.append(node)
            
            # 写入文件
            DataGenerator._write_nodes_to_file(filename, nodes)
            return True
            
        except Exception as e:
            print(f"生成节点数据集失败: {e}")
            return False
    
    @staticmethod
    def _determine_task_type(cpu_demand: int, memory_demand: int, bandwidth_demand: int, params: TaskGenerationParams) -> TaskType:
        """确定任务类型"""
        # 计算各资源需求的权重
        total_demand = cpu_demand + memory_demand + bandwidth_demand
        cpu_ratio = cpu_demand / total_demand
        memory_ratio = memory_demand / total_demand
        network_ratio = bandwidth_demand / total_demand
        
        # 根据权重确定类型
        if cpu_ratio > params.cpu_weight:
            return TaskType.TASK_CPU_INTENSIVE
        elif memory_ratio > params.memory_weight:
            return TaskType.TASK_MEMORY_INTENSIVE
        elif network_ratio > params.network_weight:
            return TaskType.TASK_NETWORK_INTENSIVE
        else:
            return TaskType.TASK_GENERAL
    
    @staticmethod
    def _determine_node_type(cpu_capacity: int, memory_capacity: int, bandwidth_capacity: int, params: NodeGenerationParams) -> NodeType:
        """确定节点类型"""
        # 计算各资源容量的权重
        total_capacity = cpu_capacity + memory_capacity + bandwidth_capacity
        cpu_ratio = cpu_capacity / total_capacity
        memory_ratio = memory_capacity / total_capacity
        network_ratio = bandwidth_capacity / total_capacity
        
        # 根据权重确定类型
        if cpu_ratio > params.cpu_weight:
            return NodeType.CPU_INTENSIVE
        elif memory_ratio > params.memory_weight:
            return NodeType.MEMORY_INTENSIVE
        elif network_ratio > params.network_weight:
            return NodeType.NETWORK_INTENSIVE
        else:
            return NodeType.GENERAL
    
    @staticmethod
    def _generate_dependencies(task_id: int, existing_tasks: List[Task], params: TaskGenerationParams) -> Set[int]:
        """生成任务依赖关系"""
        dependencies = set()
        
        if not existing_tasks or random.random() > params.dependency_probability:
            return dependencies
        
        # 限制依赖数量
        max_deps = min(params.max_dependencies, len(existing_tasks))
        if max_deps == 0:
            return dependencies
        
        # 随机选择依赖任务
        num_dependencies = random.randint(1, max_deps)
        available_tasks = [task.id for task in existing_tasks if task.id < task_id]
        
        if available_tasks:
            selected_deps = random.sample(available_tasks, min(num_dependencies, len(available_tasks)))
            dependencies.update(selected_deps)
        
        return dependencies
    
    @staticmethod
    def _write_tasks_to_file(filename: str, tasks: List[Task]):
        """将任务写入文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            # 写入表头
            f.write("任务ID\t时间步\tCPU需求\t内存需求\t带宽需求\t依赖关系;任务类型\n")
            
            # 写入任务数据
            for task in tasks:
                dependencies_str = " ".join(map(str, task.dependencies)) if task.dependencies else ""
                f.write(f"{task.id}\t{task.time_step}\t{task.resource_demand.cpu_demand}\t"
                       f"{task.resource_demand.memory_demand}\t{task.resource_demand.bandwidth_demand}\t"
                       f"{dependencies_str};{task.task_type.value}\n")
    
    @staticmethod
    def _write_nodes_to_file(filename: str, nodes: List[Node]):
        """将节点写入文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            # 写入表头
            f.write("节点ID\t时间步\tCPU容量\t内存容量\t带宽容量\t当前CPU\t当前内存\t当前带宽\t节点类型\n")
            
            # 写入节点数据
            for node in nodes:
                f.write(f"{node.id}\t{node.time_step}\t{node.resource.cpu_capacity}\t"
                       f"{node.resource.memory_capacity}\t{node.resource.bandwidth_capacity}\t"
                       f"{node.resource.current_cpu}\t{node.resource.current_memory}\t"
                       f"{node.resource.current_bandwidth}\t{node.node_type.value}\n") 