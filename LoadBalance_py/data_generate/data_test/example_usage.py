"""
负载均衡仿真系统 - 使用示例
演示如何使用Python版本的数据生成器
"""

from data_structures import DataGenerator, TaskGenerationParams, NodeGenerationParams
import os


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 使用默认参数
    node_params = NodeGenerationParams()
    task_params = TaskGenerationParams()
    
    # 生成数据
    print("正在生成节点数据...")
    success = DataGenerator.generate_node_dataset("example_nodes.txt", node_params)
    if success:
        print("✓ 节点数据生成成功")
    else:
        print("✗ 节点数据生成失败")
    
    print("正在生成任务数据...")
    success = DataGenerator.generate_task_dataset("example_tasks.txt", task_params)
    if success:
        print("✓ 任务数据生成成功")
    else:
        print("✗ 任务数据生成失败")
    
    # 显示文件信息
    if os.path.exists("example_nodes.txt"):
        with open("example_nodes.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        print(f"节点文件包含 {len(lines)-1} 个节点")
    
    if os.path.exists("example_tasks.txt"):
        with open("example_tasks.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        print(f"任务文件包含 {len(lines)-1} 个任务")


def example_custom_parameters():
    """自定义参数示例"""
    print("\n=== 自定义参数示例 ===")
    
    # 自定义节点参数
    node_params = NodeGenerationParams(
        num_nodes=10,
        min_cpu_capacity=5000,
        max_cpu_capacity=15000,
        min_memory_capacity=8192,
        max_memory_capacity=16384,
        current_resource_ratio_min=0.05,
        current_resource_ratio_max=0.15
    )
    
    # 自定义任务参数
    task_params = TaskGenerationParams(
        time_steps=2,
        tasks_per_time_step=20,
        min_cpu_demand=200,
        max_cpu_demand=800,
        dependency_probability=0.5,
        max_dependencies=2
    )
    
    print("节点参数:")
    print(f"  - 节点数量: {node_params.num_nodes}")
    print(f"  - CPU容量范围: {node_params.min_cpu_capacity}-{node_params.max_cpu_capacity}")
    print(f"  - 内存容量范围: {node_params.min_memory_capacity}-{node_params.max_memory_capacity}")
    print(f"  - 资源使用比例: {node_params.current_resource_ratio_min}-{node_params.current_resource_ratio_max}")
    
    print("\n任务参数:")
    print(f"  - 时间步数: {task_params.time_steps}")
    print(f"  - 每步任务数: {task_params.tasks_per_time_step}")
    print(f"  - CPU需求范围: {task_params.min_cpu_demand}-{task_params.max_cpu_demand}")
    print(f"  - 依赖概率: {task_params.dependency_probability}")
    
    # 生成数据
    print("\n正在生成数据...")
    DataGenerator.generate_node_dataset("custom_nodes.txt", node_params)
    DataGenerator.generate_task_dataset("custom_tasks.txt", task_params)
    
    print("✓ 自定义参数数据生成完成")


def example_large_scale():
    """大规模数据生成示例"""
    print("\n=== 大规模数据生成示例 ===")
    
    # 大规模参数
    node_params = NodeGenerationParams(
        num_nodes=100,
        min_cpu_capacity=8000,
        max_cpu_capacity=25000,
        min_memory_capacity=12288,
        max_memory_capacity=24576
    )
    
    task_params = TaskGenerationParams(
        time_steps=5,
        tasks_per_time_step=200,
        min_cpu_demand=150,
        max_cpu_demand=1200,
        dependency_probability=0.4,
        max_dependencies=4
    )
    
    print("大规模参数:")
    print(f"  - 节点数量: {node_params.num_nodes}")
    print(f"  - 时间步数: {task_params.time_steps}")
    print(f"  - 每步任务数: {task_params.tasks_per_time_step}")
    print(f"  - 总任务数: {task_params.time_steps * task_params.tasks_per_time_step}")
    
    # 生成数据
    print("\n正在生成大规模数据...")
    DataGenerator.generate_node_dataset("large_scale_nodes.txt", node_params)
    DataGenerator.generate_task_dataset("large_scale_tasks.txt", task_params)
    
    print("✓ 大规模数据生成完成")


def example_data_analysis():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    # 生成一些测试数据
    node_params = NodeGenerationParams(num_nodes=50)
    task_params = TaskGenerationParams(time_steps=3, tasks_per_time_step=30)
    
    DataGenerator.generate_node_dataset("analysis_nodes.txt", node_params)
    DataGenerator.generate_task_dataset("analysis_tasks.txt", task_params)
    
    # 分析节点数据
    if os.path.exists("analysis_nodes.txt"):
        print("节点数据分析:")
        with open("analysis_nodes.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        node_types = {}
        cpu_capacities = []
        memory_capacities = []
        
        for line in lines[1:]:  # 跳过表头
            parts = line.strip().split('\t')
            node_type = int(parts[8])
            cpu_capacity = int(parts[2])
            memory_capacity = int(parts[3])
            
            node_types[node_type] = node_types.get(node_type, 0) + 1
            cpu_capacities.append(cpu_capacity)
            memory_capacities.append(memory_capacity)
        
        print(f"  - 总节点数: {len(lines)-1}")
        print(f"  - 节点类型分布: {node_types}")
        print(f"  - CPU容量范围: {min(cpu_capacities)}-{max(cpu_capacities)}")
        print(f"  - 内存容量范围: {min(memory_capacities)}-{max(memory_capacities)}")
    
    # 分析任务数据
    if os.path.exists("analysis_tasks.txt"):
        print("\n任务数据分析:")
        with open("analysis_tasks.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        task_types = {}
        time_steps = {}
        dependencies_count = 0
        
        for line in lines[1:]:  # 跳过表头
            parts = line.strip().split('\t')
            task_type = int(parts[5].split(';')[1])
            time_step = int(parts[1])
            dependencies = parts[5].split(';')[0]
            
            task_types[task_type] = task_types.get(task_type, 0) + 1
            time_steps[time_step] = time_steps.get(time_step, 0) + 1
            
            if dependencies.strip():
                dependencies_count += 1
        
        print(f"  - 总任务数: {len(lines)-1}")
        print(f"  - 任务类型分布: {task_types}")
        print(f"  - 时间步分布: {time_steps}")
        print(f"  - 有依赖关系的任务数: {dependencies_count}")


def cleanup_example_files():
    """清理示例文件"""
    print("\n=== 清理示例文件 ===")
    
    files_to_clean = [
        "example_nodes.txt", "example_tasks.txt",
        "custom_nodes.txt", "custom_tasks.txt",
        "large_scale_nodes.txt", "large_scale_tasks.txt",
        "analysis_nodes.txt", "analysis_tasks.txt"
    ]
    
    for file_path in files_to_clean:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"已删除: {file_path}")


def main():
    """主函数"""
    print("负载均衡仿真系统 - Python数据生成器示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_custom_parameters()
        example_large_scale()
        example_data_analysis()
        
        print("\n" + "=" * 50)
        print("所有示例执行完成！")
        
        # 询问是否清理文件
        response = input("\n是否清理生成的示例文件？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            cleanup_example_files()
        else:
            print("示例文件已保留，您可以查看生成的数据文件。")
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")


if __name__ == "__main__":
    main() 