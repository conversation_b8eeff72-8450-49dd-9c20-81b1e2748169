"""
负载均衡仿真系统 - 数据生成器测试
测试数据生成功能的正确性
"""

import unittest
import tempfile
import os
from data_structures import (
    DataGenerator, 
    TaskGenerationParams, 
    NodeGenerationParams,
    Task, Node, TaskResourceDemand, NodeResource,
    TaskType, NodeType
)


class TestDataGenerator(unittest.TestCase):
    """数据生成器测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.temp_dir = tempfile.mkdtemp()
        self.node_file = os.path.join(self.temp_dir, "test_nodes.txt")
        self.task_file = os.path.join(self.temp_dir, "test_tasks.txt")
    
    def tearDown(self):
        """测试后的清理工作"""
        # 清理临时文件
        for file_path in [self.node_file, self.task_file]:
            if os.path.exists(file_path):
                os.remove(file_path)
        os.rmdir(self.temp_dir)
    
    def test_node_generation_params(self):
        """测试节点生成参数"""
        params = NodeGenerationParams()
        
        # 测试默认值
        self.assertEqual(params.num_nodes, 20)
        self.assertEqual(params.min_cpu_capacity, 10000)
        self.assertEqual(params.max_cpu_capacity, 30000)
        self.assertEqual(params.cpu_weight, 0.4)
        self.assertEqual(params.memory_weight, 0.3)
        self.assertEqual(params.network_weight, 0.3)
        
        # 测试参数修改
        params.num_nodes = 50
        params.min_cpu_capacity = 5000
        self.assertEqual(params.num_nodes, 50)
        self.assertEqual(params.min_cpu_capacity, 5000)
    
    def test_task_generation_params(self):
        """测试任务生成参数"""
        params = TaskGenerationParams()
        
        # 测试默认值
        self.assertEqual(params.time_steps, 3)
        self.assertEqual(params.tasks_per_time_step, 50)
        self.assertEqual(params.min_cpu_demand, 100)
        self.assertEqual(params.max_cpu_demand, 1000)
        self.assertEqual(params.dependency_probability, 0.3)
        
        # 测试参数修改
        params.time_steps = 5
        params.tasks_per_time_step = 100
        self.assertEqual(params.time_steps, 5)
        self.assertEqual(params.tasks_per_time_step, 100)
    
    def test_node_generation(self):
        """测试节点数据生成"""
        params = NodeGenerationParams(num_nodes=5)
        
        # 生成节点数据
        success = DataGenerator.generate_node_dataset(self.node_file, params)
        self.assertTrue(success)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(self.node_file))
        
        # 验证文件内容
        with open(self.node_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 检查表头
        self.assertIn("节点ID", lines[0])
        self.assertIn("CPU容量", lines[0])
        self.assertIn("内存容量", lines[0])
        
        # 检查数据行数（表头 + 5个节点）
        self.assertEqual(len(lines), 6)
        
        # 检查数据格式
        for i in range(1, len(lines)):
            parts = lines[i].strip().split('\t')
            self.assertEqual(len(parts), 9)  # 9个字段
            
            # 检查数据类型
            node_id = int(parts[0])
            time_step = int(parts[1])
            cpu_capacity = int(parts[2])
            memory_capacity = int(parts[3])
            bandwidth_capacity = int(parts[4])
            current_cpu = int(parts[5])
            current_memory = int(parts[6])
            current_bandwidth = int(parts[7])
            node_type = int(parts[8])
            
            # 验证数据范围
            self.assertGreater(node_id, 0)
            self.assertEqual(time_step, 0)
            self.assertGreaterEqual(cpu_capacity, params.min_cpu_capacity)
            self.assertLessEqual(cpu_capacity, params.max_cpu_capacity)
            self.assertGreaterEqual(memory_capacity, params.min_memory_capacity)
            self.assertLessEqual(memory_capacity, params.max_memory_capacity)
            self.assertGreaterEqual(bandwidth_capacity, params.min_bandwidth_capacity)
            self.assertLessEqual(bandwidth_capacity, params.max_bandwidth_capacity)
            self.assertIn(node_type, [1, 2, 3, 4])  # 节点类型值
    
    def test_task_generation(self):
        """测试任务数据生成"""
        params = TaskGenerationParams(time_steps=2, tasks_per_time_step=3)
        
        # 生成任务数据
        success = DataGenerator.generate_task_dataset(self.task_file, params)
        self.assertTrue(success)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(self.task_file))
        
        # 验证文件内容
        with open(self.task_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 检查表头
        self.assertIn("任务ID", lines[0])
        self.assertIn("CPU需求", lines[0])
        self.assertIn("内存需求", lines[0])
        
        # 检查数据行数（表头 + 6个任务）
        self.assertEqual(len(lines), 7)
        
        # 检查数据格式
        for i in range(1, len(lines)):
            parts = lines[i].strip().split('\t')
            self.assertEqual(len(parts), 6)  # 6个字段
            
            # 检查数据类型
            task_id = int(parts[0])
            time_step = int(parts[1])
            cpu_demand = int(parts[2])
            memory_demand = int(parts[3])
            bandwidth_demand = int(parts[4])
            
            # 解析依赖关系和任务类型
            deps_and_type = parts[5].split(';')
            dependencies_str = deps_and_type[0]
            task_type = int(deps_and_type[1])
            
            # 验证数据范围
            self.assertGreater(task_id, 0)
            self.assertIn(time_step, [1, 2])
            self.assertGreaterEqual(cpu_demand, params.min_cpu_demand)
            self.assertLessEqual(cpu_demand, params.max_cpu_demand)
            self.assertGreaterEqual(memory_demand, params.min_memory_demand)
            self.assertLessEqual(memory_demand, params.max_memory_demand)
            self.assertGreaterEqual(bandwidth_demand, params.min_bandwidth_demand)
            self.assertLessEqual(bandwidth_demand, params.max_bandwidth_demand)
            self.assertIn(task_type, [1, 2, 3, 4])  # 任务类型值
    
    def test_task_type_determination(self):
        """测试任务类型确定逻辑"""
        params = TaskGenerationParams()
        
        # CPU密集型任务
        task_type = DataGenerator._determine_task_type(800, 100, 100, params)
        self.assertEqual(task_type, TaskType.TASK_CPU_INTENSIVE)
        
        # 内存密集型任务
        task_type = DataGenerator._determine_task_type(100, 800, 100, params)
        self.assertEqual(task_type, TaskType.TASK_MEMORY_INTENSIVE)
        
        # 网络密集型任务
        task_type = DataGenerator._determine_task_type(100, 100, 800, params)
        self.assertEqual(task_type, TaskType.TASK_NETWORK_INTENSIVE)
        
        # 通用任务
        task_type = DataGenerator._determine_task_type(300, 300, 400, params)
        self.assertEqual(task_type, TaskType.TASK_GENERAL)
    
    def test_node_type_determination(self):
        """测试节点类型确定逻辑"""
        params = NodeGenerationParams()
        
        # CPU密集型节点
        node_type = DataGenerator._determine_node_type(20000, 10000, 10000, params)
        self.assertEqual(node_type, NodeType.CPU_INTENSIVE)
        
        # 内存密集型节点
        node_type = DataGenerator._determine_node_type(10000, 20000, 10000, params)
        self.assertEqual(node_type, NodeType.MEMORY_INTENSIVE)
        
        # 网络密集型节点
        node_type = DataGenerator._determine_node_type(10000, 10000, 20000, params)
        self.assertEqual(node_type, NodeType.NETWORK_INTENSIVE)
        
        # 通用节点
        node_type = DataGenerator._determine_node_type(15000, 15000, 15000, params)
        self.assertEqual(node_type, NodeType.GENERAL)
    
    def test_dependency_generation(self):
        """测试依赖关系生成"""
        params = TaskGenerationParams(dependency_probability=1.0, max_dependencies=2)
        
        # 创建一些现有任务
        existing_tasks = [
            Task(id=1, time_step=1, resource_demand=TaskResourceDemand(100, 100, 100), 
                 dependencies=set(), task_type=TaskType.TASK_GENERAL),
            Task(id=2, time_step=1, resource_demand=TaskResourceDemand(100, 100, 100), 
                 dependencies=set(), task_type=TaskType.TASK_GENERAL),
            Task(id=3, time_step=1, resource_demand=TaskResourceDemand(100, 100, 100), 
                 dependencies=set(), task_type=TaskType.TASK_GENERAL)
        ]
        
        # 测试依赖生成
        dependencies = DataGenerator._generate_dependencies(4, existing_tasks, params)
        
        # 验证依赖关系
        self.assertIsInstance(dependencies, set)
        self.assertLessEqual(len(dependencies), params.max_dependencies)
        for dep_id in dependencies:
            self.assertIn(dep_id, [1, 2, 3])
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效文件路径
        params = NodeGenerationParams(num_nodes=5)
        
        # 尝试写入到无效路径
        success = DataGenerator.generate_node_dataset("/invalid/path/test.txt", params)
        self.assertFalse(success)


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2) 