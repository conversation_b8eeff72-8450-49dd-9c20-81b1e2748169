"""
负载均衡仿真系统 - 主数据生成程序
Python版本的主程序，对应C++版本的main_data_generator.cpp
"""

import sys
import os
from data_structures import DataGenerator, TaskGenerationParams, NodeGenerationParams


def get_user_input(prompt: str, default_value: int) -> int:
    """
    获取用户输入，如果输入无效则使用默认值
    
    Args:
        prompt: 提示信息
        default_value: 默认值
        
    Returns:
        int: 用户输入或默认值
    """
    try:
        user_input = input(prompt).strip()
        if user_input:
            return int(user_input)
        else:
            return default_value
    except ValueError:
        print(f"输入无效，使用默认值: {default_value}")
        return default_value


def main():
    """主函数"""
    print("=== 实验1：任务和节点数据集生成工具 ===")
    
    # 设置节点生成参数
    node_params = NodeGenerationParams()
    print("正在设置节点生成参数...")
    
    # 获取节点数量
    node_params.num_nodes = get_user_input("请输入要生成的节点数量(默认20个): ", 20)
    
    print("使用以下参数生成节点：")
    print(f"节点数量: {node_params.num_nodes}")
    print(f"CPU标准容量范围: {node_params.min_cpu_capacity}-{node_params.max_cpu_capacity}")
    print(f"内存标准容量范围: {node_params.min_memory_capacity}-{node_params.max_memory_capacity}")
    print(f"带宽标准容量范围: {node_params.min_bandwidth_capacity}-{node_params.max_bandwidth_capacity}")
    print(f"当前资源比例范围: {node_params.current_resource_ratio_min}-{node_params.current_resource_ratio_max}")
    print(f"节点类型权重 (CPU:内存:网络): {node_params.cpu_weight}:{node_params.memory_weight}:{node_params.network_weight}")
    
    # 设置任务生成参数
    task_params = TaskGenerationParams()
    print("\n正在设置任务生成参数...")
    
    # 获取时间步数
    task_params.time_steps = get_user_input("请输入时间步数(默认3步): ", 3)
    
    # 获取每个时间步的任务数量
    task_params.tasks_per_time_step = get_user_input("请输入每个时间步的任务数量(默认50个): ", 50)
    
    print("使用以下参数生成任务：")
    print(f"时间步数: {task_params.time_steps}")
    print(f"每个时间步的任务数量: {task_params.tasks_per_time_step}")
    print(f"CPU需求范围: {task_params.min_cpu_demand}-{task_params.max_cpu_demand}")
    print(f"内存需求范围: {task_params.min_memory_demand}-{task_params.max_memory_demand}")
    print(f"带宽需求范围: {task_params.min_bandwidth_demand}-{task_params.max_bandwidth_demand}")
    print(f"依赖概率: {task_params.dependency_probability}")
    print(f"最大依赖数量: {task_params.max_dependencies}")
    print(f"最大依赖层数: {task_params.max_dependency_layers}")
    print(f"任务类型权重 (CPU:内存:网络): {task_params.cpu_weight}:{task_params.memory_weight}:{task_params.network_weight}")
    
    # 设置输出文件名
    node_filename = "4.8_nodes.txt"
    task_filename = "4.8_tasks.txt"
    
    print("\n开始生成数据集...")
    
    # 生成节点数据集
    print("正在生成节点数据集...")
    node_data_generated = DataGenerator.generate_node_dataset(node_filename, node_params)
    if node_data_generated:
        print(f"节点数据已成功生成到 {node_filename}!")
    else:
        print("生成节点数据失败。")
        return 1
    
    # 生成任务数据集
    print("正在生成任务数据集...")
    task_data_generated = DataGenerator.generate_task_dataset(task_filename, task_params)
    if task_data_generated:
        print(f"任务数据已成功生成到 {task_filename}!")
    else:
        print("生成任务数据失败。")
        return 1
    
    print("\n数据生成成功完成：")
    print(f"节点数据文件: {node_filename}")
    print(f"任务数据文件: {task_filename}")
    
    # 显示生成统计信息
    total_tasks = task_params.time_steps * task_params.tasks_per_time_step
    print(f"\n生成统计信息：")
    print(f"- 节点数量: {node_params.num_nodes}")
    print(f"- 时间步数: {task_params.time_steps}")
    print(f"- 总任务数量: {total_tasks}")
    print(f"- 每个时间步任务数: {task_params.tasks_per_time_step}")
    
    print("\n程序执行完毕...")
    input("按回车键退出...")
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1) 