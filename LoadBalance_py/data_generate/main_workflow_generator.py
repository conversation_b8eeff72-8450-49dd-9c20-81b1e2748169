"""
负载均衡仿真系统 - 工作流数据生成主程序
整合工作流数据生成、分析、可视化和质量验证功能
"""

import os
import sys
import argparse
from datetime import datetime
from typing import List, Dict, Any

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from workflow_data_generator import WorkflowDataGenerator
from workflow_analyzer import WorkflowAnalyzer
from visualization.workflow_visualizer import WorkflowVisualizer
from data_quality_validator import DataQualityValidator


class WorkflowDataManager:
    """工作流数据管理器"""
    
    def __init__(self):
        self.generator = WorkflowDataGenerator()
        self.analyzer = WorkflowAnalyzer()
        self.visualizer = WorkflowVisualizer()
        self.validator = DataQualityValidator()
        self.workflow_types = ['montage', 'brain', 'sipht', 'epigenomics', 'cybershake']
    
    def generate_all_workflows(self, output_dir: str = r"E:\2024_New\Load_Blancing\Load_Blancing_simulation\LoadBalance_py\data_generate\data"):
        """生成所有类型的工作流数据，支持不同规模"""
        print("=== 工作流数据生成 ===")
        print(f"输出目录: {output_dir}")
        print()
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        generated_files = []
        
        # 定义不同规模的参数
        scale_configs = {
            'small': {'tasks': 15, 'nodes': 6},
            'medium': {'tasks': 25, 'nodes': 8},
            'large': {'tasks': 40, 'nodes': 12}
        }
        
        # Montage工作流：生成三种规模
        print("正在生成 Montage 工作流数据（三种规模）...")
        for scale, config in scale_configs.items():
            print(f"  - {scale}规模: {config['tasks']}个任务, {config['nodes']}个节点")
            
            try:
                workflow_data = self.generator.generate_workflow_data(
                    workflow_type='montage',
                    num_tasks=config['tasks'],
                    num_nodes=config['nodes']
                )
                
                filename = os.path.join(output_dir, f"montage_{scale}_workflows.json")
                self.generator.save_workflow_data(workflow_data, filename)
                generated_files.append(filename)
                
                print(f"    ✓ 任务数量: {workflow_data.metadata['total_tasks']}")
                print(f"    ✓ 总基础执行时间: {workflow_data.metadata['total_base_runtime']:.1f}秒")
                print(f"    ✓ 关键路径长度: {workflow_data.metadata['critical_path_length']:.1f}秒")
                print(f"    ✓ 并行度: {workflow_data.metadata['parallelism_degree']}")
                print(f"    ✓ 节点数量: {len(workflow_data.nodes)}")
                print()
                
            except Exception as e:
                print(f"    ✗ 生成 montage {scale}规模工作流时出错: {e}")
                print()
        
        # 其他工作流：只生成中规模
        other_workflows = ['brain', 'sipht', 'epigenomics', 'cybershake']
        medium_config = scale_configs['medium']
        
        for workflow_type in other_workflows:
            print(f"正在生成 {workflow_type} 工作流数据（中规模）...")
            print(f"  - 中规模: {medium_config['tasks']}个任务, {medium_config['nodes']}个节点")
            
            try:
                workflow_data = self.generator.generate_workflow_data(
                    workflow_type=workflow_type,
                    num_tasks=medium_config['tasks'],
                    num_nodes=medium_config['nodes']
                )
                
                filename = os.path.join(output_dir, f"{workflow_type}_medium_workflows.json")
                self.generator.save_workflow_data(workflow_data, filename)
                generated_files.append(filename)
                
                print(f"    ✓ 任务数量: {workflow_data.metadata['total_tasks']}")
                print(f"    ✓ 总基础执行时间: {workflow_data.metadata['total_base_runtime']:.1f}秒")
                print(f"    ✓ 关键路径长度: {workflow_data.metadata['critical_path_length']:.1f}秒")
                print(f"    ✓ 并行度: {workflow_data.metadata['parallelism_degree']}")
                print(f"    ✓ 节点数量: {len(workflow_data.nodes)}")
                print()
                
            except Exception as e:
                print(f"    ✗ 生成 {workflow_type} 工作流时出错: {e}")
                print()
        
        print(f"工作流数据生成完成！共生成 {len(generated_files)} 个文件")
        print("\n生成的文件列表:")
        for file in generated_files:
            print(f"  - {file}")
        print()
        
        return generated_files
    
    def analyze_all_workflows(self, input_dir: str = ".", generate_plots: bool = True):
        """分析所有工作流数据"""
        print("=== 工作流数据分析 ===")
        print(f"输入目录: {input_dir}")
        print(f"生成图表: {generate_plots}")
        print()
        
        analysis_results = {}
        
        for workflow_type in self.workflow_types:
            filename = os.path.join(input_dir, f"{workflow_type}_workflows.json")
            
            if not os.path.exists(filename):
                print(f"跳过 {workflow_type}: 文件不存在 ({filename})")
                continue
            
            print(f"正在分析 {workflow_type} 工作流...")
            
            try:
                # 加载数据
                self.analyzer.load_workflow_data(filename)
                
                # 分析特征
                analysis = self.analyzer.analyze_workflow_characteristics()
                analysis_results[workflow_type] = analysis
                
                # 生成报告
                report_filename = f"{workflow_type}_analysis_report.txt"
                self.analyzer.generate_analysis_report(report_filename)
                
                # 绘制图表
                if generate_plots:
                    self.analyzer.plot_workflow_characteristics()
                    self.analyzer.plot_workflow_dag()
                
                print(f"  ✓ 分析完成")
                print(f"  ✓ 报告文件: {report_filename}")
                if generate_plots:
                    print(f"  ✓ 图表文件: {workflow_type}_characteristics.png")
                    print(f"  ✓ DAG图文件: {workflow_type}_dag.png")
                print()
                
            except Exception as e:
                print(f"  ✗ 分析 {workflow_type} 工作流时出错: {e}")
                print()
        
        return analysis_results
    
    def visualize_all_workflows(self, input_dir: str = ".", generate_plots: bool = True):
        """为所有工作流创建可视化"""
        print("=== 工作流可视化 ===")
        print(f"输入目录: {input_dir}")
        print(f"生成图表: {generate_plots}")
        print()
        
        for workflow_type in self.workflow_types:
            filename = os.path.join(input_dir, f"{workflow_type}_workflows.json")
            
            if not os.path.exists(filename):
                print(f"跳过 {workflow_type}: 文件不存在 ({filename})")
                continue
            
            print(f"正在为 {workflow_type} 工作流创建可视化...")
            
            try:
                # 加载数据
                self.visualizer.load_workflow_data(filename)
                
                # 创建综合可视化
                self.visualizer.create_comprehensive_visualization(generate_plots)
                
                print(f"  ✓ 可视化完成")
                if generate_plots:
                    print(f"  ✓ 工作流图: {workflow_type}_workflow_diagram.png")
                    print(f"  ✓ 资源需求图: {workflow_type}_resource_requirements.png")
                    print(f"  ✓ 执行时间对比图: {workflow_type}_execution_time_comparison.png")
                print()
                
            except Exception as e:
                print(f"  ✗ 为 {workflow_type} 工作流创建可视化时出错: {e}")
                print()
    
    def validate_all_workflows(self, input_dir: str = ".", generate_reports: bool = True, generate_plots: bool = True):
        """验证所有工作流数据质量"""
        print("=== 工作流数据质量验证 ===")
        print(f"输入目录: {input_dir}")
        print(f"生成报告: {generate_reports}")
        print(f"生成图表: {generate_plots}")
        print()
        
        validation_results = {}
        
        for workflow_type in self.workflow_types:
            filename = os.path.join(input_dir, f"{workflow_type}_workflows.json")
            
            if not os.path.exists(filename):
                print(f"跳过 {workflow_type}: 文件不存在 ({filename})")
                continue
            
            print(f"正在验证 {workflow_type} 工作流数据质量...")
            
            try:
                # 加载数据
                workflow_data = self.validator.load_workflow_data(filename)
                
                # 验证数据质量
                validation_result = self.validator.validate_workflow_data(workflow_data)
                validation_results[workflow_type] = validation_result
                
                # 生成质量报告
                if generate_reports:
                    report_filename = f"{workflow_type}_data_quality_report.txt"
                    self.validator.generate_quality_report(validation_result, report_filename)
                
                # 生成质量分析图
                if generate_plots:
                    self.validator.plot_quality_analysis(validation_result, True, workflow_type)
                
                # 打印质量指标
                quality_metrics = validation_result['data_quality_metrics']
                print(f"  ✓ 整体质量分数: {quality_metrics['overall_quality_score']:.3f}")
                print(f"  ✓ 兼容性分数: {quality_metrics['compatibility_score']:.3f}")
                print(f"  ✓ 数据真实性分数: {quality_metrics['data_realism_score']:.3f}")
                print(f"  ✓ 数据可靠性分数: {quality_metrics['data_reliability_score']:.3f}")
                
                if generate_reports:
                    print(f"  ✓ 质量报告: {report_filename}")
                if generate_plots:
                    print(f"  ✓ 质量分析图: {workflow_type}_quality_analysis.png")
                print()
                
            except Exception as e:
                print(f"  ✗ 验证 {workflow_type} 工作流数据质量时出错: {e}")
                print()
        
        return validation_results
    
    def generate_comparison_report(self, analysis_results: Dict[str, Any], validation_results: Dict[str, Any] = None, output_filename: str = "workflow_comparison_report.txt"):
        """生成工作流对比报告"""
        print("=== 生成工作流对比报告 ===")
        
        with open(output_filename, 'w', encoding='utf-8') as f:
            f.write("=== 工作流对比分析报告 ===\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析的工作流类型: {', '.join(analysis_results.keys())}\n\n")
            
            # 基本信息对比
            f.write("=== 基本信息对比 ===\n")
            f.write(f"{'工作流类型':<15} {'任务数':<8} {'节点数':<8} {'边数':<8} {'总基础执行时间':<15}\n")
            f.write("-" * 65 + "\n")
            
            for workflow_type, analysis in analysis_results.items():
                f.write(f"{workflow_type:<15} {analysis['total_tasks']:<8} {analysis['total_nodes']:<8} "
                       f"{analysis['total_edges']:<8} {analysis['task_runtime_stats']['mean']*analysis['total_tasks']:<15.1f}\n")
            f.write("\n")
            
            # 任务特征对比
            f.write("=== 任务特征对比 ===\n")
            f.write(f"{'工作流类型':<15} {'平均基础执行时间':<15} {'平均CPU需求':<12} {'平均内存需求':<12}\n")
            f.write("-" * 65 + "\n")
            
            for workflow_type, analysis in analysis_results.items():
                runtime_mean = analysis['task_runtime_stats']['mean']
                cpu_mean = analysis['task_cpu_demand_stats']['mean']
                memory_mean = analysis['task_memory_demand_stats']['mean']
                
                f.write(f"{workflow_type:<15} {runtime_mean:<15.1f} {cpu_mean:<12.1f} {memory_mean:<12.1f}\n")
            f.write("\n")
            
            # 资源利用率对比
            f.write("=== 资源利用率对比 ===\n")
            f.write(f"{'工作流类型':<15} {'CPU利用率':<10} {'内存利用率':<10} {'I/O利用率':<10} {'网络利用率':<10}\n")
            f.write("-" * 65 + "\n")
            
            for workflow_type, analysis in analysis_results.items():
                utilization = analysis['resource_utilization']
                f.write(f"{workflow_type:<15} {utilization['cpu_utilization']:<10.2%} "
                       f"{utilization['memory_utilization']:<10.2%} {utilization['io_utilization']:<10.2%} "
                       f"{utilization['network_utilization']:<10.2%}\n")
            f.write("\n")
            
            # 工作流特征对比
            f.write("=== 工作流特征对比 ===\n")
            f.write(f"{'工作流类型':<15} {'图密度':<8} {'平均聚类系数':<12} {'任务类型数':<10}\n")
            f.write("-" * 65 + "\n")
            
            for workflow_type, analysis in analysis_results.items():
                workflow_chars = analysis['workflow_characteristics']
                task_types = len(analysis['task_type_distribution'])
                
                f.write(f"{workflow_type:<15} {workflow_chars['density']:<8.4f} "
                       f"{workflow_chars['average_clustering']:<12.4f} {task_types:<10}\n")
            f.write("\n")
            
            # 数据质量对比（如果有验证结果）
            if validation_results:
                f.write("=== 数据质量对比 ===\n")
                f.write(f"{'工作流类型':<15} {'整体质量':<10} {'兼容性':<10} {'真实性':<10} {'可靠性':<10}\n")
                f.write("-" * 65 + "\n")
                
                for workflow_type, validation in validation_results.items():
                    quality_metrics = validation['data_quality_metrics']
                    f.write(f"{workflow_type:<15} {quality_metrics['overall_quality_score']:<10.3f} "
                           f"{quality_metrics['compatibility_score']:<10.3f} "
                           f"{quality_metrics['data_realism_score']:<10.3f} "
                           f"{quality_metrics['data_reliability_score']:<10.3f}\n")
                f.write("\n")
            
            # 工作流类型特点总结
            f.write("=== 工作流类型特点总结 ===\n")
            f.write("1. Montage (天文图像拼接): CPU和I/O密集型，适合图像处理任务\n")
            f.write("2. Brain (神经影像处理): 内存密集型，适合大数据集处理\n")
            f.write("3. SIPHT (生物信息学): 计算密集型，适合复杂算法计算\n")
            f.write("4. Epigenomics (表观基因组学): 数据流密集型，适合流水线处理\n")
            f.write("5. CyberShake (地震模拟): 通信密集型，适合分布式计算\n\n")
            
            # 负载均衡建议
            f.write("=== 负载均衡策略建议 ===\n")
            f.write("• CPU密集型工作流: 优先考虑CPU负载均衡，关注计算资源分配\n")
            f.write("• 内存密集型工作流: 重点关注内存使用情况，避免内存不足\n")
            f.write("• 计算密集型工作流: 需要高性能计算节点，考虑GPU加速\n")
            f.write("• 数据流密集型工作流: 优化数据传输，减少网络瓶颈\n")
            f.write("• 通信密集型工作流: 网络拓扑优化，减少通信开销\n")
            
            # 异构节点任务分配说明
            f.write("\n=== 异构节点任务分配说明 ===\n")
            f.write("• 任务执行时间会根据节点性能动态计算\n")
            f.write("• CPU不足时，执行时间按比例增加\n")
            f.write("• 内存不足时，执行时间显著增加（可能触发交换）\n")
            f.write("• I/O带宽不足时，执行时间增加\n")
            f.write("• 网络带宽不足时，执行时间增加\n")
            f.write("• 考虑了±5%的随机波动以模拟真实环境\n")
            
            # 数据质量建议
            if validation_results:
                f.write("\n=== 数据质量建议 ===\n")
                f.write("• 整体质量分数≥0.8: 数据质量优秀，适合仿真实验\n")
                f.write("• 整体质量分数≥0.6: 数据质量良好，建议优化后使用\n")
                f.write("• 整体质量分数≥0.4: 数据质量一般，需要改进\n")
                f.write("• 整体质量分数<0.4: 数据质量较差，建议重新生成\n")
        
        print(f"对比报告已保存: {output_filename}")
    
    def run_complete_workflow(self, output_dir: str = r"E:\2024_New\Load_Blancing\Load_Blancing_simulation\LoadBalance_py\data_generate\data", generate_plots: bool = True):
        """运行完整的工作流数据生成、分析、可视化和质量验证流程"""
        print("=" * 60)
        print("负载均衡仿真系统 - 工作流数据生成、分析、可视化与质量验证")
        print("=" * 60)
        print()
        
        # 1. 生成工作流数据
        generated_files = self.generate_all_workflows(output_dir)
        
        # 2. 分析工作流数据
        analysis_results = self.analyze_all_workflows(output_dir, generate_plots)
        
        # 3. 创建可视化
        self.visualize_all_workflows(output_dir, generate_plots)
        
        # 4. 验证数据质量
        validation_results = self.validate_all_workflows(output_dir, True, generate_plots)
        
        # 5. 生成对比报告
        if analysis_results:
            self.generate_comparison_report(analysis_results, validation_results)
        
        print("=" * 60)
        print("工作流数据生成、分析、可视化与质量验证完成！")
        print("=" * 60)
        
        return generated_files, analysis_results, validation_results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="工作流数据生成、分析、可视化与质量验证工具")
    parser.add_argument("--mode", choices=["generate", "analyze", "visualize", "validate", "complete"], 
                       default="complete", help="运行模式")
    parser.add_argument("--output-dir", default=r"E:\2024_New\Load_Blancing\Load_Blancing_simulation\LoadBalance_py\data_generate\data", help="输出目录")
    parser.add_argument("--no-plots", action="store_true", help="不生成图表")
    parser.add_argument("--no-reports", action="store_true", help="不生成报告")
    
    args = parser.parse_args()
    
    manager = WorkflowDataManager()
    
    if args.mode == "generate":
        manager.generate_all_workflows(args.output_dir)
    elif args.mode == "analyze":
        manager.analyze_all_workflows(args.output_dir, not args.no_plots)
    elif args.mode == "visualize":
        manager.visualize_all_workflows(args.output_dir, not args.no_plots)
    elif args.mode == "validate":
        manager.validate_all_workflows(args.output_dir, not args.no_reports, not args.no_plots)
    else:  # complete
        manager.run_complete_workflow(args.output_dir, not args.no_plots)


if __name__ == "__main__":
    main() 