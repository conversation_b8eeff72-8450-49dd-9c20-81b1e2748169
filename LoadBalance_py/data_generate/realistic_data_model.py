"""
负载均衡仿真系统 - 真实数据模型
基于真实工作流和节点性能特征的数据生成模型
"""

import random
import numpy as np
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import json


@dataclass
class RealisticTaskProfile:
    """真实任务性能特征"""
    task_type: str
    base_runtime_range: Tuple[float, float]  # 基础执行时间范围(秒)
    cpu_intensity: float  # CPU密集度 (0-1)
    memory_intensity: float  # 内存密集度 (0-1)
    io_intensity: float  # I/O密集度 (0-1)
    network_intensity: float  # 网络密集度 (0-1)
    typical_input_size: Tuple[float, float]  # 典型输入大小范围(MB)
    typical_output_size: Tuple[float, float]  # 典型输出大小范围(MB)
    parallelization_factor: float  # 并行化因子 (0-1)
    resource_scaling_factor: float  # 资源缩放因子


@dataclass
class RealisticNodeProfile:
    """真实节点性能特征"""
    node_type: str
    cpu_cores_range: Tuple[int, int]  # CPU核心数范围
    memory_capacity_range: Tuple[float, float]  # 内存容量范围(GB)
    storage_type: str  # 存储类型 (SSD/HDD)
    io_bandwidth_range: Tuple[float, float]  # I/O带宽范围(MB/s)
    network_bandwidth_range: Tuple[float, float]  # 网络带宽范围(MB/s)
    energy_efficiency_range: Tuple[float, float]  # 能源效率范围
    cost_per_hour_range: Tuple[float, float]  # 每小时成本范围($)
    reliability_factor: float  # 可靠性因子 (0-1)


class RealisticDataModel:
    """真实数据模型"""
    
    def __init__(self):
        # 基于真实工作流的任务性能特征
        self.task_profiles = {
            # Montage工作流任务特征
            'mProject': RealisticTaskProfile(
                task_type='mProject',
                base_runtime_range=(180, 600),  # 3-10分钟
                cpu_intensity=0.8,
                memory_intensity=0.6,
                io_intensity=0.7,
                network_intensity=0.3,
                typical_input_size=(100, 500),
                typical_output_size=(80, 400),
                parallelization_factor=0.7,
                resource_scaling_factor=1.2
            ),
            'mDiffFit': RealisticTaskProfile(
                task_type='mDiffFit',
                base_runtime_range=(120, 300),
                cpu_intensity=0.6,
                memory_intensity=0.5,
                io_intensity=0.4,
                network_intensity=0.2,
                typical_input_size=(50, 200),
                typical_output_size=(40, 160),
                parallelization_factor=0.8,
                resource_scaling_factor=1.1
            ),
            'mConcatFit': RealisticTaskProfile(
                task_type='mConcatFit',
                base_runtime_range=(60, 180),
                cpu_intensity=0.4,
                memory_intensity=0.3,
                io_intensity=0.3,
                network_intensity=0.1,
                typical_input_size=(20, 100),
                typical_output_size=(15, 80),
                parallelization_factor=0.9,
                resource_scaling_factor=1.0
            ),
            'mBgModel': RealisticTaskProfile(
                task_type='mBgModel',
                base_runtime_range=(90, 240),
                cpu_intensity=0.5,
                memory_intensity=0.4,
                io_intensity=0.5,
                network_intensity=0.2,
                typical_input_size=(30, 150),
                typical_output_size=(25, 120),
                parallelization_factor=0.8,
                resource_scaling_factor=1.1
            ),
            'mBackground': RealisticTaskProfile(
                task_type='mBackground',
                base_runtime_range=(150, 360),
                cpu_intensity=0.7,
                memory_intensity=0.6,
                io_intensity=0.6,
                network_intensity=0.3,
                typical_input_size=(60, 250),
                typical_output_size=(50, 200),
                parallelization_factor=0.7,
                resource_scaling_factor=1.2
            ),
            'mAdd': RealisticTaskProfile(
                task_type='mAdd',
                base_runtime_range=(240, 480),
                cpu_intensity=0.8,
                memory_intensity=0.8,
                io_intensity=0.8,
                network_intensity=0.4,
                typical_input_size=(200, 800),
                typical_output_size=(150, 600),
                parallelization_factor=0.6,
                resource_scaling_factor=1.3
            ),
            
            # Brain工作流任务特征
            'preprocessing': RealisticTaskProfile(
                task_type='preprocessing',
                base_runtime_range=(300, 900),  # 5-15分钟
                cpu_intensity=0.5,
                memory_intensity=0.9,
                io_intensity=0.6,
                network_intensity=0.4,
                typical_input_size=(500, 2000),
                typical_output_size=(400, 1600),
                parallelization_factor=0.6,
                resource_scaling_factor=1.4
            ),
            'segmentation': RealisticTaskProfile(
                task_type='segmentation',
                base_runtime_range=(240, 600),
                cpu_intensity=0.7,
                memory_intensity=0.8,
                io_intensity=0.5,
                network_intensity=0.3,
                typical_input_size=(300, 1200),
                typical_output_size=(250, 1000),
                parallelization_factor=0.7,
                resource_scaling_factor=1.3
            ),
            'registration': RealisticTaskProfile(
                task_type='registration',
                base_runtime_range=(360, 720),
                cpu_intensity=0.6,
                memory_intensity=0.9,
                io_intensity=0.7,
                network_intensity=0.5,
                typical_input_size=(400, 1500),
                typical_output_size=(350, 1300),
                parallelization_factor=0.5,
                resource_scaling_factor=1.5
            ),
            'normalization': RealisticTaskProfile(
                task_type='normalization',
                base_runtime_range=(180, 450),
                cpu_intensity=0.5,
                memory_intensity=0.7,
                io_intensity=0.6,
                network_intensity=0.3,
                typical_input_size=(200, 800),
                typical_output_size=(180, 700),
                parallelization_factor=0.8,
                resource_scaling_factor=1.2
            ),
            'statistics': RealisticTaskProfile(
                task_type='statistics',
                base_runtime_range=(120, 300),
                cpu_intensity=0.4,
                memory_intensity=0.6,
                io_intensity=0.5,
                network_intensity=0.2,
                typical_input_size=(100, 400),
                typical_output_size=(80, 320),
                parallelization_factor=0.9,
                resource_scaling_factor=1.0
            ),
            'visualization': RealisticTaskProfile(
                task_type='visualization',
                base_runtime_range=(90, 240),
                cpu_intensity=0.3,
                memory_intensity=0.8,
                io_intensity=0.7,
                network_intensity=0.4,
                typical_input_size=(150, 600),
                typical_output_size=(200, 800),
                parallelization_factor=0.6,
                resource_scaling_factor=1.3
            ),
            
            # SIPHT工作流任务特征
            'blast': RealisticTaskProfile(
                task_type='blast',
                base_runtime_range=(900, 2400),  # 15-40分钟
                cpu_intensity=0.9,
                memory_intensity=0.6,
                io_intensity=0.5,
                network_intensity=0.3,
                typical_input_size=(200, 800),
                typical_output_size=(150, 600),
                parallelization_factor=0.8,
                resource_scaling_factor=1.1
            ),
            'clustalw': RealisticTaskProfile(
                task_type='clustalw',
                base_runtime_range=(600, 1200),
                cpu_intensity=0.8,
                memory_intensity=0.5,
                io_intensity=0.4,
                network_intensity=0.2,
                typical_input_size=(100, 400),
                typical_output_size=(80, 320),
                parallelization_factor=0.9,
                resource_scaling_factor=1.0
            ),
            'rnafold': RealisticTaskProfile(
                task_type='rnafold',
                base_runtime_range=(300, 900),
                cpu_intensity=0.7,
                memory_intensity=0.6,
                io_intensity=0.3,
                network_intensity=0.2,
                typical_input_size=(50, 200),
                typical_output_size=(40, 160),
                parallelization_factor=0.8,
                resource_scaling_factor=1.1
            ),
            'infernal': RealisticTaskProfile(
                task_type='infernal',
                base_runtime_range=(600, 1500),
                cpu_intensity=0.8,
                memory_intensity=0.7,
                io_intensity=0.5,
                network_intensity=0.3,
                typical_input_size=(80, 300),
                typical_output_size=(60, 240),
                parallelization_factor=0.7,
                resource_scaling_factor=1.2
            ),
            'cmsearch': RealisticTaskProfile(
                task_type='cmsearch',
                base_runtime_range=(450, 1200),
                cpu_intensity=0.7,
                memory_intensity=0.6,
                io_intensity=0.4,
                network_intensity=0.2,
                typical_input_size=(60, 250),
                typical_output_size=(50, 200),
                parallelization_factor=0.8,
                resource_scaling_factor=1.1
            ),
            'genome_scan': RealisticTaskProfile(
                task_type='genome_scan',
                base_runtime_range=(1800, 3600),
                cpu_intensity=0.9,
                memory_intensity=0.8,
                io_intensity=0.6,
                network_intensity=0.4,
                typical_input_size=(500, 2000),
                typical_output_size=(400, 1600),
                parallelization_factor=0.6,
                resource_scaling_factor=1.4
            ),
            
            # Epigenomics工作流任务特征
            'fastqc': RealisticTaskProfile(
                task_type='fastqc',
                base_runtime_range=(120, 300),
                cpu_intensity=0.4,
                memory_intensity=0.3,
                io_intensity=0.6,
                network_intensity=0.2,
                typical_input_size=(1000, 5000),
                typical_output_size=(50, 200),
                parallelization_factor=0.9,
                resource_scaling_factor=1.0
            ),
            'bwa': RealisticTaskProfile(
                task_type='bwa',
                base_runtime_range=(1200, 2400),  # 20-40分钟
                cpu_intensity=0.8,
                memory_intensity=0.7,
                io_intensity=0.8,
                network_intensity=0.4,
                typical_input_size=(2000, 8000),
                typical_output_size=(1500, 6000),
                parallelization_factor=0.7,
                resource_scaling_factor=1.2
            ),
            'samtools': RealisticTaskProfile(
                task_type='samtools',
                base_runtime_range=(300, 900),
                cpu_intensity=0.6,
                memory_intensity=0.5,
                io_intensity=0.7,
                network_intensity=0.3,
                typical_input_size=(500, 2000),
                typical_output_size=(400, 1600),
                parallelization_factor=0.8,
                resource_scaling_factor=1.1
            ),
            'macs2': RealisticTaskProfile(
                task_type='macs2',
                base_runtime_range=(600, 1800),
                cpu_intensity=0.7,
                memory_intensity=0.8,
                io_intensity=0.6,
                network_intensity=0.3,
                typical_input_size=(800, 3000),
                typical_output_size=(600, 2400),
                parallelization_factor=0.6,
                resource_scaling_factor=1.3
            ),
            'homer': RealisticTaskProfile(
                task_type='homer',
                base_runtime_range=(900, 2400),
                cpu_intensity=0.8,
                memory_intensity=0.9,
                io_intensity=0.7,
                network_intensity=0.4,
                typical_input_size=(1000, 4000),
                typical_output_size=(800, 3200),
                parallelization_factor=0.5,
                resource_scaling_factor=1.4
            ),
            'bedtools': RealisticTaskProfile(
                task_type='bedtools',
                base_runtime_range=(180, 600),
                cpu_intensity=0.5,
                memory_intensity=0.4,
                io_intensity=0.6,
                network_intensity=0.2,
                typical_input_size=(200, 800),
                typical_output_size=(150, 600),
                parallelization_factor=0.9,
                resource_scaling_factor=1.0
            ),
            'igv': RealisticTaskProfile(
                task_type='igv',
                base_runtime_range=(120, 360),
                cpu_intensity=0.4,
                memory_intensity=0.6,
                io_intensity=0.5,
                network_intensity=0.3,
                typical_input_size=(100, 400),
                typical_output_size=(120, 480),
                parallelization_factor=0.7,
                resource_scaling_factor=1.2
            ),
            
            # CyberShake工作流任务特征
            'rupture_generator': RealisticTaskProfile(
                task_type='rupture_generator',
                base_runtime_range=(300, 600),
                cpu_intensity=0.6,
                memory_intensity=0.5,
                io_intensity=0.4,
                network_intensity=0.7,
                typical_input_size=(200, 800),
                typical_output_size=(150, 600),
                parallelization_factor=0.8,
                resource_scaling_factor=1.1
            ),
            'velocity_model': RealisticTaskProfile(
                task_type='velocity_model',
                base_runtime_range=(600, 1200),
                cpu_intensity=0.7,
                memory_intensity=0.6,
                io_intensity=0.5,
                network_intensity=0.6,
                typical_input_size=(300, 1200),
                typical_output_size=(250, 1000),
                parallelization_factor=0.7,
                resource_scaling_factor=1.2
            ),
            'srfgen': RealisticTaskProfile(
                task_type='srfgen',
                base_runtime_range=(450, 900),
                cpu_intensity=0.6,
                memory_intensity=0.5,
                io_intensity=0.4,
                network_intensity=0.5,
                typical_input_size=(150, 600),
                typical_output_size=(120, 480),
                parallelization_factor=0.8,
                resource_scaling_factor=1.1
            ),
            'syn1d': RealisticTaskProfile(
                task_type='syn1d',
                base_runtime_range=(900, 1800),
                cpu_intensity=0.8,
                memory_intensity=0.7,
                io_intensity=0.6,
                network_intensity=0.7,
                typical_input_size=(400, 1600),
                typical_output_size=(350, 1400),
                parallelization_factor=0.6,
                resource_scaling_factor=1.3
            ),
            'syn2d': RealisticTaskProfile(
                task_type='syn2d',
                base_runtime_range=(1200, 2400),
                cpu_intensity=0.8,
                memory_intensity=0.7,
                io_intensity=0.6,
                network_intensity=0.7,
                typical_input_size=(500, 2000),
                typical_output_size=(450, 1800),
                parallelization_factor=0.6,
                resource_scaling_factor=1.3
            ),
            'syn3d': RealisticTaskProfile(
                task_type='syn3d',
                base_runtime_range=(1800, 3600),  # 30-60分钟
                cpu_intensity=0.9,
                memory_intensity=0.8,
                io_intensity=0.6,
                network_intensity=0.8,
                typical_input_size=(500, 2000),
                typical_output_size=(400, 1600),
                parallelization_factor=0.5,
                resource_scaling_factor=1.4
            ),
            'post_processing': RealisticTaskProfile(
                task_type='post_processing',
                base_runtime_range=(300, 900),
                cpu_intensity=0.5,
                memory_intensity=0.6,
                io_intensity=0.7,
                network_intensity=0.4,
                typical_input_size=(200, 800),
                typical_output_size=(300, 1200),
                parallelization_factor=0.8,
                resource_scaling_factor=1.2
            )
        }
        
        # 基于真实节点类型的性能特征
        self.node_profiles = {
            'cpu_intensive': RealisticNodeProfile(
                node_type='cpu_intensive',
                cpu_cores_range=(16, 32),
                memory_capacity_range=(32, 64),  # GB
                storage_type='SSD',
                io_bandwidth_range=(2000, 5000),  # MB/s
                network_bandwidth_range=(1000, 2000),  # MB/s
                energy_efficiency_range=(0.75, 0.90),
                cost_per_hour_range=(0.15, 0.30),
                reliability_factor=0.98
            ),
            'memory_intensive': RealisticNodeProfile(
                node_type='memory_intensive',
                cpu_cores_range=(8, 16),
                memory_capacity_range=(64, 128),  # GB
                storage_type='SSD',
                io_bandwidth_range=(1500, 3000),
                network_bandwidth_range=(800, 1500),
                energy_efficiency_range=(0.70, 0.85),
                cost_per_hour_range=(0.12, 0.25),
                reliability_factor=0.97
            ),
            'compute_intensive': RealisticNodeProfile(
                node_type='compute_intensive',
                cpu_cores_range=(32, 64),
                memory_capacity_range=(16, 32),  # GB
                storage_type='SSD',
                io_bandwidth_range=(1000, 2500),
                network_bandwidth_range=(500, 1000),
                energy_efficiency_range=(0.80, 0.95),
                cost_per_hour_range=(0.20, 0.40),
                reliability_factor=0.99
            ),
            'data_intensive': RealisticNodeProfile(
                node_type='data_intensive',
                cpu_cores_range=(4, 12),
                memory_capacity_range=(8, 16),  # GB
                storage_type='SSD',
                io_bandwidth_range=(5000, 10000),
                network_bandwidth_range=(2000, 5000),
                energy_efficiency_range=(0.65, 0.80),
                cost_per_hour_range=(0.10, 0.20),
                reliability_factor=0.96
            ),
            'network_intensive': RealisticNodeProfile(
                node_type='network_intensive',
                cpu_cores_range=(8, 20),
                memory_capacity_range=(16, 32),  # GB
                storage_type='SSD',
                io_bandwidth_range=(1000, 3000),
                network_bandwidth_range=(5000, 10000),
                energy_efficiency_range=(0.70, 0.85),
                cost_per_hour_range=(0.18, 0.35),
                reliability_factor=0.98
            )
        }
    
    def generate_realistic_task(self, task_type: str, task_id: str) -> Dict[str, Any]:
        """生成真实的任务数据"""
        if task_type not in self.task_profiles:
            raise ValueError(f"不支持的任务类型: {task_type}")
        
        profile = self.task_profiles[task_type]
        
        # 生成基础执行时间
        base_runtime = random.uniform(*profile.base_runtime_range)
        
        # 生成输入输出大小
        input_size = random.uniform(*profile.typical_input_size)
        output_size = random.uniform(*profile.typical_output_size)
        
        # 基于任务特征生成资源需求
        cpu_requirement = self._calculate_cpu_requirement(profile, base_runtime)
        memory_requirement = self._calculate_memory_requirement(profile, input_size)
        io_requirement = self._calculate_io_requirement(profile, input_size, output_size)
        network_requirement = self._calculate_network_requirement(profile, input_size, output_size)
        
        return {
            'task_id': task_id,
            'base_runtime': base_runtime,
            'input_size': input_size,
            'output_size': output_size,
            'cpu_requirement': cpu_requirement,
            'memory_requirement': memory_requirement,
            'io_requirement': io_requirement,
            'network_requirement': network_requirement,
            'task_type': task_type,
            'dependencies': []
        }
    
    def generate_realistic_node(self, node_type: str, node_id: str) -> Dict[str, Any]:
        """生成真实的节点数据"""
        if node_type not in self.node_profiles:
            raise ValueError(f"不支持的节点类型: {node_type}")
        
        profile = self.node_profiles[node_type]
        
        # 生成节点容量
        cpu_capacity = random.randint(*profile.cpu_cores_range)
        memory_capacity = random.uniform(*profile.memory_capacity_range) * 1024  # 转换为MB
        io_capacity = random.uniform(*profile.io_bandwidth_range)
        network_capacity = random.uniform(*profile.network_bandwidth_range)
        energy_efficiency = random.uniform(*profile.energy_efficiency_range)
        cost_per_hour = random.uniform(*profile.cost_per_hour_range)
        
        return {
            'node_id': node_id,
            'cpu_capacity': cpu_capacity,
            'memory_capacity': memory_capacity,
            'io_capacity': io_capacity,
            'network_capacity': network_capacity,
            'energy_efficiency': energy_efficiency,
            'cost_per_hour': cost_per_hour,
            'node_type': node_type,
            'reliability_factor': profile.reliability_factor
        }
    
    def _calculate_cpu_requirement(self, profile: RealisticTaskProfile, base_runtime: float) -> float:
        """计算CPU需求"""
        # 基于任务密集度和执行时间计算CPU需求
        base_cpu = profile.cpu_intensity * 4  # 基础CPU需求
        runtime_factor = base_runtime / 300  # 基于5分钟标准化
        scaling_factor = profile.resource_scaling_factor
        
        cpu_requirement = base_cpu * runtime_factor * scaling_factor
        
        # 添加随机波动 (±10%)
        noise = random.uniform(0.9, 1.1)
        cpu_requirement *= noise
        
        return max(1.0, min(16.0, cpu_requirement))  # 限制在1-16核之间
    
    def _calculate_memory_requirement(self, profile: RealisticTaskProfile, input_size: float) -> float:
        """计算内存需求"""
        # 基于输入大小和内存密集度计算内存需求
        base_memory = input_size * profile.memory_intensity * 2  # 输入大小的2倍作为基础
        scaling_factor = profile.resource_scaling_factor
        
        memory_requirement = base_memory * scaling_factor
        
        # 添加随机波动 (±15%)
        noise = random.uniform(0.85, 1.15)
        memory_requirement *= noise
        
        return max(512, min(32768, memory_requirement))  # 限制在512MB-32GB之间
    
    def _calculate_io_requirement(self, profile: RealisticTaskProfile, input_size: float, output_size: float) -> float:
        """计算I/O需求"""
        # 基于输入输出大小和I/O密集度计算I/O需求
        total_data = input_size + output_size
        base_io = total_data * profile.io_intensity / 60  # 假设1分钟内处理完
        scaling_factor = profile.resource_scaling_factor
        
        io_requirement = base_io * scaling_factor
        
        # 添加随机波动 (±20%)
        noise = random.uniform(0.8, 1.2)
        io_requirement *= noise
        
        return max(10, min(1000, io_requirement))  # 限制在10-1000MB/s之间
    
    def _calculate_network_requirement(self, profile: RealisticTaskProfile, input_size: float, output_size: float) -> float:
        """计算网络需求"""
        # 基于输入输出大小和网络密集度计算网络需求
        total_data = input_size + output_size
        base_network = total_data * profile.network_intensity / 120  # 假设2分钟内传输完
        scaling_factor = profile.resource_scaling_factor
        
        network_requirement = base_network * scaling_factor
        
        # 添加随机波动 (±25%)
        noise = random.uniform(0.75, 1.25)
        network_requirement *= noise
        
        return max(5, min(500, network_requirement))  # 限制在5-500MB/s之间
    
    def validate_task_node_compatibility(self, task: Dict[str, Any], node: Dict[str, Any]) -> bool:
        """验证任务和节点的兼容性"""
        # 检查CPU兼容性
        if task['cpu_requirement'] > node['cpu_capacity']:
            return False
        
        # 检查内存兼容性
        if task['memory_requirement'] > node['memory_capacity']:
            return False
        
        # 检查I/O兼容性
        if task['io_requirement'] > node['io_capacity']:
            return False
        
        # 检查网络兼容性
        if task['network_requirement'] > node['network_capacity']:
            return False
        
        return True
    
    def calculate_realistic_execution_time(self, task: Dict[str, Any], node: Dict[str, Any]) -> float:
        """计算真实的执行时间"""
        base_runtime = task['base_runtime']
        
        # CPU性能影响
        cpu_ratio = task['cpu_requirement'] / node['cpu_capacity']
        if cpu_ratio > 1:
            cpu_factor = cpu_ratio ** 1.5  # 非线性增长
        elif cpu_ratio < 0.5:
            cpu_factor = 0.85  # 轻微性能提升
        else:
            cpu_factor = 1.0
        
        # 内存性能影响
        memory_ratio = task['memory_requirement'] / node['memory_capacity']
        if memory_ratio > 1:
            memory_factor = memory_ratio ** 2  # 内存不足严重影响性能
        else:
            memory_factor = 1.0
        
        # I/O性能影响
        io_ratio = task['io_requirement'] / node['io_capacity']
        if io_ratio > 1:
            io_factor = io_ratio ** 1.3
        else:
            io_factor = 1.0
        
        # 网络性能影响
        network_ratio = task['network_requirement'] / node['network_capacity']
        if network_ratio > 1:
            network_factor = network_ratio ** 1.2
        else:
            network_factor = 1.0
        
        # 计算实际执行时间
        actual_runtime = base_runtime * cpu_factor * memory_factor * io_factor * network_factor
        
        # 添加随机波动 (±8%)
        noise = random.uniform(0.92, 1.08)
        actual_runtime *= noise
        
        return max(actual_runtime, 1.0)
    
    def estimate_node_capacity(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """估算节点容量"""
        # 基于节点性能估算可同时执行的任务数量
        cpu_tasks = int(node['cpu_capacity'] / 2)  # 假设每个任务平均需要2核
        memory_tasks = int(node['memory_capacity'] / 2048)  # 假设每个任务平均需要2GB内存
        io_tasks = int(node['io_capacity'] / 100)  # 假设每个任务平均需要100MB/s I/O
        network_tasks = int(node['network_capacity'] / 50)  # 假设每个任务平均需要50MB/s网络
        
        return {
            'estimated_cpu_tasks': max(1, cpu_tasks),
            'estimated_memory_tasks': max(1, memory_tasks),
            'estimated_io_tasks': max(1, io_tasks),
            'estimated_network_tasks': max(1, network_tasks),
            'estimated_total_tasks': min(cpu_tasks, memory_tasks, io_tasks, network_tasks)
        }


def main():
    """测试真实数据模型"""
    model = RealisticDataModel()
    
    # 生成测试任务
    test_task = model.generate_realistic_task('blast', 'test_task_0')
    print("生成的任务数据:")
    print(json.dumps(test_task, indent=2, ensure_ascii=False))
    
    # 生成测试节点
    test_node = model.generate_realistic_node('compute_intensive', 'test_node_0')
    print("\n生成的节点数据:")
    print(json.dumps(test_node, indent=2, ensure_ascii=False))
    
    # 验证兼容性
    is_compatible = model.validate_task_node_compatibility(test_task, test_node)
    print(f"\n任务节点兼容性: {is_compatible}")
    
    # 计算执行时间
    execution_time = model.calculate_realistic_execution_time(test_task, test_node)
    print(f"预计执行时间: {execution_time:.2f}秒")
    
    # 估算节点容量
    capacity = model.estimate_node_capacity(test_node)
    print(f"节点容量估算: {capacity}")


if __name__ == "__main__":
    main() 