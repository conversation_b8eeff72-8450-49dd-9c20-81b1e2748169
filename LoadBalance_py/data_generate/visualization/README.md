# Workflow Visualization Module

## Overview

This module provides workflow visualization functionality for the load balancing simulation system, supporting high-quality workflow chart generation with Trans SMC journal style.

## Main Features

### 1. Workflow Diagram with Circular Nodes
- **Circular Task Representation**: Tasks are represented by circles with size based on resource requirements
- **Resource-Based Scaling**: Circle size reflects total resource intensity (CPU + Memory + I/O + Network)
- **Improved Layout**: Left-to-right hierarchical layout that better represents workflow execution order
- **Dependencies**: Curved arrows showing task dependencies with data transfer information
- **Legend**: Displaying statistics for each task type

### 2. Resource Requirements Distribution
- **CPU Requirements**: Display CPU core requirements for each task
- **Memory Requirements**: Display memory requirements (GB) for each task
- **I/O Requirements**: Display I/O bandwidth requirements (MB/s) for each task
- **Network Requirements**: Display network bandwidth requirements (MB/s) for each task

### 3. Execution Time Comparison
- **Box Plot**: Display execution time distribution for tasks across different nodes
- **Performance Analysis**: Calculate actual execution time based on CPU and memory ratios
- **Node Comparison**: Show performance differences of tasks on different node types

### 4. Resource Summary Chart (5×4 Matrix)
- **Comprehensive Overview**: 5 rows (workflows) × 4 columns (resource types)
- **Resource Types**: CPU, Memory, I/O, Network requirements for all workflows
- **Value Labels**: Direct value display on each bar for easy reading
- **Color Coding**: Consistent color scheme across all workflows

## Color Scheme

Professional color scheme following Trans SMC journal style:

### Montage Workflow - Blue Series
- mProject: #1f77b4 (Deep blue)
- mDiffFit: #ff7f0e (Orange)
- mConcatFit: #2ca02c (Green)
- mBgModel: #d62728 (Red)
- mBackground: #9467bd (Purple)
- mAdd: #8c564b (Brown)

### Brain Workflow - Green Series
- preprocessing: #2e8b57 (Sea green)
- segmentation: #32cd32 (Lime green)
- registration: #228b22 (Forest green)
- normalization: #90ee90 (Light green)
- statistics: #98fb98 (Pale green)
- visualization: #00ff7f (Spring green)

### SIPHT Workflow - Red Series
- blast: #dc143c (Crimson)
- clustalw: #ff4500 (Orange red)
- rnafold: #ff6347 (Tomato)
- infernal: #ff0000 (Red)
- cmsearch: #b22222 (Fire brick)
- genome_scan: #cd5c5c (Indian red)

### Epigenomics Workflow - Purple Series
- fastqc: #8a2be2 (Blue violet)
- bwa: #9370db (Medium purple)
- samtools: #ba55d3 (Medium orchid)
- macs2: #9932cc (Dark orchid)
- homer: #da70d6 (Orchid)
- bedtools: #dda0dd (Plum)
- igv: #ee82ee (Violet)

### CyberShake Workflow - Orange Series
- rupture_generator: #ff8c00 (Dark orange)
- velocity_model: #ffa500 (Orange)
- srfgen: #ff7f50 (Coral)
- syn1d: #ff6347 (Tomato)
- syn2d: #ff4500 (Orange red)
- syn3d: #ff8c00 (Dark orange)
- post_processing: #ffa07a (Light salmon)

## File Structure

```
visualization/
├── workflow_visualizer.py          # Main visualization program
├── README.md                       # Documentation
├── workflow_resource_summary.png   # 5×4 resource summary chart
├── montage_workflow_diagram.png    # Montage workflow diagram (circular nodes)
├── montage_resource_requirements.png # Montage resource requirements
├── montage_execution_time_comparison.png # Montage execution time comparison
├── brain_workflow_diagram.png      # Brain workflow diagram (circular nodes)
├── brain_resource_requirements.png # Brain resource requirements
├── brain_execution_time_comparison.png # Brain execution time comparison
├── sipht_workflow_diagram.png      # SIPHT workflow diagram (circular nodes)
├── sipht_resource_requirements.png # SIPHT resource requirements
├── sipht_execution_time_comparison.png # SIPHT execution time comparison
├── epigenomics_workflow_diagram.png # Epigenomics workflow diagram (circular nodes)
├── epigenomics_resource_requirements.png # Epigenomics resource requirements
├── epigenomics_execution_time_comparison.png # Epigenomics execution time comparison
├── cybershake_workflow_diagram.png # CyberShake workflow diagram (circular nodes)
├── cybershake_resource_requirements.png # CyberShake resource requirements
└── cybershake_execution_time_comparison.png # CyberShake execution time comparison
```

## Usage

### Command Line Execution
```bash
cd LoadBalance_py/data_generate
python visualization/workflow_visualizer.py
```

### Programming Interface
```python
from visualization.workflow_visualizer import WorkflowVisualizer

# Create visualizer
visualizer = WorkflowVisualizer()

# Load workflow data
visualizer.load_workflow_data("data/montage_workflows.json")

# Generate workflow diagram with circular nodes
visualizer.plot_workflow_diagram()

# Generate resource requirements chart
visualizer.plot_resource_requirements()

# Generate execution time comparison
visualizer.plot_execution_time_comparison()

# Generate resource summary chart (5×4 matrix)
visualizer.plot_resource_summary()

# Generate all charts
visualizer.create_comprehensive_visualization()
```

## Technical Features

### 1. Data Source
- Load workflow data from `data/` folder
- Support JSON format workflow definition files

### 2. Output Format
- All charts saved to `visualization/` folder
- High-resolution PNG format (300 DPI)
- Suitable for academic paper publication

### 3. Layout Optimization
- **Circular Node Design**: Tasks represented as circles with size based on resource requirements
- **Resource-Based Scaling**: Circle size = 0.1 + (total_resource_intensity * 0.7)
- **Automatic Calculation**: Task levels and dependencies calculated automatically
- **Curved Dependencies**: Curved arrows for better dependency visualization
- **Smart Positioning**: Node positioning to avoid overlap

### 4. Style Settings
- Trans SMC journal style professional color scheme
- Times New Roman font family
- Unified font sizes (12pt base, 14pt titles, 18pt main titles)
- Clear grid lines and borders
- Consistent legend and label styles
- All text in English for international compatibility

## Supported Workflow Types

1. **Montage**: Astronomical image mosaic workflow
2. **Brain**: Brain imaging processing workflow
3. **SIPHT**: Bioinformatics workflow
4. **Epigenomics**: Epigenomics workflow
5. **CyberShake**: Earthquake simulation workflow

## Key Improvements in v4.1

### Montage Workflow Blue Series
- **Blue Color Scheme**: Montage workflow resource distribution charts use blue series colors
- **Consistent Blue Theme**: All montage charts (individual and summary) use blue color palette
- **Professional Appearance**: Blue series provides clean, professional look for astronomical workflow
- **Enhanced Distinction**: Clear visual distinction between montage and other workflows

### Circular Node Design
- **Resource-Based Sizing**: Circle size reflects total resource requirements
- **Normalized Scaling**: Resource requirements normalized across all tasks
- **Visual Intensity**: Larger circles indicate higher resource demands
- **Clear Labels**: Task ID, type, and resource info clearly displayed

### Resource Summary Chart
- **5×4 Matrix Layout**: 5 workflows × 4 resource types
- **Comprehensive View**: All workflows and resource types in one chart
- **Value Labels**: Direct value display on each bar
- **Consistent Colors**: Same color scheme across all subplots
- **Professional Layout**: Large format (20×25 inches) for detailed viewing

### Enhanced Visualization
- **Better Resource Representation**: Circle size directly correlates with resource usage
- **Improved Readability**: Clear labels and positioning
- **Professional Appearance**: High-quality output suitable for publications
- **Comprehensive Analysis**: Both individual workflow and summary views available

## Notes

1. Ensure `data/` folder contains corresponding workflow data files
2. Program automatically creates `visualization/` folder
3. All charts use English titles and labels for international compatibility
4. All charts are high-resolution suitable for academic publication
5. Circle size in workflow diagrams reflects total resource intensity
6. Resource summary chart provides comprehensive overview of all workflows
7. Montage workflow uses blue series colors for resource distribution charts

## Update Log

### v4.1 (Current Version)
- **Montage Blue Series**: Montage workflow resource charts use blue color palette
- **Enhanced Color Consistency**: Blue series applied to both individual and summary charts
- **Professional Blue Theme**: Clean, professional appearance for astronomical workflow

### v4.0
- **Circular Node Design**: Tasks represented as circles with resource-based sizing
- **Resource Summary Chart**: 5×4 matrix showing all workflows and resource types
- **Enhanced Resource Visualization**: Circle size reflects total resource requirements
- **Improved Layout**: Better spacing and positioning for circular nodes
- **Comprehensive Analysis**: Both individual and summary views available

### v3.0
- Complete English interface for international compatibility
- Improved workflow layout with curved dependency arrows
- Unified font settings with Times New Roman family
- Enhanced node design with better information layout
- Optimized aspect ratios and spacing
- Professional color scheme following Trans SMC journal standards

### v2.0
- Vertical layout for better workflow execution order representation
- Updated to Trans SMC journal style color scheme
- Fixed module import issues
- Used English titles to avoid font issues
- Optimized chart styles and layouts

### v1.0
- Basic visualization functionality
- Horizontal layout
- Basic color scheme 