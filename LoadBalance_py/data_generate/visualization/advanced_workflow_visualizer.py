"""
高级工作流可视化程序
基于JSON数据格式，支持四种性能类型和五种颜色
"""

import json
import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from collections import defaultdict
from matplotlib.patches import Patch
import networkx as nx
from typing import Dict, List, Any, Optional


class AdvancedWorkflowVisualizer:
    """高级工作流可视化器"""
    
    def __init__(self):
        # 基础颜色配置 - 五种颜色对应四种性能类型
        self.BASE_COLORS = {
            1: ['#FFE6E6', '#FF0000'],  # CPU密集型 - 红色渐变
            2: ['#E6FFE6', '#00FF00'],  # 内存密集型 - 绿色渐变  
            3: ['#E6E6FF', '#0000FF'],  # I/O密集型 - 蓝色渐变
            4: ['#FFE6FF', '#FF00FF'],  # 网络密集型 - 紫色渐变
            5: ['#FFFFE6', '#FFFF00']   # 通用型 - 黄色渐变
        }
        
        # 设置matplotlib样式
        plt.style.use('default')
        plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.linewidth'] = 1.5
        
    def generate_color_gradients(self, max_level: int) -> Dict[int, List[str]]:
        """根据最大层级动态生成颜色梯度"""
        max_level = max(max_level, 2)
        
        color_gradients = {}
        for task_type, colors in self.BASE_COLORS.items():
            start_color = mcolors.to_rgb(colors[0])
            end_color = mcolors.to_rgb(colors[1])
            
            gradient = []
            for i in range(max_level):
                t = i / (max_level - 1)
                r = start_color[0] + (end_color[0] - start_color[0]) * t
                g = start_color[1] + (end_color[1] - start_color[1]) * t
                b = start_color[2] + (end_color[2] - start_color[2]) * t
                
                color = mcolors.to_hex((r, g, b))
                gradient.append(color)
            
            color_gradients[task_type] = gradient
        
        return color_gradients
    
    def load_workflow_data(self, file_path: str) -> Dict[str, Any]:
        """加载JSON格式的工作流数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"已加载工作流数据: {file_path}")
        return data
    
    def calculate_dependency_levels(self, tasks: Dict[str, Any]) -> Dict[str, int]:
        """计算任务依赖层级"""
        levels = {}
        
        # 初始化没有依赖的任务
        for task_id in tasks:
            if not tasks[task_id].get('dependencies', []):
                levels[task_id] = 1
        
        # 迭代计算层级
        updated = True
        while updated:
            updated = False
            for task_id in tasks:
                if task_id in levels:
                    continue
                deps = tasks[task_id].get('dependencies', [])
                if all(dep in levels for dep in deps):
                    levels[task_id] = max(levels[dep] for dep in deps) + 1
                    updated = True
        
        # 处理可能的循环依赖
        for task_id in tasks:
            if task_id not in levels:
                levels[task_id] = 1
        
        return levels
    
    def get_task_type_from_name(self, task_type: str) -> int:
        """根据任务类型名称确定性能类型"""
        type_mapping = {
            # Montage工作流
            'mProject': 1, 'mDiffFit': 1, 'mConcatFit': 1, 'mBgModel': 1, 'mBackground': 1, 'mAdd': 1,
            # Brain工作流
            'preprocessing': 2, 'segmentation': 2, 'registration': 2, 'normalization': 2, 'statistics': 2, 'visualization': 2,
            # SIPHT工作流
            'blast': 3, 'clustalw': 3, 'rnafold': 3, 'infernal': 3, 'cmsearch': 3, 'genome_scan': 3,
            # Epigenomics工作流
            'fastqc': 4, 'bwa': 4, 'samtools': 4, 'macs2': 4, 'homer': 4, 'bedtools': 4, 'igv': 4,
            # CyberShake工作流
            'rupture_generator': 5, 'velocity_model': 5, 'srfgen': 5, 'syn1d': 5, 'syn2d': 5, 'syn3d': 5, 'post_processing': 5
        }
        return type_mapping.get(task_type, 5)  # 默认为通用型
    
    def visualize_workflow_diagram(self, data: Dict[str, Any], output_file: str = None):
        """可视化工作流图"""
        tasks = data['tasks']
        edges = data['edges']
        workflow_type = data['workflow_type']
        
        # 计算依赖层级
        levels = self.calculate_dependency_levels(tasks)
        max_level = max(levels.values()) if levels else 1
        
        # 生成颜色梯度
        color_gradients = self.generate_color_gradients(max_level)
        
        # 创建NetworkX图
        G = nx.DiGraph()
        
        # 添加节点
        for task_id, task in tasks.items():
            task_type = self.get_task_type_from_name(task['task_type'])
            level = levels.get(task_id, 1)
            color_index = min(level - 1, len(color_gradients[task_type]) - 1)
            color = color_gradients[task_type][color_index]
            
            # 节点标签
            label = f"Task {task_id}\n{task['task_type']}\nCPU: {task['cpu_requirement']:.2f}\nMEM: {task['memory_requirement']/1024:.2f}GB"
            
            G.add_node(task_id, 
                      label=label,
                      color=color,
                      level=level,
                      task_type=task_type)
        
        # 添加边
        for edge in edges:
            source, target, edge_data = edge
            G.add_edge(source, target, 
                      data_size=edge_data.get('data_size', 0),
                      transfer_time=edge_data.get('transfer_time', 0))
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(20, 12))
        
        # 计算节点位置
        pos = self._calculate_node_positions(G, levels)
        
        # 绘制节点
        self._draw_nodes(ax, G, pos, color_gradients)
        
        # 绘制边
        self._draw_edges(ax, G, pos)
        
        # 设置图形属性
        ax.set_xlim(-1, max_level + 1)
        ax.set_ylim(-1, max(len([n for n in G.nodes() if G.nodes[n]['level'] == level]) for level in range(1, max_level + 1)) + 1)
        ax.axis('off')
        
        # 添加标题
        plt.title(f'{workflow_type.upper()} Workflow Diagram', fontsize=18, fontweight='bold', pad=30)
        
        # 添加图例
        self._add_legend(ax, color_gradients, max_level)
        
        plt.tight_layout()
        
        # 保存图形
        if output_file is None:
            output_file = f"{workflow_type}_advanced_diagram.png"
        
        os.makedirs("visualization", exist_ok=True)
        output_path = os.path.join("visualization", output_file)
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"工作流图已保存: {output_path}")
        
        plt.show()
    
    def _calculate_node_positions(self, G: nx.DiGraph, levels: Dict[str, int]) -> Dict[str, tuple]:
        """计算节点位置"""
        pos = {}
        nodes_by_level = defaultdict(list)
        
        for node in G.nodes():
            level = G.nodes[node]['level']
            nodes_by_level[level].append(node)
        
        for level in sorted(nodes_by_level.keys()):
            nodes = nodes_by_level[level]
            y_positions = np.linspace(-len(nodes)/2, len(nodes)/2, len(nodes))
            
            for i, node in enumerate(nodes):
                pos[node] = (level, y_positions[i])
        
        return pos
    
    def _draw_nodes(self, ax, G: nx.DiGraph, pos: Dict[str, tuple], color_gradients: Dict[int, List[str]]):
        """绘制节点"""
        for node in G.nodes():
            x, y = pos[node]
            node_data = G.nodes[node]
            color = node_data['color']
            level = node_data['level']
            
            # 绘制圆形节点
            circle = plt.Circle((x, y), 0.3, 
                              facecolor=color, edgecolor='#333333', 
                              linewidth=2.0, alpha=0.9)
            ax.add_patch(circle)
            
            # 添加任务ID标签
            ax.text(x, y, f"Task {node}", 
                   ha='center', va='center', fontsize=10, fontweight='bold', color='white')
            
            # 添加任务类型标签
            ax.text(x, y - 0.4, node_data['label'].split('\n')[1], 
                   ha='center', va='center', fontsize=8,
                   bbox=dict(boxstyle="round,pad=0.1", facecolor='white', alpha=0.95, edgecolor='#333333'))
    
    def _draw_edges(self, ax, G: nx.DiGraph, pos: Dict[str, tuple]):
        """绘制边"""
        for edge in G.edges(data=True):
            source, target, edge_data = edge
            source_pos = pos[source]
            target_pos = pos[target]
            
            # 绘制箭头
            ax.annotate('', xy=target_pos, xytext=source_pos,
                       arrowprops=dict(arrowstyle='->', lw=2, color='#333333', alpha=0.8))
            
            # 添加数据传输大小标签
            if 'data_size' in edge_data and edge_data['data_size'] > 0:
                mid_x = (source_pos[0] + target_pos[0]) / 2
                mid_y = (source_pos[1] + target_pos[1]) / 2 + 0.2
                ax.text(mid_x, mid_y, f"{edge_data['data_size']:.2f}MB", 
                       ha='center', va='center', fontsize=8,
                       bbox=dict(boxstyle="round,pad=0.1", facecolor='#fffacd', alpha=0.95, edgecolor='#333333'))
    
    def _add_legend(self, ax, color_gradients: Dict[int, List[str]], max_level: int):
        """添加图例"""
        legend_elements = []
        type_names = {
            1: "CPU Intensive", 
            2: "Memory Intensive", 
            3: "I/O Intensive", 
            4: "Network Intensive", 
            5: "General Type"
        }
        
        # 添加任务类型图例
        for task_type in range(1, 6):
            color = color_gradients[task_type][-1]  # 使用最深色
            legend_elements.append(
                Patch(color=color, label=type_names[task_type])
            )
        
        # 添加层级图例（使用CPU密集型作为示例）
        for level in range(1, min(max_level + 1, 6)):  # 最多显示5个层级
            color = color_gradients[1][level - 1]
            legend_elements.append(
                Patch(color=color, label=f'Level {level}')
            )
        
        ax.legend(handles=legend_elements, loc='upper right', 
                 bbox_to_anchor=(1.05, 1), fontsize=10, framealpha=0.95,
                 title="Task Types & Levels", title_fontsize=12)
    
    def visualize_resource_distribution(self, data: Dict[str, Any], output_file: str = None):
        """可视化资源分布"""
        tasks = data['tasks']
        workflow_type = data['workflow_type']
        
        # 提取资源数据
        task_ids = list(tasks.keys())
        cpu_reqs = [tasks[task_id]['cpu_requirement'] for task_id in task_ids]
        mem_reqs = [tasks[task_id]['memory_requirement'] / 1024 for task_id in task_ids]  # GB
        io_reqs = [tasks[task_id]['io_requirement'] for task_id in task_ids]
        net_reqs = [tasks[task_id]['network_requirement'] for task_id in task_ids]
        
        # 获取任务类型颜色
        colors = []
        for task_id in task_ids:
            task_type = self.get_task_type_from_name(tasks[task_id]['task_type'])
            colors.append(self.BASE_COLORS[task_type][1])  # 使用最深色
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'{workflow_type.upper()} Resource Distribution', fontsize=16, fontweight='bold')
        
        # CPU分布
        axes[0, 0].bar(range(len(task_ids)), cpu_reqs, color=colors, alpha=0.8)
        axes[0, 0].set_title('CPU Requirements', fontweight='bold')
        axes[0, 0].set_xlabel('Task ID')
        axes[0, 0].set_ylabel('CPU (cores)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 内存分布
        axes[0, 1].bar(range(len(task_ids)), mem_reqs, color=colors, alpha=0.8)
        axes[0, 1].set_title('Memory Requirements', fontweight='bold')
        axes[0, 1].set_xlabel('Task ID')
        axes[0, 1].set_ylabel('Memory (GB)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # I/O分布
        axes[1, 0].bar(range(len(task_ids)), io_reqs, color=colors, alpha=0.8)
        axes[1, 0].set_title('I/O Requirements', fontweight='bold')
        axes[1, 0].set_xlabel('Task ID')
        axes[1, 0].set_ylabel('I/O (MB/s)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 网络分布
        axes[1, 1].bar(range(len(task_ids)), net_reqs, color=colors, alpha=0.8)
        axes[1, 1].set_title('Network Requirements', fontweight='bold')
        axes[1, 1].set_xlabel('Task ID')
        axes[1, 1].set_ylabel('Network (MB/s)')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图形
        if output_file is None:
            output_file = f"{workflow_type}_resource_distribution.png"
        
        os.makedirs("visualization", exist_ok=True)
        output_path = os.path.join("visualization", output_file)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"资源分布图已保存: {output_path}")
        
        plt.show()
    
    def create_comprehensive_visualization(self, data: Dict[str, Any]):
        """创建综合可视化"""
        workflow_type = data['workflow_type']
        print(f"正在为 {workflow_type} 工作流创建高级可视化...")
        
        # 1. 工作流图
        self.visualize_workflow_diagram(data)
        
        # 2. 资源分布图
        self.visualize_resource_distribution(data)
        
        print(f"{workflow_type} 高级可视化完成!")


def main():
    """主函数"""
    visualizer = AdvancedWorkflowVisualizer()
    
    # 数据文件路径
    data_dir = r"E:\2024_New\Load_Blancing\Load_Blancing_simulation\LoadBalance_py\data_generate\data"
    
    # 要处理的工作流文件
    workflow_files = [
        "montage_small_workflows.json",
        "montage_medium_workflows.json", 
        "montage_large_workflows.json",
        "brain_medium_workflows.json",
        "sipht_medium_workflows.json",
        "epigenomics_medium_workflows.json",
        "cybershake_medium_workflows.json"
    ]
    
    for filename in workflow_files:
        file_path = os.path.join(data_dir, filename)
        
        if os.path.exists(file_path):
            try:
                print(f"\n处理文件: {filename}")
                data = visualizer.load_workflow_data(file_path)
                visualizer.create_comprehensive_visualization(data)
            except Exception as e:
                print(f"处理 {filename} 时出错: {e}")
        else:
            print(f"文件不存在: {file_path}")
    
    print("\n所有高级可视化完成!")


if __name__ == "__main__":
    main() 