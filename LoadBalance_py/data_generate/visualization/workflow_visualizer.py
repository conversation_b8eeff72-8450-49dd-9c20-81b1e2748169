"""
Load Balancing Simulation System - Workflow Visualizer
Generate task flow diagrams with Trans SMC journal style
"""

import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import networkx as nx
import numpy as np
from typing import Dict, List, Any, Optional
import seaborn as sns
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import matplotlib.patches as mpatches
import os


class WorkflowVisualizer:
    """Workflow Visualizer"""
    
    def __init__(self):
        self.workflow_data = None
        
        # Trans SMC journal style color scheme
        self.task_colors = {
            # Montage workflow - Blue series
            'mProject': '#1f77b4',      # Deep blue
            'mDiffFit': '#ff7f0e',      # Orange
            'mConcatFit': '#2ca02c',    # Green
            'mBgModel': '#d62728',      # Red
            'mBackground': '#9467bd',   # Purple
            'mAdd': '#8c564b',          # Brown
            
            # Brain workflow - Green series
            'preprocessing': '#2e8b57',  # Sea green
            'segmentation': '#32cd32',   # Lime green
            'registration': '#228b22',   # <PERSON> green
            'normalization': '#90ee90',  # Light green
            'statistics': '#98fb98',     # Pale green
            'visualization': '#00ff7f',  # Spring green
            
            # SIPHT workflow - Red series
            'blast': '#dc143c',          # Crimson
            'clustalw': '#ff4500',      # Orange red
            'rnafold': '#ff6347',       # Tomato
            'infernal': '#ff0000',      # Red
            'cmsearch': '#b22222',      # Fire brick
            'genome_scan': '#cd5c5c',   # Indian red
            
            # Epigenomics workflow - Purple series
            'fastqc': '#8a2be2',        # Blue violet
            'bwa': '#9370db',           # Medium purple
            'samtools': '#ba55d3',      # Medium orchid
            'macs2': '#9932cc',         # Dark orchid
            'homer': '#da70d6',         # Orchid
            'bedtools': '#dda0dd',      # Plum
            'igv': '#ee82ee',           # Violet
            
            # CyberShake workflow - Orange series
            'rupture_generator': '#ff8c00',  # Dark orange
            'velocity_model': '#ffa500',     # Orange
            'srfgen': '#ff7f50',            # Coral
            'syn1d': '#ff6347',             # Tomato
            'syn2d': '#ff4500',             # Orange red
            'syn3d': '#ff8c00',             # Dark orange
            'post_processing': '#ffa07a'    # Light salmon
        }
        
        # Set Trans SMC journal style plotting parameters
        try:
            plt.style.use('seaborn-v0_8-whitegrid')
        except:
            # If style not available, use default style and manual settings
            plt.style.use('default')
        
        # Unified font settings
        plt.rcParams['font.family'] = ['Times New Roman', 'DejaVu Sans']
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.linewidth'] = 1.5
        plt.rcParams['axes.edgecolor'] = '#333333'
        plt.rcParams['grid.color'] = '#e0e0e0'
        plt.rcParams['grid.linestyle'] = '-'
        plt.rcParams['grid.linewidth'] = 0.5
        plt.rcParams['grid.alpha'] = 0.7
        
        # Set Chinese font
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def load_workflow_data(self, filename: str):
        """Load workflow data"""
        # Load data from absolute path
        data_path = os.path.join(r"E:\2024_New\Load_Blancing\Load_Blancing_simulation\LoadBalance_py\data_generate\data", filename)
        with open(data_path, 'r', encoding='utf-8') as f:
            self.workflow_data = json.load(f)
        print(f"Loaded workflow data: {data_path}")
    
    def plot_workflow_diagram(self, save_plot: bool = True, filename: str = None):
        """Plot workflow diagram with circular nodes based on resource requirements"""
        if not self.workflow_data:
            raise ValueError("Please load workflow data first")
        
        # Create figure with improved aspect ratio
        fig, ax = plt.subplots(1, 1, figsize=(16, 12))
        
        # Get tasks and dependencies
        tasks = self.workflow_data['tasks']
        edges = self.workflow_data['edges']
        
        # Build DAG
        G = nx.DiGraph()
        for edge in edges:
            G.add_edge(edge[0], edge[1])
        
        # Calculate topological sort
        try:
            topo_order = list(nx.topological_sort(G))
        except nx.NetworkXError:
            # If there are cycles, use simple node order
            topo_order = list(tasks.keys())
        
        # Calculate levels with improved algorithm
        levels = self._calculate_levels_improved(G, topo_order)
        
        # Draw task nodes as circles
        self._draw_circular_task_nodes(ax, tasks, levels, topo_order)
        
        # Draw dependencies with curved arrows
        self._draw_dependencies_improved(ax, edges, tasks, levels)
        
        # Set figure properties
        ax.set_xlim(-1, len(levels) + 1)
        ax.set_ylim(-1, max(len(level) for level in levels) + 1)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # Add title
        workflow_type = self.workflow_data['workflow_type'].upper()
        plt.title(f'{workflow_type} Workflow Task Diagram', fontsize=18, fontweight='bold', pad=30)
        
        # Add legend
        self._add_legend_improved(ax, tasks)
        
        plt.tight_layout()
        
        if save_plot:
            if not filename:
                filename = f"{self.workflow_data['workflow_type']}_workflow_diagram.png"
            # Save to visualization folder
            viz_path = os.path.join("visualization", filename)
            os.makedirs("visualization", exist_ok=True)
            plt.savefig(viz_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"Workflow diagram saved: {viz_path}")
        
        plt.show()
    
    def _calculate_levels_improved(self, G: nx.DiGraph, topo_order: List[str]) -> List[List[str]]:
        """Calculate task levels with improved algorithm"""
        levels = []
        current_level = []
        
        for task_id in topo_order:
            # Check if all dependency tasks are in previous levels
            dependencies = list(G.predecessors(task_id))
            max_dep_level = -1
            
            for dep in dependencies:
                for i, level in enumerate(levels):
                    if dep in level:
                        max_dep_level = max(max_dep_level, i)
            
            # If dependency tasks are in the previous level, current task can be placed in the next level
            if max_dep_level + 1 == len(levels):
                current_level.append(task_id)
            else:
                # Start new level
                if current_level:
                    levels.append(current_level)
                current_level = [task_id]
        
        if current_level:
            levels.append(current_level)
        
        return levels
    
    def _draw_circular_task_nodes(self, ax, tasks: Dict, levels: List[List[str]], topo_order: List[str]):
        """Draw task nodes as circles based on resource requirements"""
        # Calculate resource requirements for scaling
        cpu_reqs = [tasks[task_id]['cpu_requirement'] for task_id in tasks.keys()]
        mem_reqs = [tasks[task_id]['memory_requirement'] for task_id in tasks.keys()]
        io_reqs = [tasks[task_id]['io_requirement'] for task_id in tasks.keys()]
        net_reqs = [tasks[task_id]['network_requirement'] for task_id in tasks.keys()]
        
        # Normalize resource requirements for circle size (0.1 to 0.8)
        max_cpu = max(cpu_reqs) if cpu_reqs else 1
        max_mem = max(mem_reqs) if mem_reqs else 1
        max_io = max(io_reqs) if io_reqs else 1
        max_net = max(net_reqs) if net_reqs else 1
        
        for level_idx, level in enumerate(levels):
            level_x = level_idx  # Left to right arrangement
            
            for task_idx, task_id in enumerate(level):
                task = tasks[task_id]
                task_y = task_idx - (len(level) - 1) / 2
                
                # Get task color
                task_type = task['task_type']
                color = self.task_colors.get(task_type, '#cccccc')
                
                # Calculate circle size based on total resource requirements
                cpu_norm = task['cpu_requirement'] / max_cpu
                mem_norm = task['memory_requirement'] / max_mem
                io_norm = task['io_requirement'] / max_io
                net_norm = task['network_requirement'] / max_net
                
                # Total resource intensity (0-1)
                total_intensity = (cpu_norm + mem_norm + io_norm + net_norm) / 4
                
                # Circle size based on resource intensity
                circle_size = 0.1 + total_intensity * 0.7  # 0.1 to 0.8
                
                # Draw circle
                circle = plt.Circle((level_x, task_y), circle_size, 
                                  facecolor=color, edgecolor='#333333', 
                                  linewidth=2.0, alpha=0.9)
                ax.add_patch(circle)
                
                # Add task ID label
                ax.text(level_x, task_y, f"Task {task_id}", 
                       ha='center', va='center', fontsize=10, fontweight='bold', color='white')
                
                # Add task type label below circle
                ax.text(level_x, task_y - circle_size - 0.1, task_type, 
                       ha='center', va='center', fontsize=8, 
                       bbox=dict(boxstyle="round,pad=0.1", facecolor='white', alpha=0.95, edgecolor='#333333'))
                
                # Add resource requirements info
                cpu_req = task['cpu_requirement']
                mem_req = task['memory_requirement'] / 1024  # Convert to GB
                ax.text(level_x, task_y + circle_size + 0.1, f"CPU: {cpu_req:.1f}\nMEM: {mem_req:.1f}GB", 
                       ha='center', va='center', fontsize=7,
                       bbox=dict(boxstyle="round,pad=0.1", facecolor='#f8f8f8', alpha=0.9, edgecolor='#cccccc'))
    
    def _draw_dependencies_improved(self, ax, edges: List, tasks: Dict, levels: List[List[str]]):
        """Draw dependencies with curved arrows"""
        for edge in edges:
            source_id, target_id, edge_data = edge
            
            # Find source and target node positions
            source_pos = self._find_task_position_improved(source_id, levels)
            target_pos = self._find_task_position_improved(target_id, levels)
            
            if source_pos and target_pos:
                source_x, source_y = source_pos
                target_x, target_y = target_pos
                
                # Draw curved arrow
                arrow = ConnectionPatch(
                    (source_x + 0.4, source_y),  # From source node right side
                    (target_x - 0.4, target_y),  # To target node left side
                    "data", "data",
                    arrowstyle="->", shrinkA=8, shrinkB=8,
                    mutation_scale=25, fc="#333333", ec="#333333",
                    linewidth=2.0, alpha=0.8,
                    connectionstyle="arc3,rad=0.2"  # Curved connection
                )
                ax.add_patch(arrow)
                
                # Add data transfer size label
                if 'data_size' in edge_data:
                    data_size = edge_data['data_size']
                    mid_x = (source_x + target_x) / 2
                    mid_y = (source_y + target_y) / 2 + 0.3  # Offset for curved arrow
                    ax.text(mid_x, mid_y, f"{data_size:.1f}MB", 
                           ha='center', va='center', fontsize=8,
                           bbox=dict(boxstyle="round,pad=0.1", facecolor='#fffacd', alpha=0.95, edgecolor='#333333'))
    
    def _find_task_position_improved(self, task_id: str, levels: List[List[str]]) -> Optional[tuple]:
        """Find task position in levels"""
        for level_idx, level in enumerate(levels):
            if task_id in level:
                level_x = level_idx
                task_idx = level.index(task_id)
                task_y = task_idx - (len(level) - 1) / 2
                return (level_x, task_y)
        return None
    
    def _add_legend_improved(self, ax, tasks: Dict):
        """Add improved legend"""
        # Count task types
        task_types = {}
        for task in tasks.values():
            task_type = task['task_type']
            if task_type not in task_types:
                task_types[task_type] = 0
            task_types[task_type] += 1
        
        # Create legend
        legend_elements = []
        for task_type, count in sorted(task_types.items()):
            color = self.task_colors.get(task_type, '#cccccc')
            legend_elements.append(
                mpatches.Patch(color=color, label=f'{task_type} ({count})')
            )
        
        # Add legend with improved positioning
        ax.legend(handles=legend_elements, loc='upper right', 
                 bbox_to_anchor=(1.05, 1), fontsize=10, framealpha=0.95, 
                 title="Task Types", title_fontsize=12)
    
    def plot_resource_requirements(self, save_plot: bool = True, filename: str = None):
        """Plot resource requirements distribution - Trans SMC style"""
        if not self.workflow_data:
            raise ValueError("Please load workflow data first")
        
        tasks = self.workflow_data['tasks']
        workflow_type = self.workflow_data['workflow_type']
        
        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'{workflow_type.upper()} Workflow Resource Requirements Analysis', 
                    fontsize=16, fontweight='bold', y=0.95)
        
        # Extract data
        task_ids = list(tasks.keys())
        cpu_reqs = [tasks[task_id]['cpu_requirement'] for task_id in task_ids]
        mem_reqs = [tasks[task_id]['memory_requirement'] / 1024 for task_id in task_ids]  # GB
        io_reqs = [tasks[task_id]['io_requirement'] for task_id in task_ids]
        net_reqs = [tasks[task_id]['network_requirement'] for task_id in task_ids]
        
        # Define color scheme based on workflow type
        if workflow_type == 'montage':
            # Blue series for montage workflow
            colors = ['#1f77b4', '#3b7dbd', '#5a83c6', '#7889cf', '#968fd8', '#b495e1', '#d29bea', '#f0a1f3']
            # If more tasks than colors, cycle through the blue series
            colors = colors * (len(task_ids) // len(colors) + 1)
            colors = colors[:len(task_ids)]
        else:
            # Use original task type colors for other workflows
            colors = [self.task_colors.get(tasks[task_id]['task_type'], '#1f77b4') 
                     for task_id in task_ids]
        
        # 1. CPU Requirements Distribution
        axes[0, 0].bar(range(len(task_ids)), cpu_reqs, color=colors, alpha=0.8, edgecolor='#333333', linewidth=0.8)
        axes[0, 0].set_title('CPU Requirements Distribution', fontweight='bold', fontsize=14)
        axes[0, 0].set_xlabel('Task ID', fontsize=12)
        axes[0, 0].set_ylabel('CPU Requirements (cores)', fontsize=12)
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Memory Requirements Distribution
        axes[0, 1].bar(range(len(task_ids)), mem_reqs, color=colors, alpha=0.8, edgecolor='#333333', linewidth=0.8)
        axes[0, 1].set_title('Memory Requirements Distribution', fontweight='bold', fontsize=14)
        axes[0, 1].set_xlabel('Task ID', fontsize=12)
        axes[0, 1].set_ylabel('Memory Requirements (GB)', fontsize=12)
        axes[0, 1].tick_params(axis='x', rotation=45)
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. I/O Requirements Distribution
        axes[1, 0].bar(range(len(task_ids)), io_reqs, color=colors, alpha=0.8, edgecolor='#333333', linewidth=0.8)
        axes[1, 0].set_title('I/O Requirements Distribution', fontweight='bold', fontsize=14)
        axes[1, 0].set_xlabel('Task ID', fontsize=12)
        axes[1, 0].set_ylabel('I/O Requirements (MB/s)', fontsize=12)
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Network Requirements Distribution
        axes[1, 1].bar(range(len(task_ids)), net_reqs, color=colors, alpha=0.8, edgecolor='#333333', linewidth=0.8)
        axes[1, 1].set_title('Network Requirements Distribution', fontweight='bold', fontsize=14)
        axes[1, 1].set_xlabel('Task ID', fontsize=12)
        axes[1, 1].set_ylabel('Network Requirements (MB/s)', fontsize=12)
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plot:
            if not filename:
                filename = f"{workflow_type}_resource_requirements.png"
            # Save to visualization folder
            viz_path = os.path.join("visualization", filename)
            os.makedirs("visualization", exist_ok=True)
            plt.savefig(viz_path, dpi=300, bbox_inches='tight')
            print(f"Resource requirements chart saved: {viz_path}")
        
        plt.show()
    
    def plot_execution_time_comparison(self, save_plot: bool = True, filename: str = None):
        """Plot execution time comparison across different nodes - Trans SMC style"""
        if not self.workflow_data:
            raise ValueError("Please load workflow data first")
        
        tasks = self.workflow_data['tasks']
        nodes = self.workflow_data['nodes']
        
        # Select representative tasks for comparison
        sample_tasks = list(tasks.keys())[:min(8, len(tasks))]
        
        # Calculate execution time for each task on different nodes
        execution_times = {}
        for task_id in sample_tasks:
            task = tasks[task_id]
            execution_times[task_id] = []
            
            for node in nodes:
                # Simplified execution time calculation to avoid import issues
                base_runtime = task['base_runtime']
                cpu_ratio = task['cpu_requirement'] / node['cpu_capacity']
                memory_ratio = task['memory_requirement'] / node['memory_capacity']
                
                # Calculate performance impact factors
                if cpu_ratio > 1:
                    cpu_factor = cpu_ratio ** 1.5
                else:
                    cpu_factor = 1.0
                
                if memory_ratio > 1:
                    memory_factor = memory_ratio ** 2
                else:
                    memory_factor = 1.0
                
                # Calculate actual execution time
                exec_time = base_runtime * cpu_factor * memory_factor
                execution_times[task_id].append(exec_time)
        
        # Create figure
        fig, ax = plt.subplots(1, 1, figsize=(14, 10))
        
        # Plot boxplot - Trans SMC style
        data_to_plot = [execution_times[task_id] for task_id in sample_tasks]
        labels = [f"Task {task_id}\n({tasks[task_id]['task_type']})" for task_id in sample_tasks]
        
        # Use Trans SMC style colors
        colors = [self.task_colors.get(tasks[task_id]['task_type'], '#1f77b4') 
                 for task_id in sample_tasks]
        
        bp = ax.boxplot(data_to_plot, labels=labels, patch_artist=True)
        
        # Set colors
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
            patch.set_edgecolor('#333333')
            patch.set_linewidth=1.5
        
        # Set other element colors
        for element in ['whiskers', 'caps', 'medians']:
            plt.setp(bp[element], color='#333333', linewidth=1.5)
        
        ax.set_title(f'{self.workflow_data["workflow_type"].upper()} Task Execution Time Distribution', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Task ID (Task Type)', fontsize=12)
        ax.set_ylabel('Execution Time (seconds)', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plot:
            if not filename:
                filename = f"{self.workflow_data['workflow_type']}_execution_time_comparison.png"
            # Save to visualization folder
            viz_path = os.path.join("visualization", filename)
            os.makedirs("visualization", exist_ok=True)
            plt.savefig(viz_path, dpi=300, bbox_inches='tight')
            print(f"Execution time comparison chart saved: {viz_path}")
        
        plt.show()
    
    def plot_resource_summary(self, save_plot: bool = True, output_filename: str = None):
        """Plot 5x4 summary of resource requirements for all workflows"""
        workflow_files = [
            "montage_medium_workflows.json",  # Use medium scale for montage
            "brain_medium_workflows.json",
            "sipht_medium_workflows.json", 
            "epigenomics_medium_workflows.json",
            "cybershake_medium_workflows.json"
        ]
        resource_types = ['CPU', 'Memory', 'I/O', 'Network']
        
        # Create 5x4 subplot
        fig, axes = plt.subplots(5, 4, figsize=(20, 25))
        fig.suptitle('Resource Requirements Summary for All Workflows (Medium Scale)', fontsize=20, fontweight='bold', y=0.95)
        
        for row, filename in enumerate(workflow_files):
            try:
                # Load workflow data
                data_path = os.path.join(r"E:\2024_New\Load_Blancing\Load_Blancing_simulation\LoadBalance_py\data_generate\data", filename)
                with open(data_path, 'r', encoding='utf-8') as f:
                    workflow_data = json.load(f)
                
                workflow_type = workflow_data['workflow_type']
                tasks = workflow_data['tasks']
                task_ids = list(tasks.keys())
                
                # Extract resource data
                cpu_reqs = [tasks[task_id]['cpu_requirement'] for task_id in task_ids]
                mem_reqs = [tasks[task_id]['memory_requirement'] / 1024 for task_id in task_ids]  # GB
                io_reqs = [tasks[task_id]['io_requirement'] for task_id in task_ids]
                net_reqs = [tasks[task_id]['network_requirement'] for task_id in task_ids]
                
                # Define colors based on workflow type
                if workflow_type == 'montage':
                    # Blue series for montage workflow
                    colors = ['#1f77b4', '#3b7dbd', '#5a83c6', '#7889cf', '#968fd8', '#b495e1', '#d29bea', '#f0a1f3']
                    # If more tasks than colors, cycle through the blue series
                    colors = colors * (len(task_ids) // len(colors) + 1)
                    colors = colors[:len(task_ids)]
                else:
                    # Use task type colors for other workflows
                    colors = [self.task_colors.get(tasks[task_id]['task_type'], '#1f77b4') 
                             for task_id in task_ids]
                
                # Plot each resource type
                resource_data = [cpu_reqs, mem_reqs, io_reqs, net_reqs]
                resource_labels = ['CPU (cores)', 'Memory (GB)', 'I/O (MB/s)', 'Network (MB/s)']
                
                for col, (data, label) in enumerate(zip(resource_data, resource_labels)):
                    ax = axes[row, col]
                    
                    # Create bar plot
                    bars = ax.bar(range(len(task_ids)), data, color=colors, alpha=0.8, 
                                 edgecolor='#333333', linewidth=0.8)
                    
                    # Set title and labels
                    ax.set_title(f'{workflow_type.upper()} - {label}', fontweight='bold', fontsize=12)
                    ax.set_xlabel('Task ID', fontsize=10)
                    ax.set_ylabel(label, fontsize=10)
                    ax.tick_params(axis='x', rotation=45)
                    ax.grid(True, alpha=0.3)
                    
                    # Add value labels on bars
                    for i, (bar, value) in enumerate(zip(bars, data)):
                        height = bar.get_height()
                        ax.text(bar.get_x() + bar.get_width()/2., height + max(data)*0.01,
                               f'{value:.1f}', ha='center', va='bottom', fontsize=8)
                
            except FileNotFoundError:
                # If file not found, create empty subplots
                workflow_type = filename.replace('_medium_workflows.json', '').replace('_workflows.json', '')
                for col in range(4):
                    ax = axes[row, col]
                    ax.text(0.5, 0.5, f'{workflow_type.upper()}\nData Not Available', 
                           ha='center', va='center', transform=ax.transAxes, fontsize=12)
                    ax.set_title(f'{workflow_type.upper()} - No Data', fontweight='bold', fontsize=12)
        
        plt.tight_layout()
        
        if save_plot:
            if not output_filename:
                output_filename = "workflow_resource_summary.png"
            # Save to visualization folder
            viz_path = os.path.join("visualization", output_filename)
            os.makedirs("visualization", exist_ok=True)
            plt.savefig(viz_path, dpi=300, bbox_inches='tight')
            print(f"Resource summary chart saved: {viz_path}")
        
        plt.show()
    
    def create_comprehensive_visualization(self, save_plots: bool = True):
        """Create comprehensive visualization including summary"""
        if not self.workflow_data:
            raise ValueError("Please load workflow data first")
        
        print(f"Creating visualization for {self.workflow_data['workflow_type']} workflow...")
        
        # 1. Workflow diagram with circular nodes
        #self.plot_workflow_diagram(save_plots)
        
        # 2. Resource requirements distribution
        #self.plot_resource_requirements(save_plots)
        
        # 3. Execution time comparison
        #self.plot_execution_time_comparison(save_plots)
        
        print("Visualization creation completed!")


def main():
    """Main function - Create visualization for all workflows and summary"""
    visualizer = WorkflowVisualizer()
    
    # Create visualization for all workflows with new naming format
    workflow_files = [
        # Montage workflows (three scales)
        "montage_small_workflows.json",
        "montage_medium_workflows.json", 
        "montage_large_workflows.json",
        # Other workflows (medium scale only)
        "brain_medium_workflows.json",
        "sipht_medium_workflows.json",
        "epigenomics_medium_workflows.json",
        "cybershake_medium_workflows.json"
    ]
    
    for filename in workflow_files:
        try:
            print(f"Creating visualization for {filename}...")
            
            # Load data
            visualizer.load_workflow_data(filename)
            
            # Create comprehensive visualization
            visualizer.create_comprehensive_visualization()
            
            print(f"{filename} visualization completed!\n")
            
        except FileNotFoundError:
            print(f"File {filename} not found, skipping visualization\n")
        except Exception as e:
            print(f"Error creating visualization for {filename}: {e}\n")
    
    # Create resource summary chart
    print("Creating resource summary chart...")
    visualizer.plot_resource_summary()
    print("Resource summary chart completed!")
    
    print("All workflow visualizations completed!")


if __name__ == "__main__":
    main() 