"""
负载均衡仿真系统 - 工作流数据分析器
分析工作流数据的特征和性能指标
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import networkx as nx
from typing import Dict, List, Any
import pandas as pd
from datetime import datetime


class WorkflowAnalyzer:
    """工作流数据分析器"""
    
    def __init__(self):
        self.workflow_data = None
        self.analysis_results = {}
    
    def load_workflow_data(self, filename: str):
        """加载工作流数据"""
        with open(filename, 'r', encoding='utf-8') as f:
            self.workflow_data = json.load(f)
        print(f"已加载工作流数据: {filename}")
    
    def analyze_workflow_characteristics(self) -> Dict[str, Any]:
        """分析工作流特征"""
        if not self.workflow_data:
            raise ValueError("请先加载工作流数据")
        
        tasks = self.workflow_data['tasks']
        edges = self.workflow_data['edges']
        nodes = self.workflow_data['nodes']
        
        # 任务特征分析
        task_runtimes = [task['runtime'] for task in tasks.values()]
        task_cpu_demands = [task['cpu_demand'] for task in tasks.values()]
        task_memory_demands = [task['memory_demand'] for task in tasks.values()]
        task_io_operations = [task['io_operations'] for task in tasks.values()]
        task_network_transfers = [task['network_transfer'] for task in tasks.values()]
        
        # 节点特征分析
        node_cpu_capacities = [node['cpu_capacity'] for node in nodes]
        node_memory_capacities = [node['memory_capacity'] for node in nodes]
        node_io_capacities = [node['io_capacity'] for node in nodes]
        node_network_capacities = [node['network_capacity'] for node in nodes]
        
        # 计算统计指标
        analysis = {
            'workflow_type': self.workflow_data['workflow_type'],
            'total_tasks': len(tasks),
            'total_nodes': len(nodes),
            'total_edges': len(edges),
            
            # 任务统计
            'task_runtime_stats': {
                'mean': np.mean(task_runtimes),
                'std': np.std(task_runtimes),
                'min': np.min(task_runtimes),
                'max': np.max(task_runtimes),
                'median': np.median(task_runtimes)
            },
            'task_cpu_demand_stats': {
                'mean': np.mean(task_cpu_demands),
                'std': np.std(task_cpu_demands),
                'min': np.min(task_cpu_demands),
                'max': np.max(task_cpu_demands),
                'median': np.median(task_cpu_demands)
            },
            'task_memory_demand_stats': {
                'mean': np.mean(task_memory_demands),
                'std': np.std(task_memory_demands),
                'min': np.min(task_memory_demands),
                'max': np.max(task_memory_demands),
                'median': np.median(task_memory_demand_stats)
            },
            'task_io_stats': {
                'mean': np.mean(task_io_operations),
                'std': np.std(task_io_operations),
                'min': np.min(task_io_operations),
                'max': np.max(task_io_operations),
                'median': np.median(task_io_operations)
            },
            'task_network_stats': {
                'mean': np.mean(task_network_transfers),
                'std': np.std(task_network_transfers),
                'min': np.min(task_network_transfers),
                'max': np.max(task_network_transfers),
                'median': np.median(task_network_transfers)
            },
            
            # 节点统计
            'node_cpu_capacity_stats': {
                'mean': np.mean(node_cpu_capacities),
                'std': np.std(node_cpu_capacities),
                'min': np.min(node_cpu_capacities),
                'max': np.max(node_cpu_capacities),
                'median': np.median(node_cpu_capacities)
            },
            'node_memory_capacity_stats': {
                'mean': np.mean(node_memory_capacities),
                'std': np.std(node_memory_capacities),
                'min': np.min(node_memory_capacities),
                'max': np.max(node_memory_capacities),
                'median': np.median(node_memory_capacities)
            },
            'node_io_capacity_stats': {
                'mean': np.mean(node_io_capacities),
                'std': np.std(node_io_capacities),
                'min': np.min(node_io_capacities),
                'max': np.max(node_io_capacities),
                'median': np.median(node_io_capacities)
            },
            'node_network_capacity_stats': {
                'mean': np.mean(node_network_capacities),
                'std': np.std(node_network_capacities),
                'min': np.min(node_network_capacities),
                'max': np.max(node_network_capacities),
                'median': np.median(node_network_capacities)
            },
            
            # 工作流特征
            'workflow_characteristics': self._analyze_workflow_structure(),
            'resource_utilization': self._analyze_resource_utilization(),
            'task_type_distribution': self._analyze_task_type_distribution()
        }
        
        self.analysis_results = analysis
        return analysis
    
    def _analyze_workflow_structure(self) -> Dict[str, Any]:
        """分析工作流结构"""
        tasks = self.workflow_data['tasks']
        edges = self.workflow_data['edges']
        
        # 构建DAG
        G = nx.DiGraph()
        for edge in edges:
            G.add_edge(edge[0], edge[1])
        
        # 计算图论指标
        characteristics = {
            'in_degree_distribution': dict(nx.in_degree_centrality(G)),
            'out_degree_distribution': dict(nx.out_degree_centrality(G)),
            'closeness_centrality': dict(nx.closeness_centrality(G)),
            'betweenness_centrality': dict(nx.betweenness_centrality(G)),
            'average_clustering': nx.average_clustering(G.to_undirected()),
            'density': nx.density(G),
            'diameter': nx.diameter(G.to_undirected()) if nx.is_connected(G.to_undirected()) else None,
            'average_shortest_path': nx.average_shortest_path_length(G.to_undirected()) if nx.is_connected(G.to_undirected()) else None
        }
        
        return characteristics
    
    def _analyze_resource_utilization(self) -> Dict[str, Any]:
        """分析资源利用率"""
        tasks = self.workflow_data['tasks']
        nodes = self.workflow_data['nodes']
        
        # 计算总资源需求
        total_cpu_demand = sum(task['cpu_demand'] for task in tasks.values())
        total_memory_demand = sum(task['memory_demand'] for task in tasks.values())
        total_io_operations = sum(task['io_operations'] for task in tasks.values())
        total_network_transfer = sum(task['network_transfer'] for task in tasks.values())
        
        # 计算总资源容量
        total_cpu_capacity = sum(node['cpu_capacity'] for node in nodes)
        total_memory_capacity = sum(node['memory_capacity'] for node in nodes)
        total_io_capacity = sum(node['io_capacity'] for node in nodes)
        total_network_capacity = sum(node['network_capacity'] for node in nodes)
        
        utilization = {
            'cpu_utilization': total_cpu_demand / total_cpu_capacity if total_cpu_capacity > 0 else 0,
            'memory_utilization': total_memory_demand / total_memory_capacity if total_memory_capacity > 0 else 0,
            'io_utilization': total_io_operations / (total_io_capacity * 3600) if total_io_capacity > 0 else 0,  # 假设1小时
            'network_utilization': total_network_transfer / (total_network_capacity * 3600) if total_network_capacity > 0 else 0,
            'total_cpu_demand': total_cpu_demand,
            'total_memory_demand': total_memory_demand,
            'total_cpu_capacity': total_cpu_capacity,
            'total_memory_capacity': total_memory_capacity
        }
        
        return utilization
    
    def _analyze_task_type_distribution(self) -> Dict[str, Any]:
        """分析任务类型分布"""
        tasks = self.workflow_data['tasks']
        
        task_types = {}
        for task in tasks.values():
            task_type = task['task_type']
            if task_type not in task_types:
                task_types[task_type] = {
                    'count': 0,
                    'total_runtime': 0,
                    'total_cpu_demand': 0,
                    'total_memory_demand': 0
                }
            
            task_types[task_type]['count'] += 1
            task_types[task_type]['total_runtime'] += task['runtime']
            task_types[task_type]['total_cpu_demand'] += task['cpu_demand']
            task_types[task_type]['total_memory_demand'] += task['memory_demand']
        
        # 计算平均值
        for task_type in task_types:
            count = task_types[task_type]['count']
            task_types[task_type]['avg_runtime'] = task_types[task_type]['total_runtime'] / count
            task_types[task_type]['avg_cpu_demand'] = task_types[task_type]['total_cpu_demand'] / count
            task_types[task_type]['avg_memory_demand'] = task_types[task_type]['total_memory_demand'] / count
        
        return task_types
    
    def plot_workflow_characteristics(self, save_plots: bool = True):
        """绘制工作流特征图"""
        if not self.analysis_results:
            self.analyze_workflow_characteristics()
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'{self.workflow_data["workflow_type"].upper()} 工作流特征分析', fontsize=16)
        
        tasks = self.workflow_data['tasks']
        nodes = self.workflow_data['nodes']
        
        # 1. 任务执行时间分布
        task_runtimes = [task['runtime'] for task in tasks.values()]
        axes[0, 0].hist(task_runtimes, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('任务执行时间分布')
        axes[0, 0].set_xlabel('执行时间 (秒)')
        axes[0, 0].set_ylabel('任务数量')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. CPU需求分布
        task_cpu_demands = [task['cpu_demand'] for task in tasks.values()]
        axes[0, 1].hist(task_cpu_demands, bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0, 1].set_title('任务CPU需求分布')
        axes[0, 1].set_xlabel('CPU需求 (核数)')
        axes[0, 1].set_ylabel('任务数量')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 内存需求分布
        task_memory_demands = [task['memory_demand'] for task in tasks.values()]
        axes[0, 2].hist(task_memory_demands, bins=15, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[0, 2].set_title('任务内存需求分布')
        axes[0, 2].set_xlabel('内存需求 (MB)')
        axes[0, 2].set_ylabel('任务数量')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. 节点资源容量对比
        node_ids = [node['node_id'] for node in nodes]
        node_cpu_capacities = [node['cpu_capacity'] for node in nodes]
        node_memory_capacities = [node['memory_capacity'] / 1024 for node in nodes]  # 转换为GB
        
        x = np.arange(len(node_ids))
        width = 0.35
        
        ax1 = axes[1, 0]
        ax2 = ax1.twinx()
        
        bars1 = ax1.bar(x - width/2, node_cpu_capacities, width, label='CPU容量', color='skyblue', alpha=0.7)
        bars2 = ax2.bar(x + width/2, node_memory_capacities, width, label='内存容量', color='lightcoral', alpha=0.7)
        
        ax1.set_xlabel('节点ID')
        ax1.set_ylabel('CPU容量 (核数)', color='skyblue')
        ax2.set_ylabel('内存容量 (GB)', color='lightcoral')
        ax1.set_title('节点资源容量对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(node_ids, rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # 5. 任务类型分布
        task_type_dist = self.analysis_results['task_type_distribution']
        task_types = list(task_type_dist.keys())
        task_counts = [task_type_dist[t]['count'] for t in task_types]
        
        axes[1, 1].pie(task_counts, labels=task_types, autopct='%1.1f%%', startangle=90)
        axes[1, 1].set_title('任务类型分布')
        
        # 6. 资源利用率
        utilization = self.analysis_results['resource_utilization']
        resource_types = ['CPU', '内存', 'I/O', '网络']
        utilization_values = [
            utilization['cpu_utilization'],
            utilization['memory_utilization'],
            utilization['io_utilization'],
            utilization['network_utilization']
        ]
        
        colors = ['skyblue', 'lightgreen', 'lightcoral', 'gold']
        bars = axes[1, 2].bar(resource_types, utilization_values, color=colors, alpha=0.7)
        axes[1, 2].set_title('资源利用率')
        axes[1, 2].set_ylabel('利用率')
        axes[1, 2].set_ylim(0, 1)
        
        # 添加数值标签
        for bar, value in zip(bars, utilization_values):
            axes[1, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{value:.2%}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        if save_plots:
            filename = f"{self.workflow_data['workflow_type']}_characteristics.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"工作流特征图已保存: {filename}")
        
        plt.show()
    
    def plot_workflow_dag(self, save_plot: bool = True):
        """绘制工作流DAG图"""
        if not self.workflow_data:
            raise ValueError("请先加载工作流数据")
        
        # 构建DAG
        G = nx.DiGraph()
        edges = self.workflow_data['edges']
        
        for edge in edges:
            G.add_edge(edge[0], edge[1])
        
        # 设置图形
        plt.figure(figsize=(12, 8))
        plt.title(f'{self.workflow_data["workflow_type"].upper()} 工作流DAG图', fontsize=16)
        
        # 使用spring布局
        pos = nx.spring_layout(G, k=1, iterations=50)
        
        # 绘制节点
        nx.draw_networkx_nodes(G, pos, node_color='lightblue', 
                              node_size=1000, alpha=0.7)
        
        # 绘制边
        nx.draw_networkx_edges(G, pos, edge_color='gray', 
                              arrows=True, arrowsize=20, alpha=0.6)
        
        # 添加节点标签
        nx.draw_networkx_labels(G, pos, font_size=8, font_weight='bold')
        
        plt.axis('off')
        
        if save_plot:
            filename = f"{self.workflow_data['workflow_type']}_dag.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"工作流DAG图已保存: {filename}")
        
        plt.show()
    
    def generate_analysis_report(self, filename: str = None):
        """生成分析报告"""
        if not self.analysis_results:
            self.analyze_workflow_characteristics()
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.workflow_data['workflow_type']}_analysis_report_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"=== {self.workflow_data['workflow_type'].upper()} 工作流分析报告 ===\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"工作流类型: {self.workflow_data['workflow_type']}\n\n")
            
            # 基本信息
            f.write("=== 基本信息 ===\n")
            f.write(f"任务总数: {self.analysis_results['total_tasks']}\n")
            f.write(f"节点总数: {self.analysis_results['total_nodes']}\n")
            f.write(f"依赖关系数: {self.analysis_results['total_edges']}\n\n")
            
            # 任务统计
            f.write("=== 任务统计 ===\n")
            runtime_stats = self.analysis_results['task_runtime_stats']
            f.write(f"执行时间统计:\n")
            f.write(f"  平均值: {runtime_stats['mean']:.2f}秒\n")
            f.write(f"  标准差: {runtime_stats['std']:.2f}秒\n")
            f.write(f"  最小值: {runtime_stats['min']:.2f}秒\n")
            f.write(f"  最大值: {runtime_stats['max']:.2f}秒\n")
            f.write(f"  中位数: {runtime_stats['median']:.2f}秒\n\n")
            
            cpu_stats = self.analysis_results['task_cpu_demand_stats']
            f.write(f"CPU需求统计:\n")
            f.write(f"  平均值: {cpu_stats['mean']:.2f}核\n")
            f.write(f"  标准差: {cpu_stats['std']:.2f}核\n")
            f.write(f"  最小值: {cpu_stats['min']:.2f}核\n")
            f.write(f"  最大值: {cpu_stats['max']:.2f}核\n")
            f.write(f"  中位数: {cpu_stats['median']:.2f}核\n\n")
            
            memory_stats = self.analysis_results['task_memory_demand_stats']
            f.write(f"内存需求统计:\n")
            f.write(f"  平均值: {memory_stats['mean']:.2f}MB\n")
            f.write(f"  标准差: {memory_stats['std']:.2f}MB\n")
            f.write(f"  最小值: {memory_stats['min']:.2f}MB\n")
            f.write(f"  最大值: {memory_stats['max']:.2f}MB\n")
            f.write(f"  中位数: {memory_stats['median']:.2f}MB\n\n")
            
            # 节点统计
            f.write("=== 节点统计 ===\n")
            node_cpu_stats = self.analysis_results['node_cpu_capacity_stats']
            f.write(f"CPU容量统计:\n")
            f.write(f"  平均值: {node_cpu_stats['mean']:.2f}核\n")
            f.write(f"  标准差: {node_cpu_stats['std']:.2f}核\n")
            f.write(f"  最小值: {node_cpu_stats['min']:.2f}核\n")
            f.write(f"  最大值: {node_cpu_stats['max']:.2f}核\n\n")
            
            node_memory_stats = self.analysis_results['node_memory_capacity_stats']
            f.write(f"内存容量统计:\n")
            f.write(f"  平均值: {node_memory_stats['mean']:.2f}MB\n")
            f.write(f"  标准差: {node_memory_stats['std']:.2f}MB\n")
            f.write(f"  最小值: {node_memory_stats['min']:.2f}MB\n")
            f.write(f"  最大值: {node_memory_stats['max']:.2f}MB\n\n")
            
            # 资源利用率
            f.write("=== 资源利用率 ===\n")
            utilization = self.analysis_results['resource_utilization']
            f.write(f"CPU利用率: {utilization['cpu_utilization']:.2%}\n")
            f.write(f"内存利用率: {utilization['memory_utilization']:.2%}\n")
            f.write(f"I/O利用率: {utilization['io_utilization']:.2%}\n")
            f.write(f"网络利用率: {utilization['network_utilization']:.2%}\n\n")
            
            # 工作流特征
            f.write("=== 工作流特征 ===\n")
            workflow_chars = self.analysis_results['workflow_characteristics']
            f.write(f"图密度: {workflow_chars['density']:.4f}\n")
            f.write(f"平均聚类系数: {workflow_chars['average_clustering']:.4f}\n")
            if workflow_chars['diameter']:
                f.write(f"图直径: {workflow_chars['diameter']}\n")
            if workflow_chars['average_shortest_path']:
                f.write(f"平均最短路径: {workflow_chars['average_shortest_path']:.4f}\n\n")
            
            # 任务类型分布
            f.write("=== 任务类型分布 ===\n")
            task_type_dist = self.analysis_results['task_type_distribution']
            for task_type, stats in task_type_dist.items():
                f.write(f"{task_type}:\n")
                f.write(f"  数量: {stats['count']}\n")
                f.write(f"  平均执行时间: {stats['avg_runtime']:.2f}秒\n")
                f.write(f"  平均CPU需求: {stats['avg_cpu_demand']:.2f}核\n")
                f.write(f"  平均内存需求: {stats['avg_memory_demand']:.2f}MB\n\n")
        
        print(f"分析报告已保存: {filename}")


def main():
    """主函数 - 分析工作流数据"""
    analyzer = WorkflowAnalyzer()
    
    # 分析所有工作流数据
    workflow_types = ['montage', 'brain', 'sipht', 'epigenomics', 'cybershake']
    
    for workflow_type in workflow_types:
        filename = f"{workflow_type}_workflows.json"
        try:
            print(f"正在分析 {workflow_type} 工作流...")
            
            # 加载数据
            analyzer.load_workflow_data(filename)
            
            # 分析特征
            analysis = analyzer.analyze_workflow_characteristics()
            
            # 生成报告
            analyzer.generate_analysis_report()
            
            # 绘制图表
            analyzer.plot_workflow_characteristics()
            analyzer.plot_workflow_dag()
            
            print(f"{workflow_type} 工作流分析完成！\n")
            
        except FileNotFoundError:
            print(f"未找到 {filename} 文件，跳过分析\n")
        except Exception as e:
            print(f"分析 {workflow_type} 工作流时出错: {e}\n")
    
    print("所有工作流数据分析完成！")


if __name__ == "__main__":
    main() 