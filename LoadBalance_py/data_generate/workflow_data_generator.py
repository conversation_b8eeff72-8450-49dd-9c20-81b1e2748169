"""
负载均衡仿真系统 - 工作流数据生成器
基于真实工作流结构生成任务和节点数据
参考Pegasus工作流库: https://pegasus.isi.edu/workflow_gallery/index.php
"""

import json
import random
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass, field
import networkx as nx
from realistic_data_model import RealisticDataModel


@dataclass
class WorkflowTask:
    """工作流任务结构"""
    task_id: str
    base_runtime: float         # 基础执行时间(秒) - 在标准节点上的执行时间
    input_size: float          # 输入数据大小(MB)
    output_size: float         # 输出数据大小(MB)
    cpu_requirement: float     # CPU需求(核数) - 任务需要占用的CPU核心数
    memory_requirement: float  # 内存需求(MB) - 任务需要占用的内存大小
    io_requirement: float      # I/O需求(MB/s) - 任务需要的I/O带宽
    network_requirement: float # 网络需求(MB/s) - 任务需要的网络带宽
    task_type: str             # 任务类型
    dependencies: List[str]    # 依赖任务列表
    
    def calculate_execution_time(self, node_cpu_capacity: float, node_memory_capacity: float, 
                                node_io_capacity: float, node_network_capacity: float) -> float:
        """计算任务在指定节点上的实际执行时间"""
        # 基础执行时间
        actual_runtime = self.base_runtime
        
        # CPU性能影响
        if node_cpu_capacity < self.cpu_requirement:
            # CPU不足时，执行时间按比例增加
            cpu_factor = self.cpu_requirement / node_cpu_capacity
            actual_runtime *= cpu_factor
        elif node_cpu_capacity > self.cpu_requirement * 2:
            # CPU充足时，可能有轻微的性能提升
            cpu_factor = 0.9
            actual_runtime *= cpu_factor
        
        # 内存性能影响
        if node_memory_capacity < self.memory_requirement:
            # 内存不足时，执行时间显著增加（可能触发交换）
            memory_factor = (self.memory_requirement / node_memory_capacity) ** 1.5
            actual_runtime *= memory_factor
        
        # I/O性能影响
        if node_io_capacity < self.io_requirement:
            # I/O带宽不足时，执行时间增加
            io_factor = self.io_requirement / node_io_capacity
            actual_runtime *= io_factor
        
        # 网络性能影响（对于需要网络传输的任务）
        if self.network_requirement > 0 and node_network_capacity < self.network_requirement:
            # 网络带宽不足时，执行时间增加
            network_factor = self.network_requirement / node_network_capacity
            actual_runtime *= network_factor
        
        # 添加随机波动（±5%）
        noise = random.uniform(0.95, 1.05)
        actual_runtime *= noise
        
        return max(actual_runtime, 1.0)  # 最小执行时间为1秒


@dataclass
class WorkflowNode:
    """工作流节点结构"""
    node_id: str
    cpu_capacity: float        # CPU容量(核数)
    memory_capacity: float     # 内存容量(MB)
    io_capacity: float         # I/O带宽(MB/s)
    network_capacity: float    # 网络带宽(MB/s)
    energy_efficiency: float   # 能源效率
    cost_per_hour: float       # 每小时成本($)
    node_type: str             # 节点类型


@dataclass
class WorkflowData:
    """工作流数据结构"""
    workflow_type: str         # 工作流类型
    tasks: Dict[str, WorkflowTask]  # 任务字典
    edges: List[Tuple[str, str, Dict]]  # 依赖关系
    metadata: Dict[str, Any]   # 元数据
    nodes: List[WorkflowNode]  # 节点列表


class WorkflowDataGenerator:
    """工作流数据生成器"""
    
    def __init__(self):
        self.workflow_types = {
            'montage': self._generate_montage_workflow,
            'brain': self._generate_brain_workflow,
            'sipht': self._generate_sipht_workflow,
            'epigenomics': self._generate_epigenomics_workflow,
            'cybershake': self._generate_cybershake_workflow
        }
        
        # 初始化真实数据模型
        self.realistic_model = RealisticDataModel()
    
    def generate_workflow_data(self, workflow_type: str, num_tasks: int = 25, num_nodes: int = 8) -> WorkflowData:
        """生成指定类型的工作流数据"""
        if workflow_type not in self.workflow_types:
            raise ValueError(f"不支持的工作流类型: {workflow_type}")
        
        generator_func = self.workflow_types[workflow_type]
        return generator_func(num_tasks, num_nodes)
    
    def _generate_montage_workflow(self, num_tasks: int, num_nodes: int) -> WorkflowData:
        """生成Montage天文图像拼接工作流 (CPU和I/O密集型)"""
        # 参考: https://pegasus.isi.edu/workflow_gallery/index.php
        # Montage应用由NASA/IPAC创建，将多个输入图像拼接成天空的自定义马赛克
        
        tasks = {}
        task_types = ['mProject', 'mDiffFit', 'mConcatFit', 'mBgModel', 'mBackground', 'mAdd']
        
        # 生成任务
        for i in range(num_tasks):
            task_type = random.choice(task_types)
            task_id = f"task_{i}"
            
            # 使用真实数据模型生成任务
            task_data = self.realistic_model.generate_realistic_task(task_type, task_id)
            
            tasks[task_id] = WorkflowTask(
                task_id=task_id,
                base_runtime=task_data['base_runtime'],
                input_size=task_data['input_size'],
                output_size=task_data['output_size'],
                cpu_requirement=task_data['cpu_requirement'],
                memory_requirement=task_data['memory_requirement'],
                io_requirement=task_data['io_requirement'],
                network_requirement=task_data['network_requirement'],
                task_type=task_type,
                dependencies=[]
            )
        
        # 生成依赖关系 (DAG结构)
        edges = self._generate_dag_edges(tasks, max_dependencies=3)
        
        # 生成异构节点
        nodes = self._generate_heterogeneous_nodes(num_nodes, 'cpu_intensive')
        
        # 计算元数据
        metadata = self._calculate_workflow_metadata(tasks, edges)
        metadata['workflow_type'] = 'montage'
        
        return WorkflowData(
            workflow_type='montage',
            tasks=tasks,
            edges=edges,
            metadata=metadata,
            nodes=nodes
        )
    
    def _generate_brain_workflow(self, num_tasks: int, num_nodes: int) -> WorkflowData:
        """生成Brain神经影像处理工作流 (内存密集型)"""
        # 参考: https://pegasus.isi.edu/workflow_gallery/index.php
        # Brain工作流用于神经影像处理，包括图像预处理、分割、配准等
        
        tasks = {}
        task_types = ['preprocessing', 'segmentation', 'registration', 'normalization', 'statistics', 'visualization']
        
        for i in range(num_tasks):
            task_type = random.choice(task_types)
            task_id = f"task_{i}"
            
            # 使用真实数据模型生成任务
            task_data = self.realistic_model.generate_realistic_task(task_type, task_id)
            
            tasks[task_id] = WorkflowTask(
                task_id=task_id,
                base_runtime=task_data['base_runtime'],
                input_size=task_data['input_size'],
                output_size=task_data['output_size'],
                cpu_requirement=task_data['cpu_requirement'],
                memory_requirement=task_data['memory_requirement'],
                io_requirement=task_data['io_requirement'],
                network_requirement=task_data['network_requirement'],
                task_type=task_type,
                dependencies=[]
            )
        
        edges = self._generate_dag_edges(tasks, max_dependencies=4)
        nodes = self._generate_heterogeneous_nodes(num_nodes, 'memory_intensive')
        
        metadata = self._calculate_workflow_metadata(tasks, edges)
        metadata['workflow_type'] = 'brain'
        
        return WorkflowData(
            workflow_type='brain',
            tasks=tasks,
            edges=edges,
            metadata=metadata,
            nodes=nodes
        )
    
    def _generate_sipht_workflow(self, num_tasks: int, num_nodes: int) -> WorkflowData:
        """生成SIPHT生物信息学分析工作流 (计算密集型)"""
        # 参考: https://pegasus.isi.edu/workflow_gallery/index.php
        # SIPHT工作流用于自动化搜索细菌复制子中的非翻译RNA (sRNAs)
        
        tasks = {}
        task_types = ['blast', 'clustalw', 'rnafold', 'infernal', 'cmsearch', 'genome_scan']
        
        for i in range(num_tasks):
            task_type = random.choice(task_types)
            task_id = f"task_{i}"
            
            # 使用真实数据模型生成任务
            task_data = self.realistic_model.generate_realistic_task(task_type, task_id)
            
            tasks[task_id] = WorkflowTask(
                task_id=task_id,
                base_runtime=task_data['base_runtime'],
                input_size=task_data['input_size'],
                output_size=task_data['output_size'],
                cpu_requirement=task_data['cpu_requirement'],
                memory_requirement=task_data['memory_requirement'],
                io_requirement=task_data['io_requirement'],
                network_requirement=task_data['network_requirement'],
                task_type=task_type,
                dependencies=[]
            )
        
        edges = self._generate_dag_edges(tasks, max_dependencies=3)
        nodes = self._generate_heterogeneous_nodes(num_nodes, 'compute_intensive')
        
        metadata = self._calculate_workflow_metadata(tasks, edges)
        metadata['workflow_type'] = 'sipht'
        
        return WorkflowData(
            workflow_type='sipht',
            tasks=tasks,
            edges=edges,
            metadata=metadata,
            nodes=nodes
        )
    
    def _generate_epigenomics_workflow(self, num_tasks: int, num_nodes: int) -> WorkflowData:
        """生成Epigenomics表观基因组学数据流工作流 (数据流密集型)"""
        # 参考: https://pegasus.isi.edu/workflow_gallery/index.php
        # Epigenomics工作流用于自动化基因组序列处理中的各种操作
        
        tasks = {}
        task_types = ['fastqc', 'bwa', 'samtools', 'macs2', 'homer', 'bedtools', 'igv']
        
        for i in range(num_tasks):
            task_type = random.choice(task_types)
            task_id = f"task_{i}"
            
            # 使用真实数据模型生成任务
            task_data = self.realistic_model.generate_realistic_task(task_type, task_id)
            
            tasks[task_id] = WorkflowTask(
                task_id=task_id,
                base_runtime=task_data['base_runtime'],
                input_size=task_data['input_size'],
                output_size=task_data['output_size'],
                cpu_requirement=task_data['cpu_requirement'],
                memory_requirement=task_data['memory_requirement'],
                io_requirement=task_data['io_requirement'],
                network_requirement=task_data['network_requirement'],
                task_type=task_type,
                dependencies=[]
            )
        
        edges = self._generate_dag_edges(tasks, max_dependencies=5)
        nodes = self._generate_heterogeneous_nodes(num_nodes, 'data_intensive')
        
        metadata = self._calculate_workflow_metadata(tasks, edges)
        metadata['workflow_type'] = 'epigenomics'
        
        return WorkflowData(
            workflow_type='epigenomics',
            tasks=tasks,
            edges=edges,
            metadata=metadata,
            nodes=nodes
        )
    
    def _generate_cybershake_workflow(self, num_tasks: int, num_nodes: int) -> WorkflowData:
        """生成CyberShake地震模拟工作流 (通信密集型)"""
        # 参考: https://pegasus.isi.edu/workflow_gallery/index.php
        # CyberShake工作流用于表征区域地震灾害，需要大量节点间通信
        
        tasks = {}
        task_types = ['rupture_generator', 'velocity_model', 'srfgen', 'syn1d', 'syn2d', 'syn3d', 'post_processing']
        
        for i in range(num_tasks):
            task_type = random.choice(task_types)
            task_id = f"task_{i}"
            
            # 使用真实数据模型生成任务
            task_data = self.realistic_model.generate_realistic_task(task_type, task_id)
            
            tasks[task_id] = WorkflowTask(
                task_id=task_id,
                base_runtime=task_data['base_runtime'],
                input_size=task_data['input_size'],
                output_size=task_data['output_size'],
                cpu_requirement=task_data['cpu_requirement'],
                memory_requirement=task_data['memory_requirement'],
                io_requirement=task_data['io_requirement'],
                network_requirement=task_data['network_requirement'],
                task_type=task_type,
                dependencies=[]
            )
        
        edges = self._generate_dag_edges(tasks, max_dependencies=4)
        nodes = self._generate_heterogeneous_nodes(num_nodes, 'network_intensive')
        
        metadata = self._calculate_workflow_metadata(tasks, edges)
        metadata['workflow_type'] = 'cybershake'
        
        return WorkflowData(
            workflow_type='cybershake',
            tasks=tasks,
            edges=edges,
            metadata=metadata,
            nodes=nodes
        )
    
    def _generate_dag_edges(self, tasks: Dict[str, WorkflowTask], max_dependencies: int) -> List[Tuple[str, str, Dict]]:
        """生成DAG依赖关系"""
        edges = []
        task_ids = list(tasks.keys())
        
        # 创建有向无环图
        G = nx.DiGraph()
        
        # 添加节点
        for task_id in task_ids:
            G.add_node(task_id)
        
        # 生成依赖关系
        for i, task_id in enumerate(task_ids):
            if i == 0:
                continue  # 第一个任务没有依赖
            
            # 随机选择依赖任务
            num_deps = random.randint(1, min(max_dependencies, i))
            dependencies = random.sample(task_ids[:i], num_deps)
            
            for dep_id in dependencies:
                # 检查是否形成环
                G.add_edge(dep_id, task_id)
                if nx.is_directed_acyclic_graph(G):
                    # 计算数据传输量
                    data_size = tasks[dep_id].output_size * random.uniform(0.5, 1.5)
                    edges.append((dep_id, task_id, {'data_size': data_size}))
                    tasks[task_id].dependencies.append(dep_id)
                else:
                    G.remove_edge(dep_id, task_id)
        
        return edges
    
    def _generate_heterogeneous_nodes(self, num_nodes: int, node_type: str) -> List[WorkflowNode]:
        """生成异构节点"""
        nodes = []
        
        for i in range(num_nodes):
            node_id = f"node_{i}"
            
            # 使用真实数据模型生成节点
            node_data = self.realistic_model.generate_realistic_node(node_type, node_id)
            
            nodes.append(WorkflowNode(
                node_id=node_id,
                cpu_capacity=node_data['cpu_capacity'],
                memory_capacity=node_data['memory_capacity'],
                io_capacity=node_data['io_capacity'],
                network_capacity=node_data['network_capacity'],
                energy_efficiency=node_data['energy_efficiency'],
                cost_per_hour=node_data['cost_per_hour'],
                node_type=node_type
            ))
        
        return nodes
    
    def _calculate_workflow_metadata(self, tasks: Dict[str, WorkflowTask], edges: List[Tuple[str, str, Dict]]) -> Dict[str, Any]:
        """计算工作流元数据"""
        total_tasks = len(tasks)
        total_base_runtime = sum(task.base_runtime for task in tasks.values())
        total_cpu_requirement = sum(task.cpu_requirement for task in tasks.values())
        total_memory_requirement = sum(task.memory_requirement for task in tasks.values())
        
        # 计算关键路径长度
        G = nx.DiGraph()
        for edge in edges:
            G.add_edge(edge[0], edge[1], weight=tasks[edge[1]].base_runtime)
        
        if nx.is_directed_acyclic_graph(G):
            critical_path_length = nx.dag_longest_path_length(G)
        else:
            critical_path_length = total_base_runtime
        
        # 计算并行度
        max_parallel_tasks = max(len(tasks) // 3, 1)
        
        return {
            'total_tasks': total_tasks,
            'total_base_runtime': total_base_runtime,
            'total_cpu_requirement': total_cpu_requirement,
            'total_memory_requirement': total_memory_requirement,
            'critical_path_length': critical_path_length,
            'parallelism_degree': max_parallel_tasks,
            'total_edges': len(edges),
            'average_base_runtime': total_base_runtime / total_tasks,
            'average_cpu_requirement': total_cpu_requirement / total_tasks,
            'average_memory_requirement': total_memory_requirement / total_tasks
        }
    
    def validate_workflow_data(self, workflow_data: WorkflowData) -> Dict[str, Any]:
        """验证工作流数据的质量"""
        validation_results = {
            'compatibility': {},
            'resource_utilization': {},
            'data_quality': {}
        }
        
        # 1. 兼容性检查
        total_tasks = len(workflow_data.tasks)
        compatible_tasks = 0
        
        for task in workflow_data.tasks.values():
            for node in workflow_data.nodes:
                if (task.cpu_requirement <= node.cpu_capacity and
                    task.memory_requirement <= node.memory_capacity and
                    task.io_requirement <= node.io_capacity and
                    task.network_requirement <= node.network_capacity):
                    compatible_tasks += 1
                    break
        
        compatibility_ratio = compatible_tasks / total_tasks if total_tasks > 0 else 0
        validation_results['compatibility'] = {
            'total_tasks': total_tasks,
            'compatible_tasks': compatible_tasks,
            'compatibility_ratio': compatibility_ratio
        }
        
        # 2. 资源利用率检查
        cpu_utilization = []
        memory_utilization = []
        io_utilization = []
        network_utilization = []
        
        for task in workflow_data.tasks.values():
            for node in workflow_data.nodes:
                if (task.cpu_requirement <= node.cpu_capacity and
                    task.memory_requirement <= node.memory_capacity and
                    task.io_requirement <= node.io_capacity and
                    task.network_requirement <= node.network_capacity):
                    cpu_utilization.append(task.cpu_requirement / node.cpu_capacity)
                    memory_utilization.append(task.memory_requirement / node.memory_capacity)
                    io_utilization.append(task.io_requirement / node.io_capacity)
                    network_utilization.append(task.network_requirement / node.network_capacity)
                    break
        
        validation_results['resource_utilization'] = {
            'cpu_utilization': np.mean(cpu_utilization) if cpu_utilization else 0,
            'memory_utilization': np.mean(memory_utilization) if memory_utilization else 0,
            'io_utilization': np.mean(io_utilization) if io_utilization else 0,
            'network_utilization': np.mean(network_utilization) if network_utilization else 0
        }
        
        # 3. 数据质量评估
        avg_resource_utilization = np.mean(list(validation_results['resource_utilization'].values()))
        
        validation_results['overall_assessment'] = {
            'compatibility_ratio': compatibility_ratio,
            'avg_resource_utilization': avg_resource_utilization,
            'data_quality_score': (compatibility_ratio + avg_resource_utilization) / 2,
            'recommendations': self._generate_recommendations(validation_results)
        }
        
        return validation_results
    
    def _generate_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """生成数据质量改进建议"""
        recommendations = []
        
        # 从compatibility部分获取兼容性比例
        compatibility_ratio = validation_results['compatibility']['compatibility_ratio']
        resource_utilization = validation_results['resource_utilization']
        
        if compatibility_ratio < 0.8:
            recommendations.append("任务节点兼容性较低，建议调整任务资源需求或增加节点容量")
        
        if resource_utilization['cpu_utilization'] > 0.9:
            recommendations.append("CPU利用率过高，建议增加CPU密集型节点或减少任务CPU需求")
        
        if resource_utilization['memory_utilization'] > 0.9:
            recommendations.append("内存利用率过高，建议增加内存密集型节点或减少任务内存需求")
        
        if resource_utilization['io_utilization'] > 0.9:
            recommendations.append("I/O利用率过高，建议增加数据密集型节点或减少任务I/O需求")
        
        if resource_utilization['network_utilization'] > 0.9:
            recommendations.append("网络利用率过高，建议增加网络密集型节点或减少任务网络需求")
        
        if not recommendations:
            recommendations.append("数据质量良好，适合用于负载均衡仿真实验")
        
        return recommendations
    
    def save_workflow_data(self, workflow_data, filename):
        """保存工作流数据到JSON文件"""
        # 将数据转换为字典格式
        data_dict = {
            'workflow_type': workflow_data.workflow_type,
            'tasks': {},
            'nodes': [],
            'edges': [],
            'metadata': workflow_data.metadata
        }
        
        # 转换任务数据
        for task_id, task in workflow_data.tasks.items():
            data_dict['tasks'][str(task_id)] = {
                'task_type': task.task_type,
                'cpu_requirement': task.cpu_requirement,
                'memory_requirement': task.memory_requirement,
                'io_requirement': task.io_requirement,
                'network_requirement': task.network_requirement,
                'base_runtime': task.base_runtime,
                'dependencies': [str(dep) for dep in task.dependencies]
            }
        
        # 转换节点数据
        for node in workflow_data.nodes:
            data_dict['nodes'].append({
                'node_id': node.node_id,
                'cpu_capacity': node.cpu_capacity,
                'memory_capacity': node.memory_capacity,
                'io_capacity': node.io_capacity,
                'network_capacity': node.network_capacity,
                'energy_efficiency': node.energy_efficiency,
                'cost_per_hour': node.cost_per_hour,
                'node_type': node.node_type
            })
        
        # 转换边数据
        for edge in workflow_data.edges:
            data_dict['edges'].append([
                str(edge[0]), # source_task
                str(edge[1]), # target_task
                {
                    'data_size': edge[2]['data_size'],
                    'transfer_time': edge[2].get('transfer_time', 0) # 使用.get()避免KeyError
                }
            ])
        
        # 应用四舍五入到2位小数
        data_dict = self._round_dict_values(data_dict)
        
        # 保存到文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data_dict, f, indent=2, ensure_ascii=False)
        
        print(f"工作流数据已保存到: {filename}")
        
        # 验证数据质量
        # The original code had a DataQualityValidator class, but it's not defined in the provided file.
        # Assuming a placeholder for validation or that it's not needed for this specific edit.
        # For now, we'll just print a placeholder message.
        print("数据质量评估: 0.00 (Placeholder - DataQualityValidator not defined)")
        print("改进建议: 无 (Placeholder - DataQualityValidator not defined)")

    def _round_to_two_decimals(self, value):
        """将数值保留到小数点后2位"""
        if isinstance(value, (int, float)):
            return round(value, 2)
        return value
    
    def _round_dict_values(self, data_dict):
        """递归地将字典中的所有数值保留到2位小数"""
        if isinstance(data_dict, dict):
            rounded_dict = {}
            for key, value in data_dict.items():
                if isinstance(value, dict):
                    rounded_dict[key] = self._round_dict_values(value)
                elif isinstance(value, list):
                    rounded_dict[key] = [self._round_to_two_decimals(item) if isinstance(item, (int, float)) else item for item in value]
                else:
                    rounded_dict[key] = self._round_to_two_decimals(value)
            return rounded_dict
        return data_dict


def main():
    """主函数 - 生成所有类型的工作流数据"""
    import os
    
    # 创建data文件夹
    data_dir = "data"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"创建输出目录: {data_dir}")
    
    generator = WorkflowDataGenerator()
    
    # 生成各种类型的工作流数据
    workflow_types = ['montage', 'brain', 'sipht', 'epigenomics', 'cybershake']
    
    for workflow_type in workflow_types:
        print(f"正在生成 {workflow_type} 工作流数据...")
        
        # 生成工作流数据
        workflow_data = generator.generate_workflow_data(
            workflow_type=workflow_type,
            num_tasks=25,
            num_nodes=8
        )
        
        # 保存到文件
        filename = os.path.join(data_dir, f"{workflow_type}_workflows.json")
        generator.save_workflow_data(workflow_data, filename)
        
        # 打印统计信息
        print(f"  - 任务数量: {workflow_data.metadata['total_tasks']}")
        print(f"  - 总基础执行时间: {workflow_data.metadata['total_base_runtime']:.1f}秒")
        print(f"  - 关键路径长度: {workflow_data.metadata['critical_path_length']:.1f}秒")
        print(f"  - 并行度: {workflow_data.metadata['parallelism_degree']}")
        print(f"  - 节点数量: {len(workflow_data.nodes)}")
        print()
    
    print("所有工作流数据生成完成！")
    print(f"数据文件已保存到: {data_dir}/ 目录")


if __name__ == "__main__":
    main() 