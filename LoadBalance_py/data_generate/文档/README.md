# 负载均衡仿真系统 - 工作流数据生成模块

## 📋 概述

本模块是负载均衡仿真系统的核心数据生成组件，基于真实工作流结构生成任务和节点数据。参考[Pegasus工作流库](https://pegasus.isi.edu/workflow_gallery/index.php)中的真实应用场景，生成的数据具有高度的真实性和实用性，适用于异构节点负载均衡算法的仿真和测试。

### 🎯 主要功能
- **真实工作流数据生成**: 基于5种真实工作流类型生成任务和节点数据
- **异构节点支持**: 支持不同类型的计算节点和性能特征
- **DAG依赖关系**: 生成有向无环图结构，避免循环依赖
- **数据质量验证**: 自动验证生成数据的真实性和可靠性
- **可视化分析**: 提供工作流结构、资源分布和执行时间分析图表
- **批量处理**: 支持批量生成、分析和可视化所有工作流类型

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 进入data_generate目录
cd LoadBalance_py/data_generate
```

### 2. 一键生成所有数据
```bash
# 生成所有工作流数据、分析和可视化
python main_workflow_generator.py

# 或者指定参数
python main_workflow_generator.py --tasks 30 --nodes 10
```

### 3. 分步执行
```bash
# 仅生成数据
python main_workflow_generator.py --mode generate

# 仅分析数据
python main_workflow_generator.py --mode analyze

# 仅可视化
python main_workflow_generator.py --mode visualize
```

## 📊 支持的工作流类型

### 1. **Montage (天文图像拼接)**
- **特点**: CPU和I/O密集型
- **应用**: 将多个输入图像拼接成天空的自定义马赛克
- **任务类型**: mProject, mDiffFit, mConcatFit, mBgModel, mBackground, mAdd
- **资源需求**: 高CPU使用率，大量I/O操作
- **适用场景**: CPU密集型负载均衡算法测试

### 2. **Brain (神经影像处理)**
- **特点**: 内存密集型
- **应用**: 神经影像处理，包括图像预处理、分割、配准等
- **任务类型**: preprocessing, segmentation, registration, normalization, statistics, visualization
- **资源需求**: 高内存使用率，大数据集处理
- **适用场景**: 内存密集型负载均衡算法测试

### 3. **SIPHT (生物信息学分析)**
- **特点**: 计算密集型
- **应用**: 自动化搜索细菌复制子中的非翻译RNA (sRNAs)
- **任务类型**: blast, clustalw, rnafold, infernal, cmsearch, genome_scan
- **资源需求**: 高CPU使用率，复杂算法计算
- **适用场景**: 计算密集型负载均衡算法测试

### 4. **Epigenomics (表观基因组学)**
- **特点**: 数据流密集型
- **应用**: 自动化基因组序列处理中的各种操作
- **任务类型**: fastqc, bwa, samtools, macs2, homer, bedtools, igv
- **资源需求**: 大量数据传输，流水线处理
- **适用场景**: 数据流密集型负载均衡算法测试

### 5. **CyberShake (地震模拟)**
- **特点**: 通信密集型
- **应用**: 表征区域地震灾害，需要大量节点间通信
- **任务类型**: rupture_generator, velocity_model, srfgen, syn1d, syn2d, syn3d, post_processing
- **资源需求**: 高网络带宽，分布式计算
- **适用场景**: 通信密集型负载均衡算法测试

## 📁 文件结构

```
data_generate/
├── README.md                           # 主说明文档
├── 数据真实性与可靠性说明.md              # 数据质量详细说明
├── main_workflow_generator.py          # 主程序入口
├── main_data_generator.py              # 基础数据生成器
├── data_structures.py                  # 数据结构定义
├── realistic_data_model.py             # 真实数据模型
├── workflow_data_generator.py          # 工作流数据生成器
├── workflow_analyzer.py                # 工作流分析器
├── workflow_visualizer.py              # 工作流可视化器
├── data_quality_validator.py           # 数据质量验证器
├── example_usage.py                    # 使用示例
├── test_data_generator.py              # 测试程序
└── requirements.txt                    # 依赖包列表
```

## 🔧 核心组件

### 1. **数据结构 (data_structures.py)**
定义任务、节点和工作流的核心数据结构：
- `Task`: 任务结构，包含资源需求和依赖关系
- `Node`: 节点结构，包含资源容量和当前使用情况
- `TaskGenerationParams`: 任务生成参数
- `NodeGenerationParams`: 节点生成参数

### 2. **真实数据模型 (realistic_data_model.py)**
基于真实工作流特征的数据模型：
- `RealisticTaskProfile`: 真实任务性能特征
- `RealisticNodeProfile`: 真实节点性能特征
- 支持5种工作流类型的详细建模

### 3. **工作流数据生成器 (workflow_data_generator.py)**
核心数据生成功能：
- 生成DAG结构的工作流
- 计算任务依赖关系
- 分配资源需求
- 生成异构节点配置

### 4. **工作流分析器 (workflow_analyzer.py)**
数据分析功能：
- 工作流特征分析
- 资源利用率计算
- 图论指标计算
- 生成分析报告

### 5. **工作流可视化器 (workflow_visualizer.py)**
可视化功能：
- 工作流任务流程图
- 资源需求分布图
- 执行时间对比图
- 工作流特征分析图

### 6. **数据质量验证器 (data_quality_validator.py)**
质量验证功能：
- 任务节点兼容性检查
- 资源利用率验证
- 数据真实性评估
- 生成质量报告

## 📊 数据结构详解

### 任务数据结构
```python
{
    'task_id': 'task_0',
    'base_runtime': 45.2,         # 基础执行时间(秒)
    'input_size': 1024.5,         # 输入数据大小(MB)
    'output_size': 512.3,         # 输出数据大小(MB)
    'cpu_requirement': 2.5,       # CPU需求(核数)
    'memory_requirement': 4096.0, # 内存需求(MB)
    'io_requirement': 150.0,      # I/O需求(MB/s)
    'network_requirement': 256.7, # 网络需求(MB/s)
    'task_type': 'compute',       # 任务类型
    'dependencies': []            # 依赖任务列表
}
```

### 节点数据结构
```python
{
    'node_id': 'node_0',
    'cpu_capacity': 8.0,           # CPU容量(核数)
    'memory_capacity': 16384.0,    # 内存容量(MB)
    'io_capacity': 1000.0,         # I/O带宽(MB/s)
    'network_capacity': 1000.0,    # 网络带宽(MB/s)
    'energy_efficiency': 0.85,     # 能源效率
    'cost_per_hour': 0.12,         # 每小时成本($)
    'node_type': 'cpu_intensive'   # 节点类型
}
```

### 工作流数据结构
```python
{
    'workflow_type': 'montage',
    'tasks': { ... },              # 任务字典
    'edges': [                     # 依赖关系
        ('task_0', 'task_1', {'data_size': 128.5}),
        ('task_0', 'task_2', {'data_size': 64.2})
    ],
    'metadata': {                  # 元数据
        'total_tasks': 25,
        'critical_path_length': 156.8,
        'parallelism_degree': 3.2
    },
    'nodes': [ ... ]               # 节点列表
}
```

## 🎯 使用方法

### 1. 命令行使用
```bash
python main_workflow_generator.py [选项]

选项:
  --mode {generate,analyze,visualize,complete}  运行模式 (默认: complete)
  --tasks INT                                   任务数量 (默认: 25)
  --nodes INT                                   节点数量 (默认: 8)
  --output-dir PATH                             输出目录 (默认: 当前目录)
  --no-plots                                    不生成图表
  --help                                        显示帮助信息
```

### 2. 编程接口使用
```python
from workflow_data_generator import WorkflowDataGenerator
from workflow_analyzer import WorkflowAnalyzer
from workflow_visualizer import WorkflowVisualizer

# 生成工作流数据
generator = WorkflowDataGenerator()
workflow_data = generator.generate_workflow_data(
    workflow_type='montage',
    num_tasks=25,
    num_nodes=8
)

# 保存数据
generator.save_workflow_data(workflow_data, 'montage_workflows.json')

# 分析工作流数据
analyzer = WorkflowAnalyzer()
analyzer.load_workflow_data('montage_workflows.json')
analysis = analyzer.analyze_workflow_characteristics()

# 创建可视化
visualizer = WorkflowVisualizer()
visualizer.load_workflow_data('montage_workflows.json')
visualizer.create_comprehensive_visualization()
```

### 3. 批量处理
```python
from main_workflow_generator import WorkflowDataManager

manager = WorkflowDataManager()
files, results = manager.run_complete_workflow(25, 8)
```

## 📈 输出文件

### 生成的数据文件
- `montage_workflows.json` - Montage工作流数据
- `brain_workflows.json` - Brain工作流数据
- `sipht_workflows.json` - SIPHT工作流数据
- `epigenomics_workflows.json` - Epigenomics工作流数据
- `cybershake_workflows.json` - CyberShake工作流数据

### 分析报告文件
- `*_analysis_report.txt` - 各工作流的详细分析报告
- `workflow_comparison_report.txt` - 工作流对比分析报告
- `*_quality_report.txt` - 数据质量报告

### 可视化图表
- `*_workflow_diagram.png` - 工作流任务流程图
- `*_resource_requirements.png` - 资源需求分布图
- `*_execution_time_comparison.png` - 执行时间对比图
- `*_characteristics.png` - 工作流特征分析图
- `*_dag.png` - 工作流DAG图
- `*_quality_analysis.png` - 数据质量分析图

## 🎨 可视化功能

### 1. **工作流任务流程图**
- 按层级显示任务依赖关系
- 不同任务类型使用不同颜色
- 显示资源需求和数据传输量
- 参考Pegasus工作流库的可视化效果

### 2. **资源需求分布图**
- CPU、内存、I/O、网络需求分布
- 按任务类型着色
- 便于识别资源密集型任务

### 3. **执行时间对比图**
- 任务在不同节点上的执行时间分布
- 箱线图显示时间变化范围
- 体现异构节点性能差异

### 4. **工作流特征分析图**
- 图密度、聚类系数等图论指标
- 入度/出度分布
- 中心性指标分析

### 5. **数据质量分析图**
- 兼容性分数分布
- 资源利用率分析
- 数据真实性评估

## 🔍 数据质量保障

### 1. **真实性保障**
- 基于Pegasus工作流库的真实应用场景
- 真实的任务性能特征建模
- 真实的节点性能特征建模
- 考虑不同工作流类型的特征差异

### 2. **可靠性保障**
- 任务节点兼容性验证
- 资源利用率检查
- 执行时间合理性验证
- 依赖关系正确性检查

### 3. **质量评估指标**
- **兼容性分数**: 确保大部分任务都能在至少一个节点上执行
- **资源利用率分数**: 避免资源过度利用或严重浪费
- **数据真实性分数**: 确保任务资源需求与节点容量比例合理
- **整体质量分数**: 综合评估数据质量

## 📊 分析指标

### 任务统计指标
- 基础执行时间分布 (均值、标准差、最小值、最大值、中位数)
- CPU需求分布
- 内存需求分布
- I/O需求分布
- 网络需求分布

### 节点统计指标
- CPU容量分布
- 内存容量分布
- I/O带宽分布
- 网络带宽分布

### 工作流特征指标
- 图密度
- 平均聚类系数
- 图直径
- 平均最短路径长度
- 入度/出度分布
- 中心性指标

### 资源利用率指标
- CPU利用率
- 内存利用率
- I/O利用率
- 网络利用率

## 🎯 负载均衡策略建议

### CPU密集型工作流 (Montage, SIPHT)
- 优先考虑CPU负载均衡
- 关注计算资源分配
- 考虑任务优先级调度

### 内存密集型工作流 (Brain)
- 重点关注内存使用情况
- 避免内存不足导致的性能下降
- 考虑内存碎片化影响

### 数据流密集型工作流 (Epigenomics)
- 优化数据传输
- 减少网络瓶颈
- 考虑数据本地化策略

### 通信密集型工作流 (CyberShake)
- 网络拓扑优化
- 减少通信开销
- 考虑网络带宽分配

## 🔧 技术实现

### 核心算法
- **DAG生成算法**: 使用NetworkX库生成有向无环图
- **资源分配算法**: 基于工作流类型的资源需求分配
- **执行时间计算算法**: 考虑节点性能的动态计算
- **统计分析算法**: 使用NumPy进行统计分析
- **可视化算法**: 使用Matplotlib和Seaborn进行数据可视化

### 依赖库
- `networkx`: 图论和网络分析
- `numpy`: 数值计算
- `matplotlib`: 数据可视化
- `seaborn`: 统计图表
- `pandas`: 数据处理

## 📝 使用示例

### 示例1: 生成Montage工作流
```python
from workflow_data_generator import WorkflowDataGenerator

generator = WorkflowDataGenerator()
montage_data = generator.generate_workflow_data('montage', 30, 10)
generator.save_workflow_data(montage_data, 'montage_workflows.json')
```

### 示例2: 分析工作流特征
```python
from workflow_analyzer import WorkflowAnalyzer

analyzer = WorkflowAnalyzer()
analyzer.load_workflow_data('montage_workflows.json')
analysis = analyzer.analyze_workflow_characteristics()
analyzer.plot_workflow_characteristics()
```

### 示例3: 创建可视化
```python
from workflow_visualizer import WorkflowVisualizer

visualizer = WorkflowVisualizer()
visualizer.load_workflow_data('montage_workflows.json')
visualizer.create_comprehensive_visualization()
```

### 示例4: 验证数据质量
```python
from data_quality_validator import DataQualityValidator

validator = DataQualityValidator()
validation_results = validator.validate_workflow_data(workflow_data)
validator.generate_quality_report(validation_results)
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个模块！

### 开发环境设置
```bash
# 安装依赖
pip install -r requirements.txt

# 运行测试
python test_data_generator.py

# 代码格式化
black *.py
```

### 代码规范
- 遵循PEP 8代码风格
- 添加详细的文档字符串
- 编写单元测试
- 保持代码的可读性和可维护性

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🙏 致谢

感谢[Pegasus工作流管理系统](https://pegasus.isi.edu/)提供的真实工作流案例，这些案例为我们的数据生成提供了重要的参考。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
- 参与项目讨论

---

**注意**: 本模块生成的数据仅用于学术研究和算法仿真，请勿用于生产环境。 