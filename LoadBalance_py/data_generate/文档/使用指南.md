# 负载均衡仿真系统 - 数据生成模块使用指南

## 📋 目录
1. [快速开始](#快速开始)
2. [环境配置](#环境配置)
3. [基本使用](#基本使用)
4. [高级功能](#高级功能)
5. [数据质量验证](#数据质量验证)
6. [可视化分析](#可视化分析)
7. [故障排除](#故障排除)
8. [最佳实践](#最佳实践)

## 🚀 快速开始

### 1. 环境配置
```bash
# 克隆项目
git clone <repository-url>
cd LoadBalance_py/data_generate

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import numpy, pandas, networkx, matplotlib; print('环境配置成功！')"
```

### 2. 一键生成所有数据
```bash
# 使用默认参数生成所有工作流数据
python main_workflow_generator.py

# 使用自定义参数
python main_workflow_generator.py --tasks 50 --nodes 16 --output-dir ./output
```

### 3. 查看生成结果
```bash
# 查看生成的文件
ls -la *.json *.txt *.png

# 查看分析报告
cat montage_analysis_report.txt
```

## 🔧 环境配置

### 系统要求
- **Python版本**: 3.8+
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **内存**: 建议4GB+
- **存储**: 建议2GB+可用空间

### 依赖包安装
```bash
# 使用pip安装
pip install -r requirements.txt

# 或者使用conda安装
conda install numpy pandas networkx matplotlib seaborn scipy scikit-learn
pip install plotly tqdm colorama pyyaml jsonschema

# 开发环境额外安装
pip install pytest pytest-cov black flake8 mypy
```

### 环境验证
```python
# 运行验证脚本
python -c "
import sys
import importlib

required_packages = [
    'numpy', 'pandas', 'networkx', 'matplotlib', 
    'seaborn', 'scipy', 'scikit-learn', 'plotly'
]

missing_packages = []
for package in required_packages:
    try:
        importlib.import_module(package)
        print(f'✓ {package}')
    except ImportError:
        missing_packages.append(package)
        print(f'✗ {package}')

if missing_packages:
    print(f'\n缺少包: {missing_packages}')
    print('请运行: pip install -r requirements.txt')
else:
    print('\n环境配置完成！')
"
```

## 📊 基本使用

### 1. 命令行使用

#### 生成数据
```bash
# 生成所有工作流数据
python main_workflow_generator.py --mode generate

# 生成特定工作流
python main_workflow_generator.py --mode generate --workflow montage

# 自定义参数
python main_workflow_generator.py --mode generate --tasks 100 --nodes 20
```

#### 分析数据
```bash
# 分析所有工作流
python main_workflow_generator.py --mode analyze

# 分析特定工作流
python main_workflow_generator.py --mode analyze --workflow brain

# 不生成图表
python main_workflow_generator.py --mode analyze --no-plots
```

#### 可视化数据
```bash
# 可视化所有工作流
python main_workflow_generator.py --mode visualize

# 可视化特定工作流
python main_workflow_generator.py --mode visualize --workflow sipht

# 指定输出目录
python main_workflow_generator.py --mode visualize --output-dir ./plots
```

### 2. 编程接口使用

#### 基本数据生成
```python
from workflow_data_generator import WorkflowDataGenerator

# 创建生成器
generator = WorkflowDataGenerator()

# 生成Montage工作流
montage_data = generator.generate_workflow_data(
    workflow_type='montage',
    num_tasks=25,
    num_nodes=8
)

# 保存数据
generator.save_workflow_data(montage_data, 'montage_workflows.json')

print(f"生成了 {montage_data.metadata['total_tasks']} 个任务")
print(f"生成了 {len(montage_data.nodes)} 个节点")
```

#### 批量生成
```python
from main_workflow_generator import WorkflowDataManager

# 创建管理器
manager = WorkflowDataManager()

# 批量生成所有工作流
files, results = manager.run_complete_workflow(
    num_tasks=30,
    num_nodes=10,
    output_dir='./output',
    generate_plots=True
)

print(f"生成了 {len(files)} 个文件")
for file in files:
    print(f"  - {file}")
```

#### 自定义参数生成
```python
from data_structures import TaskGenerationParams, NodeGenerationParams
from data_structures import DataGenerator

# 自定义任务参数
task_params = TaskGenerationParams(
    time_steps=3,
    tasks_per_time_step=20,
    min_cpu_demand=200,
    max_cpu_demand=800,
    dependency_probability=0.4,
    max_dependencies=3
)

# 自定义节点参数
node_params = NodeGenerationParams(
    num_nodes=12,
    min_cpu_capacity=8000,
    max_cpu_capacity=20000,
    min_memory_capacity=8192,
    max_memory_capacity=16384
)

# 生成数据
DataGenerator.generate_task_dataset("custom_tasks.txt", task_params)
DataGenerator.generate_node_dataset("custom_nodes.txt", node_params)
```

## 🔍 高级功能

### 1. 工作流分析

#### 基本分析
```python
from workflow_analyzer import WorkflowAnalyzer

# 创建分析器
analyzer = WorkflowAnalyzer()

# 加载数据
analyzer.load_workflow_data('montage_workflows.json')

# 分析特征
analysis = analyzer.analyze_workflow_characteristics()

# 打印结果
print("工作流特征分析:")
print(f"  任务数量: {analysis['total_tasks']}")
print(f"  节点数量: {analysis['total_nodes']}")
print(f"  图密度: {analysis['graph_density']:.3f}")
print(f"  平均聚类系数: {analysis['avg_clustering']:.3f}")
print(f"  关键路径长度: {analysis['critical_path_length']:.1f}秒")
```

#### 详细分析
```python
# 生成详细分析报告
analyzer.generate_analysis_report('detailed_analysis.txt')

# 分析资源分布
resource_analysis = analyzer.analyze_resource_distribution()
print("资源分布分析:")
for resource_type, stats in resource_analysis.items():
    print(f"  {resource_type}:")
    print(f"    均值: {stats['mean']:.2f}")
    print(f"    标准差: {stats['std']:.2f}")
    print(f"    最小值: {stats['min']:.2f}")
    print(f"    最大值: {stats['max']:.2f}")
```

### 2. 数据质量验证

#### 基本验证
```python
from data_quality_validator import DataQualityValidator

# 创建验证器
validator = DataQualityValidator()

# 加载数据
workflow_data = validator.load_workflow_data('montage_workflows.json')

# 验证数据质量
validation_results = validator.validate_workflow_data(workflow_data)

# 查看验证结果
print("数据质量验证结果:")
print(f"  兼容性分数: {validation_results['compatibility_score']:.3f}")
print(f"  资源利用率分数: {validation_results['resource_utilization_score']:.3f}")
print(f"  数据真实性分数: {validation_results['realism_score']:.3f}")
print(f"  整体质量分数: {validation_results['overall_quality_score']:.3f}")
```

#### 详细验证
```python
# 生成质量报告
validator.generate_quality_report(validation_results, 'quality_report.txt')

# 生成质量分析图
validator.plot_quality_analysis(validation_results, 'quality_analysis.png')

# 检查兼容性问题
compatibility_issues = validator.check_compatibility_issues(workflow_data)
if compatibility_issues:
    print("发现兼容性问题:")
    for issue in compatibility_issues:
        print(f"  - {issue}")
```

### 3. 可视化分析

#### 基本可视化
```python
from workflow_visualizer import WorkflowVisualizer

# 创建可视化器
visualizer = WorkflowVisualizer()

# 加载数据
visualizer.load_workflow_data('montage_workflows.json')

# 创建工作流图
visualizer.create_workflow_diagram('montage_workflow.png')

# 创建资源需求图
visualizer.create_resource_requirements_plot('montage_resources.png')

# 创建执行时间对比图
visualizer.create_execution_time_comparison('montage_execution.png')
```

#### 高级可视化
```python
# 创建综合可视化
visualizer.create_comprehensive_visualization('montage_comprehensive.png')

# 创建DAG图
visualizer.create_dag_visualization('montage_dag.png')

# 创建特征分析图
visualizer.create_characteristics_plot('montage_characteristics.png')

# 自定义可视化
visualizer.create_custom_plot(
    plot_type='resource_distribution',
    output_file='custom_plot.png',
    title='自定义资源分布图',
    figsize=(12, 8)
)
```

## 📈 数据质量验证

### 1. 质量指标说明

#### 兼容性分数 (Compatibility Score)
- **定义**: 兼容任务数 / 总任务数
- **理想范围**: ≥0.8
- **说明**: 确保大部分任务都能在至少一个节点上执行

#### 资源利用率分数 (Resource Utilization Score)
- **定义**: 平均资源利用率
- **理想范围**: 0.3-0.8
- **说明**: 避免资源过度利用或严重浪费

#### 数据真实性分数 (Realism Score)
- **定义**: 基于任务需求与节点容量的比例合理性
- **理想范围**: ≥0.7
- **说明**: 确保任务资源需求与节点容量比例符合真实场景

#### 整体质量分数 (Overall Quality Score)
- **定义**: (兼容性分数 + 资源利用率分数) / 2
- **理想范围**: ≥0.8
- **说明**: 综合评估数据质量

### 2. 质量改进策略

#### 兼容性问题改进
```python
# 问题: 任务节点兼容性较低
# 解决方案: 调整任务资源需求范围

from realistic_data_model import RealisticTaskProfile

# 降低任务资源需求
profile = RealisticTaskProfile(
    task_type='compute',
    base_runtime_range=(30, 120),  # 降低执行时间
    cpu_intensity=0.6,             # 降低CPU密集度
    memory_intensity=0.5,          # 降低内存密集度
    resource_scaling_factor=0.8    # 降低资源缩放因子
)
```

#### 资源利用率问题改进
```python
# 问题: 资源利用率过高或过低
# 解决方案: 调整节点容量配置

from realistic_data_model import RealisticNodeProfile

# 调整节点容量
profile = RealisticNodeProfile(
    node_type='cpu_intensive',
    cpu_cores_range=(12, 24),      # 增加CPU核心数
    memory_capacity_range=(16384, 32768),  # 增加内存容量
    io_bandwidth_range=(1500, 3000),       # 增加I/O带宽
    cost_per_hour_range=(0.15, 0.25)       # 调整成本
)
```

## 🎨 可视化分析

### 1. 工作流任务流程图
- **用途**: 显示任务依赖关系和执行顺序
- **特点**: 按层级布局，不同任务类型使用不同颜色
- **信息**: 显示资源需求和数据传输量

### 2. 资源需求分布图
- **用途**: 分析任务资源需求分布
- **特点**: 按任务类型着色，便于识别资源密集型任务
- **信息**: CPU、内存、I/O、网络需求分布

### 3. 执行时间对比图
- **用途**: 比较任务在不同节点上的执行时间
- **特点**: 箱线图显示时间变化范围
- **信息**: 体现异构节点性能差异

### 4. 工作流特征分析图
- **用途**: 分析工作流的结构特征
- **特点**: 显示图论指标和统计特征
- **信息**: 图密度、聚类系数、中心性指标

### 5. 数据质量分析图
- **用途**: 评估生成数据的质量
- **特点**: 多维度质量指标可视化
- **信息**: 兼容性、利用率、真实性分数

## 🔧 故障排除

### 1. 常见问题

#### 问题1: 导入模块失败
```bash
# 错误信息: ModuleNotFoundError: No module named 'numpy'
# 解决方案:
pip install numpy pandas networkx matplotlib seaborn
```

#### 问题2: 内存不足
```bash
# 错误信息: MemoryError
# 解决方案:
# 1. 减少任务和节点数量
python main_workflow_generator.py --tasks 20 --nodes 5

# 2. 增加系统内存或使用虚拟内存
```

#### 问题3: 图形显示问题
```bash
# 错误信息: TclError: no display name and no $DISPLAY environment variable
# 解决方案:
# 1. 设置matplotlib后端
export MPLBACKEND=Agg

# 2. 或者在代码中设置
import matplotlib
matplotlib.use('Agg')
```

#### 问题4: 文件权限问题
```bash
# 错误信息: PermissionError: [Errno 13] Permission denied
# 解决方案:
# 1. 检查文件权限
ls -la output/

# 2. 修改权限
chmod 755 output/
```

### 2. 调试技巧

#### 启用详细日志
```python
import logging

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)

# 在代码中添加日志
logging.debug("正在生成任务...")
logging.info("任务生成完成")
logging.warning("发现兼容性问题")
logging.error("生成失败")
```

#### 使用调试器
```python
import pdb

# 在代码中添加断点
pdb.set_trace()

# 或者使用ipdb (需要安装)
# pip install ipdb
import ipdb; ipdb.set_trace()
```

#### 检查数据完整性
```python
def check_data_integrity(workflow_data):
    """检查数据完整性"""
    # 检查必要字段
    required_fields = ['tasks', 'nodes', 'edges', 'metadata']
    for field in required_fields:
        if field not in workflow_data:
            print(f"缺少必要字段: {field}")
            return False
    
    # 检查数据类型
    if not isinstance(workflow_data['tasks'], dict):
        print("tasks字段类型错误")
        return False
    
    # 检查数据一致性
    task_ids = set(workflow_data['tasks'].keys())
    edge_task_ids = set()
    for edge in workflow_data['edges']:
        edge_task_ids.add(edge[0])
        edge_task_ids.add(edge[1])
    
    if not edge_task_ids.issubset(task_ids):
        print("边中的任务ID不存在")
        return False
    
    print("数据完整性检查通过")
    return True
```

## 💡 最佳实践

### 1. 数据生成最佳实践

#### 选择合适的参数
```python
# 推荐参数配置
recommended_params = {
    'small_scale': {
        'tasks': 25,
        'nodes': 8,
        'description': '适合快速测试和开发'
    },
    'medium_scale': {
        'tasks': 50,
        'nodes': 16,
        'description': '适合算法验证和性能测试'
    },
    'large_scale': {
        'tasks': 100,
        'nodes': 32,
        'description': '适合大规模仿真和压力测试'
    }
}
```

#### 工作流类型选择
```python
# 根据算法特点选择工作流类型
workflow_selection = {
    'cpu_intensive_algorithm': ['montage', 'sipht'],
    'memory_intensive_algorithm': ['brain'],
    'network_intensive_algorithm': ['cybershake'],
    'data_flow_intensive_algorithm': ['epigenomics'],
    'general_algorithm': ['montage', 'brain', 'sipht', 'epigenomics', 'cybershake']
}
```

### 2. 性能优化建议

#### 内存优化
```python
# 1. 使用生成器而不是列表
def generate_tasks(num_tasks):
    for i in range(num_tasks):
        yield create_task(i)

# 2. 及时释放不需要的数据
import gc
del large_data
gc.collect()

# 3. 使用numpy数组而不是Python列表
import numpy as np
data = np.array(data_list)
```

#### 计算优化
```python
# 1. 使用向量化操作
import numpy as np
# 慢的方式
result = [x * 2 for x in data]
# 快的方式
result = np.array(data) * 2

# 2. 使用并行处理
from multiprocessing import Pool
with Pool() as pool:
    results = pool.map(process_task, tasks)
```

### 3. 代码质量建议

#### 代码组织
```python
# 1. 使用类和模块组织代码
class WorkflowGenerator:
    def __init__(self, config):
        self.config = config
    
    def generate(self):
        pass
    
    def validate(self):
        pass

# 2. 使用配置文件
import yaml
with open('config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# 3. 使用类型注解
from typing import List, Dict, Any
def generate_tasks(num_tasks: int) -> List[Dict[str, Any]]:
    pass
```

#### 错误处理
```python
# 1. 使用异常处理
try:
    workflow_data = generator.generate_workflow_data()
except ValueError as e:
    print(f"参数错误: {e}")
    return None
except MemoryError as e:
    print(f"内存不足: {e}")
    return None

# 2. 使用断言验证
assert len(tasks) > 0, "任务数量必须大于0"
assert all(task['cpu_requirement'] > 0 for task in tasks), "CPU需求必须大于0"
```

### 4. 文档和注释

#### 代码注释
```python
def calculate_execution_time(task, node):
    """
    计算任务在指定节点上的执行时间
    
    Args:
        task (dict): 任务信息，包含资源需求
        node (dict): 节点信息，包含资源容量
    
    Returns:
        float: 预计执行时间(秒)
    
    Raises:
        ValueError: 当任务需求超过节点容量时
    """
    # 检查兼容性
    if not is_compatible(task, node):
        raise ValueError("任务与节点不兼容")
    
    # 计算执行时间
    base_time = task['base_runtime']
    cpu_factor = calculate_cpu_factor(task, node)
    memory_factor = calculate_memory_factor(task, node)
    
    return base_time * cpu_factor * memory_factor
```

#### 文档字符串
```python
class WorkflowDataGenerator:
    """
    工作流数据生成器
    
    基于真实工作流特征生成任务和节点数据，支持多种工作流类型
    包括Montage、Brain、SIPHT、Epigenomics和CyberShake。
    
    Attributes:
        workflow_types (list): 支持的工作流类型列表
        task_profiles (dict): 任务性能特征配置
        node_profiles (dict): 节点性能特征配置
    
    Example:
        >>> generator = WorkflowDataGenerator()
        >>> data = generator.generate_workflow_data('montage', 25, 8)
        >>> generator.save_workflow_data(data, 'montage.json')
    """
```

## 📞 获取帮助

### 1. 查看帮助信息
```bash
# 查看命令行帮助
python main_workflow_generator.py --help

# 查看模块帮助
python -c "import workflow_data_generator; help(workflow_data_generator)"
```

### 2. 运行测试
```bash
# 运行所有测试
python test_data_generator.py

# 运行特定测试
python -m pytest test_data_generator.py::test_workflow_generation
```

### 3. 查看示例
```bash
# 运行使用示例
python example_usage.py

# 查看示例代码
cat example_usage.py
```

### 4. 联系支持
- **GitHub Issues**: 提交问题和建议
- **文档**: 查看README.md和本文档
- **代码**: 查看源代码和注释

---

**注意**: 本指南涵盖了数据生成模块的主要使用方法。如有其他问题，请参考源代码注释或提交GitHub Issue。 