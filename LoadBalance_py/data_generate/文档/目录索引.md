# 负载均衡仿真系统 - 数据生成模块目录索引

## 📁 文件结构概览

```
data_generate/
├── 📄 文档文件
│   ├── README.md                           # 主说明文档
│   ├── 使用指南.md                         # 详细使用指南
│   ├── 项目说明.md                         # 项目背景和架构
│   ├── 数据真实性与可靠性说明.md           # 数据质量保障机制
│   ├── 目录索引.md                         # 本文档
│   └── requirements.txt                    # 依赖包列表
├── 🔧 核心代码
│   ├── main_workflow_generator.py          # 主程序入口
│   ├── main_data_generator.py              # 基础数据生成器
│   ├── data_structures.py                  # 数据结构定义
│   ├── realistic_data_model.py             # 真实数据模型
│   ├── workflow_data_generator.py          # 工作流数据生成器
│   ├── workflow_analyzer.py                # 工作流分析器
│   ├── workflow_visualizer.py              # 工作流可视化器
│   └── data_quality_validator.py           # 数据质量验证器
├── 📚 示例和测试
│   ├── example_usage.py                    # 使用示例
│   └── test_data_generator.py              # 测试程序
└── 📊 输出文件 (运行时生成)
    ├── *_workflows.json                    # 工作流数据文件
    ├── *_analysis_report.txt               # 分析报告
    ├── *_quality_report.txt                # 质量报告
    └── *.png                               # 可视化图表
```

## 📄 文档文件说明

### 1. README.md
- **用途**: 项目概述和快速开始指南
- **内容**: 
  - 项目简介和主要功能
  - 快速开始步骤
  - 支持的工作流类型
  - 基本使用方法
  - 输出文件说明
- **适用人群**: 所有用户
- **阅读时间**: 10-15分钟

### 2. 使用指南.md
- **用途**: 详细的使用方法和示例
- **内容**:
  - 环境配置和安装
  - 基本使用和高级功能
  - 数据质量验证
  - 可视化分析
  - 故障排除和最佳实践
- **适用人群**: 需要深入了解功能的用户
- **阅读时间**: 30-45分钟

### 3. 项目说明.md
- **用途**: 项目背景、架构和应用场景
- **内容**:
  - 项目背景和目标
  - 系统架构设计
  - 应用场景分析
  - 技术特性说明
  - 质量保证机制
- **适用人群**: 研究人员、开发者
- **阅读时间**: 20-30分钟

### 4. 数据真实性与可靠性说明.md
- **用途**: 数据质量保障机制详细说明
- **内容**:
  - 数据真实性保障
  - 数据可靠性保障
  - 质量评估指标
  - 改进策略
  - 验证流程
- **适用人群**: 对数据质量有要求的用户
- **阅读时间**: 25-35分钟

### 5. 目录索引.md
- **用途**: 快速查找文件和功能
- **内容**:
  - 文件结构概览
  - 各文件功能说明
  - 快速导航指南
  - 常见问题解答
- **适用人群**: 所有用户
- **阅读时间**: 5-10分钟

## 🔧 核心代码文件说明

### 1. main_workflow_generator.py
- **功能**: 主程序入口，整合所有功能
- **主要类**: `WorkflowDataManager`
- **主要方法**:
  - `generate_all_workflows()`: 生成所有工作流数据
  - `analyze_all_workflows()`: 分析所有工作流数据
  - `visualize_all_workflows()`: 可视化所有工作流数据
  - `validate_all_workflows()`: 验证所有工作流数据
- **使用方式**: 命令行调用

### 2. data_structures.py
- **功能**: 定义核心数据结构
- **主要类**:
  - `Task`: 任务结构
  - `Node`: 节点结构
  - `TaskGenerationParams`: 任务生成参数
  - `NodeGenerationParams`: 节点生成参数
- **使用方式**: 被其他模块导入使用

### 3. realistic_data_model.py
- **功能**: 真实数据模型定义
- **主要类**:
  - `RealisticTaskProfile`: 真实任务性能特征
  - `RealisticNodeProfile`: 真实节点性能特征
- **使用方式**: 被数据生成器使用

### 4. workflow_data_generator.py
- **功能**: 工作流数据生成器
- **主要类**: `WorkflowDataGenerator`
- **主要方法**:
  - `generate_workflow_data()`: 生成工作流数据
  - `save_workflow_data()`: 保存工作流数据
  - `load_workflow_data()`: 加载工作流数据
- **使用方式**: 编程接口调用

### 5. workflow_analyzer.py
- **功能**: 工作流分析器
- **主要类**: `WorkflowAnalyzer`
- **主要方法**:
  - `analyze_workflow_characteristics()`: 分析工作流特征
  - `analyze_resource_distribution()`: 分析资源分布
  - `generate_analysis_report()`: 生成分析报告
- **使用方式**: 编程接口调用

### 6. workflow_visualizer.py
- **功能**: 工作流可视化器
- **主要类**: `WorkflowVisualizer`
- **主要方法**:
  - `create_workflow_diagram()`: 创建工作流图
  - `create_resource_requirements_plot()`: 创建资源需求图
  - `create_execution_time_comparison()`: 创建执行时间对比图
- **使用方式**: 编程接口调用

### 7. data_quality_validator.py
- **功能**: 数据质量验证器
- **主要类**: `DataQualityValidator`
- **主要方法**:
  - `validate_workflow_data()`: 验证工作流数据
  - `generate_quality_report()`: 生成质量报告
  - `plot_quality_analysis()`: 绘制质量分析图
- **使用方式**: 编程接口调用

## 📚 示例和测试文件

### 1. example_usage.py
- **功能**: 提供使用示例
- **包含示例**:
  - 基本使用示例
  - 自定义参数示例
  - 大规模数据生成示例
  - 数据分析示例
- **使用方式**: 直接运行或参考代码

### 2. test_data_generator.py
- **功能**: 测试程序
- **测试内容**:
  - 数据生成功能测试
  - 数据质量验证测试
  - 分析功能测试
  - 可视化功能测试
- **使用方式**: 运行测试验证功能

## 🚀 快速导航指南

### 新用户入门
1. 阅读 `README.md` 了解项目概况
2. 查看 `使用指南.md` 学习基本使用方法
3. 运行 `example_usage.py` 体验功能
4. 使用 `main_workflow_generator.py` 生成数据

### 研究人员使用
1. 阅读 `项目说明.md` 了解技术架构
2. 查看 `数据真实性与可靠性说明.md` 了解数据质量
3. 使用 `workflow_data_generator.py` 生成研究数据
4. 使用 `workflow_analyzer.py` 分析数据特征

### 开发者贡献
1. 阅读 `项目说明.md` 了解系统架构
2. 查看 `使用指南.md` 了解开发环境配置
3. 运行 `test_data_generator.py` 验证功能
4. 参考 `example_usage.py` 了解API使用

### 数据质量关注者
1. 阅读 `数据真实性与可靠性说明.md` 了解质量保障机制
2. 使用 `data_quality_validator.py` 验证数据质量
3. 查看质量报告和可视化结果
4. 根据质量指标调整参数

## 📊 输出文件说明

### 数据文件 (*.json)
- **montage_workflows.json**: Montage工作流数据
- **brain_workflows.json**: Brain工作流数据
- **sipht_workflows.json**: SIPHT工作流数据
- **epigenomics_workflows.json**: Epigenomics工作流数据
- **cybershake_workflows.json**: CyberShake工作流数据

### 报告文件 (*.txt)
- **montage_analysis_report.txt**: Montage工作流分析报告
- **montage_quality_report.txt**: Montage工作流质量报告
- **workflow_comparison_report.txt**: 工作流对比分析报告

### 可视化文件 (*.png)
- **montage_workflow_diagram.png**: Montage工作流任务流程图
- **montage_resource_requirements.png**: Montage资源需求分布图
- **montage_execution_time_comparison.png**: Montage执行时间对比图
- **montage_characteristics.png**: Montage工作流特征分析图
- **montage_quality_analysis.png**: Montage数据质量分析图

## ❓ 常见问题解答

### Q1: 如何快速开始使用？
**A**: 阅读 `README.md` 文件，按照快速开始步骤操作。

### Q2: 如何生成特定工作流的数据？
**A**: 使用命令 `python main_workflow_generator.py --workflow montage --tasks 50 --nodes 16`

### Q3: 如何验证生成数据的质量？
**A**: 使用 `data_quality_validator.py` 或运行 `python main_workflow_generator.py --mode validate`

### Q4: 如何自定义数据生成参数？
**A**: 参考 `example_usage.py` 中的自定义参数示例。

### Q5: 如何分析工作流特征？
**A**: 使用 `workflow_analyzer.py` 或运行 `python main_workflow_generator.py --mode analyze`

### Q6: 如何创建可视化图表？
**A**: 使用 `workflow_visualizer.py` 或运行 `python main_workflow_generator.py --mode visualize`

### Q7: 如何解决导入模块错误？
**A**: 确保已安装所有依赖包：`pip install -r requirements.txt`

### Q8: 如何贡献代码？
**A**: 阅读 `使用指南.md` 中的贡献指南部分。

## 📞 获取帮助

### 文档资源
- **README.md**: 项目概述和快速开始
- **使用指南.md**: 详细使用方法和示例
- **项目说明.md**: 项目背景和架构
- **数据真实性与可靠性说明.md**: 数据质量保障机制

### 代码资源
- **example_usage.py**: 使用示例代码
- **test_data_generator.py**: 测试代码
- 各模块的源代码和注释

### 联系方式
- **GitHub Issues**: 提交问题和建议
- **邮件联系**: 发送邮件至项目维护者
- **项目讨论**: 参与项目讨论和贡献

---

**注意**: 本索引文件帮助您快速找到所需的文件和功能。如有其他问题，请参考相关文档或联系项目维护者。 