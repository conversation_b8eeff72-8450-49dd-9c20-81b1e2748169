"""
负载均衡仿真系统 - 动态性能仿真演示
展示节点性能的连续变化和真实计算机仿真
"""

import time
import threading
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
from data_structures import (
    Node, NodeResource, NodeType, DynamicNodePerformance,
    DynamicPerformanceSimulator
)
from dynamic_simulation import (
    AdvancedPerformanceSimulator, PerformanceModel,
    create_realistic_performance_model, create_stress_test_model,
    SimulationTools
)


def demo_basic_dynamic_performance():
    """基础动态性能演示"""
    print("=== 基础动态性能演示 ===")
    
    # 创建测试节点
    nodes = []
    for i in range(3):
        node = Node(
            id=i + 1,
            time_step=0,
            resource=NodeResource(
                cpu_capacity=20000,
                memory_capacity=20480,
                bandwidth_capacity=15360,
                current_cpu=3000,
                current_memory=4000,
                current_bandwidth=2000
            ),
            node_type=NodeType.GENERAL,
            is_monitoring=True
        )
        nodes.append(node)
    
    # 创建基础仿真器
    simulator = DynamicPerformanceSimulator(nodes, update_interval=2.0)
    
    print("启动基础动态性能仿真...")
    simulator.start_simulation()
    
    try:
        # 运行演示
        for i in range(8):  # 运行8个周期
            time.sleep(2)
            
            print(f"\n--- 第 {i+1} 个周期 ---")
            for node in nodes:
                print(f"节点 {node.id}: "
                      f"CPU={node.dynamic_performance.cpu_performance_factor:.3f}, "
                      f"内存={node.dynamic_performance.memory_performance_factor:.3f}, "
                      f"网络={node.dynamic_performance.network_performance_factor:.3f}, "
                      f"评分={node.get_overall_performance_score():.3f}")
            
            # 显示摘要
            summary = simulator.get_performance_summary()
            print(f"平均CPU因子: {summary['average_cpu_factor']:.3f}")
            print(f"平均内存因子: {summary['average_memory_factor']:.3f}")
            print(f"平均网络因子: {summary['average_network_factor']:.3f}")
            print(f"平均整体评分: {summary['average_overall_score']:.3f}")
    
    except KeyboardInterrupt:
        print("\n用户中断演示")
    finally:
        simulator.stop_simulation()
        print("基础动态性能仿真演示结束")


def demo_advanced_performance_simulation():
    """高级性能仿真演示"""
    print("\n=== 高级性能仿真演示 ===")
    
    # 创建更多测试节点
    nodes = []
    for i in range(5):
        node = Node(
            id=i + 1,
            time_step=0,
            resource=NodeResource(
                cpu_capacity=25000,
                memory_capacity=25600,
                bandwidth_capacity=20480,
                current_cpu=4000,
                current_memory=5000,
                current_bandwidth=3000
            ),
            node_type=NodeType.GENERAL,
            is_monitoring=True
        )
        nodes.append(node)
    
    # 创建真实性能模型
    realistic_model = create_realistic_performance_model()
    
    # 创建高级仿真器
    simulator = AdvancedPerformanceSimulator(nodes, realistic_model)
    
    print("启动高级性能仿真...")
    print(f"CPU模式: {realistic_model.cpu_pattern}")
    print(f"内存模式: {realistic_model.memory_pattern}")
    print(f"网络模式: {realistic_model.network_pattern}")
    
    simulator.start_simulation(update_interval=1.5)
    
    try:
        # 运行演示
        for i in range(12):  # 运行12个周期
            time.sleep(1.5)
            
            print(f"\n--- 第 {i+1} 个周期 ---")
            
            # 显示前3个节点的详细信息
            for j, node in enumerate(nodes[:3]):
                print(f"节点 {node.id}: "
                      f"CPU={node.dynamic_performance.cpu_performance_factor:.3f}, "
                      f"内存={node.dynamic_performance.memory_performance_factor:.3f}, "
                      f"网络={node.dynamic_performance.network_performance_factor:.3f}")
            
            # 显示摘要
            summary = simulator.get_detailed_summary()
            print(f"平均CPU因子: {summary['current_average_cpu_factor']:.3f}")
            print(f"平均内存因子: {summary['current_average_memory_factor']:.3f}")
            print(f"平均网络因子: {summary['current_average_network_factor']:.3f}")
            print(f"平均整体评分: {summary['current_average_overall_score']:.3f}")
            print(f"仿真持续时间: {summary['simulation_duration']:.1f}秒")
    
    except KeyboardInterrupt:
        print("\n用户中断演示")
    finally:
        simulator.stop_simulation()
        
        # 生成性能报告
        report_file = simulator.generate_performance_report()
        print(f"性能报告已生成: {report_file}")
        
        print("高级性能仿真演示结束")


def demo_stress_test_simulation():
    """压力测试仿真演示"""
    print("\n=== 压力测试仿真演示 ===")
    
    # 创建测试节点
    nodes = []
    for i in range(4):
        node = Node(
            id=i + 1,
            time_step=0,
            resource=NodeResource(
                cpu_capacity=30000,
                memory_capacity=30720,
                bandwidth_capacity=25600,
                current_cpu=6000,
                current_memory=8000,
                current_bandwidth=5000
            ),
            node_type=NodeType.GENERAL,
            is_monitoring=True
        )
        nodes.append(node)
    
    # 创建压力测试模型
    stress_model = create_stress_test_model()
    
    # 创建高级仿真器
    simulator = AdvancedPerformanceSimulator(nodes, stress_model)
    
    print("启动压力测试仿真...")
    print(f"CPU模式: {stress_model.cpu_pattern} (指数衰减)")
    print(f"内存模式: {stress_model.memory_pattern} (逻辑增长)")
    print(f"网络模式: {stress_model.network_pattern} (随机游走)")
    print(f"CPU波动幅度: {stress_model.cpu_volatility:.3f}")
    print(f"内存波动幅度: {stress_model.memory_volatility:.3f}")
    print(f"网络波动幅度: {stress_model.network_volatility:.3f}")
    
    simulator.start_simulation(update_interval=1.0)
    
    try:
        # 运行演示
        for i in range(15):  # 运行15个周期
            time.sleep(1.0)
            
            print(f"\n--- 第 {i+1} 个周期 ---")
            
            # 显示所有节点的性能
            for node in nodes:
                print(f"节点 {node.id}: "
                      f"CPU={node.dynamic_performance.cpu_performance_factor:.3f}, "
                      f"内存={node.dynamic_performance.memory_performance_factor:.3f}, "
                      f"网络={node.dynamic_performance.network_performance_factor:.3f}, "
                      f"评分={node.get_overall_performance_score():.3f}")
            
            # 显示摘要
            summary = simulator.get_detailed_summary()
            print(f"平均CPU因子: {summary['current_average_cpu_factor']:.3f}")
            print(f"平均内存因子: {summary['current_average_memory_factor']:.3f}")
            print(f"平均网络因子: {summary['current_average_network_factor']:.3f}")
            print(f"平均整体评分: {summary['current_average_overall_score']:.3f}")
            
            # 检查性能下降警告
            if summary['current_average_overall_score'] < 0.6:
                print("⚠️  警告: 整体性能评分较低!")
            if summary['current_average_cpu_factor'] < 0.7:
                print("⚠️  警告: CPU性能因子较低!")
    
    except KeyboardInterrupt:
        print("\n用户中断演示")
    finally:
        simulator.stop_simulation()
        
        # 生成性能报告和趋势图
        report_file = simulator.generate_performance_report("stress_test_report.txt")
        simulator.plot_performance_trends(save_plot=True, filename="stress_test_trends.png")
        
        print(f"压力测试报告已生成: {report_file}")
        print("压力测试仿真演示结束")


def demo_system_performance_monitoring():
    """系统性能监控演示"""
    print("\n=== 系统性能监控演示 ===")
    
    print("获取当前系统性能信息...")
    
    # 获取系统性能
    system_perf = SimulationTools.get_system_performance()
    
    if system_perf:
        print("当前系统性能:")
        print(f"  CPU使用率: {system_perf.get('cpu_usage', 0):.1f}%")
        print(f"  内存使用率: {system_perf.get('memory_usage', 0):.1f}%")
        print(f"  可用内存: {system_perf.get('memory_available', 0):.1f} GB")
        print(f"  磁盘使用率: {system_perf.get('disk_usage', 0):.1f}%")
        print(f"  可用磁盘空间: {system_perf.get('disk_free', 0):.1f} GB")
        print(f"  网络发送字节: {system_perf.get('network_bytes_sent', 0):,}")
        print(f"  网络接收字节: {system_perf.get('network_bytes_recv', 0):,}")
    else:
        print("无法获取系统性能信息")
    
    # 创建性能数据集
    print("\n创建模拟性能数据集...")
    df = SimulationTools.create_performance_dataset(n_samples=100)
    
    print(f"数据集大小: {len(df)} 条记录")
    print(f"时间范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
    
    # 分析性能趋势
    print("\n分析性能趋势...")
    analysis = SimulationTools.analyze_performance_trends(df)
    
    print("性能统计:")
    print(f"  CPU因子 - 均值: {analysis['cpu_stats']['mean']:.3f}, 标准差: {analysis['cpu_stats']['std']:.3f}")
    print(f"  内存因子 - 均值: {analysis['memory_stats']['mean']:.3f}, 标准差: {analysis['memory_stats']['std']:.3f}")
    print(f"  网络因子 - 均值: {analysis['network_stats']['mean']:.3f}, 标准差: {analysis['network_stats']['std']:.3f}")
    
    print("\n趋势分析:")
    print(f"  CPU趋势: {analysis['trends']['cpu_trend']:.6f}")
    print(f"  内存趋势: {analysis['trends']['memory_trend']:.6f}")
    print(f"  网络趋势: {analysis['trends']['network_trend']:.6f}")
    
    print("\n相关性分析:")
    print(f"  CPU-内存相关性: {analysis['correlations']['cpu_memory']:.3f}")
    print(f"  CPU-网络相关性: {analysis['correlations']['cpu_network']:.3f}")
    print(f"  内存-网络相关性: {analysis['correlations']['memory_network']:.3f}")
    
    print("系统性能监控演示结束")


def demo_performance_visualization():
    """性能可视化演示"""
    print("\n=== 性能可视化演示 ===")
    
    # 创建测试节点
    nodes = []
    for i in range(6):
        node = Node(
            id=i + 1,
            time_step=0,
            resource=NodeResource(
                cpu_capacity=20000,
                memory_capacity=20480,
                bandwidth_capacity=15360,
                current_cpu=3000,
                current_memory=4000,
                current_bandwidth=2000
            ),
            node_type=NodeType.GENERAL,
            is_monitoring=True
        )
        nodes.append(node)
    
    # 创建性能模型
    model = PerformanceModel(
        cpu_pattern="sinusoidal",
        memory_pattern="random_walk",
        network_pattern="step_function",
        cpu_volatility=0.08,
        memory_volatility=0.06,
        network_volatility=0.10,
        cpu_period=60.0,
        memory_period=90.0,
        network_period=45.0
    )
    
    # 创建高级仿真器
    simulator = AdvancedPerformanceSimulator(nodes, model)
    
    print("启动可视化演示仿真...")
    print(f"CPU模式: {model.cpu_pattern} (正弦波)")
    print(f"内存模式: {model.memory_pattern} (随机游走)")
    print(f"网络模式: {model.network_pattern} (阶跃函数)")
    
    simulator.start_simulation(update_interval=1.0)
    
    try:
        # 运行仿真一段时间
        print("运行仿真30秒以收集数据...")
        time.sleep(30)
        
        # 生成可视化
        print("生成性能趋势图...")
        simulator.plot_performance_trends(save_plot=True, filename="demo_performance_trends.png")
        
        # 生成报告
        report_file = simulator.generate_performance_report("demo_performance_report.txt")
        print(f"演示报告已生成: {report_file}")
        
    except KeyboardInterrupt:
        print("\n用户中断演示")
    finally:
        simulator.stop_simulation()
        print("性能可视化演示结束")


def main():
    """主演示函数"""
    print("负载均衡仿真系统 - 动态性能仿真演示")
    print("=" * 50)
    
    try:
        # 运行各种演示
        demo_basic_dynamic_performance()
        demo_advanced_performance_simulation()
        demo_stress_test_simulation()
        demo_system_performance_monitoring()
        demo_performance_visualization()
        
        print("\n" + "=" * 50)
        print("所有演示完成！")
        print("生成的文件:")
        print("- stress_test_report.txt: 压力测试报告")
        print("- stress_test_trends.png: 压力测试趋势图")
        print("- demo_performance_trends.png: 演示性能趋势图")
        print("- demo_performance_report.txt: 演示性能报告")
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 