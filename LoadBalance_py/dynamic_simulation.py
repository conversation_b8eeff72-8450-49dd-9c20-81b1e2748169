"""
负载均衡仿真系统 - 动态性能仿真模块
集成现有的Python仿真工具来模拟真实计算机性能变化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.signal import savgol_filter
import psutil
import time
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
import random

from data_structures import Node, DynamicNodePerformance, NodeType


@dataclass
class PerformanceModel:
    """性能模型配置"""
    # 基础性能参数
    base_cpu_performance: float = 1.0
    base_memory_performance: float = 1.0
    base_network_performance: float = 1.0
    
    # 性能变化模式
    cpu_pattern: str = "random_walk"  # random_walk, sinusoidal, step_function
    memory_pattern: str = "random_walk"
    network_pattern: str = "random_walk"
    
    # 变化幅度
    cpu_volatility: float = 0.1
    memory_volatility: float = 0.1
    network_volatility: float = 0.1
    
    # 趋势参数
    cpu_trend: float = 0.0
    memory_trend: float = 0.0
    network_trend: float = 0.0
    
    # 周期性参数（用于正弦波模式）
    cpu_period: float = 60.0  # 秒
    memory_period: float = 60.0
    network_period: float = 60.0
    
    # 噪声参数
    noise_level: float = 0.02
    noise_type: str = "gaussian"  # gaussian, uniform, poisson


class AdvancedPerformanceSimulator:
    """高级性能仿真器 - 集成多种仿真工具"""
    
    def __init__(self, nodes: List[Node], model: PerformanceModel = None):
        self.nodes = nodes
        self.model = model or PerformanceModel()
        self.is_running = False
        self.simulation_thread = None
        self.start_time = datetime.now()
        self.simulation_data = []
        
        # 性能历史记录
        self.performance_history = {
            'timestamps': [],
            'node_performances': {}
        }
        
        # 统计信息
        self.stats = {
            'total_updates': 0,
            'average_cpu_factor': [],
            'average_memory_factor': [],
            'average_network_factor': [],
            'performance_variance': []
        }
    
    def start_simulation(self, update_interval: float = 1.0):
        """启动高级性能仿真"""
        if not self.is_running:
            self.is_running = True
            self.simulation_thread = threading.Thread(
                target=self._advanced_simulation_loop, 
                args=(update_interval,)
            )
            self.simulation_thread.daemon = True
            self.simulation_thread.start()
            print(f"高级性能仿真已启动，更新间隔: {update_interval}秒")
    
    def stop_simulation(self):
        """停止仿真"""
        self.is_running = False
        if self.simulation_thread:
            self.simulation_thread.join()
        print("高级性能仿真已停止")
    
    def _advanced_simulation_loop(self, update_interval: float):
        """高级仿真主循环"""
        while self.is_running:
            try:
                current_time = datetime.now()
                simulation_time = (current_time - self.start_time).total_seconds()
                
                # 更新所有节点的性能
                self._update_all_nodes_advanced_performance(simulation_time)
                
                # 记录性能数据
                self._record_performance_data(current_time)
                
                # 更新统计信息
                self._update_statistics()
                
                time.sleep(update_interval)
                
            except Exception as e:
                print(f"高级性能仿真出错: {e}")
    
    def _update_all_nodes_advanced_performance(self, simulation_time: float):
        """使用高级模型更新所有节点的性能"""
        for node in self.nodes:
            self._update_node_advanced_performance(node, simulation_time)
    
    def _update_node_advanced_performance(self, node: Node, simulation_time: float):
        """使用高级模型更新单个节点的性能"""
        dp = node.dynamic_performance
        
        # 根据模式更新CPU性能
        dp.cpu_performance_factor = self._calculate_performance_factor(
            simulation_time, self.model.cpu_pattern, self.model.base_cpu_performance,
            self.model.cpu_volatility, self.model.cpu_trend, self.model.cpu_period,
            self.model.noise_level, self.model.noise_type
        )
        
        # 根据模式更新内存性能
        dp.memory_performance_factor = self._calculate_performance_factor(
            simulation_time, self.model.memory_pattern, self.model.base_memory_performance,
            self.model.memory_volatility, self.model.memory_trend, self.model.memory_period,
            self.model.noise_level, self.model.noise_type
        )
        
        # 根据模式更新网络性能
        dp.network_performance_factor = self._calculate_performance_factor(
            simulation_time, self.model.network_pattern, self.model.base_network_performance,
            self.model.network_volatility, self.model.network_trend, self.model.network_period,
            self.model.noise_level, self.model.noise_type
        )
        
        # 限制性能因子范围
        dp.cpu_performance_factor = np.clip(dp.cpu_performance_factor, 0.5, 1.5)
        dp.memory_performance_factor = np.clip(dp.memory_performance_factor, 0.5, 1.5)
        dp.network_performance_factor = np.clip(dp.network_performance_factor, 0.5, 1.5)
        
        # 记录性能历史
        if node.is_monitoring:
            performance_record = {
                'timestamp': datetime.now(),
                'cpu_factor': dp.cpu_performance_factor,
                'memory_factor': dp.memory_performance_factor,
                'network_factor': dp.network_performance_factor,
                'cpu_utilization': node.get_cpu_utilization(),
                'memory_utilization': node.get_memory_utilization(),
                'network_utilization': node.get_network_utilization(),
                'overall_score': node.get_overall_performance_score()
            }
            node.performance_history.append(performance_record)
            
            # 限制历史记录长度
            if len(node.performance_history) > 1000:
                node.performance_history = node.performance_history[-500:]
    
    def _calculate_performance_factor(self, time: float, pattern: str, base_performance: float,
                                   volatility: float, trend: float, period: float,
                                   noise_level: float, noise_type: str) -> float:
        """计算性能因子"""
        # 基础性能
        performance = base_performance
        
        # 根据模式添加变化
        if pattern == "random_walk":
            # 随机游走模式
            performance += trend * time + volatility * np.random.normal(0, 1)
            
        elif pattern == "sinusoidal":
            # 正弦波模式
            performance += volatility * np.sin(2 * np.pi * time / period)
            
        elif pattern == "step_function":
            # 阶跃函数模式
            step_time = int(time / period)
            performance += volatility * (step_time % 2 - 0.5)
            
        elif pattern == "exponential_decay":
            # 指数衰减模式
            performance *= np.exp(-volatility * time)
            
        elif pattern == "logistic_growth":
            # 逻辑增长模式
            performance = base_performance / (1 + np.exp(-volatility * (time - period/2)))
            
        # 添加噪声
        if noise_type == "gaussian":
            noise = np.random.normal(0, noise_level)
        elif noise_type == "uniform":
            noise = np.random.uniform(-noise_level, noise_level)
        elif noise_type == "poisson":
            noise = np.random.poisson(noise_level * 10) / 10 - noise_level
        else:
            noise = 0
        
        performance += noise
        
        return performance
    
    def _record_performance_data(self, timestamp: datetime):
        """记录性能数据"""
        self.performance_history['timestamps'].append(timestamp)
        
        for node in self.nodes:
            if node.id not in self.performance_history['node_performances']:
                self.performance_history['node_performances'][node.id] = []
            
            node_data = {
                'cpu_factor': node.dynamic_performance.cpu_performance_factor,
                'memory_factor': node.dynamic_performance.memory_performance_factor,
                'network_factor': node.dynamic_performance.network_performance_factor,
                'overall_score': node.get_overall_performance_score()
            }
            self.performance_history['node_performances'][node.id].append(node_data)
    
    def _update_statistics(self):
        """更新统计信息"""
        cpu_factors = [node.dynamic_performance.cpu_performance_factor for node in self.nodes]
        memory_factors = [node.dynamic_performance.memory_performance_factor for node in self.nodes]
        network_factors = [node.dynamic_performance.network_performance_factor for node in self.nodes]
        
        self.stats['total_updates'] += 1
        self.stats['average_cpu_factor'].append(np.mean(cpu_factors))
        self.stats['average_memory_factor'].append(np.mean(memory_factors))
        self.stats['average_network_factor'].append(np.mean(network_factors))
        
        # 计算性能方差
        overall_scores = [node.get_overall_performance_score() for node in self.nodes]
        self.stats['performance_variance'].append(np.var(overall_scores))
    
    def get_detailed_summary(self) -> Dict[str, Any]:
        """获取详细摘要"""
        summary = {
            'total_nodes': len(self.nodes),
            'monitoring_nodes': sum(1 for node in self.nodes if node.is_monitoring),
            'simulation_duration': (datetime.now() - self.start_time).total_seconds(),
            'total_updates': self.stats['total_updates'],
            'current_average_cpu_factor': np.mean([node.dynamic_performance.cpu_performance_factor for node in self.nodes]),
            'current_average_memory_factor': np.mean([node.dynamic_performance.memory_performance_factor for node in self.nodes]),
            'current_average_network_factor': np.mean([node.dynamic_performance.network_performance_factor for node in self.nodes]),
            'current_average_overall_score': np.mean([node.get_overall_performance_score() for node in self.nodes]),
            'performance_model': {
                'cpu_pattern': self.model.cpu_pattern,
                'memory_pattern': self.model.memory_pattern,
                'network_pattern': self.model.network_pattern,
                'cpu_volatility': self.model.cpu_volatility,
                'memory_volatility': self.model.memory_volatility,
                'network_volatility': self.model.network_volatility
            }
        }
        
        # 添加统计信息
        if self.stats['average_cpu_factor']:
            summary['stats'] = {
                'cpu_factor_mean': np.mean(self.stats['average_cpu_factor']),
                'cpu_factor_std': np.std(self.stats['average_cpu_factor']),
                'memory_factor_mean': np.mean(self.stats['average_memory_factor']),
                'memory_factor_std': np.std(self.stats['average_memory_factor']),
                'network_factor_mean': np.mean(self.stats['average_network_factor']),
                'network_factor_std': np.std(self.stats['average_network_factor']),
                'performance_variance_mean': np.mean(self.stats['performance_variance'])
            }
        
        return summary
    
    def generate_performance_report(self, filename: str = None):
        """生成性能报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.txt"
        
        summary = self.get_detailed_summary()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== 负载均衡仿真系统 - 性能报告 ===\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"仿真持续时间: {summary['simulation_duration']:.2f} 秒\n")
            f.write(f"总更新次数: {summary['total_updates']}\n")
            f.write(f"节点总数: {summary['total_nodes']}\n")
            f.write(f"监控节点数: {summary['monitoring_nodes']}\n\n")
            
            f.write("当前性能状态:\n")
            f.write(f"  平均CPU性能因子: {summary['current_average_cpu_factor']:.3f}\n")
            f.write(f"  平均内存性能因子: {summary['current_average_memory_factor']:.3f}\n")
            f.write(f"  平均网络性能因子: {summary['current_average_network_factor']:.3f}\n")
            f.write(f"  平均整体评分: {summary['current_average_overall_score']:.3f}\n\n")
            
            f.write("性能模型配置:\n")
            f.write(f"  CPU模式: {summary['performance_model']['cpu_pattern']}\n")
            f.write(f"  内存模式: {summary['performance_model']['memory_pattern']}\n")
            f.write(f"  网络模式: {summary['performance_model']['network_pattern']}\n")
            f.write(f"  CPU波动幅度: {summary['performance_model']['cpu_volatility']:.3f}\n")
            f.write(f"  内存波动幅度: {summary['performance_model']['memory_volatility']:.3f}\n")
            f.write(f"  网络波动幅度: {summary['performance_model']['network_volatility']:.3f}\n\n")
            
            if 'stats' in summary:
                f.write("统计信息:\n")
                f.write(f"  CPU因子均值: {summary['stats']['cpu_factor_mean']:.3f}\n")
                f.write(f"  CPU因子标准差: {summary['stats']['cpu_factor_std']:.3f}\n")
                f.write(f"  内存因子均值: {summary['stats']['memory_factor_mean']:.3f}\n")
                f.write(f"  内存因子标准差: {summary['stats']['memory_factor_std']:.3f}\n")
                f.write(f"  网络因子均值: {summary['stats']['network_factor_mean']:.3f}\n")
                f.write(f"  网络因子标准差: {summary['stats']['network_factor_std']:.3f}\n")
                f.write(f"  性能方差均值: {summary['stats']['performance_variance_mean']:.3f}\n")
        
        print(f"性能报告已生成: {filename}")
        return filename
    
    def plot_performance_trends(self, save_plot: bool = True, filename: str = None):
        """绘制性能趋势图"""
        if not self.performance_history['timestamps']:
            print("没有性能数据可供绘图")
            return
        
        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('节点性能趋势分析', fontsize=16)
        
        # 准备数据
        timestamps = self.performance_history['timestamps']
        time_seconds = [(t - self.start_time).total_seconds() for t in timestamps]
        
        # 1. CPU性能因子趋势
        cpu_factors = []
        for node_id in self.performance_history['node_performances']:
            node_cpu = [data['cpu_factor'] for data in self.performance_history['node_performances'][node_id]]
            cpu_factors.append(node_cpu)
        
        for i, cpu_data in enumerate(cpu_factors):
            if len(cpu_data) == len(time_seconds):
                axes[0, 0].plot(time_seconds, cpu_data, label=f'节点{i+1}', alpha=0.7)
        
        axes[0, 0].set_title('CPU性能因子趋势')
        axes[0, 0].set_xlabel('时间 (秒)')
        axes[0, 0].set_ylabel('CPU性能因子')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 内存性能因子趋势
        memory_factors = []
        for node_id in self.performance_history['node_performances']:
            node_memory = [data['memory_factor'] for data in self.performance_history['node_performances'][node_id]]
            memory_factors.append(node_memory)
        
        for i, memory_data in enumerate(memory_factors):
            if len(memory_data) == len(time_seconds):
                axes[0, 1].plot(time_seconds, memory_data, label=f'节点{i+1}', alpha=0.7)
        
        axes[0, 1].set_title('内存性能因子趋势')
        axes[0, 1].set_xlabel('时间 (秒)')
        axes[0, 1].set_ylabel('内存性能因子')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 网络性能因子趋势
        network_factors = []
        for node_id in self.performance_history['node_performances']:
            node_network = [data['network_factor'] for data in self.performance_history['node_performances'][node_id]]
            network_factors.append(node_network)
        
        for i, network_data in enumerate(network_factors):
            if len(network_data) == len(time_seconds):
                axes[1, 0].plot(time_seconds, network_data, label=f'节点{i+1}', alpha=0.7)
        
        axes[1, 0].set_title('网络性能因子趋势')
        axes[1, 0].set_xlabel('时间 (秒)')
        axes[1, 0].set_ylabel('网络性能因子')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 整体评分趋势
        overall_scores = []
        for node_id in self.performance_history['node_performances']:
            node_scores = [data['overall_score'] for data in self.performance_history['node_performances'][node_id]]
            overall_scores.append(node_scores)
        
        for i, score_data in enumerate(overall_scores):
            if len(score_data) == len(time_seconds):
                axes[1, 1].plot(time_seconds, score_data, label=f'节点{i+1}', alpha=0.7)
        
        axes[1, 1].set_title('整体性能评分趋势')
        axes[1, 1].set_xlabel('时间 (秒)')
        axes[1, 1].set_ylabel('整体评分')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plot:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"performance_trends_{timestamp}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"性能趋势图已保存: {filename}")
        
        plt.show()


def create_realistic_performance_model() -> PerformanceModel:
    """创建真实的性能模型"""
    return PerformanceModel(
        base_cpu_performance=1.0,
        base_memory_performance=1.0,
        base_network_performance=1.0,
        cpu_pattern="random_walk",
        memory_pattern="sinusoidal",
        network_pattern="step_function",
        cpu_volatility=0.05,
        memory_volatility=0.03,
        network_volatility=0.08,
        cpu_trend=-0.001,  # 轻微下降趋势
        memory_trend=0.0,
        network_trend=0.0,
        cpu_period=120.0,  # 2分钟周期
        memory_period=60.0,  # 1分钟周期
        network_period=30.0,  # 30秒周期
        noise_level=0.01,
        noise_type="gaussian"
    )


def create_stress_test_model() -> PerformanceModel:
    """创建压力测试模型"""
    return PerformanceModel(
        base_cpu_performance=1.0,
        base_memory_performance=1.0,
        base_network_performance=1.0,
        cpu_pattern="exponential_decay",
        memory_pattern="logistic_growth",
        network_pattern="random_walk",
        cpu_volatility=0.15,
        memory_volatility=0.12,
        network_volatility=0.18,
        cpu_trend=-0.005,  # 快速下降
        memory_trend=0.003,  # 缓慢增长
        network_trend=-0.002,  # 轻微下降
        noise_level=0.03,
        noise_type="gaussian"
    )


# 集成现有的Python仿真工具
class SimulationTools:
    """仿真工具集成类"""
    
    @staticmethod
    def get_system_performance() -> Dict[str, float]:
        """获取当前系统性能（使用psutil）"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available': memory.available / (1024**3),  # GB
                'disk_usage': disk.percent,
                'disk_free': disk.free / (1024**3),  # GB
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv
            }
        except Exception as e:
            print(f"获取系统性能失败: {e}")
            return {}
    
    @staticmethod
    def create_performance_dataset(n_samples: int = 1000) -> pd.DataFrame:
        """创建性能数据集"""
        data = []
        
        for i in range(n_samples):
            # 模拟时间序列
            timestamp = datetime.now() + timedelta(seconds=i)
            
            # 模拟性能数据
            cpu_factor = 1.0 + 0.1 * np.sin(i * 0.1) + 0.05 * np.random.normal(0, 1)
            memory_factor = 1.0 + 0.08 * np.cos(i * 0.15) + 0.03 * np.random.normal(0, 1)
            network_factor = 1.0 + 0.12 * np.sin(i * 0.2) + 0.06 * np.random.normal(0, 1)
            
            # 添加趋势
            cpu_factor += 0.001 * i  # 轻微上升趋势
            memory_factor -= 0.0005 * i  # 轻微下降趋势
            network_factor += 0.002 * i  # 上升趋势
            
            data.append({
                'timestamp': timestamp,
                'cpu_factor': np.clip(cpu_factor, 0.5, 1.5),
                'memory_factor': np.clip(memory_factor, 0.5, 1.5),
                'network_factor': np.clip(network_factor, 0.5, 1.5),
                'overall_score': (cpu_factor + memory_factor + network_factor) / 3
            })
        
        return pd.DataFrame(data)
    
    @staticmethod
    def analyze_performance_trends(df: pd.DataFrame) -> Dict[str, Any]:
        """分析性能趋势"""
        analysis = {}
        
        # 基本统计
        analysis['cpu_stats'] = {
            'mean': df['cpu_factor'].mean(),
            'std': df['cpu_factor'].std(),
            'min': df['cpu_factor'].min(),
            'max': df['cpu_factor'].max()
        }
        
        analysis['memory_stats'] = {
            'mean': df['memory_factor'].mean(),
            'std': df['memory_factor'].std(),
            'min': df['memory_factor'].min(),
            'max': df['memory_factor'].max()
        }
        
        analysis['network_stats'] = {
            'mean': df['network_factor'].mean(),
            'std': df['network_factor'].std(),
            'min': df['network_factor'].min(),
            'max': df['network_factor'].max()
        }
        
        # 趋势分析
        analysis['trends'] = {
            'cpu_trend': np.polyfit(range(len(df)), df['cpu_factor'], 1)[0],
            'memory_trend': np.polyfit(range(len(df)), df['memory_factor'], 1)[0],
            'network_trend': np.polyfit(range(len(df)), df['network_factor'], 1)[0]
        }
        
        # 相关性分析
        analysis['correlations'] = {
            'cpu_memory': df['cpu_factor'].corr(df['memory_factor']),
            'cpu_network': df['cpu_factor'].corr(df['network_factor']),
            'memory_network': df['memory_factor'].corr(df['network_factor'])
        }
        
        return analysis 