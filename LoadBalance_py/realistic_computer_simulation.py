"""
负载均衡仿真系统 - 真实计算机性能模拟
模拟真实计算机的各种性能指标和变化模式
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.signal import savgol_filter
import psutil
import time
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
import random
import os
import platform
import subprocess

from data_structures import Node, DynamicNodePerformance, NodeType


@dataclass
class RealisticComputerSpecs:
    """真实计算机规格参数"""
    # CPU规格
    cpu_cores: int = 8                    # CPU核心数
    cpu_frequency: float = 3.6            # CPU频率 (GHz)
    cpu_cache_l1: int = 32                # L1缓存 (KB)
    cpu_cache_l2: int = 256               # L2缓存 (KB)
    cpu_cache_l3: int = 8192              # L3缓存 (KB)
    cpu_architecture: str = "x86_64"      # CPU架构
    
    # 内存规格
    memory_total: int = 16384             # 总内存 (MB)
    memory_type: str = "DDR4"             # 内存类型
    memory_frequency: int = 3200          # 内存频率 (MHz)
    memory_channels: int = 2              # 内存通道数
    
    # 存储规格
    storage_type: str = "SSD"             # 存储类型 (SSD/HDD)
    storage_capacity: int = 512000        # 存储容量 (MB)
    storage_read_speed: int = 3500        # 读取速度 (MB/s)
    storage_write_speed: int = 3000       # 写入速度 (MB/s)
    
    # 网络规格
    network_type: str = "Ethernet"        # 网络类型
    network_speed: int = 1000             # 网络速度 (Mbps)
    network_latency: float = 1.0          # 网络延迟 (ms)
    network_packet_loss: float = 0.001    # 丢包率
    
    # 散热和功耗
    thermal_design_power: int = 95        # 热设计功耗 (W)
    current_temperature: float = 45.0     # 当前温度 (°C)
    max_temperature: float = 85.0         # 最大温度 (°C)
    power_consumption: float = 65.0       # 当前功耗 (W)


@dataclass
class RealisticPerformanceFactors:
    """真实性能影响因素"""
    # CPU性能影响因素
    cpu_thermal_throttling: float = 1.0   # 温度节流因子 (0.5-1.0)
    cpu_power_throttling: float = 1.0     # 功耗节流因子 (0.5-1.0)
    cpu_frequency_scaling: float = 1.0    # 频率缩放因子 (0.5-1.0)
    cpu_cache_miss_rate: float = 0.1      # 缓存未命中率 (0.0-1.0)
    cpu_branch_misprediction: float = 0.05 # 分支预测错误率 (0.0-1.0)
    
    # 内存性能影响因素
    memory_bandwidth_utilization: float = 0.3  # 内存带宽利用率 (0.0-1.0)
    memory_latency_factor: float = 1.0    # 内存延迟因子 (1.0-2.0)
    memory_fragmentation: float = 0.1     # 内存碎片化程度 (0.0-1.0)
    memory_page_faults: float = 0.01      # 页面错误率 (0.0-1.0)
    
    # 存储性能影响因素
    storage_fragmentation: float = 0.2    # 存储碎片化程度 (0.0-1.0)
    storage_wear_leveling: float = 0.8    # SSD磨损均衡因子 (0.5-1.0)
    storage_trim_support: bool = True     # TRIM支持
    storage_over_provisioning: float = 0.1 # 过度配置比例 (0.0-0.3)
    
    # 网络性能影响因素
    network_congestion: float = 0.1       # 网络拥塞程度 (0.0-1.0)
    network_buffer_overflow: float = 0.05 # 缓冲区溢出率 (0.0-1.0)
    network_retransmission: float = 0.02  # 重传率 (0.0-1.0)
    network_bandwidth_fluctuation: float = 0.1 # 带宽波动 (0.0-1.0)
    
    # 系统级影响因素
    os_overhead: float = 0.05             # 操作系统开销 (0.0-1.0)
    background_processes: float = 0.1     # 后台进程影响 (0.0-1.0)
    antivirus_impact: float = 0.03        # 杀毒软件影响 (0.0-1.0)
    system_updates: float = 0.0           # 系统更新影响 (0.0-1.0)


class RealisticComputerSimulator:
    """真实计算机性能仿真器"""
    
    def __init__(self, nodes: List[Node], specs: RealisticComputerSpecs = None):
        self.nodes = nodes
        self.specs = specs or RealisticComputerSpecs()
        self.performance_factors = RealisticPerformanceFactors()
        self.is_running = False
        self.simulation_thread = None
        self.start_time = datetime.now()
        
        # 性能历史记录
        self.performance_history = {
            'timestamps': [],
            'node_performances': {},
            'system_metrics': []
        }
        
        # 真实系统监控
        self.real_system_monitor = RealSystemMonitor()
        
        # 性能模型参数
        self.thermal_model = ThermalModel(self.specs)
        self.power_model = PowerModel(self.specs)
        self.network_model = NetworkModel(self.specs)
        
    def start_simulation(self, update_interval: float = 1.0):
        """启动真实计算机性能仿真"""
        if not self.is_running:
            self.is_running = True
            self.simulation_thread = threading.Thread(
                target=self._realistic_simulation_loop, 
                args=(update_interval,)
            )
            self.simulation_thread.daemon = True
            self.simulation_thread.start()
            print(f"真实计算机性能仿真已启动，更新间隔: {update_interval}秒")
    
    def stop_simulation(self):
        """停止仿真"""
        self.is_running = False
        if self.simulation_thread:
            self.simulation_thread.join()
        print("真实计算机性能仿真已停止")
    
    def _realistic_simulation_loop(self, update_interval: float):
        """真实仿真主循环"""
        while self.is_running:
            try:
                current_time = datetime.now()
                simulation_time = (current_time - self.start_time).total_seconds()
                
                # 更新真实系统指标
                real_metrics = self.real_system_monitor.get_current_metrics()
                
                # 更新性能影响因素
                self._update_performance_factors(simulation_time, real_metrics)
                
                # 更新所有节点的性能
                self._update_all_nodes_realistic_performance(simulation_time, real_metrics)
                
                # 记录性能数据
                self._record_realistic_performance_data(current_time, real_metrics)
                
                time.sleep(update_interval)
                
            except Exception as e:
                print(f"真实计算机性能仿真出错: {e}")
    
    def _update_performance_factors(self, simulation_time: float, real_metrics: Dict[str, float]):
        """更新性能影响因素"""
        pf = self.performance_factors
        
        # 基于真实系统指标更新性能因子
        if 'cpu_usage' in real_metrics:
            # CPU使用率影响温度
            cpu_usage = real_metrics['cpu_usage'] / 100.0
            self.specs.current_temperature = self.thermal_model.update_temperature(
                cpu_usage, simulation_time
            )
            
            # 温度影响CPU性能
            if self.specs.current_temperature > 70:
                pf.cpu_thermal_throttling = max(0.5, 1.0 - (self.specs.current_temperature - 70) * 0.02)
            else:
                pf.cpu_thermal_throttling = 1.0
        
        # 功耗影响
        if 'memory_usage' in real_metrics:
            memory_usage = real_metrics['memory_usage'] / 100.0
            self.specs.power_consumption = self.power_model.update_power_consumption(
                cpu_usage, memory_usage, simulation_time
            )
            
            # 功耗节流
            if self.specs.power_consumption > self.specs.thermal_design_power * 0.9:
                pf.cpu_power_throttling = 0.8
            else:
                pf.cpu_power_throttling = 1.0
        
        # 网络拥塞模拟
        if 'network_io' in real_metrics:
            network_usage = real_metrics['network_io'] / (self.specs.network_speed * 1000000)  # 转换为比例
            pf.network_congestion = min(1.0, network_usage * 2.0)
            
            # 网络拥塞影响丢包率
            if pf.network_congestion > 0.8:
                pf.network_packet_loss = 0.01 + (pf.network_congestion - 0.8) * 0.05
            else:
                pf.network_packet_loss = 0.001
        
        # 存储性能影响
        if 'disk_usage' in real_metrics:
            disk_usage = real_metrics['disk_usage'] / 100.0
            pf.storage_fragmentation = min(1.0, disk_usage * 0.5)
            
            # SSD磨损
            if self.specs.storage_type == "SSD":
                pf.storage_wear_leveling = max(0.5, 1.0 - disk_usage * 0.3)
    
    def _update_all_nodes_realistic_performance(self, simulation_time: float, real_metrics: Dict[str, float]):
        """使用真实模型更新所有节点的性能"""
        for node in self.nodes:
            self._update_node_realistic_performance(node, simulation_time, real_metrics)
    
    def _update_node_realistic_performance(self, node: Node, simulation_time: float, real_metrics: Dict[str, float]):
        """使用真实模型更新单个节点的性能"""
        dp = node.dynamic_performance
        pf = self.performance_factors
        
        # 计算真实CPU性能因子
        cpu_performance = self._calculate_realistic_cpu_performance(simulation_time, real_metrics)
        dp.cpu_performance_factor = cpu_performance
        
        # 计算真实内存性能因子
        memory_performance = self._calculate_realistic_memory_performance(simulation_time, real_metrics)
        dp.memory_performance_factor = memory_performance
        
        # 计算真实网络性能因子
        network_performance = self._calculate_realistic_network_performance(simulation_time, real_metrics)
        dp.network_performance_factor = network_performance
        
        # 限制性能因子范围
        dp.cpu_performance_factor = np.clip(dp.cpu_performance_factor, 0.3, 1.2)
        dp.memory_performance_factor = np.clip(dp.memory_performance_factor, 0.4, 1.1)
        dp.network_performance_factor = np.clip(dp.network_performance_factor, 0.2, 1.3)
        
        # 记录性能历史
        if node.is_monitoring:
            performance_record = {
                'timestamp': datetime.now(),
                'cpu_factor': dp.cpu_performance_factor,
                'memory_factor': dp.memory_performance_factor,
                'network_factor': dp.network_performance_factor,
                'cpu_utilization': node.get_cpu_utilization(),
                'memory_utilization': node.get_memory_utilization(),
                'network_utilization': node.get_network_utilization(),
                'overall_score': node.get_overall_performance_score(),
                'temperature': self.specs.current_temperature,
                'power_consumption': self.specs.power_consumption,
                'real_cpu_usage': real_metrics.get('cpu_usage', 0),
                'real_memory_usage': real_metrics.get('memory_usage', 0)
            }
            node.performance_history.append(performance_record)
            
            # 限制历史记录长度
            if len(node.performance_history) > 1000:
                node.performance_history = node.performance_history[-500:]
    
    def _calculate_realistic_cpu_performance(self, simulation_time: float, real_metrics: Dict[str, float]) -> float:
        """计算真实CPU性能"""
        pf = self.performance_factors
        base_performance = 1.0
        
        # 温度节流
        thermal_factor = pf.cpu_thermal_throttling
        
        # 功耗节流
        power_factor = pf.cpu_power_throttling
        
        # 频率缩放
        if 'cpu_usage' in real_metrics:
            cpu_usage = real_metrics['cpu_usage'] / 100.0
            if cpu_usage > 0.8:
                pf.cpu_frequency_scaling = 0.9  # 高负载时降频
            elif cpu_usage < 0.2:
                pf.cpu_frequency_scaling = 1.0  # 低负载时全频
            else:
                pf.cpu_frequency_scaling = 0.95
        
        # 缓存性能影响
        cache_factor = 1.0 - pf.cpu_cache_miss_rate * 0.3
        
        # 分支预测影响
        branch_factor = 1.0 - pf.cpu_branch_misprediction * 0.1
        
        # 操作系统开销
        os_factor = 1.0 - pf.os_overhead
        
        # 后台进程影响
        background_factor = 1.0 - pf.background_processes
        
        # 杀毒软件影响
        antivirus_factor = 1.0 - pf.antivirus_impact
        
        # 系统更新影响
        update_factor = 1.0 - pf.system_updates
        
        # 综合计算
        cpu_performance = (base_performance * thermal_factor * power_factor * 
                          pf.cpu_frequency_scaling * cache_factor * branch_factor *
                          os_factor * background_factor * antivirus_factor * update_factor)
        
        # 添加随机波动
        noise = np.random.normal(0, 0.02)
        cpu_performance += noise
        
        return cpu_performance
    
    def _calculate_realistic_memory_performance(self, simulation_time: float, real_metrics: Dict[str, float]) -> float:
        """计算真实内存性能"""
        pf = self.performance_factors
        base_performance = 1.0
        
        # 内存带宽利用率影响
        bandwidth_factor = 1.0 - pf.memory_bandwidth_utilization * 0.2
        
        # 内存延迟影响
        latency_factor = 2.0 - pf.memory_latency_factor
        
        # 内存碎片化影响
        fragmentation_factor = 1.0 - pf.memory_fragmentation * 0.3
        
        # 页面错误影响
        page_fault_factor = 1.0 - pf.memory_page_faults * 0.5
        
        # 内存使用率影响
        if 'memory_usage' in real_metrics:
            memory_usage = real_metrics['memory_usage'] / 100.0
            if memory_usage > 0.9:
                # 内存不足时性能急剧下降
                memory_pressure_factor = 0.5
            elif memory_usage > 0.7:
                # 内存压力较大时性能下降
                memory_pressure_factor = 0.8
            else:
                memory_pressure_factor = 1.0
        else:
            memory_pressure_factor = 1.0
        
        # 综合计算
        memory_performance = (base_performance * bandwidth_factor * latency_factor *
                             fragmentation_factor * page_fault_factor * memory_pressure_factor)
        
        # 添加随机波动
        noise = np.random.normal(0, 0.015)
        memory_performance += noise
        
        return memory_performance
    
    def _calculate_realistic_network_performance(self, simulation_time: float, real_metrics: Dict[str, float]) -> float:
        """计算真实网络性能"""
        pf = self.performance_factors
        base_performance = 1.0
        
        # 网络拥塞影响
        congestion_factor = 1.0 - pf.network_congestion * 0.4
        
        # 缓冲区溢出影响
        buffer_factor = 1.0 - pf.network_buffer_overflow * 0.3
        
        # 重传影响
        retransmission_factor = 1.0 - pf.network_retransmission * 0.2
        
        # 带宽波动影响
        bandwidth_fluctuation = 1.0 - pf.network_bandwidth_fluctuation * 0.1
        
        # 丢包率影响
        packet_loss_factor = 1.0 - pf.network_packet_loss * 10.0
        
        # 网络延迟影响
        latency_factor = 1.0 / (1.0 + pf.network_latency * 0.1)
        
        # 综合计算
        network_performance = (base_performance * congestion_factor * buffer_factor *
                              retransmission_factor * bandwidth_fluctuation * 
                              packet_loss_factor * latency_factor)
        
        # 添加随机波动
        noise = np.random.normal(0, 0.03)
        network_performance += noise
        
        return network_performance
    
    def _record_realistic_performance_data(self, timestamp: datetime, real_metrics: Dict[str, float]):
        """记录真实性能数据"""
        self.performance_history['timestamps'].append(timestamp)
        
        # 记录系统指标
        system_metric = {
            'timestamp': timestamp,
            'temperature': self.specs.current_temperature,
            'power_consumption': self.specs.power_consumption,
            'real_cpu_usage': real_metrics.get('cpu_usage', 0),
            'real_memory_usage': real_metrics.get('memory_usage', 0),
            'real_disk_usage': real_metrics.get('disk_usage', 0),
            'real_network_io': real_metrics.get('network_io', 0)
        }
        self.performance_history['system_metrics'].append(system_metric)
        
        # 记录节点性能
        for node in self.nodes:
            if node.id not in self.performance_history['node_performances']:
                self.performance_history['node_performances'][node.id] = []
            
            node_data = {
                'cpu_factor': node.dynamic_performance.cpu_performance_factor,
                'memory_factor': node.dynamic_performance.memory_performance_factor,
                'network_factor': node.dynamic_performance.network_performance_factor,
                'overall_score': node.get_overall_performance_score()
            }
            self.performance_history['node_performances'][node.id].append(node_data)


class RealSystemMonitor:
    """真实系统监控器"""
    
    def __init__(self):
        self.last_cpu_times = psutil.cpu_times()
        self.last_network_io = psutil.net_io_counters()
        self.last_disk_io = psutil.disk_io_counters()
    
    def get_current_metrics(self) -> Dict[str, float]:
        """获取当前系统指标"""
        try:
            metrics = {}
            
            # CPU使用率
            metrics['cpu_usage'] = psutil.cpu_percent(interval=0.1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            metrics['memory_usage'] = memory.percent
            metrics['memory_available'] = memory.available / (1024**3)  # GB
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            metrics['disk_usage'] = disk.percent
            metrics['disk_free'] = disk.free / (1024**3)  # GB
            
            # 网络IO
            current_network = psutil.net_io_counters()
            metrics['network_io'] = (current_network.bytes_sent + current_network.bytes_recv) / (1024**2)  # MB
            self.last_network_io = current_network
            
            # 磁盘IO
            current_disk = psutil.disk_io_counters()
            metrics['disk_io'] = (current_disk.read_bytes + current_disk.write_bytes) / (1024**2)  # MB
            self.last_disk_io = current_disk
            
            # 进程数量
            metrics['process_count'] = len(psutil.pids())
            
            # 系统负载（Linux）
            if platform.system() == "Linux":
                try:
                    load_avg = os.getloadavg()
                    metrics['load_average_1min'] = load_avg[0]
                    metrics['load_average_5min'] = load_avg[1]
                    metrics['load_average_15min'] = load_avg[2]
                except:
                    pass
            
            return metrics
            
        except Exception as e:
            print(f"获取系统指标失败: {e}")
            return {}


class ThermalModel:
    """温度模型"""
    
    def __init__(self, specs: RealisticComputerSpecs):
        self.specs = specs
        self.ambient_temperature = 25.0  # 环境温度
        self.thermal_resistance = 0.5    # 热阻
        self.thermal_capacity = 100.0    # 热容
    
    def update_temperature(self, cpu_usage: float, time: float) -> float:
        """更新温度"""
        # 简化的热模型
        heat_generation = cpu_usage * self.specs.thermal_design_power * 0.8
        heat_dissipation = (self.specs.current_temperature - self.ambient_temperature) / self.thermal_resistance
        
        # 温度变化率
        dT_dt = (heat_generation - heat_dissipation) / self.thermal_capacity
        
        # 更新温度
        new_temperature = self.specs.current_temperature + dT_dt * 1.0  # 1秒时间步长
        
        # 限制温度范围
        new_temperature = np.clip(new_temperature, self.ambient_temperature, self.specs.max_temperature)
        
        return new_temperature


class PowerModel:
    """功耗模型"""
    
    def __init__(self, specs: RealisticComputerSpecs):
        self.specs = specs
        self.idle_power = 20.0  # 空闲功耗
        self.max_power = specs.thermal_design_power
    
    def update_power_consumption(self, cpu_usage: float, memory_usage: float, time: float) -> float:
        """更新功耗"""
        # CPU功耗
        cpu_power = self.idle_power + (self.max_power - self.idle_power) * cpu_usage
        
        # 内存功耗
        memory_power = 10.0 * memory_usage
        
        # 基础功耗
        base_power = 15.0
        
        # 总功耗
        total_power = cpu_power + memory_power + base_power
        
        # 添加随机波动
        noise = np.random.normal(0, 2.0)
        total_power += noise
        
        return max(0, total_power)


class NetworkModel:
    """网络模型"""
    
    def __init__(self, specs: RealisticComputerSpecs):
        self.specs = specs
        self.base_latency = specs.network_latency
        self.base_packet_loss = specs.network_packet_loss
    
    def update_network_conditions(self, congestion: float) -> Tuple[float, float]:
        """更新网络条件"""
        # 拥塞影响延迟
        latency = self.base_latency * (1.0 + congestion * 5.0)
        
        # 拥塞影响丢包率
        packet_loss = self.base_packet_loss * (1.0 + congestion * 10.0)
        
        return latency, packet_loss


def create_realistic_computer_specs() -> RealisticComputerSpecs:
    """创建真实计算机规格"""
    return RealisticComputerSpecs(
        cpu_cores=8,
        cpu_frequency=3.6,
        cpu_cache_l1=32,
        cpu_cache_l2=256,
        cpu_cache_l3=8192,
        cpu_architecture="x86_64",
        memory_total=16384,
        memory_type="DDR4",
        memory_frequency=3200,
        memory_channels=2,
        storage_type="SSD",
        storage_capacity=512000,
        storage_read_speed=3500,
        storage_write_speed=3000,
        network_type="Ethernet",
        network_speed=1000,
        network_latency=1.0,
        network_packet_loss=0.001,
        thermal_design_power=95,
        current_temperature=45.0,
        max_temperature=85.0,
        power_consumption=65.0
    )


def demo_realistic_computer_simulation():
    """真实计算机仿真演示"""
    print("=== 真实计算机性能仿真演示 ===")
    
    # 创建真实计算机规格
    specs = create_realistic_computer_specs()
    
    # 创建测试节点
    nodes = []
    for i in range(3):
        node = Node(
            id=i + 1,
            time_step=0,
            resource=NodeResource(
                cpu_capacity=20000,
                memory_capacity=20480,
                bandwidth_capacity=15360,
                current_cpu=3000,
                current_memory=4000,
                current_bandwidth=2000
            ),
            node_type=NodeType.GENERAL,
            is_monitoring=True
        )
        nodes.append(node)
    
    # 创建真实计算机仿真器
    simulator = RealisticComputerSimulator(nodes, specs)
    
    print("启动真实计算机性能仿真...")
    print(f"CPU规格: {specs.cpu_cores}核 {specs.cpu_frequency}GHz")
    print(f"内存规格: {specs.memory_total}MB {specs.memory_type}")
    print(f"存储规格: {specs.storage_type} {specs.storage_capacity}MB")
    print(f"网络规格: {specs.network_type} {specs.network_speed}Mbps")
    
    simulator.start_simulation(update_interval=2.0)
    
    try:
        # 运行演示
        for i in range(10):  # 运行10个周期
            time.sleep(2)
            
            print(f"\n--- 第 {i+1} 个周期 ---")
            
            # 显示系统指标
            real_metrics = simulator.real_system_monitor.get_current_metrics()
            print(f"真实系统指标:")
            print(f"  CPU使用率: {real_metrics.get('cpu_usage', 0):.1f}%")
            print(f"  内存使用率: {real_metrics.get('memory_usage', 0):.1f}%")
            print(f"  磁盘使用率: {real_metrics.get('disk_usage', 0):.1f}%")
            print(f"  网络IO: {real_metrics.get('network_io', 0):.1f}MB")
            
            # 显示节点性能
            for node in nodes:
                print(f"节点 {node.id}: "
                      f"CPU={node.dynamic_performance.cpu_performance_factor:.3f}, "
                      f"内存={node.dynamic_performance.memory_performance_factor:.3f}, "
                      f"网络={node.dynamic_performance.network_performance_factor:.3f}, "
                      f"评分={node.get_overall_performance_score():.3f}")
            
            # 显示系统状态
            print(f"系统状态:")
            print(f"  温度: {simulator.specs.current_temperature:.1f}°C")
            print(f"  功耗: {simulator.specs.power_consumption:.1f}W")
            print(f"  CPU节流: {simulator.performance_factors.cpu_thermal_throttling:.3f}")
            print(f"  网络拥塞: {simulator.performance_factors.network_congestion:.3f}")
    
    except KeyboardInterrupt:
        print("\n用户中断演示")
    finally:
        simulator.stop_simulation()
        print("真实计算机性能仿真演示结束")


if __name__ == "__main__":
    demo_realistic_computer_simulation() 