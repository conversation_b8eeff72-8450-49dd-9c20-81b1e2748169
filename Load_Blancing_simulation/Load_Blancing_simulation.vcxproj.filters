<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="simulation.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Load_Blancing\Load_Blancing\data_generator.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="params.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="data.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="task_allocation.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="task_classification.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="multi_timestep_simulation.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="timestep_classification_group.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="simulation.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Load_Blancing\Load_Blancing\data_generator.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="task_generator.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="node_generator.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="main_data_generator.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="task_allocation.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="main_experiment.cpp.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="main_Task Classification and Node Grouping.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Modified_Algorithm.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Multi-Timestep Task Allocation.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="main_Multi-Timestep.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Task Classification and Node Grouping.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="main_timestep_classification_group.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="timestep_classification_group.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
</Project>