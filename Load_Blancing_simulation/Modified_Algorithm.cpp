#include "task_allocation.h"
#include "task_classification.h"
#include <algorithm>
#include <cmath>
#include <iostream>
#include <limits>
#include <numeric>
#include <random>

// �Ľ���Ļ�ϻ���-ģ���˻���������㷨
std::vector<int> TaskAllocator::hybridGWSAAllocation() {
    // ����������ɫ�ͷ���
    TaskColorizer colorizer(tasks, resWeights);
    std::vector<TaskColorInfo> coloredTasks = colorizer.colorTasks();
    
    // ���нڵ����
    NodeGrouper grouper(nodes, resWeights);
    std::vector<NodeGroup> nodeGroups = grouper.groupNodes();
    
    const int populationSize = algParams.populationSize;
    const int maxIterations = algParams.maxIterations;
    double temperature = algParams.initialTemp;
    const double coolingRate = algParams.coolingRate;

    std::vector<double> convergenceHistory;
    std::vector<double> iterationTimes;
    auto startTime = std::chrono::high_resolution_clock::now();

    // ��¼ԭʼ�ڵ����Դ
    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) originalNodeResources.push_back(node.resource);

    // ���ڽ�����պϲ��ı���
    std::vector<int> finalAssignment(tasks.size(), 0);
    
    // ��ȡ������������񣨰���������
    std::vector<size_t> taskOrder = getTaskTopologicalOrder();
    
    // �������������з���
    std::map<int, std::vector<size_t>> tasksByLevel;
    for (size_t i = 0; i < coloredTasks.size(); i++) {
        int taskIdx = -1;
        for (size_t j = 0; j < tasks.size(); j++) {
            if (tasks[j].id == coloredTasks[i].taskId) {
                taskIdx = j;
                break;
            }
        }
        if (taskIdx != -1) {
            tasksByLevel[coloredTasks[i].level].push_back(taskIdx);
        }
    }
    
    // ��������һ��������
    for (const auto& levelPair : tasksByLevel) {
        int level = levelPair.first;
        const std::vector<size_t>& levelTasks = levelPair.second;
        
        std::cout << "������ " << level << " ����������: " << levelTasks.size() << std::endl;
        
        // ����ɫ���Ͷ�ͬ����������з���
        std::map<TaskColorType, std::vector<size_t>> tasksByColor;
        for (size_t taskIdx : levelTasks) {
            int taskId = tasks[taskIdx].id;
            for (const auto& colorInfo : coloredTasks) {
                if (colorInfo.taskId == taskId) {
                    tasksByColor[colorInfo.colorType].push_back(taskIdx);
                    break;
                }
            }
        }
        
        // Ϊÿ����ɫ��������񵽶�Ӧ�Ľڵ���
        for (int colorType = 0; colorType < 4; colorType++) {
            TaskColorType taskColor = static_cast<TaskColorType>(colorType);
            NodeGroupType nodeGroupType = static_cast<NodeGroupType>(colorType);
            
            if (tasksByColor[taskColor].empty() || nodeGroups[nodeGroupType].nodes.empty()) {
                continue;
            }
            
            std::cout << "  ������ɫ " << colorType << " ����������: " << tasksByColor[taskColor].size() << std::endl;
            
            // ������ǰ�������ͽڵ��Ӽ�
            std::vector<Task> groupTasks;
            std::vector<Node> groupNodes;
            std::vector<size_t> taskIndices = tasksByColor[taskColor]; // ����ԭʼ����
            
            for (size_t taskIdx : taskIndices) {
                groupTasks.push_back(tasks[taskIdx]);
            }
            
            for (const auto& nodeInfo : nodeGroups[nodeGroupType].nodes) {
                for (const auto& node : nodes) {
                    if (node.id == nodeInfo.nodeId) {
                        groupNodes.push_back(node);
                        break;
                    }
                }
            }
            
            if (groupTasks.empty() || groupNodes.empty()) {
                continue;
            }
            
            // ������ʹ�û���-ģ���˻��㷨��������
            std::vector<int> groupAssignment = hybridGWSAGroupAllocation(
                groupTasks, groupNodes, taskIndices);
            
            // �����ڷ������ϲ������շ�����
            for (size_t i = 0; i < taskIndices.size(); i++) {
                size_t originalIdx = taskIndices[i];
                if (i < groupAssignment.size() && groupAssignment[i] > 0) {
                    finalAssignment[originalIdx] = groupAssignment[i];
                }
            }
        }
        
        // ����Ƿ���δ��������񣬽�����䵽ͨ�ýڵ���
        for (size_t taskIdx : levelTasks) {
            if (finalAssignment[taskIdx] == 0) {
                // Ѱ��һ�����ʵ�ͨ�ýڵ�
                for (const auto& nodeInfo : nodeGroups[GENERAL_GROUP].nodes) {
                    for (const auto& node : nodes) {
                        if (node.id == nodeInfo.nodeId && canNodeSatisfyTask(tasks[taskIdx], node, finalAssignment)) {
                            finalAssignment[taskIdx] = node.id;
                            break;
                        }
                    }
                    if (finalAssignment[taskIdx] > 0) break;
                }
                
                // �������δ���䣬����������Ľڵ�
                if (finalAssignment[taskIdx] == 0) {
                    for (const auto& group : nodeGroups) {
                        if (group.groupType == GENERAL_GROUP) continue; // �Ѿ����Թ�ͨ����
                        
                        for (const auto& nodeInfo : group.nodes) {
                            for (const auto& node : nodes) {
                                if (node.id == nodeInfo.nodeId && canNodeSatisfyTask(tasks[taskIdx], node, finalAssignment)) {
                                    finalAssignment[taskIdx] = node.id;
                                    break;
                                }
                            }
                            if (finalAssignment[taskIdx] > 0) break;
                        }
                        if (finalAssignment[taskIdx] > 0) break;
                    }
                }
            }
        }
    }
    
    // ���ʣ��δ���������
    int unassignedCount = std::count(finalAssignment.begin(), finalAssignment.end(), 0);
    if (unassignedCount > 0) {
        std::cout << "���棺���� " << unassignedCount << " ������δ���䣬����ǿ�Ʒ���..." << std::endl;
        finalAssignment = ensureAllTasksAllocated(finalAssignment);
    }
    
    // ����Ŀ�꺯��ֵ����¼
    double finalFitness = calculateObjectiveValue(finalAssignment);
    convergenceHistory.push_back(finalFitness);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    lastConvergenceHistory = convergenceHistory;
    lastIterationTimes = iterationTimes;
    avgIterationTime = duration.count() / (iterationTimes.size() > 0 ? iterationTimes.size() : 1);
    
    return finalAssignment;
}

// ���ڻ�ϻ���-ģ���˻��㷨
std::vector<int> TaskAllocator::hybridGWSAGroupAllocation(
    const std::vector<Task>& groupTasks, 
    const std::vector<Node>& groupNodes,
    const std::vector<size_t>& taskIndices) {
    
    const int populationSize = algParams.populationSize;
    const int maxIterations = algParams.maxIterations;
    double temperature = algParams.initialTemp;
    const double coolingRate = algParams.coolingRate;
    
    std::vector<double> iterationTimes;
    std::vector<double> convergenceHistory;
    
    // �������������
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> uniformDist(0.0, 1.0);
    
    // ��ʼ��������Ⱥ
    std::vector<std::vector<int>> population(populationSize, std::vector<int>(groupTasks.size(), 0));
    std::vector<double> fitness(populationSize);
    
    // �����ʼ����Ⱥ
    for (int i = 0; i < populationSize; i++) {
        for (size_t j = 0; j < groupTasks.size(); j++) {
            // ���ѡ��һ���ڵ�
            std::uniform_int_distribution<> nodeDist(0, groupNodes.size() - 1);
            population[i][j] = groupNodes[nodeDist(gen)].id;
        }
        
        // ��������������ϵ
        population[i] = repairDependencies(population[i], groupTasks, taskIndices);
        
        // ������Ӧ��
        fitness[i] = 1.0 / (calculateGroupObjectiveValue(population[i], groupTasks, groupNodes) + 1e-10);
    }
    
    // �ҳ���ʼ�����Ž�
    int bestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
    double bestFitness = fitness[bestIdx];
    std::vector<int> bestSolution = population[bestIdx];
    
    // ��¼������ʷ
    convergenceHistory.push_back(1.0 / bestFitness);
    
    // ��ʼ����
    for (int iter = 0; iter < maxIterations; iter++) {
        auto iterStartTime = std::chrono::high_resolution_clock::now();
        
        // ����aֵ����2���Եݼ���0��
        double a = 2.0 - iter * (2.0 / maxIterations);
        
        // ����Ӧ������
        std::vector<std::pair<double, int>> fitnessIndices(populationSize);
        for (int i = 0; i < populationSize; i++) {
            fitnessIndices[i] = {fitness[i], i};
        }
        std::sort(fitnessIndices.begin(), fitnessIndices.end(), std::greater<>());
        
        // ѡ��alpha��beta��delta��
        std::vector<int> alphaWolf = population[fitnessIndices[0].second];
        std::vector<int> betaWolf = population[fitnessIndices[1].second];
        std::vector<int> deltaWolf = population[fitnessIndices[2].second];
        
        // ����ÿ������λ��
        for (int i = 0; i < populationSize; i++) {
            // �������ܽϲ���ǣ�������50%�������ø������ĸ��²���
            if (i > populationSize / 2) {
                for (size_t j = 0; j < groupTasks.size(); j++) {
                    // �����������쵼�ǵľ���
                    double A1 = 2 * a * uniformDist(gen) - a;
                    double C1 = 2 * uniformDist(gen);
                    double D_alpha = std::abs(C1 * alphaWolf[j] - population[i][j]);
                    double X1 = alphaWolf[j] - A1 * D_alpha;
                    
                    double A2 = 2 * a * uniformDist(gen) - a;
                    double C2 = 2 * uniformDist(gen);
                    double D_beta = std::abs(C2 * betaWolf[j] - population[i][j]);
                    double X2 = betaWolf[j] - A2 * D_beta;
                    
                    double A3 = 2 * a * uniformDist(gen) - a;
                    double C3 = 2 * uniformDist(gen);
                    double D_delta = std::abs(C3 * deltaWolf[j] - population[i][j]);
                    double X3 = deltaWolf[j] - A3 * D_delta;
                    
                    // ����λ��
                    int newPosition = std::round((X1 + X2 + X3) / 3.0);
                    
                    // ȷ���ڵ�ID��Ч
                    bool validNodeId = false;
                    for (const auto& node : groupNodes) {
                        if (node.id == newPosition) {
                            validNodeId = true;
                            break;
                        }
                    }
                    
                    // �����Ч�������ѡ��һ����Ч�ڵ�
                    if (!validNodeId) {
                        std::uniform_int_distribution<> nodeDist(0, groupNodes.size() - 1);
                        population[i][j] = groupNodes[nodeDist(gen)].id;
                    } else {
                        population[i][j] = newPosition;
                    }
                }
            } 
            // �������ܽϺõ��ǣ�����ǰ50%����ʹ�ø������ĸ��²���
            else {
                // ���ѡ���Ƿ����ģ���˻��Ŷ�
                if (uniformDist(gen) < 0.3) {  // 30%�ĸ��ʽ����Ŷ�
                    // ����һ���µĽ�
                    std::vector<int> newSolution = population[i];
                    
                    // ���ѡ�񼸸���������Ŷ�
                    int disturbCount = std::max(1, static_cast<int>(groupTasks.size() * 0.2));
                    for (int k = 0; k < disturbCount; k++) {
                        std::uniform_int_distribution<> taskDist(0, groupTasks.size() - 1);
                        int taskIdx = taskDist(gen);
                        std::uniform_int_distribution<> nodeDist(0, groupNodes.size() - 1);
                        int nodeIdx = nodeDist(gen);
                        newSolution[taskIdx] = groupNodes[nodeIdx].id;
                    }
                    
                    // �޸�������ϵ
                    newSolution = repairDependencies(newSolution, groupTasks, taskIndices);
                    
                    // �����½����Ӧ��
                    double newFitness = 1.0 / (calculateGroupObjectiveValue(newSolution, groupTasks, groupNodes) + 1e-10);
                    
                    // ������Ӧ�ȱ仯
                    double deltaFitness = newFitness - fitness[i];
                    
                    // ����½���û���ͨ�����ʽ���
                    if (deltaFitness > 0 || uniformDist(gen) < std::exp(deltaFitness / temperature)) {
                        population[i] = newSolution;
                        fitness[i] = newFitness;
                    }
                }
                // ����ʹ�ó���Ļ���λ�ø���
                else {
                    for (size_t j = 0; j < groupTasks.size(); j++) {
                        // ʹ�ø����ص��ƶ�����
                        double A1 = 2 * a * uniformDist(gen) - a;
                        double C1 = 2 * uniformDist(gen);
                        double D_alpha = std::abs(C1 * alphaWolf[j] - population[i][j]);
                        double X1 = alphaWolf[j] - A1 * D_alpha * 0.5;  // ��С����
                        
                        // �������벢ȷ���ڵ�ID��Ч
                        int newPosition = std::round(X1);
                        
                        // ȷ���ڵ�ID��Ч
                        bool validNodeId = false;
                        for (const auto& node : groupNodes) {
                            if (node.id == newPosition) {
                                validNodeId = true;
                                break;
                            }
                        }
                        
                        // �����Ч���򱣳�ԭ����λ��
                        if (!validNodeId) {
                            // ����ԭλ��
                        } else {
                            population[i][j] = newPosition;
                        }
                    }
                }
            }
            
            // �޸�������ϵ
            population[i] = repairDependencies(population[i], groupTasks, taskIndices);
            
            // ���¼�����Ӧ��
            fitness[i] = 1.0 / (calculateGroupObjectiveValue(population[i], groupTasks, groupNodes) + 1e-10);
        }
        
        // �ҳ���ǰ���������Ž�
        int currentBestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
        
        // ����ȫ�����Ž�
        if (fitness[currentBestIdx] > bestFitness) {
            bestFitness = fitness[currentBestIdx];
            bestSolution = population[currentBestIdx];
        }
        
        // ģ���˻�ȫ���¶�˥��
        temperature *= coolingRate;
        
        // ��¼������ʷ
        convergenceHistory.push_back(1.0 / bestFitness);
        
        // ��¼����ʱ��
        auto iterEndTime = std::chrono::high_resolution_clock::now();
        auto iterDuration = std::chrono::duration_cast<std::chrono::milliseconds>(iterEndTime - iterStartTime);
        iterationTimes.push_back(iterDuration.count());
    }
    
    // ȷ�����������ѷ���
    bestSolution = ensureGroupTasksAllocated(bestSolution, groupTasks, groupNodes);
    
    return bestSolution;
}

// �޸�������ϵ
std::vector<int> TaskAllocator::repairDependencies(const std::vector<int>& assignment,
                                                   const std::vector<Task>& groupTasks,
                                                   const std::vector<size_t>& taskIndices) {
    std::vector<int> repairedAssignment = assignment;
    
    // ����ԭʼ����ID������������ӳ��
    std::map<int, size_t> taskIdToIndex;
    for (size_t i = 0; i < groupTasks.size(); i++) {
        taskIdToIndex[groupTasks[i].id] = i;
    }
    
    // ���ÿ�������������ϵ
    for (size_t i = 0; i < groupTasks.size(); i++) {
        for (int depTaskId : groupTasks[i].dependencies) {
            // ������������Ƿ��ڵ�ǰ����
            if (taskIdToIndex.find(depTaskId) != taskIdToIndex.end()) {
                size_t depIdx = taskIdToIndex[depTaskId];
                
                // ����Ƿ���γ�ѭ������
                if (wouldCreateCyclicDependency(groupTasks[i], repairedAssignment[depIdx], repairedAssignment)) {
                    // Ѱ��һ�������γ�ѭ�������Ľڵ�
                    for (const auto& node : nodes) {
                        if (!wouldCreateCyclicDependency(groupTasks[i], node.id, repairedAssignment)) {
                            repairedAssignment[i] = node.id;
                            break;
                        }
                    }
                }
            }
        }
    }
    
    return repairedAssignment;
}

// ȷ���������������ѷ���
std::vector<int> TaskAllocator::ensureGroupTasksAllocated(const std::vector<int>& assignment,
                                                          const std::vector<Task>& groupTasks,
                                                          const std::vector<Node>& groupNodes) {
    std::vector<int> finalAssignment = assignment;
    
    // ����Ƿ���δ���������
    for (size_t i = 0; i < finalAssignment.size(); i++) {
        if (finalAssignment[i] == 0) {
            // Ѱ��һ��������������Ľڵ�
            for (const auto& node : groupNodes) {
                if (canNodeSatisfyTask(groupTasks[i], node, finalAssignment)) {
                    finalAssignment[i] = node.id;
                    break;
                }
            }
            
            // �����Ȼδ���䣬��������䵽һ���ڵ�
            if (finalAssignment[i] == 0 && !groupNodes.empty()) {
                std::random_device rd;
                std::mt19937 gen(rd());
                std::uniform_int_distribution<> dist(0, groupNodes.size() - 1);
                finalAssignment[i] = groupNodes[dist(gen)].id;
            }
        }
    }
    
    return finalAssignment;
}

// ��������Ŀ�꺯��ֵ
double TaskAllocator::calculateGroupObjectiveValue(const std::vector<int>& assignment,
                                                  const std::vector<Task>& groupTasks,
                                                  const std::vector<Node>& groupNodes) {
    // �����ڵ�ID��������ӳ��
    std::map<int, size_t> nodeIdToIndex;
    for (size_t i = 0; i < groupNodes.size(); i++) {
        nodeIdToIndex[groupNodes[i].id] = i;
    }
    
    // ����һ���ڵ���Դ״̬
    std::vector<NodeResource> nodeResources;
    for (const auto& node : groupNodes) {
        nodeResources.push_back(node.resource);
    }
    
    // ������Դ������
    double cpuUtilization = 0.0;
    double memUtilization = 0.0;
    double netUtilization = 0.0;
    
    double totalCpuCapacity = 0.0;
    double totalMemCapacity = 0.0;
    double totalNetCapacity = 0.0;
    
    // ����ڵ�������
    for (const auto& node : groupNodes) {
        totalCpuCapacity += node.resource.cpuCapacity;
        totalMemCapacity += node.resource.memoryCapacity;
        totalNetCapacity += node.resource.bandwidthCapacity;
    }
    
    // ������ڵ���������ʱ��
    std::vector<double> nodeTotalTimes(groupNodes.size(), 0.0);
    
    for (size_t i = 0; i < assignment.size(); i++) {
        int nodeId = assignment[i];
        auto it = nodeIdToIndex.find(nodeId);
        
        if (it != nodeIdToIndex.end()) {
            size_t nodeIdx = it->second;
            
            // �ۼ���Դ����
            cpuUtilization += groupTasks[i].resourceDemand.cpuDemand;
            memUtilization += groupTasks[i].resourceDemand.memoryDemand;
            netUtilization += groupTasks[i].resourceDemand.bandwidthDemand;
            
            // ��������ִ��ʱ��
            TaskExecutionTime execTime = calculateTaskExecutionTime(groupTasks[i], groupNodes[nodeIdx]);
            nodeTotalTimes[nodeIdx] += execTime.totalTime;
        }
    }
    
    // ��һ����Դ������
    double RU_CPU = cpuUtilization / totalCpuCapacity;
    double RU_Mem = memUtilization / totalMemCapacity;
    double RU_Net = netUtilization / totalNetCapacity;
    
    double normalizedRU = 1.0 - (resWeights.cpuWeight * RU_CPU + 
                                resWeights.memWeight * RU_Mem + 
                                resWeights.netWeight * RU_Net);
    
    // ����Makespan
    double makespan = *std::max_element(nodeTotalTimes.begin(), nodeTotalTimes.end());
    
    // �����������ִ��ʱ��
    double maxTheoreticalTime = 0.0;
    for (const auto& task : groupTasks) {
        double worstTime = 0.0;
        for (const auto& node : groupNodes) {
            TaskExecutionTime execTime = calculateTaskExecutionTime(task, node);
            worstTime = std::max(worstTime, execTime.totalTime);
        }
        maxTheoreticalTime += worstTime;
    }
    
    // ��һ��Makespan
    double normalizedMS = makespan / maxTheoreticalTime;
    
    // ���㸺�ؾ����
    double avgLoad = std::accumulate(nodeTotalTimes.begin(), nodeTotalTimes.end(), 0.0) / nodeTotalTimes.size();
    double loadVariance = 0.0;
    for (const double& load : nodeTotalTimes) {
        loadVariance += std::pow(load - avgLoad, 2);
    }
    loadVariance /= nodeTotalTimes.size();
    double loadBalanceDegree = std::sqrt(loadVariance);
    
    // ��һ�����ؾ����
    double maxPossibleVariance = std::pow(maxTheoreticalTime, 2);
    double normalizedPsi = loadBalanceDegree / maxPossibleVariance;
    
    // ����������ʱ��
    double totalTaskTime = std::accumulate(nodeTotalTimes.begin(), nodeTotalTimes.end(), 0.0);
    
    // ��һ��������ʱ��
    double avgExecTimeExpected = maxTheoreticalTime / groupTasks.size();
    double normalizedTotalTime = totalTaskTime / (groupTasks.size() * avgExecTimeExpected);
    
    // ����Ŀ�꺯��ֵ��Ȩ������Ŀ�꺯��һ�£�
    double objectiveValue = objWeights.alpha * normalizedRU +
                           objWeights.beta * 0.0 +  // ʡ����Ӧʱ�䣬��Ϊ�������ڷ���
                           objWeights.gamma * normalizedMS +
                           objWeights.epsilon * normalizedPsi +
                           objWeights.xi * normalizedTotalTime;
    
    return objectiveValue;
}