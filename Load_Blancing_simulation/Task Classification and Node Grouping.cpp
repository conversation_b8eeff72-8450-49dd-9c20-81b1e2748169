#include "task_classification.h"
#include <iostream>
#include <algorithm>
#include <queue>
#include <set>
#include <map>
#include <cmath>

// ����������ɫʵ��
TaskColorizer::TaskColorizer(const std::vector<Task>& tasks,
    const ResourceWeights& resWeights)
    : tasks(tasks), resWeights(resWeights) {
}

// ����������ۺ���Դ����
double TaskColorizer::calculateResourceIndex(const Task& task) {
    double R_i = resWeights.cpuWeight * task.resourceDemand.cpuDemand +
        resWeights.memWeight * task.resourceDemand.memoryDemand +
        resWeights.netWeight * task.resourceDemand.bandwidthDemand;
    return R_i;
}

// ȷ���������ɫ����
TaskColorType TaskColorizer::determineTaskColorType(const Task& task) {
    double R_i = calculateResourceIndex(task);
    double cpuRatio = (resWeights.cpuWeight * task.resourceDemand.cpuDemand) / R_i;
    double memRatio = (resWeights.memWeight * task.resourceDemand.memoryDemand) / R_i;
    double netRatio = (resWeights.netWeight * task.resourceDemand.bandwidthDemand) / R_i;

    // ���ĳ����Դ���󳬹��ۺ������60%������Ϊ�Ǹ����͵�����
    if (cpuRatio > 0.6) {
        return CPU_INTENSIVE_COLOR;
    }
    else if (memRatio > 0.6) {
        return MEMORY_INTENSIVE_COLOR;
    }
    else if (netRatio > 0.6) {
        return NETWORK_INTENSIVE_COLOR;
    }
    else {
        return GENERAL_COLOR;
    }
}

// ̰����ɫ�㷨
std::vector<TaskColorInfo> TaskColorizer::colorTasks() {
    std::vector<TaskColorInfo> coloredTasks(tasks.size());

    // ����1: ������Դ��������Ϊÿ����������ʼ��ɫ
    for (size_t i = 0; i < tasks.size(); i++) {
        coloredTasks[i].taskId = tasks[i].id;
        coloredTasks[i].colorType = determineTaskColorType(tasks[i]);
        coloredTasks[i].level = 0; // ��ʼ������Ϊ0
        coloredTasks[i].dependencyProcessed = false;
    }

    // ����2: ������������ͼ
    std::map<int, std::vector<int>> dependencyGraph;
    std::map<int, int> indegree;

    for (size_t i = 0; i < tasks.size(); i++) {
        int taskId = tasks[i].id;
        indegree[taskId] = tasks[i].dependencies.size();

        // Ϊ������ǰ�����������ӱ�
        for (int depTaskId : tasks[i].dependencies) {
            dependencyGraph[depTaskId].push_back(taskId);
        }
    }

    // ����3: ʹ����������ȷ�����񼶱�
    std::queue<int> zeroIndegree;

    // �ҳ��������Ϊ0������������������
    for (const auto& task : tasks) {
        if (indegree[task.id] == 0) {
            zeroIndegree.push(task.id);
        }
    }

    int currentLevel = 1;
    while (!zeroIndegree.empty()) {
        int size = zeroIndegree.size();

        for (int i = 0; i < size; i++) {
            int currentTask = zeroIndegree.front();
            zeroIndegree.pop();

            // �ҵ�������coloredTasks�е�����
            int taskIndex = -1;
            for (size_t j = 0; j < coloredTasks.size(); j++) {
                if (coloredTasks[j].taskId == currentTask) {
                    taskIndex = j;
                    break;
                }
            }

            if (taskIndex != -1) {
                coloredTasks[taskIndex].level = currentLevel;
                coloredTasks[taskIndex].dependencyProcessed = true;

                // ���������ڵ�ǰ�������������
                for (int dependentTask : dependencyGraph[currentTask]) {
                    indegree[dependentTask]--;
                    if (indegree[dependentTask] == 0) {
                        zeroIndegree.push(dependentTask);
                    }
                }
            }
        }

        currentLevel++;
    }

    // ����4: ����������ϵ����ɫ��ͻ������
    for (size_t i = 0; i < tasks.size(); i++) {
        for (int depTaskId : tasks[i].dependencies) {
            int depTaskIndex = -1;
            for (size_t j = 0; j < coloredTasks.size(); j++) {
                if (coloredTasks[j].taskId == depTaskId) {
                    depTaskIndex = j;
                    break;
                }
            }

            if (depTaskIndex != -1) {
                // �����������͵�ǰ������ɫ��ͬ���޸ĵ�ǰ������ɫ��ǳ
                if (coloredTasks[depTaskIndex].colorType == coloredTasks[i].colorType) {
                    coloredTasks[i].colorShade = std::min(coloredTasks[i].colorShade + 1, 5);
                }
            }
        }
    }

    // ��ӡ������ɫ���
    std::cout << "������ɫ�����" << std::endl;
    for (const auto& task : coloredTasks) {
        std::string colorTypeStr;
        switch (task.colorType) {
        case CPU_INTENSIVE_COLOR: colorTypeStr = "CPU�ܼ���"; break;
        case MEMORY_INTENSIVE_COLOR: colorTypeStr = "�ڴ��ܼ���"; break;
        case NETWORK_INTENSIVE_COLOR: colorTypeStr = "�����ܼ���"; break;
        case GENERAL_COLOR: colorTypeStr = "ͨ����"; break;
        }

        std::cout << "���� " << task.taskId << ": ��ɫ=" << colorTypeStr
            << ", ��ǳ=" << task.colorShade
            << ", ����=" << task.level << std::endl;
    }

    return coloredTasks;
}

// �ڵ����ͷ���ʵ��
NodeGrouper::NodeGrouper(const std::vector<Node>& nodes,
    const ResourceWeights& resWeights)
    : nodes(nodes), resWeights(resWeights) {
}

// ����ڵ���ۺ�����ָ��
double NodeGrouper::calculatePerformanceIndex(const Node& node) {
    double p_eff = node.resource.currentCpu;
    double m_avail = node.resource.currentMemory;
    double b_real = node.resource.currentBandwidth;

    double S_f = resWeights.cpuWeight * p_eff +
        resWeights.memWeight * m_avail +
        resWeights.netWeight * b_real;

    return S_f;
}

// ȷ���ڵ��������
NodeGroupType NodeGrouper::determineNodeGroupType(const Node& node) {
    double S_f = calculatePerformanceIndex(node);
    double cpuRatio = (resWeights.cpuWeight * node.resource.currentCpu) / S_f;
    double memRatio = (resWeights.memWeight * node.resource.currentMemory) / S_f;
    double netRatio = (resWeights.netWeight * node.resource.currentBandwidth) / S_f;

    // ���ĳ����Դռ�ȳ����ۺ����ܵ�60%������Ϊ�Ǹ����͵Ľڵ�
    if (cpuRatio > 0.6) {
        return CPU_INTENSIVE_GROUP;
    }
    else if (memRatio > 0.6) {
        return MEMORY_INTENSIVE_GROUP;
    }
    else if (netRatio > 0.6) {
        return NETWORK_INTENSIVE_GROUP;
    }
    else {
        return GENERAL_GROUP;
    }
}

// ����ڵ���ۺϸ�����
double NodeGrouper::calculateLoadRate(const Node& node) {
    double L_CPU = static_cast<double>(node.resource.currentCpu) / node.resource.cpuCapacity;
    double L_Mem = std::tanh(static_cast<double>(node.resource.currentMemory) / node.resource.memoryCapacity);
    double L_Net = std::min(1.0, static_cast<double>(node.resource.currentBandwidth) / (0.8 * node.resource.bandwidthCapacity));

    double psi = resWeights.cpuWeight * L_CPU +
        resWeights.memWeight * L_Mem +
        resWeights.netWeight * L_Net;

    return psi;
}

// ȷ���ڵ�ĸ���״̬
NodeLoadState NodeGrouper::determineLoadState(const Node& node) {
    double psi = calculateLoadRate(node);
    double cpuRatio = resWeights.cpuWeight * (static_cast<double>(node.resource.currentCpu) / node.resource.cpuCapacity) / psi;
    double memRatio = resWeights.memWeight * std::tanh(static_cast<double>(node.resource.currentMemory) / node.resource.memoryCapacity) / psi;
    double netRatio = resWeights.netWeight * std::min(1.0, static_cast<double>(node.resource.currentBandwidth) / (0.8 * node.resource.bandwidthCapacity)) / psi;

    NodeLoadState state = BALANCED;

    if (cpuRatio > 0.8) {
        state = static_cast<NodeLoadState>(state | CPU_OVERLOAD);
    }
    else if (cpuRatio < 0.25) {
        state = static_cast<NodeLoadState>(state | CPU_UNDERLOAD);
    }

    if (memRatio > 0.8) {
        state = static_cast<NodeLoadState>(state | MEMORY_OVERLOAD);
    }
    else if (memRatio < 0.25) {
        state = static_cast<NodeLoadState>(state | MEMORY_UNDERLOAD);
    }

    if (netRatio > 0.8) {
        state = static_cast<NodeLoadState>(state | NETWORK_OVERLOAD);
    }
    else if (netRatio < 0.25) {
        state = static_cast<NodeLoadState>(state | NETWORK_UNDERLOAD);
    }

    return state;
}

// ����ڵ㲢ѡ�ټ�ؽڵ�
std::vector<NodeGroup> NodeGrouper::groupNodes() {
    std::vector<NodeGroup> nodeGroups(4); // 4��������

    // ��ʼ����
    nodeGroups[0].groupType = CPU_INTENSIVE_GROUP;
    nodeGroups[1].groupType = MEMORY_INTENSIVE_GROUP;
    nodeGroups[2].groupType = NETWORK_INTENSIVE_GROUP;
    nodeGroups[3].groupType = GENERAL_GROUP;

    // ����1: ������Դ���ͽ��ڵ���䵽��ͬ��
    for (const auto& node : nodes) {
        NodeGroupType groupType = determineNodeGroupType(node);
        NodeInfo nodeInfo;
        nodeInfo.nodeId = node.id;
        nodeInfo.performance = calculatePerformanceIndex(node);
        nodeInfo.loadRate = calculateLoadRate(node);
        nodeInfo.loadState = determineLoadState(node);

        nodeGroups[groupType].nodes.push_back(nodeInfo);
    }

    // ����2: Ϊÿ����ѡ�ټ�ؽڵ㣨ѡ��������ߵĽڵ㣩
    for (auto& group : nodeGroups) {
        if (!group.nodes.empty()) {
            // ������ָ������
            std::sort(group.nodes.begin(), group.nodes.end(),
                [](const NodeInfo& a, const NodeInfo& b) {
                    return a.performance > b.performance;
                });

            // ѡ��������ߵĽڵ���Ϊ��ؽڵ�
            group.monitorNodeId = group.nodes[0].nodeId;

            // �������ƽ��������
            double totalLoadRate = 0.0;
            for (const auto& nodeInfo : group.nodes) {
                totalLoadRate += nodeInfo.loadRate;
            }
            group.avgLoadRate = totalLoadRate / group.nodes.size();
        }
    }

    // ��ӡ�ڵ������
    std::cout << "�ڵ��������" << std::endl;
    for (const auto& group : nodeGroups) {
        std::string groupTypeStr;
        switch (group.groupType) {
        case CPU_INTENSIVE_GROUP: groupTypeStr = "CPU�ܼ�����"; break;
        case MEMORY_INTENSIVE_GROUP: groupTypeStr = "�ڴ��ܼ�����"; break;
        case NETWORK_INTENSIVE_GROUP: groupTypeStr = "�����ܼ�����"; break;
        case GENERAL_GROUP: groupTypeStr = "ͨ������"; break;
        }

        std::cout << groupTypeStr << "��" << std::endl;
        std::cout << "  �ڵ�����: " << group.nodes.size() << std::endl;
        if (!group.nodes.empty()) {
            std::cout << "  ��ؽڵ�ID: " << group.monitorNodeId << std::endl;
            std::cout << "  ƽ��������: " << group.avgLoadRate << std::endl;
            std::cout << "  �ڵ��б�: ";
            for (const auto& nodeInfo : group.nodes) {
                std::cout << nodeInfo.nodeId << " ";
            }
            std::cout << std::endl;
        }
    }

    return nodeGroups;
}