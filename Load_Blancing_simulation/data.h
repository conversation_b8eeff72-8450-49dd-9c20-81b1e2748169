#ifndef DATA_H
#define DATA_H

#include <string>
#include <vector>
#include <unordered_set>

// �ڵ�����ö��
enum NodeType {
    CPU_INTENSIVE = 1,    // CPU�ܼ���
    MEMORY_INTENSIVE = 2, // �ڴ��ܼ���
    NETWORK_INTENSIVE = 3, // ͨ���ܼ���
    GENERAL = 4           // ��ͨ��
};

// ��������ö��
enum TaskType {
    TASK_CPU_INTENSIVE = 1,    // CPU�ܼ�������
    TASK_MEMORY_INTENSIVE = 2, // �ڴ��ܼ�������
    TASK_NETWORK_INTENSIVE = 3, // ͨ���ܼ�������
    TASK_GENERAL = 4           // ��ͨ������
};

// ������Դ����ṹ��
struct TaskResourceDemand {
    int cpuDemand;      // CPU����(100-1000)
    int memoryDemand;   // �ڴ�����(100-1000)
    int bandwidthDemand; // ��������(100-1000)
};

// ����ṹ��
struct Task {
    int id;                              // ������
    int timeStep;                        // ʱ��
    TaskResourceDemand resourceDemand;   // ��Դ����
    std::unordered_set<int> dependencies; // �������񼯺�
    TaskType type;                       // ��������
};

// �ڵ���Դ�ṹ��
struct NodeResource {
    int cpuCapacity;        // CPU��׼��������
    int memoryCapacity;     // �ڴ��׼������
    int bandwidthCapacity;  // ����������
    int currentCpu;         // ��ǰCPU��������
    int currentMemory;      // ��ǰ�ڴ�����
    int currentBandwidth;   // ��ǰ�������
};

// �ڵ�ṹ��
struct Node {
    int id;                 // �ڵ���
    int timeStep;           // ʱ��(��ʼΪ0)
    NodeResource resource;  // �ڵ���Դ
    NodeType type;          // �ڵ����/��������
};

// �������ɲ���
struct TaskGenerationParams {
    int timeSteps = 3;               // ʱ�䲽��
    int tasksPerTimeStep = 50;       // ÿ��ʱ�䲽����������
    int minCpuDemand = 100;          // ��СCPU����
    int maxCpuDemand = 1000;         // ���CPU����
    int minMemoryDemand = 100;       // ��С�ڴ�����
    int maxMemoryDemand = 1000;      // ����ڴ�����
    int minBandwidthDemand = 100;    // ��С��������
    int maxBandwidthDemand = 1000;   // ����������
    double dependencyProbability = 0.3; // ��������
    int maxDependencies = 3;         // �����������
    int maxDependencyLayers = 5;     // �����������

    // ��������Ȩ�ز���
    double cpuWeight = 0.4;         // CPUȨ�� 
    double memoryWeight = 0.3;      // �ڴ�Ȩ��
    double networkWeight = 0.3;     // ����Ȩ��
};

// �ڵ����ɲ���
struct NodeGenerationParams {
    int numNodes = 20;                 // �ڵ�����
    int minCpuCapacity = 10000;        // ��СCPU��׼����
    int maxCpuCapacity = 30000;        // ���CPU��׼����
    int minMemoryCapacity = 10240;     // ��С�ڴ��׼����
    int maxMemoryCapacity = 30720;     // ����ڴ��׼����
    int minBandwidthCapacity = 10240;  // ��С�����׼����
    int maxBandwidthCapacity = 30720;  // �������׼����
    double currentResourceRatioMin = 0.1; // ��ǰ��Դ��С����
    double currentResourceRatioMax = 0.2; // ��ǰ��Դ������

    // �ڵ����Ȩ�ز���
    double cpuWeight = 0.4;         // CPUȨ��
    double memoryWeight = 0.3;      // �ڴ�Ȩ��
    double networkWeight = 0.3;     // ����Ȩ��
};




// ������������
class DataGenerator {
public:
    // �����������ݼ�
    static bool generateTaskDataset(const std::string& filename, const TaskGenerationParams& params);
    // ���ɽڵ����ݼ�
    static bool generateNodeDataset(const std::string& filename, const NodeGenerationParams& params);
};

#endif // DATA_H