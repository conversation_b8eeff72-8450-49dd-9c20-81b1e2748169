#include "data_generator.h"
#include <sstream>
#include <algorithm>  // ���� std::shuffle
#include <random>
#include <iostream>
#include <fstream>
#include <iterator>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <queue>

std::random_device DataGenerator::rd;

std::default_random_engine DataGenerator::getRandomEngine() {
    return std::default_random_engine(rd());
}

int DataGenerator::getRandomInt(std::default_random_engine& engine, int min, int max) {
    // ȷ�� min ������ max
    if (min > max) {
        std::swap(min, max);
    }
    std::uniform_int_distribution<int> dist(min, max);
    return dist(engine);
}

double DataGenerator::getRandomDouble(std::default_random_engine& engine, double min, double max) {
    std::uniform_real_distribution<double> dist(min, max);
    return dist(engine);
}

std::vector<int> DataGenerator::getRandomSubset(std::default_random_engine& engine, const std::vector<int>& source, int num) {
    std::vector<int> subset = source;

    // ����Ӽ���С����Դ�����Ĵ�С������ΪԴ�����Ĵ�С
    if (num > subset.size()) {
        num = subset.size();
    }

    // ʹ�� std::shuffle ���������д���
    std::shuffle(subset.begin(), subset.end(), engine);

    // ����ǰ num ��Ԫ��
    subset.resize(num);
    return subset;
}

// ����������Ƿ���ڴ���������ϵ
bool DataGenerator::hasTransitiveDependency(
    const std::unordered_map<int, std::unordered_set<int>>& dependencyMap,
    int from, int to, std::unordered_set<int>& visited) {

    // ����Ѿ����ʹ�����ڵ㣬����ѭ������
    if (visited.find(from) != visited.end()) {
        return false;
    }

    // ��ǵ�ǰ�ڵ�Ϊ�ѷ���
    visited.insert(from);

    // ����Ƿ����ֱ������
    auto it = dependencyMap.find(from);
    if (it != dependencyMap.end()) {
        const auto& deps = it->second;

        // �������ֱ��������ϵ
        if (deps.find(to) != deps.end()) {
            return true;
        }

        // ����Ƿ���ڴ���������ϵ
        for (int dep : deps) {
            if (hasTransitiveDependency(dependencyMap, dep, to, visited)) {
                return true;
            }
        }
    }

    return false;
}

// �Ż�������ϵ���Ƴ���������
std::unordered_set<int> DataGenerator::optimizeDependencies(
    const std::unordered_map<int, std::unordered_set<int>>& dependencyMap,
    int taskId) {

    std::unordered_set<int> result;
    auto it = dependencyMap.find(taskId);
    if (it == dependencyMap.end()) {
        return result;
    }

    const auto& directDeps = it->second;
    for (int dep : directDeps) {
        bool isRedundant = false;

        // ������������Ƿ���Դ��ݵ��������
        for (int otherDep : directDeps) {
            if (otherDep != dep) {
                std::unordered_set<int> visited;
                if (hasTransitiveDependency(dependencyMap, otherDep, dep, visited)) {
                    isRedundant = true;
                    break;
                }
            }
        }

        // ���������������������ӵ������
        if (!isRedundant) {
            result.insert(dep);
        }
    }

    return result;
}

bool DataGenerator::generateNodeDataset(const std::string& filename, const NodeGenerationParams& params) {
    try {
        std::ofstream outFile(filename);
        if (!outFile.is_open()) {
            std::cerr << "Failed to open file: " << filename << std::endl;
            return false;
        }

        outFile << "�ڵ��� CPU�������� ��ǰ������ �ڴ������� ��ʹ���ڴ��� ���������� ����ӵ��ϵ�� ��·����ϵ�� ͨ������" << std::endl;

        auto engine = getRandomEngine();

        for (int nodeId = 1; nodeId <= params.numNodes; ++nodeId) {
            int cpuCapacity = getRandomInt(engine, params.minCpuCapacity, params.maxCpuCapacity);
            double cpuLoadRate = getRandomDouble(engine, params.minCpuLoadRate, params.maxCpuLoadRate);
            int totalMemory = getRandomInt(engine, params.minMemory, params.maxMemory);
            int usedMemory = getRandomInt(engine, 0, totalMemory / 3);
            double bandwidthRatio = getRandomDouble(engine, params.minBandwidthRatio, params.maxBandwidthRatio);
            double networkCongestion = getRandomDouble(engine, params.minNetworkCongestion, params.maxNetworkCongestion);
            double linkQuality = getRandomDouble(engine, params.minLinkQuality, params.maxLinkQuality);

            // ����ͨ������
            double communicationAbility = params.totalBandwidth * bandwidthRatio * networkCongestion * linkQuality;

            outFile << nodeId << " "
                << cpuCapacity << " "
                << cpuLoadRate << " "
                << totalMemory << " "
                << usedMemory << " "
                << bandwidthRatio << " "
                << networkCongestion << " "
                << linkQuality << " "
                << communicationAbility << std::endl;
        }

        outFile.close();
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error generating node dataset: " << e.what() << std::endl;
        return false;
    }
}

bool DataGenerator::generateTaskDataset(const std::string& filename, const TaskGenerationParams& params) {
    try {
        std::ofstream outFile(filename);
        if (!outFile.is_open()) {
            std::cerr << "Failed to open file: " << filename << std::endl;
            return false;
        }

        outFile << "������ ʱ�� CPU���� �ڴ����� �������� ��������" << std::endl;

        auto engine = getRandomEngine();
        std::vector<std::vector<int>> tasksPerTimeStep(params.timeSteps + 1);

        // �洢���������������ϵ
        std::unordered_map<int, std::unordered_set<int>> dependencyMap;

        int taskId = 1;
        for (int timeStep = 1; timeStep <= params.timeSteps; ++timeStep) {
            std::vector<int>& currentTasks = tasksPerTimeStep[timeStep];

            for (int j = 0; j < params.tasksPerTimeStep; ++j) {
                int cpuDemand = getRandomInt(engine, params.minCpuDemand, params.maxCpuDemand);
                int memoryDemand = getRandomInt(engine, params.minMemoryDemand, params.maxMemoryDemand);
                int bandwidthDemand = getRandomInt(engine, params.minBandwidthDemand, params.maxBandwidthDemand);

                currentTasks.push_back(taskId);

                // ���������������
                std::vector<int> dependencies;
                std::unordered_set<int> dependencySet;

                if (getRandomDouble(engine, 0.0, 1.0) < params.dependencyProbability) {
                    // ֻ����������ǰʱ���Ѿ����ɵ�����
                    std::vector<int> allCurrentTasks;
                    for (int t = 0; t < j; ++t) {  // ��ǰʱ��֮ǰ�Ѿ����ɵ�����
                        allCurrentTasks.push_back(currentTasks[t]);
                    }

                    // ���ѡ����������
                    int numDependencies = getRandomInt(engine, 1, std::min(params.maxDependencies, (int)allCurrentTasks.size()));
                    dependencies = getRandomSubset(engine, allCurrentTasks, numDependencies);

                    // ��������ӵ����Ϻ�ӳ����
                    for (int dep : dependencies) {
                        dependencySet.insert(dep);
                    }
                    dependencyMap[taskId] = dependencySet;
                }

                // �Ż�������ϵ��д��TXT�ļ�
                // ע�⣺�����ȼ�¼����������ϵ���������Ż�
                taskId++;
            }
        }

        // ��������������������Ѿ����ɣ���ʼ�Ż���д���ļ�
        taskId = 1;
        for (int timeStep = 1; timeStep <= params.timeSteps; ++timeStep) {
            std::vector<int>& currentTasks = tasksPerTimeStep[timeStep];

            for (int j = 0; j < currentTasks.size(); ++j) {
                int currentTaskId = currentTasks[j];

                // ��ȡ�����CPU���ڴ�ʹ�������
                int cpuDemand = getRandomInt(engine, params.minCpuDemand, params.maxCpuDemand);
                int memoryDemand = getRandomInt(engine, params.minMemoryDemand, params.maxMemoryDemand);
                int bandwidthDemand = getRandomInt(engine, params.minBandwidthDemand, params.maxBandwidthDemand);

                // �Ż�������ϵ���Ƴ���������
                std::unordered_set<int> optimizedDeps = optimizeDependencies(dependencyMap, currentTaskId);

                // ���������ַ���
                std::stringstream depStream;
                int count = 0;
                for (int dep : optimizedDeps) {
                    depStream << dep;
                    if (++count < optimizedDeps.size()) {
                        depStream << " ";
                    }
                }

                // д��������Ϣ
                outFile << currentTaskId << " "
                    << timeStep << " "
                    << cpuDemand << " "
                    << memoryDemand << " "
                    << bandwidthDemand << " "
                    << depStream.str() << std::endl;
            }
        }

        outFile.close();
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error generating task dataset: " << e.what() << std::endl;
        return false;
    }
}