#ifndef DATA_GENERATOR_H
#define DATA_GENERATOR_H

#include <string>
#include <vector>
#include <random>
#include <iostream>
#include <fstream>
#include <unordered_map>
#include <unordered_set>

struct NodeGenerationParams {
    int numNodes;
    int minCpuCapacity;
    int maxCpuCapacity;
    double minCpuLoadRate;
    double maxCpuLoadRate;
    int minMemory;
    int maxMemory;

    double minBandwidthRatio;
    double maxBandwidthRatio;
    double minNetworkCongestion;
    double maxNetworkCongestion;
    double minLinkQuality;
    double maxLinkQuality;
    double totalBandwidth; // �ܴ���
};

struct TaskGenerationParams {
    int timeSteps;
    int tasksPerTimeStep;
    int minCpuDemand;
    int maxCpuDemand;
    int minMemoryDemand;
    int maxMemoryDemand;
    int minBandwidthDemand;
    int maxBandwidthDemand;
    double dependencyProbability;
    int maxDependencies;
};

class DataGenerator {
public:
    static bool generateNodeDataset(const std::string& filename, const NodeGenerationParams& params);
    static bool generateTaskDataset(const std::string& filename, const TaskGenerationParams& params);

private:
    static std::random_device rd;
    static std::default_random_engine getRandomEngine();
    static int getRandomInt(std::default_random_engine& engine, int min, int max);
    static double getRandomDouble(std::default_random_engine& engine, double min, double max);
    static std::vector<int> getRandomSubset(std::default_random_engine& engine, const std::vector<int>& source, int num);

    // ����������Ƿ���ڴ���������ϵ
    static bool hasTransitiveDependency(
        const std::unordered_map<int, std::unordered_set<int>>& dependencyMap,
        int from, int to, std::unordered_set<int>& visited);

    // �������Ż�������ϵ���Ƴ���������
    static std::unordered_set<int> optimizeDependencies(
        const std::unordered_map<int, std::unordered_set<int>>& dependencyMap,
        int taskId);
};

#endif // DATA_GENERATOR_H