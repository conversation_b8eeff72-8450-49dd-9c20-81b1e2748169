// main.cpp
#include "simulation.h"
#include "data_generator.h"
#include <iostream>
#include <chrono>

void generateData() {
    std::cout << "Generating simulation data..." << std::endl;


    NodeGenerationParams nodeParams;
    TaskGenerationParams taskParams;

    // ���ýڵ����ɲ���
    NodeGenerationParams nodeParams;
    nodeParams.numNodes = 100;  // ����20���ڵ�
    nodeParams.minCpuCapacity = 2000;
    nodeParams.maxCpuCapacity = 10000;
    nodeParams.minCpuLoadRate = 0.0;
    nodeParams.maxCpuLoadRate = 0.2;
    nodeParams.minMemory = 8192;
    nodeParams.maxMemory = 32768;
    nodeParams.minBandwidthRatio = 0.0;
    nodeParams.maxBandwidthRatio = 0.3;
    nodeParams.minNetworkCongestion = 0.0;
    nodeParams.maxNetworkCongestion = 0.3;
    nodeParams.minLinkQuality = 0.6;
    nodeParams.maxLinkQuality = 1.0;
    nodeParams.totalBandwidth = 10000;

    // �����������ɲ���
    TaskGenerationParams taskParams;
    taskParams.timeSteps = 10;  // ����3��ʱ��
    taskParams.tasksPerTimeStep = 500;  // ÿ��ʱ������50������
    taskParams.minCpuDemand = 100;
    taskParams.maxCpuDemand = 1000;
    taskParams.minMemoryDemand = 100;
    taskParams.maxMemoryDemand = 1000;
    taskParams.minBandwidthDemand = 100;
    taskParams.maxBandwidthDemand = 1000;
    taskParams.dependencyProbability = 0.3;
    taskParams.maxDependencies = 3;

    // ��������
    bool nodeDataGenerated = DataGenerator::generateNodeDataset("nodes.txt", nodeParams);
    if (nodeDataGenerated) {
        std::cout << "Node data generated successfully in nodes.txt!" << std::endl;
    }
    else {
        std::cerr << "Failed to generate node data." << std::endl;
    }

    bool taskDataGenerated = DataGenerator::generateTaskDataset("tasks.txt", taskParams);
    if (taskDataGenerated) {
        std::cout << "Task data generated successfully in tasks.txt!" << std::endl;
    }
    else {
        std::cerr << "Failed to generate task data." << std::endl;
    }

    if (nodeDataGenerated && taskDataGenerated) {
        std::cout << "Data generation completed successfully!" << std::endl;
    }
}

// �޸ĺ��runSimulation����
void runSimulation() {
    std::cout << "=== Load Balancing Simulation ===" << std::endl;

    // ��main.cpp��runSimulation������
    SimulationParams params{
            0.4,   // wCpu
            0.3,   // wMem
            0.3,   // wNet
            0.85,  // cpuEfficiency
            0.95,  // memEfficiency
            0.9,   // netEfficiency
            0.75,  // cpuDecayFactor
            0.8    // networkSafeThreshold
    };

    // ����ģ����ʵ��
    Simulator simulator(params);

    // ���������ļ�·��
    std::string nodeFile = "nodes.txt";
    std::string taskFile = "tasks.txt";

    // ���ؽڵ�����
    if (!simulator.loadNodes(nodeFile)) {
        std::cerr << "Failed to load node data." << std::endl;
        return;
    }

    // ������������
    if (!simulator.loadTasks(taskFile)) {
        std::cerr << "Failed to load task data." << std::endl;
        return;
    }

    // ���÷������
    int maxTimeSteps = 3;
    double timeInterval = 1.0;

    // ִ�з���
    auto startTime = std::chrono::high_resolution_clock::now();
    simulator.run(maxTimeSteps, timeInterval);
    auto endTime = std::chrono::high_resolution_clock::now();

    // �����ʱ
    std::chrono::duration<double> duration = endTime - startTime;
    std::cout << "\nSimulation completed in " << duration.count() << " seconds." << std::endl;

    // ������
    simulator.saveResults("simulation_results");
}


int main() {
    int choice;
    std::cout << "Load Balancing Simulation System\n";
    std::cout << "1. Generate Simulation Data\n";
    std::cout << "2. Run Simulation\n";
    std::cout << "Enter your choice (1-2): ";
    std::cin >> choice;

    switch (choice) {
    case 1:
        generateData();
        break;
    case 2:
        runSimulation();
        break;
    default:
        std::cout << "Invalid choice. Exiting." << std::endl;
        break;
    }




    return 0;
}