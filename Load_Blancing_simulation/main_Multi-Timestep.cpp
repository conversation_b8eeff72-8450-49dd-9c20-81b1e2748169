#include "multi_timestep_simulation.h"
#include <iostream>
#include <string>




// ���������רע�ڶ�ʱ�䲽����
int main() {
    // ���÷������
    SimulationParams simParams;

    // Ŀ�꺯��Ȩ��
    simParams.objectiveWeights.alpha = 0.0;  // ��Դ������Ȩ��
    simParams.objectiveWeights.beta = 0.1;   // ��Ӧʱ��Ȩ��
    simParams.objectiveWeights.gamma = 0.1;  // MakespanȨ��
    simParams.objectiveWeights.epsilon = 0.4; // ���ؾ����Ȩ��
    simParams.objectiveWeights.xi = 0.4;     // ������ʱ��Ȩ��

    // ��ԴȨ��
    simParams.resourceWeights.cpuWeight = 0.4;  // CPU��ԴȨ��
    simParams.resourceWeights.memWeight = 0.3;  // �ڴ���ԴȨ��
    simParams.resourceWeights.netWeight = 0.3;  // ������ԴȨ��

    // �㷨����
    simParams.algorithmParams.maxIterations = 50;  // ����������
    simParams.algorithmParams.populationSize = 50;  // ��Ⱥ��С
    simParams.algorithmParams.initialTemp = 100.0;  // ��ʼ�¶�
    simParams.algorithmParams.coolingRate = 0.95;   // ��ȴ��

    // ��ԴЧ�ʲ���
    simParams.cpuEfficiency = 0.85;  // CPU��Դ����Ч��
    simParams.memEfficiency = 0.95;  // �ڴ���Դ����Ч��
    simParams.netEfficiency = 0.90;  // ������Դ����Ч��

    // CPU����˥�����ӣ�ģ��ɢ�����Ƶ��µķ����Խ�Ƶ��
    simParams.cpuDecayFactor = 0.75;

    // ���簲ȫ��ֵ��������ֵ�ᵼ������ӵ����
    simParams.networkSafeThreshold = 0.80;

    std::cout << "=== ��ʱ�䲽����ϵͳ ===" << std::endl;
    std::cout << "��ϵͳ���Զ��ʱ�䲽��������ж�̬���䣬��������Դ���ͷźͻ��ա�" << std::endl;

    // ������ʱ�䲽����ʵ��
    MultiTimestepSimulation simulation(simParams);

    // ��������ͽڵ�����
    std::cout << "\n���ڼ�������..." << std::endl;
    if (!simulation.loadData("tasks_timestep.txt", "nodes_timestep.txt")) {
        std::cerr << "��������ʧ�ܣ������˳�" << std::endl;
        return 1;
    }

    // ִ�з���
    std::cout << "\n����ִ�ж�ʱ�䲽����..." << std::endl;
    simulation.runSimulation();

    std::cout << "\n��ʱ�䲽������ɣ�����ѱ��浽�ļ���" << std::endl;

    return 0;
}