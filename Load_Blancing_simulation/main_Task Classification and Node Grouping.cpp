#include "task_allocation.h"
#include <iostream>
#include <fstream>
#include <string>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <map>
#include <vector>
#include <codecvt>
#include <locale>


std::vector<Task> loadTasks(const std::string& filename) {
    std::vector<Task> tasks;
    std::ifstream inFile(filename);
    if (!inFile.is_open()) {
        std::cerr << "�޷��������ļ�: " << filename << std::endl;
        return tasks;
    }

    std::string line;
    // ����������
    std::getline(inFile, line);

    int taskCount = 0;
    while (std::getline(inFile, line)) {
        std::istringstream iss(line);
        Task task;
        int taskId, timeStep, cpuDemand, memoryDemand, bandwidthDemand;

        // ��ȡ������Ϣ
        if (!(iss >> taskId >> timeStep >> cpuDemand >> memoryDemand >> bandwidthDemand)) {
            std::cerr << "��������������Ϣʧ��: " << line << std::endl;
            continue; // ������Ч��
        }

        task.id = taskId;
        task.timeStep = timeStep;
        task.resourceDemand.cpuDemand = cpuDemand;
        task.resourceDemand.memoryDemand = memoryDemand;
        task.resourceDemand.bandwidthDemand = bandwidthDemand;

        // ��ȡ�������������
        std::string dependenciesAndType;
        std::getline(iss, dependenciesAndType);

        // �����������������
        size_t semicolonPos = dependenciesAndType.find(';');
        if (semicolonPos != std::string::npos) {
            // ������������
            std::string dependenciesStr = dependenciesAndType.substr(0, semicolonPos);
            std::istringstream depIss(dependenciesStr);
            int depId;
            while (depIss >> depId) {
                task.dependencies.insert(depId);
            }

            // ��������
            std::string typeStr = dependenciesAndType.substr(semicolonPos + 1);
            std::istringstream typeIss(typeStr);
            int type;
            if (typeIss >> type) {
                task.type = static_cast<TaskType>(type);
            }
        }
        else {
            // ���û�зֺŷָ���������ֱ�ӽ���
            std::istringstream fullIss(dependenciesAndType);
            std::string token;
            bool foundType = false;
            while (fullIss >> token) {
                try {
                    int value = std::stoi(token);
                    if (value >= 1 && value <= 4 && !foundType) {
                        task.type = static_cast<TaskType>(value);
                        foundType = true;
                    }
                    else {
                        task.dependencies.insert(value);
                    }
                }
                catch (...) {
                    // ���������ֲ���
                }
            }

            // Ĭ������Ϊ��ͨ����
            if (!foundType) {
                task.type = TASK_GENERAL;
            }
        }

        // ֻѡ���ض�ʱ�̵�����
        if (task.timeStep == 1) { // ֻ����ʱ��1������
            tasks.push_back(task);
            taskCount++;

            // ÿ100���������һ�ν���
            if (taskCount % 100 == 0) {
                std::cout << "�Ѷ�ȡ " << taskCount << " ������..." << std::endl;
            }
        }
    }

    inFile.close();
    std::cout << "�ܹ���ȡ�� " << tasks.size() << " ������" << std::endl;
    return tasks;
}



int main() {
    // ��ȡ��������
    std::vector<Task> tasks = loadTasks("tasks.txt");
    if (tasks.empty()) {
        std::cerr << "δ�ܶ�ȡ���κ����񣬳����˳�" << std::endl;
        return 1;
    }

    // ��ȡ�ڵ�����
    std::vector<Node> nodes;
    std::ifstream nodeFile("nodes.txt");
    if (!nodeFile.is_open()) {
        std::cerr << "�޷��򿪽ڵ��ļ�!" << std::endl;
        return 1;
    }

    // ����������
    std::string line;
    std::getline(nodeFile, line);

    // �����ڵ�����
    int nodeId, nodeTimeStep, cpuCapacity, memCapacity, bwCapacity, currentCpu, currentMem, currentBw, nodeType;

    while (nodeFile >> nodeId >> nodeTimeStep >> cpuCapacity >> memCapacity >> bwCapacity >>
        currentCpu >> currentMem >> currentBw >> nodeType) {
        Node node;
        node.id = nodeId;
        node.timeStep = nodeTimeStep;
        node.resource.cpuCapacity = cpuCapacity;
        node.resource.memoryCapacity = memCapacity;
        node.resource.bandwidthCapacity = bwCapacity;
        node.resource.currentCpu = currentCpu;
        node.resource.currentMemory = currentMem;
        node.resource.currentBandwidth = currentBw;
        node.type = static_cast<NodeType>(nodeType);

        nodes.push_back(node);
    }

    nodeFile.close();

    std::cout << "�Ѽ��� " << tasks.size() << " ������� " << nodes.size() << " ���ڵ㡣" << std::endl;

    // �����ͷ�������ͽڵ�
    std::map<int, std::vector<Task>> tasksByType;
    std::map<int, std::vector<Node>> nodesByType;

    for (const auto& task : tasks) {
        tasksByType[task.type].push_back(task);
    }

    for (const auto& node : nodes) {
        nodesByType[node.type].push_back(node);
    }

    // �������ͳ��
    std::cout << "\n===== ����ͽڵ����ͳ�� =====" << std::endl;
    for (int type = 1; type <= 4; type++) {
        std::cout << "���� " << type << ": "
            << tasksByType[type].size() << " ������, "
            << nodesByType[type].size() << " ���ڵ�" << std::endl;
    }

    // ����Ŀ�꺯��Ȩ��
    ObjectiveWeights objWeights;
    objWeights.alpha = 0.0;   // ��Դ������Ȩ��
    objWeights.beta = 0.1;    // ��Ӧʱ��Ȩ��
    objWeights.gamma = 0.2;   // MakespanȨ��
    objWeights.epsilon = 0.3; // ���ؾ����Ȩ��
    objWeights.xi = 0.4;      // ������ʱ��Ȩ��

    // ������ԴȨ��
    ResourceWeights resWeights;
    resWeights.cpuWeight = 0.4;
    resWeights.memWeight = 0.3;
    resWeights.netWeight = 0.3;

    // �����㷨����
    AlgorithmParams algParams;
    algParams.maxIterations = 50;    // ����������
    algParams.populationSize = 50;    // ��Ⱥ��С
    algParams.initialTemp = 100.0;    // ��ʼ�¶�
    algParams.coolingRate = 0.95;     // ��ȴ��

    // Ҫ���Ե��㷨�б�
    std::vector<AlgorithmType> algorithms = {
        ROUND_ROBIN,
        GENETIC,
        PARTICLE_SWARM,
        BEE_COLONY,
        //ANT_COLONY,
        BAT,
        WHALE,
        SIMULATED_ANNEALING,
        GREY_WOLF,
        HYBRID_GW_SA
    };

    // �洢���㷨�ķ�����
    std::map<AlgorithmType, std::vector<int>> allAlgorithmAssignments;
    std::map<AlgorithmType, TaskAllocationResult> allAlgorithmResults;
    std::map<AlgorithmType, std::chrono::milliseconds> allAlgorithmTimes;

    // Ϊÿ���㷨ִ���������
    for (const auto& algorithm : algorithms) {
        std::cout << "\n\n========================================================" << std::endl;
        std::cout << "��ʼʹ�� " << TaskAllocator::getAlgorithmName(algorithm) << " ���з���" << std::endl;
        std::cout << "========================================================" << std::endl;

        // ���������ڷ��䲢�ϲ����
        std::vector<int> finalAssignment(tasks.size(), 0);
        std::vector<double> groupMetrics;
        int totalAssigned = 0;

        auto startTime = std::chrono::high_resolution_clock::now();

        // Ϊÿ�����ʹ���ID��������ӳ��(ȫ��)
        std::map<int, size_t> taskIdToIndex;
        for (size_t i = 0; i < tasks.size(); i++) {
            taskIdToIndex[tasks[i].id] = i;
        }

        // ��ÿ�����ͽ��з���
        for (int type = 1; type <= 4; type++) {
            if (tasksByType[type].empty()) {
                std::cout << "���� " << type << " û����������" << std::endl;
                continue;
            }

            // ���������û�нڵ㣬����ʹ��ͨ�����ͽڵ�
            std::vector<Node> currentNodes;
            if (nodesByType[type].empty() && type != 4) {
                std::cout << "���� " << type << " û�нڵ㣬����ʹ��ͨ�ýڵ�(����4)" << std::endl;
                currentNodes = nodesByType[4];
                if (currentNodes.empty()) {
                    std::cout << "ͨ�ýڵ㲻���ã��޷��������� " << type << " ������" << std::endl;
                    continue;
                }
            }
            else {
                currentNodes = nodesByType[type];
            }

            std::cout << "\n===== ʹ�� " << TaskAllocator::getAlgorithmName(algorithm)
                << " Ϊ���� " << type << " �������� ("
                << tasksByType[type].size() << "������, "
                << currentNodes.size() << "���ڵ�) =====" << std::endl;

            // ������ʹ�õ�ǰ�㷨
            TaskAllocator allocator(tasksByType[type], currentNodes, objWeights, resWeights, algParams);
            TaskAllocationResult result = allocator.allocateTasks(algorithm);

            // �������ڷ����������
            int groupAssigned = 0;
            for (int nodeId : result.nodeAssignment) {
                if (nodeId > 0) {
                    groupAssigned++;
                }
            }

            std::cout << "���� " << type << " �ɹ����� " << groupAssigned << "/"
                << tasksByType[type].size() << " ������" << std::endl;

            // ��������ָ��
            groupMetrics.push_back(result.objectiveValue);

            // �����ڷ������ϲ���ȫ��
            for (size_t i = 0; i < tasksByType[type].size() && i < result.nodeAssignment.size(); i++) {
                if (result.nodeAssignment[i] > 0) {
                    int taskId = tasksByType[type][i].id;
                    if (taskIdToIndex.find(taskId) != taskIdToIndex.end()) {
                        finalAssignment[taskIdToIndex[taskId]] = result.nodeAssignment[i];
                        totalAssigned++;
                    }
                }
            }
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        // ��������ָ��
        TaskAllocator finalAllocator(tasks, nodes, objWeights, resWeights, algParams);
        TaskAllocationResult finalResult = finalAllocator.evaluateAssignment(finalAssignment);

        // ������շ�����
        std::cout << "\n===== " << TaskAllocator::getAlgorithmName(algorithm) << " ���շ����� =====" << std::endl;
        std::cout << "��ִ��ʱ��: " << duration.count() << " ms" << std::endl;
        std::cout << "��Դ������: " << finalResult.resourceUtilization << "%" << std::endl;
        std::cout << "Makespan: " << finalResult.makespan << std::endl;
        std::cout << "���ؾ����: " << finalResult.loadBalanceDegree << std::endl;
        std::cout << "������ʱ��: " << finalResult.totalTaskTime << std::endl;
        std::cout << "Ŀ�꺯��ֵ: " << finalResult.objectiveValue << std::endl;

        double assignedPercentage = 100.0 * totalAssigned / tasks.size();
        std::cout << "�ɹ���������: " << totalAssigned << "/" << tasks.size()
            << " (" << std::fixed << std::setprecision(2) << assignedPercentage << "%)" << std::endl;

        // ���������
        allAlgorithmAssignments[algorithm] = finalAssignment;
        allAlgorithmResults[algorithm] = finalResult;
        allAlgorithmTimes[algorithm] = duration;

        // ������������ļ�
        std::string resultFilename = TaskAllocator::getAlgorithmName(algorithm) + "_result.txt";
        finalAllocator.saveAllocationResultToFile(finalResult, algorithm, resultFilename);

        // �����������ݵ��ļ�
        std::string convergenceFilename = TaskAllocator::getAlgorithmName(algorithm) + "_convergence.txt";
        finalAllocator.saveConvergenceToFile(finalResult, algorithm, convergenceFilename);
    }

    // ���������㷨�Ա����ݵ�CSV�ļ�
    std::ofstream csvFile("task_allocation_results_class.csv");

    if (csvFile.is_open()) {
        csvFile << "�㷨,��Դ������,��Ӧʱ��(ms),Makespan,���ؾ����,������ʱ��,��һ��RU,��һ��RT,��һ��MS,��һ��Psi,��һ����ʱ��,Ŀ�꺯��ֵ\n";

        for (const auto& algorithm : algorithms) {
            const auto& result = allAlgorithmResults[algorithm];
            const auto& duration = allAlgorithmTimes[algorithm];

            csvFile << TaskAllocator::getAlgorithmName(algorithm) << ","
                << result.resourceUtilization << ","
                << duration.count() << ","
                << result.makespan << ","
                << result.loadBalanceDegree << ","
                << result.totalTaskTime << ","
                << result.normalizedRU << ","
                << result.normalizedRT << ","
                << result.normalizedMS << ","
                << result.normalizedPsi << ","
                << result.normalizedTotalTime << ","
                << result.objectiveValue << "\n";
        }

        csvFile.close();
        std::cout << "\n�����㷨�ԱȽ���ѱ��浽CSV�ļ�: task_allocation_results.csv" << std::endl;
    }

    // ���������㷨�ıȽϽ����TXT�ļ�
    TaskAllocator::saveComparisonResultsToFile(allAlgorithmResults, "algorithm_comparison.txt");

    std::cout << "\n�����㷨ִ����ɣ�" << std::endl;

    return 0;
}