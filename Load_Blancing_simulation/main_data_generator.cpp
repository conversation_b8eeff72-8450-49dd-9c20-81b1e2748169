#include "data.h"
#include <iostream>
#include <fstream>
#include <string>

// ���ÿ���̨���ΪUTF-8����


int main() {
    // ���ÿ���̨���֧������


    std::cout << "=== ����ʵ�飺����ͽڵ����ݼ����ɹ��� ===" << std::endl;

    // ���ýڵ����ɲ���
    NodeGenerationParams nodeParams;
    std::cout << "�������ýڵ����ɲ���..." << std::endl;

    std::cout << "������Ҫ���ɵĽڵ�������Ĭ��20��: ";
    std::string input;
    std::getline(std::cin, input);
    if (!input.empty()) {
        try {
            nodeParams.numNodes = std::stoi(input);
        }
        catch (...) {
            std::cout << "������Ч��ʹ��Ĭ��ֵ20" << std::endl;
        }
    }

    std::cout << "ʹ�����²������ɽڵ㣺" << std::endl;
    std::cout << "�ڵ�����: " << nodeParams.numNodes << std::endl;
    std::cout << "CPU��׼����������Χ: " << nodeParams.minCpuCapacity << "-" << nodeParams.maxCpuCapacity << std::endl;
    std::cout << "�ڴ��׼������Χ: " << nodeParams.minMemoryCapacity << "-" << nodeParams.maxMemoryCapacity << std::endl;
    std::cout << "�����׼������Χ: " << nodeParams.minBandwidthCapacity << "-" << nodeParams.maxBandwidthCapacity << std::endl;
    std::cout << "��ǰ��Դ������Χ: " << nodeParams.currentResourceRatioMin << "-" << nodeParams.currentResourceRatioMax << std::endl;
    std::cout << "�ڵ����Ȩ�� (CPU:�ڴ�:����): " << nodeParams.cpuWeight << ":" << nodeParams.memoryWeight << ":" << nodeParams.networkWeight << std::endl;

    // �����������ɲ���
    TaskGenerationParams taskParams;
    std::cout << "\n���������������ɲ���..." << std::endl;

    std::cout << "������ʱ�䲽����Ĭ��3��: ";
    std::getline(std::cin, input);
    if (!input.empty()) {
        try {
            taskParams.timeSteps = std::stoi(input);
        }
        catch (...) {
            std::cout << "������Ч��ʹ��Ĭ��ֵ3" << std::endl;
        }
    }

    std::cout << "������ÿ��ʱ�䲽������������Ĭ��50��: ";
    std::getline(std::cin, input);
    if (!input.empty()) {
        try {
            taskParams.tasksPerTimeStep = std::stoi(input);
        }
        catch (...) {
            std::cout << "������Ч��ʹ��Ĭ��ֵ50" << std::endl;
        }
    }

    std::cout << "ʹ�����²�����������" << std::endl;
    std::cout << "ʱ�䲽��: " << taskParams.timeSteps << std::endl;
    std::cout << "ÿ��ʱ�䲽����������: " << taskParams.tasksPerTimeStep << std::endl;
    std::cout << "CPU����Χ: " << taskParams.minCpuDemand << "-" << taskParams.maxCpuDemand << std::endl;
    std::cout << "�ڴ�����Χ: " << taskParams.minMemoryDemand << "-" << taskParams.maxMemoryDemand << std::endl;
    std::cout << "��������Χ: " << taskParams.minBandwidthDemand << "-" << taskParams.maxBandwidthDemand << std::endl;
    std::cout << "��������: " << taskParams.dependencyProbability << std::endl;
    std::cout << "�����������: " << taskParams.maxDependencies << std::endl;
    std::cout << "�����������: " << taskParams.maxDependencyLayers << std::endl;
    std::cout << "��������Ȩ�� (CPU:�ڴ�:����): " << taskParams.cpuWeight << ":" << taskParams.memoryWeight << ":" << taskParams.networkWeight << std::endl;

    // ��������ļ���
    std::string nodeFilename = "4.8_nodes.txt";
    std::string taskFilename = "4.8_tasks.txt";

    std::cout << "\n��ʼ�������ݼ�..." << std::endl;

    // ���ɽڵ����ݼ�
    std::cout << "�������ɽڵ����ݼ�..." << std::endl;
    bool nodeDataGenerated = DataGenerator::generateNodeDataset(nodeFilename, nodeParams);
    if (nodeDataGenerated) {
        std::cout << "�ڵ������ѳɹ������� " << nodeFilename << "!" << std::endl;
    }
    else {
        std::cerr << "���ɽڵ�����ʧ�ܡ�" << std::endl;
        return 1;
    }

    // �����������ݼ�
    std::cout << "���������������ݼ�..." << std::endl;
    bool taskDataGenerated = DataGenerator::generateTaskDataset(taskFilename, taskParams);
    if (taskDataGenerated) {
        std::cout << "���������ѳɹ������� " << taskFilename << "!" << std::endl;
    }
    else {
        std::cerr << "������������ʧ�ܡ�" << std::endl;
        return 1;
    }

    std::cout << "\n�������ɳɹ���ɣ�" << std::endl;
    std::cout << "�ڵ������ļ�: " << nodeFilename << std::endl;
    std::cout << "���������ļ�: " << taskFilename << std::endl;

    std::cout << "\n��������˳�..." << std::endl;
    std::cin.get();

    return 0;
}