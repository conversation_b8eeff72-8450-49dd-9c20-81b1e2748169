#include "data.h"
#include "task_allocation.h"
#include <iostream>
#include <fstream>
#include <string>
#include <sstream>
#include <vector>
#include <map>
#include <iomanip>
#include <algorithm>



// ���ļ��ж�ȡ��������
std::vector<Task> loadTasks(const std::string& filename) {
    std::vector<Task> tasks;
    std::ifstream inFile(filename);

    if (!inFile.is_open()) {
        std::cerr << "�޷��������ļ�: " << filename << std::endl;
        return tasks;
    }

    std::string line;
    // ����������
    std::getline(inFile, line);

    while (std::getline(inFile, line)) {
        std::istringstream iss(line);
        Task task;
        int taskId, timeStep, cpuDemand, memoryDemand, bandwidthDemand;

        // ��ȡ������Ϣ
        if (!(iss >> taskId >> timeStep >> cpuDemand >> memoryDemand >> bandwidthDemand)) {
            continue; // ������Ч��
        }

        task.id = taskId;
        task.timeStep = timeStep;
        task.resourceDemand.cpuDemand = cpuDemand;
        task.resourceDemand.memoryDemand = memoryDemand;
        task.resourceDemand.bandwidthDemand = bandwidthDemand;

        // ��ȡ�������������
        std::string dependenciesAndType;
        std::getline(iss, dependenciesAndType);

        // �����������������
        size_t semicolonPos = dependenciesAndType.find(';');
        if (semicolonPos != std::string::npos) {
            // ������������
            std::string dependenciesStr = dependenciesAndType.substr(0, semicolonPos);
            std::istringstream depIss(dependenciesStr);
            int depId;
            while (depIss >> depId) {
                task.dependencies.insert(depId);
            }

            // ��������
            std::string typeStr = dependenciesAndType.substr(semicolonPos + 1);
            std::istringstream typeIss(typeStr);
            int type;
            if (typeIss >> type) {
                task.type = static_cast<TaskType>(type);
            }
        }
        else {
            // ���û�зֺŷָ���������ֱ�ӽ���
            std::istringstream fullIss(dependenciesAndType);
            std::string token;
            bool foundType = false;

            while (fullIss >> token) {
                try {
                    int value = std::stoi(token);
                    if (value >= 1 && value <= 4 && !foundType) {
                        task.type = static_cast<TaskType>(value);
                        foundType = true;
                    }
                    else {
                        task.dependencies.insert(value);
                    }
                }
                catch (...) {
                    // ���������ֲ���
                }
            }

            // Ĭ������Ϊ��ͨ����
            if (!foundType) {
                task.type = TASK_GENERAL;
            }
        }

        // ֻѡ���ض�ʱ�̵�����
        if (task.timeStep == 1) { // ֻ����ʱ��1������
            tasks.push_back(task);
        }
    }

    inFile.close();
    return tasks;
}

// ���ļ��ж�ȡ�ڵ�����
std::vector<Node> loadNodes(const std::string& filename) {
    std::vector<Node> nodes;
    std::ifstream inFile(filename);

    if (!inFile.is_open()) {
        std::cerr << "�޷��򿪽ڵ��ļ�: " << filename << std::endl;
        return nodes;
    }

    std::string line;
    // ����������
    std::getline(inFile, line);

    while (std::getline(inFile, line)) {
        std::istringstream iss(line);
        Node node;
        int nodeId, timeStep, cpuCapacity, memoryCapacity, bandwidthCapacity;
        int currentCpu, currentMemory, currentBandwidth, nodeType;

        if (!(iss >> nodeId >> timeStep >> cpuCapacity >> memoryCapacity >> bandwidthCapacity
            >> currentCpu >> currentMemory >> currentBandwidth >> nodeType)) {
            continue; // ������Ч��
        }

        node.id = nodeId;
        node.timeStep = timeStep;
        node.resource.cpuCapacity = cpuCapacity;
        node.resource.memoryCapacity = memoryCapacity;
        node.resource.bandwidthCapacity = bandwidthCapacity;
        node.resource.currentCpu = currentCpu;
        node.resource.currentMemory = currentMemory;
        node.resource.currentBandwidth = currentBandwidth;
        node.type = static_cast<NodeType>(nodeType);

        nodes.push_back(node);
    }

    inFile.close();
    return nodes;
}

// ���������ΪCSV�ļ�
void saveResultsToCSV(const std::string& filename,
    const std::map<AlgorithmType, TaskAllocationResult>& results) {
    std::ofstream outFile(filename);

    if (!outFile.is_open()) {
        std::cerr << "�޷���������ļ�: " << filename << std::endl;
        return;
    }

    // д��CSV����
    outFile << "�㷨,��Դ������,��Ӧʱ��(ms),Makespan,���ؾ����,������ʱ��,��һ��RU,��һ��RT,��һ��MS,��һ��Psi,��һ����ʱ��,Ŀ�꺯��ֵ\n";

    // д��ÿ���㷨�Ľ��
    for (const auto& result : results) {
        AlgorithmType algType = result.first;
        const TaskAllocationResult& metrics = result.second;

        outFile << TaskAllocator::getAlgorithmName(algType) << ","
            << metrics.resourceUtilization << ","
            << metrics.executionTime.count() << ","
            << metrics.makespan << ","
            << metrics.loadBalanceDegree << ","
            << metrics.totalTaskTime << ","
            << metrics.normalizedRU << ","
            << metrics.normalizedRT << ","
            << metrics.normalizedMS << ","
            << metrics.normalizedPsi << ","
            << metrics.normalizedTotalTime << ","
            << metrics.objectiveValue << "\n";
    }

    outFile.close();
    std::cout << "����ѱ�����: " << filename << std::endl;
}

bool saveAllConvergenceComparisonToFile(const std::map<AlgorithmType, TaskAllocationResult>& results,
    const std::string& filename) {
    try {
        std::ofstream outFile(filename);
        if (!outFile.is_open()) {
            std::cerr << "�޷������ļ�: " << filename << std::endl;
            return false;
        }

        // �ҳ�����������
        size_t maxIterations = 0;
        for (const auto& result : results) {
            maxIterations = std::max(maxIterations, result.second.convergenceHistory.size());
        }

        // д���ͷ
        outFile << "��������";
        for (const auto& result : results) {
            outFile << "\t" << TaskAllocator::getAlgorithmName(result.first);
        }
        outFile << std::endl;

        // д��ÿ�ε�����Ŀ�꺯��ֵ
        for (size_t iter = 0; iter < maxIterations; ++iter) {
            outFile << iter + 1;
            for (const auto& result : results) {
                if (iter < result.second.convergenceHistory.size()) {
                    outFile << "\t" << std::fixed << std::setprecision(8) << result.second.convergenceHistory[iter];
                }
                else {
                    outFile << "\t-"; // ������㷨û����ô���������������"-"��ʾ
                }
            }
            outFile << std::endl;
        }

        outFile.close();
        std::cout << "�����㷨�������̶Ա��ѱ�����: " << filename << std::endl;

        // ���Ᵽ�����ʱ����Ϣ
        std::string timeFilename = filename.substr(0, filename.find_last_of('.')) + "_iteration_times.csv";
        std::ofstream timeFile(timeFilename);
        if (!timeFile.is_open()) {
            std::cerr << "�޷������ļ�: " << timeFilename << std::endl;
            return false;
        }

        // д��CSV����
        timeFile << "�㷨,ƽ������ʱ��(ms)" << std::endl;

        // д��ÿ���㷨��ƽ������ʱ��
        for (const auto& result : results) {
            timeFile << TaskAllocator::getAlgorithmName(result.first) << ","
                << result.second.avgIterationTime << std::endl;
        }

        timeFile.close();
        std::cout << "�㷨ƽ������ʱ���ѱ�����: " << timeFilename << std::endl;

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "�����������̶Ա�ʱ����: " << e.what() << std::endl;
        return false;
    }
}


// ������
int main() {


    std::cout << "=== ʵ��2����������㷨�Ƚ� ===" << std::endl;

    // ��ȡ����ͽڵ�����
    std::string taskFile = "tasks.txt";
    std::string nodeFile = "nodes.txt";

    std::cout << "���ڶ�ȡ���������ļ�: " << taskFile << std::endl;
    std::vector<Task> tasks = loadTasks(taskFile);
    std::cout << "�ɹ���ȡ " << tasks.size() << " ������ʱ��1��" << std::endl;

    std::cout << "���ڶ�ȡ�ڵ������ļ�: " << nodeFile << std::endl;
    std::vector<Node> nodes = loadNodes(nodeFile);
    std::cout << "�ɹ���ȡ " << nodes.size() << " ���ڵ�" << std::endl;

    if (tasks.empty() || nodes.empty()) {
        std::cerr << "û���㹻�����ݽ���ʵ�飬�����������ݡ�" << std::endl;
        return 1;
    }

    // ����Ŀ�꺯��Ȩ��
    ObjectiveWeights objWeights;
    std::cout << "\n������Ŀ�꺯��Ȩ�ز���������5��0-1֮��ĸ��������ܺ�Ϊ1��\n"
        << "���磺0.2 0.2 0.2 0.2 0.2" << std::endl;
    std::cout << "��Դ������Ȩ��(alpha): ";
    std::cin >> objWeights.alpha;
    std::cout << "��Ӧʱ��Ȩ��(beta): ";
    std::cin >> objWeights.beta;
    std::cout << "MakespanȨ��(gamma): ";
    std::cin >> objWeights.gamma;
    std::cout << "���ؾ����Ȩ��(epsilon): ";
    std::cin >> objWeights.epsilon;
    std::cout << "������ʱ��Ȩ��(xi): ";
    std::cin >> objWeights.xi;

    // �������������
    TaskAllocator allocator(tasks, nodes, objWeights);

    // �洢ÿ���㷨�Ľ��
    std::map<AlgorithmType, TaskAllocationResult> results;

    std::cout << "\n��ʼ�����㷨ʵ��...\n" << std::endl;

    // ���������㷨
    std::vector<AlgorithmType> algorithms = {
        ROUND_ROBIN, 
        GENETIC, 
        PARTICLE_SWARM, 
        BEE_COLONY,
        //ANT_COLONY,
        BAT, 
        WHALE, 
        SIMULATED_ANNEALING, 
        GREY_WOLF,
        HYBRID_GW_SA
    };

    for (AlgorithmType alg : algorithms) {
        std::cout << "����ִ�� " << TaskAllocator::getAlgorithmName(alg) << "..." << std::endl;
        TaskAllocationResult result = allocator.allocateTasks(alg);
        results[alg] = result;

        // ��ӡ���
        std::cout << "  ��Դ������: " << result.resourceUtilization << std::endl;
        std::cout << "  ��Ӧʱ��: " << result.executionTime.count() << "ms" << std::endl;
        std::cout << "  Makespan: " << result.makespan << std::endl;
        std::cout << "  ���ؾ����: " << result.loadBalanceDegree << std::endl;
        std::cout << "  ������ʱ��: " << result.totalTaskTime << std::endl;
        std::cout << "  Ŀ�꺯��ֵ: " << result.objectiveValue << std::endl;
        std::cout << std::endl;
    }

    // ������
    saveResultsToCSV("task_allocation_results.csv", results);

    // ������㷨����ϸ���
    for (AlgorithmType alg : algorithms) {
        std::string filename = "result_" + TaskAllocator::getAlgorithmName(alg) + ".txt";
        // �滻�ļ����еĿո�������ַ�
        std::replace(filename.begin(), filename.end(), ' ', '_');
        std::replace(filename.begin(), filename.end(), '-', '_');

        TaskAllocator::saveAllocationResultToFile(results[alg], alg, filename);
    }

    // ���������㷨�ıȽϽ��
    TaskAllocator::saveComparisonResultsToFile(results, "algorithm_comparison_results.txt");

    std::cout << "\n���н���ѱ��浽TXT�ļ��С�" << std::endl;


    // �ҳ������㷨
    AlgorithmType bestAlgorithm = algorithms[0];
    double bestObjectiveValue = results[bestAlgorithm].objectiveValue;

    for (const auto& result : results) {
        if (result.second.objectiveValue < bestObjectiveValue) {
            bestAlgorithm = result.first;
            bestObjectiveValue = result.second.objectiveValue;
        }
    }

    saveAllConvergenceComparisonToFile(results, "all_algorithms_convergence_comparison.txt");

    std::cout << "\n�����㷨�У�" << TaskAllocator::getAlgorithmName(bestAlgorithm)
        << " ���������ţ�Ŀ�꺯��ֵΪ: " << bestObjectiveValue << std::endl;

    std::cout << "\nʵ����ɣ�����ѱ��浽 task_allocation_results.csv" << std::endl;

    std::cout << "\n��������˳�..." << std::endl;
    std::cin.get();
    std::cin.get();

    return 0;
}