#include "data.h"
#include <iostream>
#include <fstream>
#include <random>
#include <algorithm>
#include <ctime>

// ��ȡ�������
static std::default_random_engine getRandomEngine() {
    static std::random_device rd;
    // ʹ�õ�ǰʱ����Ϊ�����Ի�ø��õ������
    return std::default_random_engine(rd() ^ std::time(nullptr));
}

// ����ָ����Χ�ڵ��������
static int getRandomInt(std::default_random_engine& engine, int min, int max) {
    if (min > max) {
        std::swap(min, max);
    }
    std::uniform_int_distribution<int> dist(min, max);
    return dist(engine);
}

// ����ָ����Χ�ڵ����������
static double getRandomDouble(std::default_random_engine& engine, double min, double max) {
    std::uniform_real_distribution<double> dist(min, max);
    return dist(engine);
}

// ������̬�ֲ����������
static int getRandomNormalInt(std::default_random_engine& engine, int min, int max) {
    // ��ֹmin��max��Ȼ�ǳ��ӽ�
    if (max <= min + 1) {
        return min;
    }

    // �����ֵ�ͱ�׼��
    double mean = (min + max) / 2.0;
    double stddev = (max - min) / 6.0; // Լ95%��ֵ�ھ�ֵ��3����׼����

    // ȷ����׼�����0
    if (stddev <= 0.0) {
        // �����׼����Ч�����þ��ȷֲ�
        return getRandomInt(engine, min, max);
    }

    std::normal_distribution<double> dist(mean, stddev);

    // ȷ�����ɵ�ֵ�ڷ�Χ��
    int value;
    int attempts = 0;
    const int MAX_ATTEMPTS = 100; // ��ֹ����ѭ��

    do {
        value = static_cast<int>(dist(engine));
        attempts++;
        // �����γ���ʧ�ܣ��˻ص����ȷֲ�
        if (attempts >= MAX_ATTEMPTS) {
            return getRandomInt(engine, min, max);
        }
    } while (value < min || value > max);

    return value;
}

// ȷ���ڵ�����
static NodeType determineNodeType(const NodeResource& resource, const NodeGenerationParams& params) {
    // ��������Դ
    double totalResource = params.cpuWeight * resource.cpuCapacity +
        params.memoryWeight * resource.memoryCapacity +
        params.networkWeight * resource.bandwidthCapacity;

    // �������Դռ��
    double cpuRatio = (params.cpuWeight * resource.cpuCapacity) / totalResource;
    double memoryRatio = (params.memoryWeight * resource.memoryCapacity) / totalResource;
    double networkRatio = (params.networkWeight * resource.bandwidthCapacity) / totalResource;

    // �ж���Դ����
    if (cpuRatio > 0.6) {
        return CPU_INTENSIVE;
    }
    else if (memoryRatio > 0.6) {
        return MEMORY_INTENSIVE;
    }
    else if (networkRatio > 0.6) {
        return NETWORK_INTENSIVE;
    }
    else {
        return GENERAL;
    }
}

bool DataGenerator::generateNodeDataset(const std::string& filename, const NodeGenerationParams& params) {
    try {
        std::ofstream outFile(filename);
        if (!outFile.is_open()) {
            std::cerr << "�޷����ļ�: " << filename << std::endl;
            return false;
        }

        // д�������
        outFile << "�ڵ��� ʱ�� CPU��׼�������� �ڴ��׼������ ���������� ��ǰCPU�������� ��ǰ�ڴ����� ��ǰ������� �ڵ����" << std::endl;

        auto engine = getRandomEngine();

        // ����ÿ�����͵�Ŀ��ڵ�����
        int totalNodes = params.numNodes;
        int targetCpuIntensive = totalNodes * 0.3;  // 30%
        int targetMemoryIntensive = totalNodes * 0.3;  // 30%
        int targetNetworkIntensive = totalNodes * 0.3;  // 30%
        int targetGeneral = totalNodes * 0.1;  // 10%

        // ����ÿ�����͵Ľڵ�
        int cpuIntensiveCount = 0;
        int memoryIntensiveCount = 0;
        int networkIntensiveCount = 0;
        int generalCount = 0;

        for (int nodeId = 1; nodeId <= params.numNodes; ++nodeId) {
            NodeResource resource;
            NodeType nodeType;

            // ���ݵ�ǰ�����ɵĸ����ͽڵ�������������һ���ڵ������
            if (cpuIntensiveCount < targetCpuIntensive &&
                (memoryIntensiveCount >= targetMemoryIntensive ||
                    networkIntensiveCount >= targetNetworkIntensive ||
                    getRandomDouble(engine, 0.0, 1.0) < 0.4)) {
                // ����CPU�ܼ��ͽڵ�
                resource.cpuCapacity = getRandomInt(engine, params.maxCpuCapacity * 0.6, params.maxCpuCapacity);
                resource.memoryCapacity = getRandomInt(engine, params.minMemoryCapacity, params.maxMemoryCapacity * 0.5);
                resource.bandwidthCapacity = getRandomInt(engine, params.minBandwidthCapacity, params.maxBandwidthCapacity * 0.5);
                nodeType = CPU_INTENSIVE;
                cpuIntensiveCount++;

            }
            else if (memoryIntensiveCount < targetMemoryIntensive &&
                (networkIntensiveCount >= targetNetworkIntensive ||
                    getRandomDouble(engine, 0.0, 1.0) < 0.5)) {
                // �����ڴ��ܼ��ͽڵ�
                resource.cpuCapacity = getRandomInt(engine, params.minCpuCapacity, params.maxCpuCapacity * 0.5);
                resource.memoryCapacity = getRandomInt(engine, params.maxMemoryCapacity * 0.6, params.maxMemoryCapacity);
                resource.bandwidthCapacity = getRandomInt(engine, params.minBandwidthCapacity, params.maxBandwidthCapacity * 0.5);
                nodeType = MEMORY_INTENSIVE;
                memoryIntensiveCount++;
            }
            else if (networkIntensiveCount < targetNetworkIntensive) {
                // ���������ܼ��ͽڵ�
                resource.cpuCapacity = getRandomInt(engine, params.minCpuCapacity, params.maxCpuCapacity * 0.5);
                resource.memoryCapacity = getRandomInt(engine, params.minMemoryCapacity, params.maxMemoryCapacity * 0.5);
                resource.bandwidthCapacity = getRandomInt(engine, params.maxBandwidthCapacity * 0.6, params.maxBandwidthCapacity);
                nodeType = NETWORK_INTENSIVE;
                networkIntensiveCount++;
            }
            else {
                // ������ͨ�ڵ�
                resource.cpuCapacity = getRandomInt(engine, params.minCpuCapacity, params.maxCpuCapacity * 0.6);
                resource.memoryCapacity = getRandomInt(engine, params.minMemoryCapacity, params.maxMemoryCapacity * 0.6);
                resource.bandwidthCapacity = getRandomInt(engine, params.minBandwidthCapacity, params.maxBandwidthCapacity * 0.6);
                nodeType = GENERAL;
                generalCount++;
            }

            // ���㵱ǰ��Դ����׼������10%-20%��
            double currentRatio = getRandomDouble(engine, params.currentResourceRatioMin, params.currentResourceRatioMax);
            resource.currentCpu = static_cast<int>(resource.cpuCapacity * currentRatio);
            resource.currentMemory = static_cast<int>(resource.memoryCapacity * currentRatio);
            resource.currentBandwidth = static_cast<int>(resource.bandwidthCapacity * currentRatio);

            // д��ڵ���Ϣ
            outFile << nodeId << " "
                << 0 << " " // ��ʼʱ��Ϊ0
                << resource.cpuCapacity << " "
                << resource.memoryCapacity << " "
                << resource.bandwidthCapacity << " "
                << resource.currentCpu << " "
                << resource.currentMemory << " "
                << resource.currentBandwidth << " "
                << static_cast<int>(nodeType) << std::endl;
        }

        outFile.close();

        // ����ڵ�����ͳ��
        std::cout << "�ڵ�����ͳ�ƣ�" << std::endl;
        std::cout << "CPU�ܼ��ͽڵ�����: " << cpuIntensiveCount << " (" << (100.0 * cpuIntensiveCount / params.numNodes) << "%)" << std::endl;
        std::cout << "�ڴ��ܼ��ͽڵ�����: " << memoryIntensiveCount << " (" << (100.0 * memoryIntensiveCount / params.numNodes) << "%)" << std::endl;
        std::cout << "�����ܼ��ͽڵ�����: " << networkIntensiveCount << " (" << (100.0 * networkIntensiveCount / params.numNodes) << "%)" << std::endl;
        std::cout << "��ͨ�ڵ�����: " << generalCount << " (" << (100.0 * generalCount / params.numNodes) << "%)" << std::endl;

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "���ɽڵ����ݼ�ʱ����: " << e.what() << std::endl;
        return false;
    }
}