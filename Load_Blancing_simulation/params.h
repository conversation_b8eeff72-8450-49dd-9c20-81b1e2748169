// params.h
#pragma once

struct SimulationParams {
    double wCpu = 0.3;
    double wMem = 0.3;
    double wNet = 0.4;
    double cpuEfficiency = 0.85;
    double memEfficiency = 0.95;
    double netEfficiency = 0.9;
    double cpuDecayFactor = 0.75;
    double networkSafeThreshold = 0.8;
};

struct NodeGenerationParams {
    int numNodes = 100;
    // �����ڵ����ɲ���...
};

struct TaskGenerationParams {
    int timeSteps = 10;
    // �����������ɲ���...
};
