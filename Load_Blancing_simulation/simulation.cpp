// simulation.cpp
#include "simulation.h"



Simulator::Simulator(const SimulationParams& p)
    : params(p),
    currentTimeStep(0),
    simulationTime(0.0),
    timeInterval(1.0),
    wCpu(p.wCpu),
    wMem(p.wMem),
    wNet(p.wNet),
    cpuEfficiency(p.cpuEfficiency),
    memEfficiency(p.memEfficiency),
    netEfficiency(p.netEfficiency),
    cpuDecayFactor(p.cpuDecayFactor) 
{
    // ��ʼ��������Ա...
}


// Task implementation
Task::Task(int id, int timeStep, int cpu, int mem, int bw, const std::vector<int>& deps)
    : id(id), timeStep(timeStep), cpuDemand(cpu), memoryDemand(mem), bandwidthDemand(bw),
    dependencies(deps), resourceType(400), level(1), assigned(false),
    assignedNode(-1), remainingTime(0), completed(false) {}

// Node implementation
Node::Node(int id, double cpuCap, double memTotal, double bwTotal, double cpuLoad,
    double bwRatio, double netCong, double linkQual)
    : id(id), baseCpu(cpuCap), totalMemory(memTotal), totalBandwidth(bwTotal),
    effectiveCpu(cpuCap), availableMemory(memTotal),
    realBandwidth(bwTotal* bwRatio* netCong* linkQual),
    bandwidthRatio(bwRatio), linkQuality(linkQual), congestionCoeff(netCong),
    groupType(400), isMonitor(false), cpuLoadRate(cpuLoad),
    memoryLoadRate(0), networkLoadRate(0), compositeLoadRate(0) {}

double Node::calculatePerformance(double wCpu, double wMem, double wNet) const {
    return wCpu * effectiveCpu + wMem * availableMemory + wNet * realBandwidth;
}

void Node::updateLoad(double wCpu, double wMem, double wNet) {
    // CPU���ؼ��㣨������ʷ����Ӱ�죩
    static double historicalLoad = 0.0;
    const double lambda = 0.15; // ����ϵ��

    double historicalFactor = 0.2 * exp(-0.1); // �򻯵�ָ��˥����
    double cpuSum = 0;
    for (const auto& task : taskQueue) {
        cpuSum += task.cpuDemand;
    }
    // �¸��ؼ���
    double newCpuLoad = (cpuSum / baseCpu) * (1 + historicalFactor);
    cpuLoadRate = lambda * historicalLoad + (1 - lambda) * newCpuLoad;
    historicalLoad = cpuLoadRate;

    // �ڴ渺�ؼ���
    double memSum = 0;
    for (const auto& task : taskQueue) {
        memSum += task.memoryDemand;
    }
    memoryLoadRate = tanh(memSum / totalMemory);

    // ��������ؼ���
    double netSum = 0;
    for (const auto& task : taskQueue) {
        netSum += task.bandwidthDemand;
    }
    networkLoadRate = std::min(1.0, netSum / (0.8 * totalBandwidth));

    // �ۺϸ��ؼ���
    compositeLoadRate = (wCpu * cpuLoadRate + wMem * memoryLoadRate + wNet * networkLoadRate)
        / (wCpu + wMem + wNet);
}

bool Node::canAcceptTask(const Task& task, double cpuEfficiency, double memEfficiency, double netEfficiency) const {
    // �����Դ����Լ��
    double cpuSum = 0;
    double memSum = 0;
    double netSum = 0;

    for (const auto& t : taskQueue) {
        cpuSum += t.cpuDemand;
        memSum += t.memoryDemand;
        netSum += t.bandwidthDemand;
    }

    cpuSum += task.cpuDemand;
    memSum += task.memoryDemand;
    netSum += task.bandwidthDemand;

    bool cpuConstraint = cpuSum <= effectiveCpu * cpuEfficiency;
    bool memConstraint = memSum <= availableMemory * memEfficiency;
    bool netConstraint = netSum <= realBandwidth * netEfficiency;

    return cpuConstraint && memConstraint && netConstraint;
}

void Node::addTask(int taskId, const Task& task) {
    // ��������ִ��ʱ��

    double totalCpuDemand = 0;
    for (const auto& t : taskQueue) {
        totalCpuDemand += t.cpuDemand;
    }
    totalCpuDemand += task.cpuDemand;

    double totalTime = task.cpuDemand / effectiveCpu +
        task.memoryDemand / availableMemory +
        task.bandwidthDemand / realBandwidth;

    // ��ӵ�����
    TaskInfo taskInfo;
    taskInfo.taskId = taskId;
    taskInfo.remainingTime = totalTime;
    taskInfo.cpuDemand = task.cpuDemand;
    taskInfo.memoryDemand = task.memoryDemand;
    taskInfo.bandwidthDemand = task.bandwidthDemand;

    taskQueue.push_back(taskInfo);

    // ������Դ
    effectiveCpu = baseCpu * pow(1 - task.cpuDemand / baseCpu, 0.8); // ������˥��

    availableMemory -= task.memoryDemand;

    // ���´��� (�ֶ�ģ��)
    double totalTaskBandwidth = 0;
    for (const auto& t : taskQueue) {
        totalTaskBandwidth += t.bandwidthDemand;
    }

    if (totalTaskBandwidth <= 0.8 * totalBandwidth) {
        realBandwidth = totalBandwidth * bandwidthRatio * linkQuality * congestionCoeff;
    }
    else {
        realBandwidth = totalBandwidth * bandwidthRatio * linkQuality *
            (1 - (totalTaskBandwidth - 0.8 * totalBandwidth) / (0.2 * totalBandwidth));
    }
}

void Node::releaseTask(int taskId, const Task& task, double cpuEfficiency, double memEfficiency) {
    // �Ӷ������Ƴ�����
    auto it = std::find_if(taskQueue.begin(), taskQueue.end(),
        [taskId](const TaskInfo& info) {
            return info.taskId == taskId;
        });

    if (it != taskQueue.end()) {
        int cpuDemand = it->cpuDemand;
        int memDemand = it->memoryDemand;
        int bwDemand = it->bandwidthDemand;

        taskQueue.erase(it);

        // ������Դ
        effectiveCpu += cpuDemand * cpuEfficiency;
        availableMemory += memDemand * memEfficiency;

        // ���¼���CPU������ʣ������
        double remainingCpu = 0;
        for (const auto& t : taskQueue) {
            remainingCpu += t.cpuDemand;
        }
        effectiveCpu = baseCpu * pow(1 - remainingCpu / baseCpu, 0.8);


        // ��������ֶ�ģ��
        double totalBW = 0;
        for (const auto& t : taskQueue) {
            totalBW += t.bandwidthDemand;
        }

        if (totalBW <= 0.8 * totalBandwidth) {
            realBandwidth = totalBandwidth * bandwidthRatio * linkQuality * congestionCoeff;
        }
        else {
            realBandwidth = totalBandwidth * bandwidthRatio * linkQuality *
                (1 - (totalBW - 0.8 * totalBandwidth) / (0.2 * totalBandwidth));
        }
    }
}

// �޸�updateResources����
void Node::updateResources(double deltaTime, const SimulationParams& params) {
    // �����Դʹ��������
    double cpuSum = 0.0;
    double memSum = 0.0;
    double netSum = 0.0;

    for (const auto& task : taskQueue) {
        cpuSum += task.cpuDemand;
        memSum += task.memoryDemand;
        netSum += task.bandwidthDemand;
    }

    // ʹ����Щ�������к�������
    effectiveCpu = baseCpu * pow(1 - cpuSum / baseCpu, params.cpuDecayFactor);

    if (netSum > params.networkSafeThreshold * totalBandwidth) {
        realBandwidth = totalBandwidth * bandwidthRatio * linkQuality *
            (1 - (netSum - params.networkSafeThreshold * totalBandwidth) /
                ((1 - params.networkSafeThreshold) * totalBandwidth));
    }
}




bool Simulator::loadNodes(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open node file: " << filename << std::endl;
        return false;
    }

    std::string line;
    // ����������
    std::getline(file, line);

    while (std::getline(file, line)) {
        std::istringstream iss(line);
        int id;
        double cpuCap, cpuLoad, memTotal, memUsed, bwRatio, netCong, linkQual, commAbility;

        if (!(iss >> id >> cpuCap >> cpuLoad >> memTotal >> memUsed >>
            bwRatio >> netCong >> linkQual >> commAbility)) {
            continue;
        }

        // �����ڵ㲢��ӵ��ڵ��б�
        nodes.emplace_back(id, cpuCap, memTotal, commAbility, cpuLoad, bwRatio, netCong, linkQual);
    }

    std::cout << "Loaded " << nodes.size() << " nodes." << std::endl;
    return true;
}

bool Simulator::loadTasks(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open task file: " << filename << std::endl;
        return false;
    }

    std::string line;
    // ����������
    std::getline(file, line);

    while (std::getline(file, line)) {
        std::istringstream iss(line);
        int id, timeStep, cpu, mem, bw;

        if (!(iss >> id >> timeStep >> cpu >> mem >> bw)) {
            continue;
        }

        // ��ȡ��������
        std::vector<int> deps;
        int dep;
        while (iss >> dep) {
            deps.push_back(dep);
        }

        // ����������ӵ�����ӳ��
        tasks[timeStep][id] = Task(id, timeStep, cpu, mem, bw, deps);
    }

    int totalTasks = 0;
    for (const auto& pair : tasks) {
        totalTasks += pair.second.size();
    }

    std::cout << "Loaded " << totalTasks << " tasks across " << tasks.size() << " time steps." << std::endl;
    return true;
}

void Simulator::calculateTaskLevels(int timeStep) {
    auto& timeStepTasks = tasks[timeStep];
    std::unordered_map<int, int> levels;

    // ��һ�α���: ��ʼ��û������������Ϊ1��
    for (auto& pair : timeStepTasks) {
        int taskId = pair.first;
        Task& task = pair.second;

        if (task.dependencies.empty()) {
            levels[taskId] = 1;
        }
    }

    // ��������㼶
    bool updated = true;
    while (updated) {
        updated = false;

        for (auto& pair : timeStepTasks) {
            int taskId = pair.first;
            Task& task = pair.second;

            // ����������в㼶������
            if (levels.find(taskId) != levels.end()) {
                continue;
            }

            // ����������������Ƿ��в㼶
            bool allDepsHaveLevel = true;
            int maxDepLevel = 0;

            for (int depId : task.dependencies) {
                if (levels.find(depId) == levels.end()) {
                    allDepsHaveLevel = false;
                    break;
                }
                maxDepLevel = std::max(maxDepLevel, levels[depId]);
            }

            if (allDepsHaveLevel) {
                levels[taskId] = maxDepLevel + 1;
                updated = true;
            }
        }
    }

    // ȷ�����������в㼶 (����ѭ������)
    for (auto& pair : timeStepTasks) {
        int taskId = pair.first;
        if (levels.find(taskId) == levels.end()) {
            levels[taskId] = 1; // Ĭ��Ϊ1��
        }

        // ���������level����
        pair.second.level = levels[taskId];
    }

    std::cout << "Task levels calculated." << std::endl;
}

void Simulator::colorTasks(int timeStep) {
    auto& timeStepTasks = tasks[timeStep];

    // ��ɫӳ���
    std::unordered_map<int, std::vector<std::string>> colorPalette = {
        {100, {"#FFEEEE", "#FFCCCC", "#FF9999", "#FF6666", "#FF0000"}}, // ��ɫ����-CPU�ܼ���
        {200, {"#EEFFEE", "#CCFFCC", "#99FF99", "#66FF66", "#00FF00"}}, // ��ɫ����-�ڴ��ܼ���
        {300, {"#EEEEFF", "#CCCCFF", "#9999FF", "#6666FF", "#0000FF"}}, // ��ɫ����-ͨ���ܼ���
        {400, {"#F5EFFF", "#E0B0FF", "#C87FFF", "#B04FFF", "#8000FF"}}  // ��ɫ����-Ĭ����
    };

    // ������Դ����ȷ����������
    for (auto& pair : timeStepTasks) {
        Task& task = pair.second;

        // �����ۺ�����ָ��R_i
        double taskR = wCpu * task.cpuDemand + wMem * task.memoryDemand + wNet * task.bandwidthDemand;

        // �ж���������
        if (wCpu * task.cpuDemand / taskR > 0.6) {
            task.resourceType = 100; // CPU�ܼ���
        }
        else if (wMem * task.memoryDemand / taskR > 0.6) {
            task.resourceType = 200; // �ڴ��ܼ���
        }
        else if (wNet * task.bandwidthDemand / taskR > 0.6) {
            task.resourceType = 300; // ͨ���ܼ���
        }
        else {
            task.resourceType = 400; // Ĭ����
        }

        // ���ݲ㼶������������ɫ
        int level = std::min(task.level, 5); // ���֧��5��
        int colorIndex = level - 1;
        task.color = colorPalette[task.resourceType][colorIndex];
    }

    std::cout << "Task coloring completed." << std::endl;
}

void Simulator::groupNodes() {
    // ���֮ǰ�ķ���
    cpuIntensiveGroup.clear();
    memIntensiveGroup.clear();
    netIntensiveGroup.clear();
    generalGroup.clear();

    // �������нڵ���з���
    for (Node& node : nodes) {
        // ����ڵ��ۺ�����
        double nodePerformance = node.calculatePerformance(wCpu, wMem, wNet);

        // �жϽڵ�����
        if (wCpu * node.effectiveCpu / nodePerformance > 0.6) {
            node.groupType = 100; // CPU�ܼ�����
            cpuIntensiveGroup.push_back(node.id);
        }
        else if (wMem * node.availableMemory / nodePerformance > 0.6) {
            node.groupType = 200; // �ڴ��ܼ�����
            memIntensiveGroup.push_back(node.id);
        }
        else if (wNet * node.realBandwidth / nodePerformance > 0.6) {
            node.groupType = 300; // ͨ���ܼ�����
            netIntensiveGroup.push_back(node.id);
        }
        else {
            node.groupType = 400; // ��ͨ�ڵ���
            generalGroup.push_back(node.id);
        }
    }

    std::cout << "Node grouping completed:" << std::endl;
    std::cout << "  CPU Intensive Group: " << cpuIntensiveGroup.size() << " nodes" << std::endl;
    std::cout << "  Memory Intensive Group: " << memIntensiveGroup.size() << " nodes" << std::endl;
    std::cout << "  Network Intensive Group: " << netIntensiveGroup.size() << " nodes" << std::endl;
    std::cout << "  General Group: " << generalGroup.size() << " nodes" << std::endl;
}

void Simulator::electMonitorNodes() {
    // ���֮ǰ�ļ�ؽڵ�
    monitorNodes.clear();

    // Ϊ���нڵ����ü��״̬
    for (Node& node : nodes) {
        node.isMonitor = false;
    }

    // ����ѡ�ټ�ؽڵ�
    auto electForGroup = [this](const std::vector<int>& group, int groupType) {
        if (group.empty()) return;

        int bestNodeId = -1;
        double bestPerformance = -1;

        // �ҳ�����������ߵĽڵ�
        for (int nodeId : group) {
            Node& node = nodes[nodeId - 1]; // ����ڵ�ID��1��ʼ
            double performance = node.calculatePerformance(wCpu, wMem, wNet);

            if (performance > bestPerformance) {
                bestNodeId = nodeId;
                bestPerformance = performance;
            }
        }

        if (bestNodeId != -1) {
            nodes[bestNodeId - 1].isMonitor = true;
            monitorNodes[groupType] = bestNodeId;
        }
        };

    // Ϊÿ����ѡ�ټ�ؽڵ�
    electForGroup(cpuIntensiveGroup, 100);
    electForGroup(memIntensiveGroup, 200);
    electForGroup(netIntensiveGroup, 300);
    electForGroup(generalGroup, 400);

    std::cout << "Monitor nodes elected:" << std::endl;
    for (const auto& pair : monitorNodes) {
        std::string groupName;
        switch (pair.first) {
        case 100: groupName = "CPU Intensive"; break;
        case 200: groupName = "Memory Intensive"; break;
        case 300: groupName = "Network Intensive"; break;
        case 400: groupName = "General"; break;
        }
        std::cout << "  " << groupName << " Group: Node " << pair.second << std::endl;
    }
}

void Simulator::simpleTaskAssignment(int timeStep) {
    auto& timeStepTasks = tasks[timeStep];

    // ��simpleTaskAssignment�иĽ��������
    

    for (auto& pair : timeStepTasks) {
        Task& task = pair.second;
        bool dependenciesMet = true;

        // ��ʱ�䲽������飨�������룩
        for (int depId : task.dependencies) {
            bool depCompleted = false;
            for (int t = 1; t < currentTimeStep; ++t) { // �����֮ǰʱ�䲽
                if (tasks[t].count(depId) && tasks[t][depId].completed) {
                    depCompleted = true;
                    break;
                }
            }
            if (!depCompleted) {
                dependenciesMet = false;
                break;
            }
        }


        // ֻ�е���������������ʱ���Ž�������Ϊ�ɷ���
        if (!dependenciesMet) {
            task.assigned = true; // �������δ���㣬���ȱ��Ϊ�ѷ��䣨ʵ�������ݲ����䣩
        }
    }

    // ����ǰʱ�̵Ŀɷ���������Դ���ͷ���
    std::vector<Task*> cpuIntensiveTasks;
    std::vector<Task*> memIntensiveTasks;
    std::vector<Task*> netIntensiveTasks;
    std::vector<Task*> generalTasks;

    for (auto& pair : timeStepTasks) {
        Task* task = &pair.second;
        if (!task->assigned) {  // ֻѡ��δ���������������������
            switch (task->resourceType) {
            case 100: cpuIntensiveTasks.push_back(task); break;
            case 200: memIntensiveTasks.push_back(task); break;
            case 300: netIntensiveTasks.push_back(task); break;
            case 400: generalTasks.push_back(task); break;
            }
        }
    }





    // ��̰�ķ��䣺���սڵ�������Ӧ���͵�����
    auto assignTasksToGroup = [this](const std::vector<Task*>& tasks, const std::vector<int>& group) {
        if (tasks.empty() || group.empty()) return;

        // ��������㼶�����ȷ���߲㼶����
        std::vector<Task*> sortedTasks = tasks;
        std::sort(sortedTasks.begin(), sortedTasks.end(),
            [](const Task* a, const Task* b) { return a->level > b->level; });

        for (Task* task : sortedTasks) {
            // Ѱ�Ҹ�����͵Ľڵ�
            int bestNodeId = -1;
            double minLoad = std::numeric_limits<double>::max();

            for (int nodeId : group) {
                Node& node = nodes[nodeId - 1];
                if (node.canAcceptTask(*task, cpuEfficiency, memEfficiency, netEfficiency) &&
                    node.compositeLoadRate < minLoad) {
                    bestNodeId = nodeId;
                    minLoad = node.compositeLoadRate;
                }
            }

            // ����ҵ����ʵĽڵ㣬��������
            if (bestNodeId != -1) {
                task->assigned = true;
                task->assignedNode = bestNodeId;
                nodes[bestNodeId - 1].addTask(task->id, *task);
                std::cout << "  Task " << task->id << " assigned to Node " << bestNodeId << std::endl;
                // ���� simpleTaskAssignment ����
            }
            else {
                // ���û�к��ʵĽڵ㣬���������нڵ���Ѱ��
                for (Node& node : nodes) {
                    if (node.canAcceptTask(*task, cpuEfficiency, memEfficiency, netEfficiency)) {
                        task->assigned = true;
                        task->assignedNode = node.id;
                        node.addTask(task->id, *task);
                        std::cout << "  Task " << task->id << " assigned to Node " << node.id
                            << " (fallback)" << std::endl;
                        break;
                    }
                }

                // �����Ȼ�޷����䣬���������
                if (!task->assigned) {
                    std::cout << "  Warning: Task " << task->id << " cannot be assigned to any node!" << std::endl;
                }
            }
        }
        };




    // ��������ͽڵ����ͽ��з���
    std::cout << "Assigning CPU intensive tasks..." << std::endl;
    assignTasksToGroup(cpuIntensiveTasks, cpuIntensiveGroup);

    std::cout << "Assigning Memory intensive tasks..." << std::endl;
    assignTasksToGroup(memIntensiveTasks, memIntensiveGroup);

    std::cout << "Assigning Network intensive tasks..." << std::endl;
    assignTasksToGroup(netIntensiveTasks, netIntensiveGroup);

    std::cout << "Assigning General tasks..." << std::endl;
    assignTasksToGroup(generalTasks, generalGroup);
}

void Simulator::updateNodeResources(double deltaTime) {
    for (Node& node : nodes) {
        node.updateResources(deltaTime, params); // ����params����
        node.updateLoad(wCpu, wMem, wNet);
    }
}

void Simulator::updateTaskStatus(double deltaTime) {
    int completedCount = 0;

    // �������������״̬
    for (auto& timeStepPair : tasks) {
        int timeStep = timeStepPair.first;
        auto& timeStepTasks = timeStepPair.second;

        for (auto& taskPair : timeStepTasks) {
            Task& task = taskPair.second;

            // ֻ�����ѷ��䵫δ��ɵ�����
            if (task.assigned && !task.completed && task.assignedNode > 0) {
                // ����ʣ��ʱ��
                if (task.remainingTime == 0) {
                    // ��ʼ��ʣ��ʱ��
                    int nodeId = task.assignedNode;
                    Node& node = nodes[nodeId - 1];

                    // ��������������е���Ϣ
                    auto it = std::find_if(node.taskQueue.begin(), node.taskQueue.end(),
                        [&task](const Node::TaskInfo& info) {
                            return info.taskId == task.id;
                        });

                    if (it != node.taskQueue.end()) {
                        task.remainingTime = it->remainingTime;
                    }
                }
                else {
                    task.remainingTime -= deltaTime;
                }

                // ��������Ƿ����
                if (task.remainingTime <= 0) {
                    task.completed = true;
                    completedCount++;

                    std::cout << "Task " << task.id << " completed." << std::endl;
                }
            }
        }
    }

    std::cout << completedCount << " tasks completed in this time step." << std::endl;
}

double Simulator::calculateResourceUtilization() const {
    double totalCpuUsage = 0.0;
    double totalMemUsage = 0.0;
    double totalNetUsage = 0.0;
    double totalCpuCapacity = 0.0;
    double totalMemCapacity = 0.0;
    double totalNetCapacity = 0.0;

    for (const Node& node : nodes) {
        // ����ʹ����
        for (const auto& task : node.taskQueue) {
            totalCpuUsage += task.cpuDemand;
            totalMemUsage += task.memoryDemand;
            totalNetUsage += task.bandwidthDemand;
        }

        // ͳ������
        totalCpuCapacity += node.baseCpu;
        totalMemCapacity += node.totalMemory;
        totalNetCapacity += node.totalBandwidth;
    }

    double ruCpu = totalCpuUsage / totalCpuCapacity;
    double ruMem = totalMemUsage / totalMemCapacity;
    double ruNet = totalNetUsage / totalNetCapacity;

    return 1.0 - (wCpu * ruCpu + wMem * ruMem + wNet * ruNet);
}

double Simulator::calculateResponseTime() const {
    // ��ʵ�֣�ʹ�ù̶�ֵ
    return 0.01;
}

double Simulator::calculateMakespan() const {
    double maxTime = 0.0;

    for (const Node& node : nodes) {
        double nodeTime = 0.0;
        for (const auto& task : node.taskQueue) {
            nodeTime += task.remainingTime;
        }
        maxTime = std::max(maxTime, nodeTime);
    }

    return maxTime;
}

double Simulator::calculateLoadBalance() const {
    double sumLoad = 0.0;

    for (const Node& node : nodes) {
        sumLoad += node.compositeLoadRate;
    }

    double avgLoad = sumLoad / nodes.size();
    double variance = 0.0;

    for (const Node& node : nodes) {
        variance += std::pow(node.compositeLoadRate - avgLoad, 2);
    }

    return variance / nodes.size();
}

double Simulator::calculateTotalTaskTime() const {
    int totalTasks = 0;
    double totalTime = 0.0;

    for (const auto& timeStepPair : tasks) {
        for (const auto& taskPair : timeStepPair.second) {
            const Task& task = taskPair.second;
            if (task.assigned) {
                totalTasks++;

                // �����������ɣ�ʹ����ִ��ʱ�䣻����ʹ�õ�ǰʣ��ʱ��
                if (task.completed) {
                    // ����򻯴���ʹ�ù̶������ʱ��
                    totalTime += 1.0;
                }
                else {
                    totalTime += task.remainingTime;
                }
            }
        }
    }

    return (totalTasks > 0) ? totalTime / totalTasks : 0.0;
}

void Simulator::visualizeNodeStatus() const {
    std::cout << "\n=== Node Status Visualization ===\n";
    std::cout << std::left << std::setw(5) << "ID"
        << std::setw(10) << "Group"
        << std::setw(10) << "Monitor"
        << std::setw(12) << "CPU Load"
        << std::setw(12) << "Mem Load"
        << std::setw(12) << "Net Load"
        << std::setw(12) << "Composite"
        << std::setw(10) << "Tasks"
        << std::endl;

    std::cout << std::string(83, '-') << std::endl;

    for (const Node& node : nodes) {
        std::string groupName;
        switch (node.groupType) {
        case 100: groupName = "CPU"; break;
        case 200: groupName = "Memory"; break;
        case 300: groupName = "Network"; break;
        case 400: groupName = "General"; break;
        default: groupName = "Unknown";
        }

        std::cout << std::left << std::setw(5) << node.id
            << std::setw(10) << groupName
            << std::setw(10) << (node.isMonitor ? "Yes" : "No")
            << std::setw(12) << std::fixed << std::setprecision(2) << node.cpuLoadRate * 100 << "%"
            << std::setw(12) << std::fixed << std::setprecision(2) << node.memoryLoadRate * 100 << "%"
            << std::setw(12) << std::fixed << std::setprecision(2) << node.networkLoadRate * 100 << "%"
            << std::setw(12) << std::fixed << std::setprecision(2) << node.compositeLoadRate * 100 << "%"
            << std::setw(10) << node.taskQueue.size()
            << std::endl;
    }
}

void Simulator::visualizeTaskStatus(int timeStep) const {
    if (tasks.find(timeStep) == tasks.end()) {
        std::cout << "No tasks for time step " << timeStep << std::endl;
        return;
    }

    const auto& timeStepTasks = tasks.at(timeStep);

    std::cout << "\n=== Task Status for Time Step " << timeStep << " ===\n";
    std::cout << std::left << std::setw(5) << "ID"
        << std::setw(10) << "Type"
        << std::setw(8) << "Level"
        << std::setw(10) << "Status"
        << std::setw(10) << "Node"
        << std::setw(15) << "Remaining Time"
        << std::setw(15) << "Dependencies"
        << std::endl;

    std::cout << std::string(73, '-') << std::endl;

    for (const auto& pair : timeStepTasks) {
        const Task& task = pair.second;

        std::string typeName;
        switch (task.resourceType) {
        case 100: typeName = "CPU"; break;
        case 200: typeName = "Memory"; break;
        case 300: typeName = "Network"; break;
        case 400: typeName = "General"; break;
        default: typeName = "Unknown";
        }

        std::string status;
        if (task.completed) {
            status = "Completed";
        }
        else if (task.assigned && task.assignedNode > 0) {
            status = "Running";
        }
        else if (task.assigned) {
            status = "Waiting Dep";
        }
        else {
            status = "Pending";
        }

        std::ostringstream depStream;
        for (size_t i = 0; i < task.dependencies.size(); ++i) {
            depStream << task.dependencies[i];
            if (i < task.dependencies.size() - 1) {
                depStream << ",";
            }
        }

        std::cout << std::left << std::setw(5) << task.id
            << std::setw(10) << typeName
            << std::setw(8) << task.level
            << std::setw(10) << status
            << std::setw(10) << (task.assignedNode > 0 ? std::to_string(task.assignedNode) : "-")
            << std::setw(15) << std::fixed << std::setprecision(2)
            << (task.assigned && task.assignedNode > 0 ? task.remainingTime : 0.0)
            << std::setw(15) << depStream.str()
            << std::endl;
    }
}

void Simulator::printResults() const {
    std::cout << "\n==== Simulation Results ====" << std::endl;
    std::cout << "Total simulation time: " << simulationTime << std::endl;

    // �������������
    int totalTasks = 0;
    int completedTasks = 0;

    for (const auto& timeStepPair : tasks) {
        for (const auto& taskPair : timeStepPair.second) {
            totalTasks++;
            if (taskPair.second.completed) {
                completedTasks++;
            }
        }
    }

    double completionRate = (totalTasks > 0) ? (static_cast<double>(completedTasks) / totalTasks) * 100 : 0.0;

    std::cout << "Task completion: " << completedTasks << "/" << totalTasks
        << " (" << std::fixed << std::setprecision(2) << completionRate << "%)" << std::endl;

    // ��������ָ��
    double ru = calculateResourceUtilization();
    double rt = calculateResponseTime();
    double ms = calculateMakespan();
    double lb = calculateLoadBalance();
    double tt = calculateTotalTaskTime();

    std::cout << "Resource Utilization: " << std::fixed << std::setprecision(4) << ru << std::endl;
    std::cout << "Response Time: " << std::fixed << std::setprecision(4) << rt << std::endl;
    std::cout << "Makespan: " << std::fixed << std::setprecision(4) << ms << std::endl;
    std::cout << "Load Balance (variance): " << std::fixed << std::setprecision(4) << lb << std::endl;
    std::cout << "Average Task Time: " << std::fixed << std::setprecision(4) << tt << std::endl;

    // �����ۺ�����ָ��
    double alpha = 0.2;
    double beta = 0.2;
    double gamma = 0.2;
    double epsilon = 0.2;
    double xi = 0.2;

    double z = alpha * ru + beta * (1 - rt) + gamma * (1 - ms) + epsilon * (1 - lb) + xi * (1 - tt);

    std::cout << "Composite Performance (Z): " << std::fixed << std::setprecision(4) << z << std::endl;

    // ����ڵ���ͳ��
    std::cout << "\nNode Group Statistics:" << std::endl;
    std::cout << "  CPU Intensive Group: " << cpuIntensiveGroup.size() << " nodes" << std::endl;
    std::cout << "  Memory Intensive Group: " << memIntensiveGroup.size() << " nodes" << std::endl;
    std::cout << "  Network Intensive Group: " << netIntensiveGroup.size() << " nodes" << std::endl;
    std::cout << "  General Group: " << generalGroup.size() << " nodes" << std::endl;
}

void Simulator::run(int maxTimeSteps, double interval) {
    timeInterval = interval;
   timeSeriesData.clear(); // ���֮ǰ��ʱ����������

    for (currentTimeStep = 1; currentTimeStep <= maxTimeSteps; ++currentTimeStep) {
        std::cout << "\n==== Time Step " << currentTimeStep << " ====" << std::endl;

        // 1. �Ե�ǰʱ�̵��������Ԥ������ɫ���ֲ㣩
        if (tasks.find(currentTimeStep) != tasks.end()) {
            calculateTaskLevels(currentTimeStep);
            colorTasks(currentTimeStep);

            std::cout << "Processed " << tasks[currentTimeStep].size()
                << " tasks for time step " << currentTimeStep << std::endl;
        }

        // 2. ���½ڵ�״̬
        updateNodeResources(timeInterval);

        // 3. �ڵ����ͼ�ؽڵ�ѡ��
        groupNodes();
        electMonitorNodes();

        // 4. ���ӻ��ڵ�״̬
        visualizeNodeStatus();

        // 5. ʹ�ü��㷨��������
        if (tasks.find(currentTimeStep) != tasks.end()) {
            simpleTaskAssignment(currentTimeStep);

            // ���ӻ�����״̬
            visualizeTaskStatus(currentTimeStep);
        }

        // 6. ����ģ��ʱ��
        simulationTime += timeInterval;

        // 7. ��������״̬
        updateTaskStatus(timeInterval);
        recordTimeSeriesData(); // �����һ��
    }

    // ��ӡ���
    printResults();
}


void Simulator::saveResults(const std::string& folderPath) const {
    // ���������ļ����ļ���
#ifdef _WIN32
    std::string command = "mkdir \"" + folderPath + "\" 2>nul";
#else
    std::string command = "mkdir -p \"" + folderPath + "\"";
#endif
    system(command.c_str());

    // �����������
    saveNodeStatus(folderPath + "/node_status.csv");
    saveTaskResults(folderPath + "/task_results.csv");
    savePerformanceMetrics(folderPath + "/performance_metrics.csv");
    saveTimeSeriesData(folderPath);

    std::cout << "Simulation results saved to folder: " << folderPath << std::endl;
}

void Simulator::saveNodeStatus(const std::string& filename) const {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << filename << std::endl;
        return;
    }

    // д��CSV����
    file << "NodeID,GroupType,IsMonitor,CPULoad,MemoryLoad,NetworkLoad,CompositeLoad,TaskCount,EffectiveCPU,AvailableMemory,RealBandwidth" << std::endl;

    // д��ڵ�����
    for (const Node& node : nodes) {
        file << node.id << ","
            << node.groupType << ","
            << (node.isMonitor ? 1 : 0) << ","
            << node.cpuLoadRate << ","
            << node.memoryLoadRate << ","
            << node.networkLoadRate << ","
            << node.compositeLoadRate << ","
            << node.taskQueue.size() << ","
            << node.effectiveCpu << ","
            << node.availableMemory << ","
            << node.realBandwidth
            << std::endl;
    }

    file.close();
}

void Simulator::saveTaskResults(const std::string& filename) const {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << filename << std::endl;
        return;
    }

    // д��CSV����
    file << "TaskID,TimeStep,ResourceType,Level,CPUDemand,MemoryDemand,BandwidthDemand,Assigned,AssignedNode,Completed,RemainingTime,Dependencies" << std::endl;

    // д����������
    for (const auto& timestepPair : tasks) {
        for (const auto& taskPair : timestepPair.second) {
            const Task& task = taskPair.second;

            // �������б�ת��Ϊ�ַ���
            std::ostringstream depsStream;
            for (size_t i = 0; i < task.dependencies.size(); ++i) {
                depsStream << task.dependencies[i];
                if (i < task.dependencies.size() - 1) {
                    depsStream << "|";
                }
            }

            file << task.id << ","
                << task.timeStep << ","
                << task.resourceType << ","
                << task.level << ","
                << task.cpuDemand << ","
                << task.memoryDemand << ","
                << task.bandwidthDemand << ","
                << (task.assigned ? 1 : 0) << ","
                << task.assignedNode << ","
                << (task.completed ? 1 : 0) << ","
                << task.remainingTime << ","
                << depsStream.str()
                << std::endl;
        }
    }

    file.close();
}

void Simulator::savePerformanceMetrics(const std::string& filename) const {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << filename << std::endl;
        return;
    }

    // ��������ָ��
    double ru = calculateResourceUtilization();
    double rt = calculateResponseTime();
    double ms = calculateMakespan();
    double lb = calculateLoadBalance();
    double tt = calculateTotalTaskTime();

    // �����ۺ�ָ��
    double alpha = 0.2;
    double beta = 0.2;
    double gamma = 0.2;
    double epsilon = 0.2;
    double xi = 0.2;
    double z = alpha * ru + beta * (1 - rt) + gamma * (1 - ms) + epsilon * (1 - lb) + xi * (1 - tt);

    // �������������
    int totalTasks = 0;
    int completedTasks = 0;
    for (const auto& timeStepPair : tasks) {
        for (const auto& taskPair : timeStepPair.second) {
            totalTasks++;
            if (taskPair.second.completed) {
                completedTasks++;
            }
        }
    }
    double completionRate = (totalTasks > 0) ? (static_cast<double>(completedTasks) / totalTasks) : 0.0;

    // д��CSV����
    file << "Metric,Value" << std::endl;

    // д������ָ��
    file << "SimulationTime," << simulationTime << std::endl;
    file << "ResourceUtilization," << ru << std::endl;
    file << "ResponseTime," << rt << std::endl;
    file << "Makespan," << ms << std::endl;
    file << "LoadBalance," << lb << std::endl;
    file << "TotalTaskTime," << tt << std::endl;
    file << "CompositePerformance," << z << std::endl;
    file << "TotalTasks," << totalTasks << std::endl;
    file << "CompletedTasks," << completedTasks << std::endl;
    file << "CompletionRate," << completionRate << std::endl;
    file << "CPUIntensiveNodes," << cpuIntensiveGroup.size() << std::endl;
    file << "MemoryIntensiveNodes," << memIntensiveGroup.size() << std::endl;
    file << "NetworkIntensiveNodes," << netIntensiveGroup.size() << std::endl;
    file << "GeneralNodes," << generalGroup.size() << std::endl;

    file.close();
}



    // ��¼ÿ��ʱ�䲽������ָ��
    void Simulator::recordTimeSeriesData() {
        std::map<std::string, double> stepData;
        stepData["TimeStep"] = currentTimeStep;
        stepData["ResourceUtilization"] = calculateResourceUtilization();
        stepData["ResponseTime"] = calculateResponseTime();
        stepData["Makespan"] = calculateMakespan();
        stepData["LoadBalance"] = calculateLoadBalance();
        stepData["TotalTaskTime"] = calculateTotalTaskTime();

        // ���㵱ǰ�����������
        int totalTasks = 0;
        int completedTasks = 0;
        for (const auto& timeStepPair : tasks) {
            if (timeStepPair.first <= currentTimeStep) {
                for (const auto& taskPair : timeStepPair.second) {
                    totalTasks++;
                    if (taskPair.second.completed) {
                        completedTasks++;
                    }
                }
            }
        }
        stepData["CompletionRate"] = (totalTasks > 0) ?
            (static_cast<double>(completedTasks) / totalTasks) : 0.0;

        // ��¼�ڵ�ƽ������
        double avgCpuLoad = 0.0;
        double avgMemLoad = 0.0;
        double avgNetLoad = 0.0;
        double avgCompositeLoad = 0.0;

        for (const Node& node : nodes) {
            avgCpuLoad += node.cpuLoadRate;
            avgMemLoad += node.memoryLoadRate;
            avgNetLoad += node.networkLoadRate;
            avgCompositeLoad += node.compositeLoadRate;
        }

        if (!nodes.empty()) {
            avgCpuLoad /= nodes.size();
            avgMemLoad /= nodes.size();
            avgNetLoad /= nodes.size();
            avgCompositeLoad /= nodes.size();
        }

        stepData["AvgCPULoad"] = avgCpuLoad;
        stepData["AvgMemLoad"] = avgMemLoad;
        stepData["AvgNetLoad"] = avgNetLoad;
        stepData["AvgCompositeLoad"] = avgCompositeLoad;

        timeSeriesData.push_back(stepData);
    }

    void Simulator::saveTimeSeriesData(const std::string& folderPath) const {
        std::ofstream file(folderPath + "/time_series.csv");
        if (!file.is_open()) {
            std::cerr << "Failed to open file for writing: " << folderPath << "/time_series.csv" << std::endl;
            return;
        }

        // д��CSV���� - ʹ�õ�һ�����ݵ�����м�
        if (!timeSeriesData.empty()) {
            bool first = true;
            for (const auto& pair : timeSeriesData[0]) {
                if (!first) file << ",";
                file << pair.first;
                first = false;
            }
            file << std::endl;

            // д��ÿ��ʱ�䲽������
            for (const auto& stepData : timeSeriesData) {
                first = true;
                for (const auto& pair : timeSeriesData[0]) { // ʹ�õ�һ�����ݵ�ļ�������˳��һ��
                    if (!first) file << ",";
                    auto it = stepData.find(pair.first);
                    if (it != stepData.end()) {
                        file << it->second;
                    }
                    else {
                        file << "0"; // ���û���������д��0
                    }
                    first = false;
                }
                file << std::endl;
            }
        }

        file.close();
    }

