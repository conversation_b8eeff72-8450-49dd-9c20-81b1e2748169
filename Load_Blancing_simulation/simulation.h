// simulation.h
#ifndef SIMULATION_H
#define SIMULATION_H

#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <unordered_set>
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <random>
#include <iomanip>
#include "params.h"

// ������
// �޸� Task ��Ķ��壬���Ĭ�Ϲ��캯��
class Task {
public:
    int id;
    int timeStep;
    int cpuDemand;
    int memoryDemand;
    int bandwidthDemand;
    std::vector<int> dependencies;

    // ������ɫ�������
    int resourceType;  // 100: CPU�ܼ���, 200: �ڴ��ܼ���, 300: ͨ���ܼ���, 400: Ĭ����
    int level;         // ���������㼶
    std::string color; // ���ӻ���ɫ

    // ����ִ��״̬
    bool assigned;     // �Ƿ��ѷ���
    int assignedNode;  // ���䵽�Ľڵ�
    double remainingTime; // ʣ��ִ��ʱ��
    bool completed;    // �Ƿ������

    // ���Ĭ�Ϲ��캯��
    Task() : id(0), timeStep(0), cpuDemand(0), memoryDemand(0), bandwidthDemand(0),
        resourceType(400), level(1), assigned(false), assignedNode(-1),
        remainingTime(0), completed(false) {}

    // �������Ĺ��캯��
    Task(int id, int timeStep, int cpu, int mem, int bw, const std::vector<int>& deps);
};

// �ڵ���
class Node {
public:
    int id;

    // �ڵ��׼��Դ��������Դ��
    double baseCpu;    // CPU��׼��������
    double totalMemory; // �ڴ��׼������
    double totalBandwidth; // �����׼������

    // �ڵ�ʵʱ������Դ
    double effectiveCpu;    // ʵʱ����CPU
    double availableMemory; // ʵʱ�����ڴ�
    double realBandwidth;   // ʵʱ���ô���

    // �ڵ㶯̬����
    double bandwidthRatio;   // ����������
    double linkQuality;      // ��·����ϵ��
    double congestionCoeff;  // ��̬ӵ��ϵ��

    // �ڵ����
    int groupType;  // 100: CPU�ܼ���, 200: �ڴ��ܼ���, 300: ͨ���ܼ���, 400: ��ͨ��
    bool isMonitor; // �Ƿ�Ϊ��ؽڵ�

    // �ڵ㸺��״̬
    double cpuLoadRate;    // CPU������
    double memoryLoadRate; // �ڴ渺����
    double networkLoadRate; // ���縺����
    double compositeLoadRate; // �ۺϸ�����

    // ����ִ�ж���
    struct TaskInfo {
        int taskId;
        double remainingTime;
        int cpuDemand;
        int memoryDemand;
        int bandwidthDemand;
    };
    std::vector<TaskInfo> taskQueue;

    Node(int id, double cpuCap, double memTotal, double bwTotal, double cpuLoad,
        double bwRatio, double netCong, double linkQual);

    // ����ڵ��ۺ�����
    double calculatePerformance(double wCpu, double wMem, double wNet) const;

    // ���½ڵ㸺��
    void updateLoad(double wCpu, double wMem, double wNet);

    // ����Ƿ���Խ���������
    bool canAcceptTask(const Task& task, double cpuEfficiency, double memEfficiency, double netEfficiency) const;

    // ������񵽶���
    void addTask(int taskId, const Task& task);

    // �ͷ�������Դ
    void releaseTask(int taskId, const Task& task, double cpuEfficiency, double memEfficiency);

    // ���½ڵ���Դ״̬
    void updateResources(double deltaTime, const SimulationParams& params); // ���params����
};

// ģ������
class Simulator {
private:
    std::vector<Node> nodes;
    std::unordered_map<int, std::unordered_map<int, Task>> tasks; // <timeStep, <taskId, Task>>
    int currentTimeStep;
    double simulationTime;
    double timeInterval;


    // ��ԴȨ��
    double wCpu, wMem, wNet;

    // ��Դ����Ч��
    double cpuEfficiency, memEfficiency, netEfficiency;

    // ����˥������
    double cpuDecayFactor;

    // �ڵ����
    std::vector<int> cpuIntensiveGroup;
    std::vector<int> memIntensiveGroup;
    std::vector<int> netIntensiveGroup;
    std::vector<int> generalGroup;
    std::map<int, int> monitorNodes; // <groupType, nodeId>

    // ��������㼶
    void calculateTaskLevels(int timeStep);

    // ������ɫ
    void colorTasks(int timeStep);

    // �ڵ����
    void groupNodes();

    // ѡ�ټ�ؽڵ�
    void electMonitorNodes();

    // �򵥵�������� (�����Ż��㷨)
    void simpleTaskAssignment(int timeStep);

    // ���½ڵ���Դ
    void updateNodeResources(double deltaTime);

    // ��������״̬
    void updateTaskStatus(double deltaTime);

    // ��������ָ��
    double calculateResourceUtilization() const;
    double calculateResponseTime() const;
    double calculateMakespan() const;
    double calculateLoadBalance() const;
    double calculateTotalTaskTime() const;


    SimulationParams params;


public:
    Simulator();


    Simulator(const SimulationParams& p = {}) : params(p) {
        // ����У��
        if (std::abs(params.wCpu + params.wMem + params.wNet - 1.0) > 1e-6) {
            throw std::invalid_argument("Ȩ��ϵ��֮�ͱ���Ϊ1");
        }
    }



    explicit Simulator(const SimulationParams& params);
    bool loadNodes(const std::string& filename);
    bool loadTasks(const std::string& filename);
    void run(int maxTimeSteps, double interval);

    // ���ʱ���������ݴ洢
    std::vector<std::map<std::string, double>> timeSeriesData;

    // ��Ӽ�¼ʱ���������ݵķ���
    void recordTimeSeriesData();


    // ���ӻ�
    void visualizeNodeStatus() const;
    void visualizeTaskStatus(int timeStep) const;

    // ��ӡ���
    void printResults() const;

    

    // ��ӱ������ķ���
    void saveResults(const std::string& folderPath) const;
    void saveTimeSeriesData(const std::string& folderPath) const;

    // ��������.cpp��ʵ�ֵķ�������
    void saveNodeStatus(const std::string& filename) const;
    void saveTaskResults(const std::string& filename) const;
    void savePerformanceMetrics(const std::string& filename) const;
};



#endif // SIMULATION_H