#include "task_allocation.h"
#include <iostream>
#include <algorithm>
#include <random>
#include <cmath>
#include <numeric>
#include <limits>
#include <map>
#include <set>
#include <ctime>
#include <functional>
#include <queue>

#include <iostream>
#include <fstream>
#include <string>
#include <iomanip>

#include <sstream>std::stringstream ss;

// �����������
static std::mt19937 rng(std::random_device{}());

// ���캯��
TaskAllocator::TaskAllocator(const std::vector<Task>& tasks,
    const std::vector<Node>& nodes,
    const ObjectiveWeights& objWeights,
    const ResourceWeights& resWeights,
    const AlgorithmParams& algParams)
    : tasks(tasks), nodes(nodes), objWeights(objWeights),
    resWeights(resWeights), algParams(algParams) {
}

// ��ȡ�㷨����
std::string TaskAllocator::getAlgorithmName(AlgorithmType algorithm) {
    switch (algorithm) {
    case ROUND_ROBIN: return "��ѯ�㷨";
    case GENETIC: return "�Ŵ��㷨";
    case PARTICLE_SWARM: return "����Ⱥ�㷨";
    case BEE_COLONY: return "��Ⱥ�㷨";
    case ANT_COLONY: return "��Ⱥ�㷨";
    case BAT: return "�����㷨";
    case WHALE: return "�����㷨";
    case SIMULATED_ANNEALING: return "ģ���˻��㷨";
    case GREY_WOLF: return "�����㷨";
    case HYBRID_GW_SA: return "����-ģ���˻����㷨";
    default: return "δ֪�㷨";
    }
}

// �������������
TaskAllocationResult TaskAllocator::allocateTasks(AlgorithmType algorithm) {
    // ��¼��ʼʱ��
    auto startTime = std::chrono::high_resolution_clock::now();

    // ����ѡ����㷨��������
    std::vector<int> nodeAssignment;

    switch (algorithm) {
    case ROUND_ROBIN:
        nodeAssignment = roundRobinAllocation();
        break;
    case GENETIC:
        nodeAssignment = geneticAllocation();
        break;
    case PARTICLE_SWARM:
        nodeAssignment = particleSwarmAllocation();
        break;
    case BEE_COLONY:
        nodeAssignment = beeColonyAllocation();
        break;
    case ANT_COLONY:
        nodeAssignment = antColonyAllocation();
        break;
    case BAT:
        nodeAssignment = batAllocation();
        break;
    case WHALE:
        nodeAssignment = whaleAllocation();
        break;
    case SIMULATED_ANNEALING:
        nodeAssignment = simulatedAnnealingAllocation();
        break;
    case GREY_WOLF:
        nodeAssignment = greyWolfAllocation();
        break;
    case HYBRID_GW_SA:
        nodeAssignment = hybridGWSAAllocation();
        break;

    }

    // ��¼����ʱ��
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    // �������ָ��
    return calculateMetrics(nodeAssignment, duration);
}

// ��������ִ��ʱ��
TaskExecutionTime TaskAllocator::calculateTaskExecutionTime(const Task& task, const Node& node) {
    TaskExecutionTime time;

    // ���ݹ�ʽ�������ִ��ʱ��
    time.cpuTime = static_cast<double>(task.resourceDemand.cpuDemand) / node.resource.currentCpu;
    time.memTime = static_cast<double>(task.resourceDemand.memoryDemand) / node.resource.currentMemory;
    time.netTime = static_cast<double>(task.resourceDemand.bandwidthDemand) / node.resource.currentBandwidth;

    // ��ִ��ʱ��
    time.totalTime = time.cpuTime + time.memTime + time.netTime;

    return time;
}

// ���ڵ��Ƿ��ܹ�����������Դ����
bool TaskAllocator::canNodeSatisfyTask(const Task& task, const Node& node, const std::vector<int>& currentAllocation) {
    
    // ���ȼ�����������Ƿ�����
    if (!areDependenciesSatisfied(task, currentAllocation)) {
        return false; // ��������δȫ������
    }
    
    // �������Ƿ�ᵼ��ѭ������
    if (wouldCreateCyclicDependency(task, node.id, currentAllocation)) {
        return false; // �ᵼ��ѭ������
    }
    
    // ����ڵ����ѷ����������Դ�����ܺ�
    int allocatedCpuDemand = 0;
    int allocatedMemoryDemand = 0;
    int allocatedBandwidthDemand = 0;

    for (size_t i = 0; i < currentAllocation.size(); ++i) {
        if (currentAllocation[i] == node.id && i < tasks.size()) {
            allocatedCpuDemand += tasks[i].resourceDemand.cpuDemand;
            allocatedMemoryDemand += tasks[i].resourceDemand.memoryDemand;
            allocatedBandwidthDemand += tasks[i].resourceDemand.bandwidthDemand;
        }
    }

    // ���ڵ��Ƿ����㹻��ʣ����Դ
    bool cpuSatisfied = node.resource.currentCpu >= allocatedCpuDemand + task.resourceDemand.cpuDemand;
    bool memorySatisfied = node.resource.currentMemory >= allocatedMemoryDemand + task.resourceDemand.memoryDemand;
    bool bandwidthSatisfied = node.resource.currentBandwidth >= allocatedBandwidthDemand + task.resourceDemand.bandwidthDemand;

    return cpuSatisfied && memorySatisfied && bandwidthSatisfied;
}


// ������䷽���ĸ���ָ��
#include <numeric>  // ������ͷ�ļ�����std::accumulate

TaskAllocationResult TaskAllocator::calculateMetrics(const std::vector<int>& nodeAssignment, std::chrono::milliseconds executionTime) {
    TaskAllocationResult result;
    result.nodeAssignment = nodeAssignment;
    result.executionTime = executionTime;
    result.avgIterationTime = this->avgIterationTime;
    result.iterationTimes = this->lastIterationTimes;

    // �����ڵ㵽�����ӳ��
    result.nodeToTasks.clear();
    for (size_t i = 0; i < nodeAssignment.size(); ++i) {
        int nodeId = nodeAssignment[i];
        if (nodeId > 0) {
            result.nodeToTasks[nodeId].push_back(i + 1);
        }
    }

    // 1. ������Դ������
    double totalCpuCapacity = 0.0001;  // ���������
    double totalMemoryCapacity = 0.0001;
    double totalBandwidthCapacity = 0.0001;

    double totalCpuDemand = 0.0;
    double totalMemoryDemand = 0.0;
    double totalBandwidthDemand = 0.0;

    for (const auto& node : nodes) {
        totalCpuCapacity += node.resource.cpuCapacity;
        totalMemoryCapacity += node.resource.memoryCapacity;
        totalBandwidthCapacity += node.resource.bandwidthCapacity;
    }

    // �����ѷ�����������
    int assignedTaskCount = 0;
    for (size_t i = 0; i < tasks.size() && i < nodeAssignment.size(); ++i) {
        if (nodeAssignment[i] > 0) {
            const Task& task = tasks[i];
            totalCpuDemand += task.resourceDemand.cpuDemand;
            totalMemoryDemand += task.resourceDemand.memoryDemand;
            totalBandwidthDemand += task.resourceDemand.bandwidthDemand;
            assignedTaskCount++;
        }
    }

    double ruCpu = totalCpuDemand / totalCpuCapacity;
    double ruMem = totalMemoryDemand / totalMemoryCapacity;
    double ruNet = totalBandwidthDemand / totalBandwidthCapacity;

    result.resourceUtilization = (resWeights.cpuWeight * ruCpu +
        resWeights.memWeight * ruMem +
        resWeights.netWeight * ruNet) * 100.0;

    // ��һ����Դ������
    result.normalizedRU = std::max(0.0, std::min(1.0, 1.0 - result.resourceUtilization / 100.0));

    // 2. ������Ӧʱ��
    result.responseTime = executionTime.count() / 1000.0;

    // �����������ִ��ʱ��
    double maxTheoricalTime = 0.1;  // ��ֹ������
    for (const auto& task : tasks) {
        double worstTime = 0.1;
        for (const auto& node : nodes) {
            if (node.resource.currentCpu > 0 && node.resource.currentMemory > 0 && node.resource.currentBandwidth > 0) {
                double time = static_cast<double>(task.resourceDemand.cpuDemand) / std::max(1.0, (double)node.resource.currentCpu) +
                    static_cast<double>(task.resourceDemand.memoryDemand) / std::max(1.0, (double)node.resource.currentMemory) +
                    static_cast<double>(task.resourceDemand.bandwidthDemand) / std::max(1.0, (double)node.resource.currentBandwidth);
                worstTime = std::max(worstTime, time);
            }
        }
        maxTheoricalTime += worstTime;
    }

    // ��һ����Ӧʱ��
    result.normalizedRT = std::max(0.0, std::min(1.0, result.responseTime / std::max(0.1, maxTheoricalTime)));

    // 3. ����Makespan
    std::vector<double> nodeTotalTimes(nodes.size(), 0.0);

    // �����ѷ����������Դ����ӳ��
    std::map<int, TaskResourceDemand> nodeResourceUsed;
    for (size_t i = 0; i < tasks.size() && i < nodeAssignment.size(); ++i) {
        int nodeId = nodeAssignment[i];
        if (nodeId > 0) {
            nodeResourceUsed[nodeId].cpuDemand += tasks[i].resourceDemand.cpuDemand;
            nodeResourceUsed[nodeId].memoryDemand += tasks[i].resourceDemand.memoryDemand;
            nodeResourceUsed[nodeId].bandwidthDemand += tasks[i].resourceDemand.bandwidthDemand;
        }
    }

    for (size_t i = 0; i < nodes.size(); ++i) {
        const Node& node = nodes[i];

        // ��ȡ�ýڵ��ϵ���Դ�����ܺ�
        TaskResourceDemand& demand = nodeResourceUsed[node.id];

        // ����ִ��ʱ��
        if (demand.cpuDemand > 0 || demand.memoryDemand > 0 || demand.bandwidthDemand > 0) {
            double cpuTime = static_cast<double>(demand.cpuDemand) / std::max(1.0, (double)node.resource.currentCpu);
            double memTime = static_cast<double>(demand.memoryDemand) / std::max(1.0, (double)node.resource.currentMemory);
            double netTime = static_cast<double>(demand.bandwidthDemand) / std::max(1.0, (double)node.resource.currentBandwidth);

            nodeTotalTimes[i] = cpuTime + memTime + netTime;
        }
    }

    // ����makespan (���ִ��ʱ��)
    result.makespan = nodeTotalTimes.empty() ? 0.1 : *std::max_element(nodeTotalTimes.begin(), nodeTotalTimes.end());
    result.makespan = std::max(0.1, result.makespan); // ��ֹ������

    // ��һ��Makespan
    result.normalizedMS = std::max(0.0, std::min(1.0, result.makespan / std::max(0.1, maxTheoricalTime)));

    // 4. ���㸺�ؾ����
    double avgLoad = 0.0;
    if (!nodeTotalTimes.empty()) {
        avgLoad = std::accumulate(nodeTotalTimes.begin(), nodeTotalTimes.end(), 0.0) / nodeTotalTimes.size();
    }

    double loadVariance = 0.0;
    for (const double& load : nodeTotalTimes) {
        loadVariance += std::pow(std::max(0.0, load - avgLoad), 2);
    }

    // ��ֹ������
    if (nodeTotalTimes.size() > 0) {
        loadVariance /= nodeTotalTimes.size();
    }

    result.loadBalanceDegree = std::sqrt(std::max(0.0, loadVariance));

    // ��һ�����ؾ����
    double maxPossibleVariance = std::pow(std::max(0.1, maxTheoricalTime), 2);
    result.normalizedPsi = std::max(0.0, std::min(1.0, result.loadBalanceDegree / std::max(0.1, maxPossibleVariance)));

    // 5. ����������ʱ��
    result.totalTaskTime = std::accumulate(nodeTotalTimes.begin(), nodeTotalTimes.end(), 0.0);

    // ����ƽ��ִ��ʱ��Ԥ��ֵ
    double avgExecTimeExpected = maxTheoricalTime / std::max(1, (int)tasks.size());

    // ��һ��������ʱ��
    result.normalizedTotalTime = std::max(0.0, std::min(1.0, result.totalTaskTime / (std::max(1, (int)tasks.size()) * std::max(0.1, avgExecTimeExpected))));

    // 6. ����Ŀ�꺯��ֵ
    result.objectiveValue = objWeights.alpha * result.normalizedRU +
        objWeights.beta * result.normalizedRT +
        objWeights.gamma * result.normalizedMS +
        objWeights.epsilon * result.normalizedPsi +
        objWeights.xi * result.normalizedTotalTime;

    // ���������ʷ
    result.convergenceHistory = this->lastConvergenceHistory;

    return result;
}




// ��task_allocation.cpp��ʵ���������
TaskAllocationResult TaskAllocator::evaluateAssignment(const std::vector<int>& assignment) {
    auto startTime = std::chrono::high_resolution_clock::now();
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    // ֱ�Ӽ���ָ�겢���ؽ��
    return calculateMetrics(assignment, duration);
}


// ��ӵ�TaskAllocator����
std::vector<int> TaskAllocator::ensureAllTasksAllocated(std::vector<int> initialAssignment) {
    std::vector<int> assignment = initialAssignment;

    // ��ȡ����������������
    std::vector<size_t> taskOrder = getTaskTopologicalOrder();

    // ����Ƿ���δ���������
    std::vector<size_t> unallocatedTasks;
    for (size_t i = 0; i < assignment.size(); ++i) {
        if (assignment[i] == 0) {
            unallocatedTasks.push_back(i);
        }
    }

    // �����δ��������񣬽��ж���ĵ���
    int maxExtraIterations = 20; // ���ƶ��������������ֹ����ѭ��
    int iteration = 0;

    while (!unallocatedTasks.empty() && iteration < maxExtraIterations) {
        iteration++;
        std::cout << "���ڽ��ж�������Է���ʣ�� " << unallocatedTasks.size() << " ������..." << std::endl;

        // ������������˳���Է���δ���������
        std::vector<size_t> newlyAllocated;

        for (size_t taskOrderIdx : taskOrder) {
            // ���������Ƿ���δ�����б���
            if (std::find(unallocatedTasks.begin(), unallocatedTasks.end(), taskOrderIdx) == unallocatedTasks.end()) {
                continue;
            }

            const Task& task = tasks[taskOrderIdx];

            // ������������Ƿ�����
            bool dependenciesSatisfied = areDependenciesSatisfied(task, assignment);
            if (!dependenciesSatisfied) continue;

            // �ҳ�����������Žڵ�
            int bestNodeId = 0;
            double bestFitness = std::numeric_limits<double>::max();

            for (const auto& node : nodes) {
                // ����Ƿ�ᵼ��ѭ������
                if (wouldCreateCyclicDependency(task, node.id, assignment)) continue;

                // ������ʱ���䷽��
                std::vector<int> tempAssignment = assignment;
                tempAssignment[taskOrderIdx] = node.id;

                // ������Ӧ��
                double fitness = calculateObjectiveValue(tempAssignment);

                // �������Žڵ�
                if (fitness < bestFitness) {
                    bestFitness = fitness;
                    bestNodeId = node.id;
                }
            }

            // ����ҵ����ʵĽڵ㣬���з���
            if (bestNodeId > 0) {
                assignment[taskOrderIdx] = bestNodeId;
                newlyAllocated.push_back(taskOrderIdx);
                std::cout << "  ���� " << task.id << " �ѷ��䵽�ڵ� " << bestNodeId << std::endl;
            }
        }

        // ����δ���������б�
        for (size_t taskIdx : newlyAllocated) {
            unallocatedTasks.erase(std::remove(unallocatedTasks.begin(), unallocatedTasks.end(), taskIdx), unallocatedTasks.end());
        }

        // ���û���µ����񱻷��䣬���Էſ�Լ��
        if (newlyAllocated.empty()) {
            std::cout << "  ���Էſ�Լ���Է���ʣ������..." << std::endl;

            // �ҳ���Դ��ḻ�Ľڵ���з���
            for (size_t taskIdx : unallocatedTasks) {
                const Task& task = tasks[taskIdx];

                // ������еĽڵ�
                int bestNodeId = 0;
                double maxAvailableResource = -1.0;

                for (const auto& node : nodes) {
                    if (wouldCreateCyclicDependency(task, node.id, assignment)) continue;

                    // ����ڵ���Դ������
                    double cpuUsed = 0.0, memUsed = 0.0, bwUsed = 0.0;
                    for (size_t i = 0; i < assignment.size(); ++i) {
                        if (assignment[i] == node.id) {
                            cpuUsed += tasks[i].resourceDemand.cpuDemand;
                            memUsed += tasks[i].resourceDemand.memoryDemand;
                            bwUsed += tasks[i].resourceDemand.bandwidthDemand;
                        }
                    }

                    double cpuAvailable = (node.resource.currentCpu - cpuUsed) / node.resource.cpuCapacity;
                    double memAvailable = (node.resource.currentMemory - memUsed) / node.resource.memoryCapacity;
                    double bwAvailable = (node.resource.currentBandwidth - bwUsed) / node.resource.bandwidthCapacity;

                    if (cpuAvailable < 0) cpuAvailable = 0;
                    if (memAvailable < 0) memAvailable = 0;
                    if (bwAvailable < 0) bwAvailable = 0;

                    double availableResource = cpuAvailable * resWeights.cpuWeight +
                        memAvailable * resWeights.memWeight +
                        bwAvailable * resWeights.netWeight;

                    if (availableResource > maxAvailableResource) {
                        maxAvailableResource = availableResource;
                        bestNodeId = node.id;
                    }
                }

                if (bestNodeId > 0) {
                    assignment[taskIdx] = bestNodeId;
                    newlyAllocated.push_back(taskIdx);
                    std::cout << "  ���� " << task.id << " �ѷ��䵽��Դ��ḻ�Ľڵ� " << bestNodeId << std::endl;
                }
            }

            // ����δ���������б�
            for (size_t taskIdx : newlyAllocated) {
                unallocatedTasks.erase(std::remove(unallocatedTasks.begin(), unallocatedTasks.end(), taskIdx), unallocatedTasks.end());
            }

            // �����Ȼû�з��䣬ǿ�Ʒ��䵽�κνڵ�
            if (newlyAllocated.empty()) {
                std::cout << "  ǿ�Ʒ���ʣ������..." << std::endl;

                for (size_t taskIdx : unallocatedTasks) {
                    const Task& task = tasks[taskIdx];

                    // ���ѡ��һ�����ᵼ��ѭ�������Ľڵ�
                    std::vector<int> validNodeIds;
                    for (const auto& node : nodes) {
                        if (!wouldCreateCyclicDependency(task, node.id, assignment)) {
                            validNodeIds.push_back(node.id);
                        }
                    }

                    if (!validNodeIds.empty()) {
                        std::uniform_int_distribution<> dist(0, validNodeIds.size() - 1);
                        int randomIdx = dist(rng);
                        int selectedNodeId = validNodeIds[randomIdx];

                        assignment[taskIdx] = selectedNodeId;
                        newlyAllocated.push_back(taskIdx);
                        std::cout << "  ���� " << task.id << " ��ǿ�Ʒ��䵽�ڵ� " << selectedNodeId << std::endl;
                    }
                }

                // ����δ���������б�
                for (size_t taskIdx : newlyAllocated) {
                    unallocatedTasks.erase(std::remove(unallocatedTasks.begin(), unallocatedTasks.end(), taskIdx), unallocatedTasks.end());
                }
            }
        }
    }

    // ��¼ʣ��δ��������
    if (!unallocatedTasks.empty()) {
        std::cout << "����: ���������������� " << unallocatedTasks.size() << " �������޷�����!" << std::endl;
        for (size_t taskIdx : unallocatedTasks) {
            std::cout << "  δ��������ID: " << tasks[taskIdx].id << std::endl;
        }
    }
    else {
        std::cout << "���������ѳɹ�����!" << std::endl;
    }

    return assignment;
}



// ���浥���㷨�ķ�������TXT�ļ�
bool TaskAllocator::saveAllocationResultToFile(const TaskAllocationResult& result,
    AlgorithmType algorithm,
    const std::string& filename) {
    try {
        std::ofstream outFile(filename);
        if (!outFile.is_open()) {
            std::cerr << "�޷������ļ�: " << filename << std::endl;
            return false;
        }

        // д���㷨���ƺ�ʱ���
        outFile << "================================================================" << std::endl;
        outFile << "�㷨: " << getAlgorithmName(algorithm) << std::endl;
        //outFile << "����ʱ��: " << getCurrentTimeStamp() << std::endl;
        //outFile << "================================================================" << std::endl << std::endl;

        // д�������ժҪ
        outFile << "������ժҪ:" << std::endl;
        outFile << "--------------------------------" << std::endl;
        outFile << "����������: " << result.nodeAssignment.size() << std::endl;

        // ����ɹ��������������
        int assignedTaskCount = 0;
        for (int nodeId : result.nodeAssignment) {
            if (nodeId > 0) {
                assignedTaskCount++;
            }
        }

        outFile << "�ɹ�������������: " << assignedTaskCount << " ("
            << (100.0 * assignedTaskCount / result.nodeAssignment.size()) << "%)" << std::endl;

        // ��������ڵ�������������
        outFile << "���ڵ�������:" << std::endl;
        std::map<int, int> nodeTaskCounts;
        for (int nodeId : result.nodeAssignment) {
            if (nodeId > 0) {
                nodeTaskCounts[nodeId]++;
            }
        }

        for (const auto& pair : nodeTaskCounts) {
            outFile << "  �ڵ� " << pair.first << ": " << pair.second << " ������" << std::endl;
        }

        outFile << std::endl;

        // д������ָ��
        outFile << "����ָ��:" << std::endl;
        outFile << "--------------------------------" << std::endl;
        outFile << "��Դ������: " << result.resourceUtilization << std::endl;
        outFile << "�㷨��Ӧʱ��: " << result.executionTime.count() << " ms" << std::endl;
        outFile << "Makespan: " << result.makespan << std::endl;
        outFile << "���ؾ����: " << result.loadBalanceDegree << std::endl;
        outFile << "������ʱ��: " << result.totalTaskTime << std::endl;
        outFile << std::endl;

        // д���һ��ָ��
        outFile << "��һ��ָ��:" << std::endl;
        outFile << "--------------------------------" << std::endl;
        outFile << "��һ����Դ������: " << result.normalizedRU << std::endl;
        outFile << "��һ����Ӧʱ��: " << result.normalizedRT << std::endl;
        outFile << "��һ��Makespan: " << result.normalizedMS << std::endl;
        outFile << "��һ�����ؾ����: " << result.normalizedPsi << std::endl;
        outFile << "��һ��������ʱ��: " << result.normalizedTotalTime << std::endl;
        outFile << std::endl;

        // д��Ŀ�꺯��ֵ
        outFile << "Ŀ�꺯��ֵ: " << result.objectiveValue << std::endl;
        outFile << std::endl;

        // д����ϸ������������
        outFile << "��ϸ����������:" << std::endl;
        outFile << "--------------------------------" << std::endl;
        outFile << "����ID\t�ڵ�ID" << std::endl;

        for (size_t i = 0; i < result.nodeAssignment.size(); ++i) {
            outFile << (i + 1) << "\t" << result.nodeAssignment[i] << std::endl;
        }

        outFile.close();
        std::cout << "�������ѱ�����: " << filename << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "���������ʱ����: " << e.what() << std::endl;
        return false;
    }
}

// ���������㷨�ıȽϽ����TXT�ļ�
bool TaskAllocator::saveComparisonResultsToFile(const std::map<AlgorithmType, TaskAllocationResult>& results,
    const std::string& filename) {
    try {
        std::ofstream outFile(filename);
        if (!outFile.is_open()) {
            std::cerr << "�޷������ļ�: " << filename << std::endl;
            return false;
        }

        // д������ʱ���
       // outFile << "================================================================" << std::endl;
       // outFile << "��������㷨�ȽϽ��" << std::endl;
        //outFile << "����ʱ��: " << getCurrentTimeStamp() << std::endl;
        //outFile << "================================================================" << std::endl << std::endl;

        // д��ժҪ��Ϣ
        outFile << "�㷨����: " << results.size() << std::endl;
        if (!results.empty()) {
            outFile << "��������: " << results.begin()->second.nodeAssignment.size() << std::endl;
        }
        outFile << std::endl;

        // д�������
        outFile << std::setw(20) << std::left << "�㷨"
            << std::setw(15) << std::right << "Ŀ�꺯��ֵ"
            << std::setw(15) << "��Դ������"
            << std::setw(15) << "��Ӧʱ��(ms)"
            << std::setw(15) << "Makespan"
            << std::setw(15) << "���ؾ����"
            << std::setw(15) << "������ʱ��" << std::endl;

        outFile << std::string(100, '-') << std::endl;

        // �ҳ������㷨��Ŀ�꺯��ֵ��С��
        AlgorithmType bestAlgorithm = results.begin()->first;
        double bestObjectiveValue = results.begin()->second.objectiveValue;

        for (const auto& result : results) {
            if (result.second.objectiveValue < bestObjectiveValue) {
                bestAlgorithm = result.first;
                bestObjectiveValue = result.second.objectiveValue;
            }
        }

        // д��ÿ���㷨�Ľ��
        for (const auto& result : results) {
            std::string algName = getAlgorithmName(result.first);
            const TaskAllocationResult& metrics = result.second;

            // ��������㷨
            if (result.first == bestAlgorithm) {
                algName += " (����)";
            }

            outFile << std::setw(20) << std::left << algName
                << std::setw(15) << std::right << std::fixed << std::setprecision(6) << metrics.objectiveValue
                << std::setw(15) << std::fixed << std::setprecision(4) << metrics.resourceUtilization
                << std::setw(15) << metrics.executionTime.count()
                << std::setw(15) << std::fixed << std::setprecision(4) << metrics.makespan
                << std::setw(15) << std::fixed << std::setprecision(4) << metrics.loadBalanceDegree
                << std::setw(15) << std::fixed << std::setprecision(4) << metrics.totalTaskTime << std::endl;
        }

        outFile << std::endl;

        // д���һ��ָ����
        outFile << "��һ��ָ��Ƚ�:" << std::endl;
        outFile << std::string(100, '-') << std::endl;

        outFile << std::setw(20) << std::left << "�㷨"
            << std::setw(15) << std::right << "��һ��RU"
            << std::setw(15) << "��һ��RT"
            << std::setw(15) << "��һ��MS"
            << std::setw(15) << "��һ��Psi"
            << std::setw(15) << "��һ����ʱ��" << std::endl;

        outFile << std::string(100, '-') << std::endl;

        for (const auto& result : results) {
            std::string algName = getAlgorithmName(result.first);
            const TaskAllocationResult& metrics = result.second;

            // ��������㷨
            if (result.first == bestAlgorithm) {
                algName += " (����)";
            }

            outFile << std::setw(20) << std::left << algName
                << std::setw(15) << std::right << std::fixed << std::setprecision(6) << metrics.normalizedRU
                << std::setw(15) << std::fixed << std::setprecision(6) << metrics.normalizedRT
                << std::setw(15) << std::fixed << std::setprecision(6) << metrics.normalizedMS
                << std::setw(15) << std::fixed << std::setprecision(6) << metrics.normalizedPsi
                << std::setw(15) << std::fixed << std::setprecision(6) << metrics.normalizedTotalTime << std::endl;
        }

        outFile.close();
        std::cout << "�㷨�ȽϽ���ѱ�����: " << filename << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "����ȽϽ��ʱ����: " << e.what() << std::endl;
        return false;
    }
}

// ��ȡ��ǰʱ����ĸ�������


// ����������������
bool TaskAllocator::saveConvergenceToFile(const TaskAllocationResult& result,
    AlgorithmType algorithm,
    const std::string& filename) {
    try {
        std::ofstream outFile(filename);
        if (!outFile.is_open()) {
            std::cerr << "�޷������ļ�: " << filename << std::endl;
            return false;
        }

        // д������ʱ���
        outFile << "�㷨: " << getAlgorithmName(algorithm) << " ��������" << std::endl;

        // д��������������
        outFile << "��������\tĿ�꺯��ֵ\tƽ������ʱ��(ms)" << std::endl;
        outFile << "--------------------------------" << std::endl;

        for (size_t i = 0; i < result.convergenceHistory.size(); ++i) {
            outFile << i + 1 << "\t" << std::fixed << std::setprecision(8) << result.convergenceHistory[i]
                << "\t" << std::fixed << std::setprecision(2) << result.iterationTimes[i] << std::endl;
        }

        outFile.close();
        std::cout << "�������������ѱ�����: " << filename << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "����������������ʱ����: " << e.what() << std::endl;
        return false;
    }
}



// ������䷽����Ŀ�꺯��ֵ
double TaskAllocator::calculateObjectiveValue(const std::vector<int>& nodeAssignment) {
    // ��ʱ����ָ�꣬����¼ִ��ʱ��
    auto metrics = calculateMetrics(nodeAssignment, std::chrono::milliseconds(0));
    return metrics.objectiveValue;
}

// ������������Ƿ����㣨�������������ѷ��䣩
bool TaskAllocator::areDependenciesSatisfied(const Task& task, const std::vector<int>& currentAllocation) {
    // �������û����������ֱ������
    if (task.dependencies.empty()) {
        return true;
    }

    // ���ÿ�����������Ƿ��ѷ���
    for (int depTaskId : task.dependencies) {
        // ��ȡ����������tasks�����е�����������IDͨ����1��ʼ��
        int depTaskIndex = depTaskId - 1;

        // ��������Ƿ���Ч���Լ����������Ƿ��ѷ���
        if (depTaskIndex < 0 || depTaskIndex >= currentAllocation.size() || currentAllocation[depTaskIndex] == 0) {
            return false; // ��������δ����
        }
    }

    return true; // �������������ѷ���
}

// �������Ƿ�ᵼ��ѭ������
bool TaskAllocator::wouldCreateCyclicDependency(const Task& task, int nodeId, const std::vector<int>& currentAllocation) {
    // ��������ͼ���ڵ��ϵ������������
    std::unordered_map<int, std::unordered_set<int>> nodeDependencyGraph;

    // ��䵱ǰ�ѷ����������ϵ
    for (size_t i = 0; i < currentAllocation.size() && i < tasks.size(); ++i) {
        if (currentAllocation[i] > 0) { // ��������ѷ���
            int taskNodeId = currentAllocation[i];

            // ��ȡ�����������
            for (int depTaskId : tasks[i].dependencies) {
                int depTaskIndex = depTaskId - 1;
                if (depTaskIndex >= 0 && depTaskIndex < currentAllocation.size() && currentAllocation[depTaskIndex] > 0) {
                    int depTaskNodeId = currentAllocation[depTaskIndex];

                    // ��ӽڵ��������ϵ
                    if (taskNodeId != depTaskNodeId) {
                        nodeDependencyGraph[taskNodeId].insert(depTaskNodeId);
                    }
                }
            }
        }
    }

    // ��ӵ�ǰҪ��������������
    for (int depTaskId : task.dependencies) {
        int depTaskIndex = depTaskId - 1;
        if (depTaskIndex >= 0 && depTaskIndex < currentAllocation.size() && currentAllocation[depTaskIndex] > 0) {
            int depTaskNodeId = currentAllocation[depTaskIndex];

            // ��ӽڵ��������ϵ
            if (nodeId != depTaskNodeId) {
                nodeDependencyGraph[nodeId].insert(depTaskNodeId);
            }
        }
    }

    // ����Ƿ����ѭ��������ʹ��DFS��
    std::unordered_set<int> visited;
    std::unordered_set<int> recursionStack;

    std::function<bool(int)> hasCycle = [&](int node) {
        // ����ڵ��ڵ�ǰ�ݹ�ջ�У������ѭ��
        if (recursionStack.find(node) != recursionStack.end()) {
            return true;
        }

        // ����ڵ��ѷ�����δ����ѭ�����������ٴμ��
        if (visited.find(node) != visited.end()) {
            return false;
        }

        // ��ǽڵ�Ϊ�ѷ��ʣ�������ݹ�ջ
        visited.insert(node);
        recursionStack.insert(node);

        // ������������ڵ�
        if (nodeDependencyGraph.find(node) != nodeDependencyGraph.end()) {
            for (int dep : nodeDependencyGraph[node]) {
                if (hasCycle(dep)) {
                    return true;
                }
            }
        }

        // ����ʱ�ӵݹ�ջ���Ƴ�
        recursionStack.erase(node);
        return false;
        };

    // �ӵ�ǰҪ����Ľڵ㿪ʼ���
    return hasCycle(nodeId);
}

// ��ȡ�����������������
std::vector<size_t> TaskAllocator::getTaskTopologicalOrder() {
    std::vector<size_t> taskOrder;
    taskOrder.reserve(tasks.size());

    // ������������ͼ
    std::unordered_map<int, std::vector<int>> taskGraph; // ����ID -> ������������ID�б�
    std::unordered_map<int, int> inDegree;              // ����ID -> ��ȣ�����������

    // ��ʼ��ͼ�����
    for (size_t i = 0; i < tasks.size(); ++i) {
        int taskId = tasks[i].id;
        inDegree[taskId] = tasks[i].dependencies.size();

        // Ϊÿ������������ӱ�
        for (int depId : tasks[i].dependencies) {
            taskGraph[depId].push_back(taskId);
        }
    }

    // ʹ����������õ�����ִ��˳��
    std::queue<int> zeroInDegree;

    // �ҳ��������Ϊ0������û������������
    for (const auto& task : tasks) {
        if (inDegree[task.id] == 0) {
            zeroInDegree.push(task.id);
        }
    }

    // ��������
    while (!zeroInDegree.empty()) {
        int currentTask = zeroInDegree.front();
        zeroInDegree.pop();

        // �ҵ�������tasks�����е�����
        size_t taskIndex = 0;
        for (size_t i = 0; i < tasks.size(); ++i) {
            if (tasks[i].id == currentTask) {
                taskIndex = i;
                break;
            }
        }

        taskOrder.push_back(taskIndex);

        // ���������������������������
        for (int dependentTask : taskGraph[currentTask]) {
            inDegree[dependentTask]--;
            if (inDegree[dependentTask] == 0) {
                zeroInDegree.push(dependentTask);
            }
        }
    }

    // ����Ƿ�����������ӵ���ִ��˳���У�û��ѭ��������
    if (taskOrder.size() != tasks.size()) {
        std::cout << "����: ������������ѭ�����޷�ȷ��������ִ��˳��ֻ�� "
            << taskOrder.size() << "/" << tasks.size() << " ����������" << std::endl;

        // ���δ���������
        std::unordered_set<size_t> orderedTasks(taskOrder.begin(), taskOrder.end());
        for (size_t i = 0; i < tasks.size(); ++i) {
            if (orderedTasks.find(i) == orderedTasks.end()) {
                taskOrder.push_back(i);
            }
        }
    }

    return taskOrder;
}

// ��֤������䷽���Ƿ���Ч
bool TaskAllocator::isValidAssignment(const std::vector<int>& assignment) {
    if (assignment.size() != tasks.size()) {
        return false; // ���䷽����С��ƥ��
    }

    // ���ÿ�����������Լ��
    for (size_t i = 0; i < tasks.size(); ++i) {
        const Task& task = tasks[i];

        // �������δ���䣬������
        if (assignment[i] == 0) {
            continue;
        }

        // ������������Ƿ�����
        for (int depTaskId : task.dependencies) {
            // ��ȡ����������tasks�����е�����
            int depTaskIndex = -1;
            for (size_t j = 0; j < tasks.size(); ++j) {
                if (tasks[j].id == depTaskId) {
                    depTaskIndex = j;
                    break;
                }
            }

            // ����Ҳ��������������������δ���䣬��Υ������Լ��
            if (depTaskIndex == -1 || depTaskIndex >= assignment.size() || assignment[depTaskIndex] == 0) {
                return false;
            }
        }

        // ���ڵ���Դ�Ƿ񳬳�
        int nodeId = assignment[i];
        Node* node = nullptr;

        // �ҵ���Ӧ�Ľڵ�
        for (auto& n : nodes) {
            if (n.id == nodeId) {
                node = &n;
                break;
            }
        }

        if (!node) {
            return false; // �Ҳ�����Ӧ�ڵ�
        }

        // ����ڵ����ѷ����������Դ�����ܺ�
        int cpuDemand = 0;
        int memoryDemand = 0;
        int bandwidthDemand = 0;

        for (size_t j = 0; j < assignment.size(); ++j) {
            if (assignment[j] == nodeId) {
                cpuDemand += tasks[j].resourceDemand.cpuDemand;
                memoryDemand += tasks[j].resourceDemand.memoryDemand;
                bandwidthDemand += tasks[j].resourceDemand.bandwidthDemand;
            }
        }

        // �����Դ�Ƿ񳬳�
        if (cpuDemand > node->resource.currentCpu ||
            memoryDemand > node->resource.currentMemory ||
            bandwidthDemand > node->resource.currentBandwidth) {
            return false;
        }
    }

    // ���ڵ���Ƿ����ѭ������
    std::unordered_map<int, std::unordered_set<int>> nodeDependencyGraph; // �ڵ�ID -> �������Ľڵ�ID����

    // �����ڵ�����ͼ
    for (size_t i = 0; i < tasks.size(); ++i) {
        if (assignment[i] == 0) continue; // ����δ���������

        int taskNodeId = assignment[i];

        for (int depTaskId : tasks[i].dependencies) {
            // �ҵ�������������
            int depTaskIndex = -1;
            for (size_t j = 0; j < tasks.size(); ++j) {
                if (tasks[j].id == depTaskId) {
                    depTaskIndex = j;
                    break;
                }
            }

            if (depTaskIndex != -1 && depTaskIndex < assignment.size() && assignment[depTaskIndex] != 0) {
                int depTaskNodeId = assignment[depTaskIndex];

                // �����������������ڲ�ͬ�ڵ��ϣ���ӽڵ������
                if (taskNodeId != depTaskNodeId) {
                    nodeDependencyGraph[taskNodeId].insert(depTaskNodeId);
                }
            }
        }
    }

    // ���ڵ�����ͼ���Ƿ���ڻ�
    std::unordered_set<int> visited;
    std::unordered_set<int> recursionStack;

    std::function<bool(int)> hasCycle = [&](int nodeId) {
        if (recursionStack.find(nodeId) != recursionStack.end()) {
            return true; // ���ֻ�
        }

        if (visited.find(nodeId) != visited.end()) {
            return false; // �ѷ����Ҳ��ڵ�ǰ·����
        }

        visited.insert(nodeId);
        recursionStack.insert(nodeId);

        if (nodeDependencyGraph.find(nodeId) != nodeDependencyGraph.end()) {
            for (int depNodeId : nodeDependencyGraph[nodeId]) {
                if (hasCycle(depNodeId)) {
                    return true;
                }
            }
        }

        recursionStack.erase(nodeId);
        return false;
        };

    // ���ÿ���ڵ�
    for (const auto& entry : nodeDependencyGraph) {
        if (visited.find(entry.first) == visited.end()) {
            if (hasCycle(entry.first)) {
                return false; // ����ѭ������
            }
        }
    }

    return true; // ���䷽����Ч
}

// �޸���Ч��������䷽��
std::vector<int> TaskAllocator::repairInvalidAssignment(const std::vector<int>& assignment) {
    // ��ȡ������������
    std::vector<size_t> taskOrder = getTaskTopologicalOrder();

    // �����µķ��䷽��
    std::vector<int> repairedAssignment(assignment.size(), 0);

    // ����������˳�����·�������
    for (size_t taskIndex : taskOrder) {
        const Task& task = tasks[taskIndex];

        // ������������Ƿ�����
        bool dependenciesSatisfied = true;
        for (int depTaskId : task.dependencies) {
            // �ҵ�������������
            int depTaskIndex = -1;
            for (size_t j = 0; j < tasks.size(); ++j) {
                if (tasks[j].id == depTaskId) {
                    depTaskIndex = j;
                    break;
                }
            }

            if (depTaskIndex == -1 || depTaskIndex >= repairedAssignment.size() || repairedAssignment[depTaskIndex] == 0) {
                dependenciesSatisfied = false;
                break;
            }
        }

        if (!dependenciesSatisfied) {
            continue; // ��������δ���������
        }

        // ���Է��䵽ԭ���Ľڵ�
        int originalNodeId = assignment[taskIndex];
        if (originalNodeId > 0) {
            // �ҵ���Ӧ�Ľڵ�
            Node* node = nullptr;
            for (auto& n : nodes) {
                if (n.id == originalNodeId) {
                    node = &n;
                    break;
                }
            }

            if (node) {
                // ����Ƿ�ᵼ��ѭ������
                std::vector<int> tempAssignment = repairedAssignment;
                tempAssignment[taskIndex] = originalNodeId;

                if (!wouldCreateCyclicDependency(task, originalNodeId, tempAssignment)) {
                    // ���ڵ���Դ�Ƿ��㹻
                    int cpuDemand = 0;
                    int memoryDemand = 0;
                    int bandwidthDemand = 0;

                    for (size_t j = 0; j < repairedAssignment.size(); ++j) {
                        if (repairedAssignment[j] == originalNodeId) {
                            cpuDemand += tasks[j].resourceDemand.cpuDemand;
                            memoryDemand += tasks[j].resourceDemand.memoryDemand;
                            bandwidthDemand += tasks[j].resourceDemand.bandwidthDemand;
                        }
                    }

                    // ���ϵ�ǰ�������Դ����
                    cpuDemand += task.resourceDemand.cpuDemand;
                    memoryDemand += task.resourceDemand.memoryDemand;
                    bandwidthDemand += task.resourceDemand.bandwidthDemand;

                    // �����Դ�Ƿ񳬳�
                    if (cpuDemand <= node->resource.currentCpu &&
                        memoryDemand <= node->resource.currentMemory &&
                        bandwidthDemand <= node->resource.currentBandwidth) {
                        // ��Դ�㹻�����䵽ԭ�ڵ�
                        repairedAssignment[taskIndex] = originalNodeId;
                        continue;
                    }
                }
            }
        }

        // ����޷����䵽ԭ�ڵ㣬������һ�����ʵĽڵ�
        for (size_t j = 0; j < nodes.size(); ++j) {
            const Node& node = nodes[j];

            // ����Ƿ�ᵼ��ѭ������
            std::vector<int> tempAssignment = repairedAssignment;
            tempAssignment[taskIndex] = node.id;

            if (!wouldCreateCyclicDependency(task, node.id, tempAssignment)) {
                // ���ڵ���Դ�Ƿ��㹻
                int cpuDemand = 0;
                int memoryDemand = 0;
                int bandwidthDemand = 0;

                for (size_t k = 0; k < repairedAssignment.size(); ++k) {
                    if (repairedAssignment[k] == node.id) {
                        cpuDemand += tasks[k].resourceDemand.cpuDemand;
                        memoryDemand += tasks[k].resourceDemand.memoryDemand;
                        bandwidthDemand += tasks[k].resourceDemand.bandwidthDemand;
                    }
                }

                // ���ϵ�ǰ�������Դ����
                cpuDemand += task.resourceDemand.cpuDemand;
                memoryDemand += task.resourceDemand.memoryDemand;
                bandwidthDemand += task.resourceDemand.bandwidthDemand;

                // �����Դ�Ƿ񳬳�
                if (cpuDemand <= node.resource.currentCpu &&
                    memoryDemand <= node.resource.currentMemory &&
                    bandwidthDemand <= node.resource.currentBandwidth) {
                    // ��Դ�㹻�����䵽����ڵ�
                    repairedAssignment[taskIndex] = node.id;
                    break;
                }
            }
        }
    }

    return repairedAssignment;
}


// ���ҽڵ�����
int TaskAllocator::findNodeIndex(int nodeId) {
    for (size_t i = 0; i < nodes.size(); ++i) {
        if (nodes[i].id == nodeId) return static_cast<int>(i);
    }
    return -1; // δ�ҵ�
}

// ������Դѡ��ڵ�
int TaskAllocator::selectNodeBasedOnResources(const Task& task, const std::vector<NodeResource>& resources) {
    std::vector<int> candidates;
    for (size_t i = 0; i < resources.size(); ++i) {
        if (resources[i].currentCpu >= task.resourceDemand.cpuDemand &&
            resources[i].currentMemory >= task.resourceDemand.memoryDemand &&
            resources[i].currentBandwidth >= task.resourceDemand.bandwidthDemand) {
            candidates.push_back(static_cast<int>(i));
        }
    }
    if (candidates.empty()) {
        int bestIdx = 0;
        double bestScore = -1.0;
        for (size_t i = 0; i < resources.size(); ++i) {
            double score = resources[i].currentCpu * resWeights.cpuWeight +
                resources[i].currentMemory * resWeights.memWeight +
                resources[i].currentBandwidth * resWeights.netWeight;
            if (score > bestScore) {
                bestScore = score;
                bestIdx = static_cast<int>(i);
            }
        }
        return bestIdx;
    }
    std::uniform_int_distribution<> dist(0, candidates.size() - 1);
    return candidates[dist(rng)];
}

// ���½ڵ���Դ
void TaskAllocator::updateNodeResources(NodeResource& resource, const Task& task, double scale) {
    resource.currentCpu -= task.resourceDemand.cpuDemand * scale;
    resource.currentMemory -= task.resourceDemand.memoryDemand * scale;
    resource.currentBandwidth -= task.resourceDemand.bandwidthDemand * scale;
}

// ����ѡ��������Ⱥ�㷨��
int TaskAllocator::rouletteSelect(const std::vector<double>& probabilities, double total) {
    std::uniform_real_distribution<> dist(0.0, total);
    double value = dist(rng);
    double sum = 0.0;
    for (size_t i = 0; i < probabilities.size(); ++i) {
        sum += probabilities[i];
        if (value <= sum) return static_cast<int>(i);
    }
    return static_cast<int>(probabilities.size() - 1);
}

// ������Ϣ�أ�������Ⱥ�㷨��
void TaskAllocator::updatePheromone(std::vector<std::vector<double>>& pheromone,
    const std::vector<std::vector<int>>& solutions,
    const std::vector<double>& fitness) {
    for (size_t i = 0; i < pheromone.size(); ++i) {
        for (size_t j = 0; j < pheromone[i].size(); ++j) {
            pheromone[i][j] *= (1.0 - algParams.rho); // ��Ϣ�ػӷ�
        }
    }
    for (size_t ant = 0; ant < solutions.size(); ++ant) {
        double delta = algParams.q0 * fitness[ant];
        for (size_t task = 0; task < solutions[ant].size(); ++task) {
            int nodeIdx = findNodeIndex(solutions[ant][task]);
            if (nodeIdx >= 0) {
                pheromone[task][nodeIdx] += delta;
            }
        }
    }
}














// ���¸Ľ�����ѯ�㷨����������������ִ��˳��
std::vector<int> TaskAllocator::roundRobinAllocation() {
    std::vector<int> assignment(tasks.size(), 0);
    int nodeIdx = 0;
    int failCount = 0; // ��¼����ʧ�ܴ���

    // ���������Ľڵ�
    std::vector<bool> nodeFull(nodes.size(), false);
    int fullNodeCount = 0; // �����ڵ�����

    // �����������ȼ����У�����������ϵȷ������ִ��˳��
    std::vector<size_t> taskOrder;
    taskOrder.reserve(tasks.size());

    // ���ȸ�������������ϵ���������޻�ͼ��DAG��
    std::unordered_map<int, std::vector<int>> taskGraph; // ����ID -> ������������ID�б�
    std::unordered_map<int, int> inDegree;              // ����ID -> ��ȣ�����������

    // ��ʼ��ͼ�����
    for (size_t i = 0; i < tasks.size(); ++i) {
        int taskId = tasks[i].id;
        inDegree[taskId] = tasks[i].dependencies.size();

        // Ϊÿ������������ӱ�
        for (int depId : tasks[i].dependencies) {
            taskGraph[depId].push_back(taskId);
        }
    }

    // ʹ����������õ�����ִ��˳��
    std::queue<int> zeroInDegree;

    // �ҳ��������Ϊ0������û������������
    for (const auto& task : tasks) {
        if (inDegree[task.id] == 0) {
            zeroInDegree.push(task.id);
        }
    }

    // ��������
    while (!zeroInDegree.empty()) {
        int currentTask = zeroInDegree.front();
        zeroInDegree.pop();

        // �ҵ�������tasks�����е�����
        size_t taskIndex = 0;
        for (size_t i = 0; i < tasks.size(); ++i) {
            if (tasks[i].id == currentTask) {
                taskIndex = i;
                break;
            }
        }

        taskOrder.push_back(taskIndex);

        // ���������������������������
        for (int dependentTask : taskGraph[currentTask]) {
            inDegree[dependentTask]--;
            if (inDegree[dependentTask] == 0) {
                zeroInDegree.push(dependentTask);
            }
        }
    }

    // ����Ƿ�����������ӵ���ִ��˳���У�û��ѭ��������
    if (taskOrder.size() != tasks.size()) {
        std::cout << "����: ������������ѭ�����޷�ȷ��������ִ��˳��ֻ�� "
            << taskOrder.size() << "/" << tasks.size() << " ����������" << std::endl;

        // ���δ���������
        std::unordered_set<size_t> orderedTasks(taskOrder.begin(), taskOrder.end());
        for (size_t i = 0; i < tasks.size(); ++i) {
            if (orderedTasks.find(i) == orderedTasks.end()) {
                taskOrder.push_back(i);
            }
        }
    }

    // �������������˳���������
    for (size_t taskIdx : taskOrder) {
        const Task& task = tasks[taskIdx];

        // ������нڵ㶼�����������ýڵ�״̬���������·��䣩
        if (fullNodeCount >= nodes.size()) {
            // ���ýڵ�״̬
            std::fill(nodeFull.begin(), nodeFull.end(), false);
            fullNodeCount = 0;
            std::cout << "����: ���нڵ���Դ���Ѻľ����������������ڵ�����ԡ�" << std::endl;
        }

        // ��ѯ�������нڵ㣬ֱ���ҵ����ýڵ���������нڵ�
        bool assigned = false;
        int attempts = 0; // ���Դ���

        while (!assigned && attempts < nodes.size()) {
            // ������֪�����Ľڵ�
            if (nodeFull[nodeIdx]) {
                nodeIdx = (nodeIdx + 1) % nodes.size();
                attempts++;
                continue;
            }

            int nodeId = nodes[nodeIdx].id;
            if (canNodeSatisfyTask(task, nodes[nodeIdx], assignment)) {
                assignment[taskIdx] = nodeId;
                assigned = true;
                failCount = 0; // ����ʧ�ܼ���
            }
            else {
                // �����ǰ�����޷����䵽����ڵ�
                attempts++;
                nodeIdx = (nodeIdx + 1) % nodes.size();

                // �����һ���ڳ�����һ�������Ľڵ㶼ʧ�ܣ���������ڵ�ʵ��������
                failCount++;
                if (failCount > nodes.size() * 2) {
                    // ��ǵ�ǰ�ڵ�Ϊ����
                    nodeFull[nodeIdx] = true;
                    fullNodeCount++;
                    failCount = 0;
                }
            }
        }

        // ������������нڵ����޷����䣬����Ƿ�����������
        if (!assigned) {
            // ���δ�����ԭ���Ƿ�����������
            bool dependencyIssue = !areDependenciesSatisfied(task, assignment);

            if (dependencyIssue) {
                std::cout << "����: ���� " << task.id << " �޷����䣬��Ϊ��������������δ���䡣" << std::endl;
                assignment[taskIdx] = 0; // ���Ϊδ����
            }
            else {
                // ��Դ���⣬�������Ŭ������
                int bestNodeIdx = -1;
                double bestResourceRatio = -1.0;

                for (size_t j = 0; j < nodes.size(); ++j) {
                    // �����ᵼ��ѭ�������Ľڵ�
                    if (wouldCreateCyclicDependency(task, nodes[j].id, assignment)) {
                        continue;
                    }

                    // ����ڵ���Դʣ�����
                    double cpuRatio = (double)nodes[j].resource.currentCpu / nodes[j].resource.cpuCapacity;
                    double memRatio = (double)nodes[j].resource.currentMemory / nodes[j].resource.memoryCapacity;
                    double netRatio = (double)nodes[j].resource.currentBandwidth / nodes[j].resource.bandwidthCapacity;

                    // ��Դ�ۺ�����
                    double resourceRatio = cpuRatio * resWeights.cpuWeight +
                        memRatio * resWeights.memWeight +
                        netRatio * resWeights.netWeight;

                    if (resourceRatio > bestResourceRatio) {
                        bestResourceRatio = resourceRatio;
                        bestNodeIdx = j;
                    }
                }

                if (bestNodeIdx >= 0) {
                    assignment[taskIdx] = nodes[bestNodeIdx].id;
                    assigned = true;
                    std::cout << "���� " << task.id << " �޷��ҵ�����������ԴҪ��Ľڵ㣬�ѷ��䵽��Դ��ḻ�Ľڵ� "
                        << nodes[bestNodeIdx].id << std::endl;
                }
                else {
                    assignment[taskIdx] = 0; // 0��ʾδ����
                    std::cout << "����: ���� " << task.id << " �޷����䵽�κνڵ㣬��Ϊ��Դ�����ᵼ��ѭ��������" << std::endl;
                }
            }
        }

        // �ƶ�����һ���ڵ㣨��ʹ��ǰ�������ɹ���
        nodeIdx = (nodeIdx + 1) % nodes.size();
    }

    // ����ɹ��������������
    int assignedCount = 0;
    for (size_t i = 0; i < assignment.size(); ++i) {
        if (assignment[i] > 0) {
            assignedCount++;
        }
    }

    std::cout << "��ѯ�㷨�ɹ�������������: " << assignedCount << "/" << tasks.size()
        << " (" << (100.0 * assignedCount / tasks.size()) << "%)" << std::endl;

    // ȷ���������񶼱�����
    if (assignedCount < tasks.size()) {
        assignment = ensureAllTasksAllocated(assignment);
    }

    // �������ս����Ӧ��
    double finalFitness = calculateObjectiveValue(assignment);

    // ��¼������ʷ(ֻ��һ����)
    this->lastConvergenceHistory.clear();
    this->lastConvergenceHistory.push_back(finalFitness);

    return assignment;
}

// �Ľ����Ŵ��㷨ʵ��
std::vector<int> TaskAllocator::geneticAllocation() {
    const int populationSize = algParams.populationSize; // ��Ⱥ��С
    const int maxIterations = algParams.maxIterations;   // ����������
    const double eliteRate = 0.2;                        // ��Ӣ����������20%��
    const int eliteCount = std::max(1, static_cast<int>(populationSize * eliteRate)); // �����ľ�Ӣ������

    // ���ڼ�¼������ʷ
    std::vector<double> convergenceHistory;

    // �����ڵ���Դ״̬�ĸ��������ڶ�̬����
    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) {
        originalNodeResources.push_back(node.resource);
    }

    // ��Ⱥ��ʼ��
    std::vector<std::vector<int>> population(populationSize, std::vector<int>(tasks.size(), 0));
    std::vector<double> fitness(populationSize);

    // Step 1: �����ʼ����Ⱥ
    std::uniform_int_distribution<> nodeDist(0, nodes.size() - 1);
    for (int i = 0; i < populationSize; ++i) {
        for (size_t j = 0; j < tasks.size(); ++j) {
            int nodeIdx = nodeDist(rng); // ���ѡ��һ���ڵ�
            population[i][j] = nodes[nodeIdx].id; // �������񵽽ڵ�
        }
        // �����ʼ��Ӧ��
        double objValue = calculateObjectiveValue(population[i]);
        fitness[i] = 1.0 / (objValue + 1e-10); // ��Ӧ��ΪĿ��ֵ�ĵ��������������
    }

    // ��¼��ʼ���Ž�
    int bestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
    double bestFitness = fitness[bestIdx];
    std::vector<int> bestSolution = population[bestIdx];
    convergenceHistory.push_back(1.0 / bestFitness); // ��¼Ŀ��ֵ

    // Step 2 & 3: ��������
    for (int iter = 0; iter < maxIterations; ++iter) {
        // ���½ڵ���Դ״̬�����ڵ�ǰ��Ⱥ��ƽ�����������
        std::vector<NodeResource> currentNodeResources = originalNodeResources;
        for (int i = 0; i < populationSize; ++i) {
            for (size_t j = 0; j < tasks.size(); ++j) {
                int nodeId = population[i][j];
                if (nodeId > 0) {
                    int nodeIdx = -1;
                    for (size_t k = 0; k < nodes.size(); ++k) {
                        if (nodes[k].id == nodeId) {
                            nodeIdx = k;
                            break;
                        }
                    }
                    if (nodeIdx >= 0) {
                        // ��ȥ������Դ���󣨾�̯��ÿ�����壩
                        double scale = 1.0 / populationSize;
                        currentNodeResources[nodeIdx].currentCpu -= tasks[j].resourceDemand.cpuDemand * scale;
                        currentNodeResources[nodeIdx].currentMemory -= tasks[j].resourceDemand.memoryDemand * scale;
                        currentNodeResources[nodeIdx].currentBandwidth -= tasks[j].resourceDemand.bandwidthDemand * scale;
                    }
                }
            }
        }

        // ����Ӧ�������ҵ���Ӣ����
        std::vector<std::pair<double, int>> fitnessIndices(populationSize);
        for (int i = 0; i < populationSize; ++i) {
            fitnessIndices[i] = { fitness[i], i };
        }
        std::sort(fitnessIndices.begin(), fitnessIndices.end(), std::greater<>()); // ��������

        // ��һ����Ⱥ
        std::vector<std::vector<int>> newPopulation(populationSize);

        // ������Ӣ����
        for (int i = 0; i < eliteCount; ++i) {
            int eliteIdx = fitnessIndices[i].second;
            newPopulation[i] = population[eliteIdx];
        }

        // �ԷǾ�Ӣ�������·���
        for (int i = eliteCount; i < populationSize; ++i) {
            std::vector<int> child(tasks.size(), 0);
            for (size_t j = 0; j < tasks.size(); ++j) {
                // �ҳ���ǰ���ýڵ�
                std::vector<int> candidateNodeIndices;
                for (size_t k = 0; k < nodes.size(); ++k) {
                    if (currentNodeResources[k].currentCpu >= tasks[j].resourceDemand.cpuDemand &&
                        currentNodeResources[k].currentMemory >= tasks[j].resourceDemand.memoryDemand &&
                        currentNodeResources[k].currentBandwidth >= tasks[j].resourceDemand.bandwidthDemand) {
                        candidateNodeIndices.push_back(k);
                    }
                }

                // ����п��ýڵ㣬���ѡ��һ��������ѡ����Դ��ḻ�Ľڵ�
                if (!candidateNodeIndices.empty()) {
                    std::uniform_int_distribution<> dist(0, candidateNodeIndices.size() - 1);
                    int selectedIdx = candidateNodeIndices[dist(rng)];
                    child[j] = nodes[selectedIdx].id;
                }
                else {
                    // �ҵ���Դ��ḻ�Ľڵ�
                    int bestNodeIdx = 0;
                    double maxResource = -1.0;
                    for (size_t k = 0; k < nodes.size(); ++k) {
                        double resourceScore =
                            currentNodeResources[k].currentCpu * resWeights.cpuWeight +
                            currentNodeResources[k].currentMemory * resWeights.memWeight +
                            currentNodeResources[k].currentBandwidth * resWeights.netWeight;
                        if (resourceScore > maxResource) {
                            maxResource = resourceScore;
                            bestNodeIdx = k;
                        }
                    }
                    child[j] = nodes[bestNodeIdx].id;
                }
            }
            newPopulation[i] = child;
        }

        // ������Ⱥ
        population = newPopulation;

        // ���¼�����Ӧ��
        for (int i = 0; i < populationSize; ++i) {
            double objValue = calculateObjectiveValue(population[i]);
            fitness[i] = 1.0 / (objValue + 1e-10);
        }

        // �������Ž�
        int currentBestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
        if (fitness[currentBestIdx] > bestFitness) {
            bestFitness = fitness[currentBestIdx];
            bestSolution = population[currentBestIdx];
        }
        convergenceHistory.push_back(1.0 / bestFitness);
    }

    // ��¼������ʷ
    this->lastConvergenceHistory = convergenceHistory;

    // ȷ���������������Ч����ѡ�޸����裩
    if (std::count(bestSolution.begin(), bestSolution.end(), 0) > 0) {
        bestSolution = ensureAllTasksAllocated(bestSolution);
    }

    return bestSolution;
}


// ����Ⱥ�㷨ʵ��
std::vector<int> TaskAllocator::particleSwarmAllocation() {
    const int particleCount = algParams.populationSize;
    const int maxIterations = algParams.maxIterations;
    std::vector<double> convergenceHistory;

    // ��ʼ���ڵ���Դ
    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) originalNodeResources.push_back(node.resource);

    // ��ʼ������Ⱥ
    std::vector<std::vector<int>> particles(particleCount, std::vector<int>(tasks.size(), 0));
    std::vector<std::vector<double>> velocities(particleCount, std::vector<double>(tasks.size(), 0.0));
    std::vector<double> fitness(particleCount);
    std::vector<std::vector<int>> pbest(particleCount);
    std::vector<double> pbestFitness(particleCount);
    std::uniform_int_distribution<> nodeDist(0, nodes.size() - 1);
    std::uniform_real_distribution<> uniformDist(0.0, 1.0);

    // �����ʼ��
    for (int i = 0; i < particleCount; ++i) {
        for (size_t j = 0; j < tasks.size(); ++j) {
            particles[i][j] = nodes[nodeDist(rng)].id;
        }
        fitness[i] = 1.0 / (calculateObjectiveValue(particles[i]) + 1e-10);
        pbest[i] = particles[i];
        pbestFitness[i] = fitness[i];
    }
    int gbestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
    std::vector<int> gbest = particles[gbestIdx];
    convergenceHistory.push_back(1.0 / fitness[gbestIdx]);

    // �����Ż�
    for (int iter = 0; iter < maxIterations; ++iter) {
        std::vector<NodeResource> currentNodeResources = originalNodeResources;
        for (int i = 0; i < particleCount; ++i) {
            for (size_t j = 0; j < tasks.size(); ++j) {
                int nodeIdx = findNodeIndex(particles[i][j]);
                if (nodeIdx >= 0) {
                    double scale = 1.0 / particleCount;
                    updateNodeResources(currentNodeResources[nodeIdx], tasks[j], scale);
                }
            }
        }

        for (int i = 0; i < particleCount; ++i) {
            if (fitness[i] < pbestFitness[i] * 0.8) { // �ϲ�������·���
                for (size_t j = 0; j < tasks.size(); ++j) {
                    int nodeIdx = selectNodeBasedOnResources(tasks[j], currentNodeResources);
                    particles[i][j] = nodes[nodeIdx].id;
                }
            }
            else { // �����ٶȺ�λ��
                for (size_t j = 0; j < tasks.size(); ++j) {
                    double r1 = uniformDist(rng), r2 = uniformDist(rng);
                    velocities[i][j] = algParams.inertiaWeight * velocities[i][j] +
                        algParams.c1 * r1 * (pbest[i][j] - particles[i][j]) +
                        algParams.c2 * r2 * (gbest[j] - particles[i][j]);
                    particles[i][j] = std::round(particles[i][j] + velocities[i][j]);
                    particles[i][j] = std::max(1, std::min(particles[i][j], static_cast<int>(nodes.size())));
                }
            }
            fitness[i] = 1.0 / (calculateObjectiveValue(particles[i]) + 1e-10);
            if (fitness[i] > pbestFitness[i]) {
                pbest[i] = particles[i];
                pbestFitness[i] = fitness[i];
            }
        }
        gbestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
        gbest = particles[gbestIdx];
        convergenceHistory.push_back(1.0 / fitness[gbestIdx]);
    }

    lastConvergenceHistory = convergenceHistory;
    if (std::count(gbest.begin(), gbest.end(), 0) > 0) {
        gbest = ensureAllTasksAllocated(gbest);
    }
    return gbest;
}

// �޸ĺ�ķ�Ⱥ�㷨ʵ�֣�������������Լ����
std::vector<int> TaskAllocator::beeColonyAllocation() {
    const int populationSize = algParams.populationSize;
    const int maxIterations = algParams.maxIterations;
    const int limit = 10; // ͣ����ֵ
    std::vector<double> convergenceHistory;

    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) originalNodeResources.push_back(node.resource);

    std::vector<std::vector<int>> population(populationSize, std::vector<int>(tasks.size(), 0));
    std::vector<double> fitness(populationSize);
    std::vector<int> trials(populationSize, 0);
    std::uniform_int_distribution<> nodeDist(0, nodes.size() - 1);

    // ��ʼ��
    for (int i = 0; i < populationSize; ++i) {
        for (size_t j = 0; j < tasks.size(); ++j) {
            population[i][j] = nodes[nodeDist(rng)].id;
        }
        fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
    }
    int bestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
    std::vector<int> bestSolution = population[bestIdx];
    convergenceHistory.push_back(1.0 / fitness[bestIdx]);

    // ����
    for (int iter = 0; iter < maxIterations; ++iter) {
        std::vector<NodeResource> currentNodeResources = originalNodeResources;
        for (int i = 0; i < populationSize; ++i) {
            for (size_t j = 0; j < tasks.size(); ++j) {
                int nodeIdx = findNodeIndex(population[i][j]);
                if (nodeIdx >= 0) {
                    double scale = 1.0 / populationSize;
                    updateNodeResources(currentNodeResources[nodeIdx], tasks[j], scale);
                }
            }
        }

        // ��Ӷ��׶�
        for (int i = 0; i < populationSize; ++i) {
            if (fitness[i] < fitness[bestIdx] * 0.7) {
                for (size_t j = 0; j < tasks.size(); ++j) {
                    int nodeIdx = selectNodeBasedOnResources(tasks[j], currentNodeResources);
                    population[i][j] = nodes[nodeIdx].id;
                }
                fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
                trials[i] = 0;
            }
            else {
                trials[i]++;
            }
        }

        // ����׶�
        for (int i = 0; i < populationSize; ++i) {
            if (trials[i] > limit) {
                for (size_t j = 0; j < tasks.size(); ++j) {
                    int nodeIdx = selectNodeBasedOnResources(tasks[j], currentNodeResources);
                    population[i][j] = nodes[nodeIdx].id;
                }
                fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
                trials[i] = 0;
            }
        }
        bestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
        bestSolution = population[bestIdx];
        convergenceHistory.push_back(1.0 / fitness[bestIdx]);
    }

    lastConvergenceHistory = convergenceHistory;
    if (std::count(bestSolution.begin(), bestSolution.end(), 0) > 0) {
        bestSolution = ensureAllTasksAllocated(bestSolution);
    }
    return bestSolution;
}


std::vector<int> TaskAllocator::antColonyAllocation() {
    const int populationSize = algParams.populationSize;
    const int maxIterations = algParams.maxIterations;
    const double initialPheromone = 1.0;
    std::vector<double> convergenceHistory;

    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) originalNodeResources.push_back(node.resource);

    // ��ʼ����Ϣ�ؾ���
    std::vector<std::vector<double>> pheromone(tasks.size(), std::vector<double>(nodes.size(), initialPheromone));

    // ��ʼ���������
    std::vector<int> bestSolution(tasks.size(), 0);
    double bestFitness = std::numeric_limits<double>::max();
    convergenceHistory.push_back(1.0 / bestFitness);

    // ��ʼ��ÿ�����������������
    std::vector<std::vector<int>> population(populationSize, std::vector<int>(tasks.size(), 0));
    std::vector<double> fitness(populationSize);

    // �����ʼ��ÿ��������䵽һ���ڵ�
    std::uniform_int_distribution<> nodeDist(0, nodes.size() - 1);
    for (int i = 0; i < populationSize; ++i) {
        for (size_t j = 0; j < tasks.size(); ++j) {
            population[i][j] = nodes[nodeDist(rng)].id;
        }
        fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
    }

    // ��������
    for (int iter = 0; iter < maxIterations; ++iter) {
        std::vector<NodeResource> currentNodeResources = originalNodeResources;
        std::vector<std::vector<int>> solutions(populationSize, std::vector<int>(tasks.size(), 0));
        std::vector<double> iterationFitness(populationSize);

        for (int ant = 0; ant < populationSize; ++ant) {
            for (size_t task = 0; task < tasks.size(); ++task) {
                // ����ÿ���ڵ��ѡ�����
                std::vector<double> probabilities(nodes.size(), 0.0);
                double total = 0.0;

                for (size_t node = 0; node < nodes.size(); ++node) {
                    // ȷ���ڵ���������������
                    if (canNodeSatisfyTask(tasks[task], nodes[node], solutions[ant])) {
                        double eta = 1.0 / (1.0 + calculateObjectiveValue(solutions[ant]));  // ��������
                        probabilities[node] = std::pow(pheromone[task][node], algParams.alpha) *
                            std::pow(eta, algParams.beta);  // ��Ϣ�غ�����������Ȩ
                        total += probabilities[node];
                    }
                }

                // ����ѡ�����ѡ��ڵ�
                int nodeIdx = total > 0 ? rouletteSelect(probabilities, total) :
                    selectNodeBasedOnResources(tasks[task], currentNodeResources);
                solutions[ant][task] = nodes[nodeIdx].id;

                // ���½ڵ����Դ
                updateNodeResources(currentNodeResources[nodeIdx], tasks[task], 1.0);
            }

            // ���㵱ǰ�����Ŀ��ֵ
            iterationFitness[ant] = 1.0 / (calculateObjectiveValue(solutions[ant]) + 1e-10);
        }

        // �������Ž⣨��СĿ��ֵ�ĸ��壩
        int bestIdx = std::max_element(iterationFitness.begin(), iterationFitness.end()) - iterationFitness.begin();
        if (iterationFitness[bestIdx] < bestFitness) {
            bestFitness = iterationFitness[bestIdx];
            bestSolution = solutions[bestIdx];
        }

        // ������Ϣ��
        updatePheromone(pheromone, solutions, iterationFitness);

        // ����������ʷ
        convergenceHistory.push_back(1.0 / bestFitness);

        // ����������ÿ���ڵ����Դ���£���ȥ�ѷ����������Դ��
        for (size_t task = 0; task < tasks.size(); ++task) {
            int nodeIdx = findNodeIndex(bestSolution[task]);
            if (nodeIdx >= 0) {
                updateNodeResources(currentNodeResources[nodeIdx], tasks[task], 1.0);
            }
        }
    }

    lastConvergenceHistory = convergenceHistory;
    if (std::count(bestSolution.begin(), bestSolution.end(), 0) > 0) {
        bestSolution = ensureAllTasksAllocated(bestSolution);
    }
    return bestSolution;
}







// �Ľ��������㷨ʵ��
std::vector<int> TaskAllocator::batAllocation() {
    const int populationSize = algParams.populationSize;
    const int maxIterations = algParams.maxIterations;
    std::vector<double> convergenceHistory;

    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) originalNodeResources.push_back(node.resource);

    std::vector<std::vector<int>> population(populationSize, std::vector<int>(tasks.size(), 0));
    std::vector<std::vector<double>> velocities(populationSize, std::vector<double>(tasks.size(), 0.0));
    std::vector<double> fitness(populationSize);
    std::uniform_int_distribution<> nodeDist(0, nodes.size() - 1);
    std::uniform_real_distribution<> uniformDist(0.0, 1.0);

    for (int i = 0; i < populationSize; ++i) {
        for (size_t j = 0; j < tasks.size(); ++j) {
            population[i][j] = nodes[nodeDist(rng)].id;
        }
        fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
    }
    int gbestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
    std::vector<int> gbest = population[gbestIdx];
    convergenceHistory.push_back(1.0 / fitness[gbestIdx]);

    for (int iter = 0; iter < maxIterations; ++iter) {
        std::vector<NodeResource> currentNodeResources = originalNodeResources;
        for (int i = 0; i < populationSize; ++i) {
            for (size_t j = 0; j < tasks.size(); ++j) {
                int nodeIdx = findNodeIndex(population[i][j]);
                if (nodeIdx >= 0) {
                    double scale = 1.0 / populationSize;
                    updateNodeResources(currentNodeResources[nodeIdx], tasks[j], scale);
                }
            }
        }

        for (int i = 0; i < populationSize; ++i) {
            if (fitness[i] < fitness[gbestIdx] * 0.8) {
                for (size_t j = 0; j < tasks.size(); ++j) {
                    int nodeIdx = selectNodeBasedOnResources(tasks[j], currentNodeResources);
                    population[i][j] = nodes[nodeIdx].id;
                }
            }
            else {
                double freq = algParams.frequencyMin + (algParams.frequencyMax - algParams.frequencyMin) * uniformDist(rng);
                for (size_t j = 0; j < tasks.size(); ++j) {
                    velocities[i][j] += (population[i][j] - gbest[j]) * freq;
                    population[i][j] = std::round(population[i][j] + velocities[i][j]);
                    population[i][j] = std::max(1, std::min(population[i][j], static_cast<int>(nodes.size())));
                }
            }
            fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
        }
        gbestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
        gbest = population[gbestIdx];
        convergenceHistory.push_back(1.0 / fitness[gbestIdx]);
    }

    lastConvergenceHistory = convergenceHistory;
    if (std::count(gbest.begin(), gbest.end(), 0) > 0) {
        gbest = ensureAllTasksAllocated(gbest);
    }
    return gbest;
}



// �޸ĺ�ľ����㷨ʵ�֣�������������Լ����
std::vector<int> TaskAllocator::whaleAllocation() {
    const int populationSize = algParams.populationSize;
    const int maxIterations = algParams.maxIterations;
    std::vector<double> convergenceHistory;

    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) originalNodeResources.push_back(node.resource);

    std::vector<std::vector<int>> population(populationSize, std::vector<int>(tasks.size(), 0));
    std::vector<double> fitness(populationSize);
    std::uniform_int_distribution<> nodeDist(0, nodes.size() - 1);
    std::uniform_real_distribution<> uniformDist(0.0, 1.0);

    for (int i = 0; i < populationSize; ++i) {
        for (size_t j = 0; j < tasks.size(); ++j) {
            population[i][j] = nodes[nodeDist(rng)].id;
        }
        fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
    }
    int gbestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
    std::vector<int> gbest = population[gbestIdx];
    convergenceHistory.push_back(1.0 / fitness[gbestIdx]);

    for (int iter = 0; iter < maxIterations; ++iter) {
        std::vector<NodeResource> currentNodeResources = originalNodeResources;
        for (int i = 0; i < populationSize; ++i) {
            for (size_t j = 0; j < tasks.size(); ++j) {
                int nodeIdx = findNodeIndex(population[i][j]);
                if (nodeIdx >= 0) {
                    double scale = 1.0 / populationSize;
                    updateNodeResources(currentNodeResources[nodeIdx], tasks[j], scale);
                }
            }
        }

        for (int i = 0; i < populationSize; ++i) {
            if (fitness[i] < fitness[gbestIdx] * 0.8) {
                for (size_t j = 0; j < tasks.size(); ++j) {
                    int nodeIdx = selectNodeBasedOnResources(tasks[j], currentNodeResources);
                    population[i][j] = nodes[nodeIdx].id;
                }
            }
            else {
                double a = algParams.a - iter * (algParams.a / maxIterations);
                double r = uniformDist(rng);
                double A = 2 * a * r - a;
                double C = 2 * uniformDist(rng);
                double p = uniformDist(rng);
                if (p < 0.5 && std::abs(A) < 1) {
                    for (size_t j = 0; j < tasks.size(); ++j) {
                        double D = std::abs(C * gbest[j] - population[i][j]);
                        population[i][j] = gbest[j] - A * D;
                    }
                }
                for (size_t j = 0; j < tasks.size(); ++j) {
                    population[i][j] = std::round(population[i][j]);
                    population[i][j] = std::max(1, std::min(population[i][j], static_cast<int>(nodes.size())));
                }
            }
            fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
        }
        gbestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
        gbest = population[gbestIdx];
        convergenceHistory.push_back(1.0 / fitness[gbestIdx]);
    }

    lastConvergenceHistory = convergenceHistory;
    if (std::count(gbest.begin(), gbest.end(), 0) > 0) {
        gbest = ensureAllTasksAllocated(gbest);
    }
    return gbest;
}



// �޸ĺ��ģ���˻��㷨ʵ�֣�������������Լ����
std::vector<int> TaskAllocator::simulatedAnnealingAllocation() {
    const int maxIterations = algParams.maxIterations;
    double temperature = algParams.initialTemp;
    const double coolingRate = algParams.coolingRate;
    std::vector<double> convergenceHistory;

    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) originalNodeResources.push_back(node.resource);

    std::vector<int> currentSolution(tasks.size(), 0);
    std::uniform_int_distribution<> nodeDist(0, nodes.size() - 1);
    std::uniform_real_distribution<> uniformDist(0.0, 1.0);

    for (size_t j = 0; j < tasks.size(); ++j) {
        currentSolution[j] = nodes[nodeDist(rng)].id;
    }
    double currentObj = calculateObjectiveValue(currentSolution);
    std::vector<int> bestSolution = currentSolution;
    double bestObj = currentObj;
    convergenceHistory.push_back(currentObj);

    for (int iter = 0; iter < maxIterations; ++iter) {
        std::vector<NodeResource> currentNodeResources = originalNodeResources;
        for (size_t j = 0; j < tasks.size(); ++j) {
            int nodeIdx = findNodeIndex(currentSolution[j]);
            if (nodeIdx >= 0) {
                updateNodeResources(currentNodeResources[nodeIdx], tasks[j], 1.0);
            }
        }

        std::vector<int> newSolution(tasks.size(), 0);
        for (size_t j = 0; j < tasks.size(); ++j) {
            if (uniformDist(rng) < 0.5) {
                int nodeIdx = selectNodeBasedOnResources(tasks[j], currentNodeResources);
                newSolution[j] = nodes[nodeIdx].id;
            }
            else {
                newSolution[j] = currentSolution[j];
            }
        }
        double newObj = calculateObjectiveValue(newSolution);
        double delta = newObj - currentObj;

        if (delta < 0 || uniformDist(rng) < std::exp(-delta / temperature)) {
            currentSolution = newSolution;
            currentObj = newObj;
            if (currentObj < bestObj) {
                bestSolution = currentSolution;
                bestObj = currentObj;
            }
        }
        else {
            for (size_t j = 0; j < tasks.size(); ++j) {
                int nodeIdx = selectNodeBasedOnResources(tasks[j], currentNodeResources);
                currentSolution[j] = nodes[nodeIdx].id;
            }
            currentObj = calculateObjectiveValue(currentSolution);
        }
        temperature *= coolingRate;
        convergenceHistory.push_back(bestObj);
    }

    lastConvergenceHistory = convergenceHistory;
    if (std::count(bestSolution.begin(), bestSolution.end(), 0) > 0) {
        bestSolution = ensureAllTasksAllocated(bestSolution);
    }
    return bestSolution;
}



// �Ľ��Ļ����㷨ʵ��
std::vector<int> TaskAllocator::greyWolfAllocation() {
    const int populationSize = algParams.populationSize;
    const int maxIterations = algParams.maxIterations;
    std::vector<double> convergenceHistory;

    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) originalNodeResources.push_back(node.resource);

    std::vector<std::vector<int>> population(populationSize, std::vector<int>(tasks.size(), 0));
    std::vector<double> fitness(populationSize);
    std::uniform_int_distribution<> nodeDist(0, nodes.size() - 1);
    std::uniform_real_distribution<> uniformDist(0.0, 1.0);

    for (int i = 0; i < populationSize; ++i) {
        for (size_t j = 0; j < tasks.size(); ++j) {
            population[i][j] = nodes[nodeDist(rng)].id;
        }
        fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
    }
    convergenceHistory.push_back(1.0 / *std::max_element(fitness.begin(), fitness.end()));

    for (int iter = 0; iter < maxIterations; ++iter) {
        std::vector<NodeResource> currentNodeResources = originalNodeResources;
        for (int i = 0; i < populationSize; ++i) {
            for (size_t j = 0; j < tasks.size(); ++j) {
                int nodeIdx = findNodeIndex(population[i][j]);
                if (nodeIdx >= 0) {
                    double scale = 1.0 / populationSize;
                    updateNodeResources(currentNodeResources[nodeIdx], tasks[j], scale);
                }
            }
        }

        std::vector<std::pair<double, int>> fitnessIndices(populationSize);
        for (int i = 0; i < populationSize; ++i) fitnessIndices[i] = { fitness[i], i };
        std::sort(fitnessIndices.begin(), fitnessIndices.end(), std::greater<>());
        std::vector<int> alpha = population[fitnessIndices[0].second];
        std::vector<int> beta = population[fitnessIndices[1].second];
        std::vector<int> delta = population[fitnessIndices[2].second];

        for (int i = 0; i < populationSize; ++i) {
            if (fitness[i] < fitnessIndices[2].first * 0.8) {
                for (size_t j = 0; j < tasks.size(); ++j) {
                    int nodeIdx = selectNodeBasedOnResources(tasks[j], currentNodeResources);
                    population[i][j] = nodes[nodeIdx].id;
                }
            }
            else {
                double a = 2.0 - iter * (2.0 / maxIterations);
                for (size_t j = 0; j < tasks.size(); ++j) {
                    double A1 = 2 * a * uniformDist(rng) - a;
                    double C1 = 2 * uniformDist(rng);
                    double D_alpha = std::abs(C1 * alpha[j] - population[i][j]);
                    double X1 = alpha[j] - A1 * D_alpha;

                    double A2 = 2 * a * uniformDist(rng) - a;
                    double C2 = 2 * uniformDist(rng);
                    double D_beta = std::abs(C2 * beta[j] - population[i][j]);
                    double X2 = beta[j] - A2 * D_beta;

                    double A3 = 2 * a * uniformDist(rng) - a;
                    double C3 = 2 * uniformDist(rng);
                    double D_delta = std::abs(C3 * delta[j] - population[i][j]);
                    double X3 = delta[j] - A3 * D_delta;

                    population[i][j] = std::round((X1 + X2 + X3) / 3.0);
                    population[i][j] = std::max(1, std::min(population[i][j], static_cast<int>(nodes.size())));
                }
            }
            fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
        }
        convergenceHistory.push_back(1.0 / *std::max_element(fitness.begin(), fitness.end()));
    }

    int bestIdx = std::max_element(fitness.begin(), fitness.end()) - fitness.begin();
    lastConvergenceHistory = convergenceHistory;
    if (std::count(population[bestIdx].begin(), population[bestIdx].end(), 0) > 0) {
        population[bestIdx] = ensureAllTasksAllocated(population[bestIdx]);
    }
    return population[bestIdx];
}

std::uniform_real_distribution<> uniformDist(0.0, 1.0);  // For generating random real numbers


std::vector<int> TaskAllocator::hybridGWSAAllocation() {
    const int populationSize = algParams.populationSize;
    const int maxIterations = algParams.maxIterations;
    double temperature = algParams.initialTemp;
    const double coolingRate = algParams.coolingRate;

    std::vector<double> convergenceHistory;

    // ��¼ԭʼ�ڵ����Դ
    std::vector<NodeResource> originalNodeResources;
    for (const auto& node : nodes) originalNodeResources.push_back(node.resource);

    // ��ʼ����Ⱥ������������񵽽ڵ�
    std::vector<std::vector<int>> population(populationSize, std::vector<int>(tasks.size(), 0));
    std::vector<double> fitness(populationSize);
    std::uniform_int_distribution<> nodeDist(0, nodes.size() - 1);
    std::uniform_real_distribution<> uniformDist(0.0, 1.0);

    // �����ʼ���������
    for (int i = 0; i < populationSize; ++i) {
        for (size_t j = 0; j < tasks.size(); ++j) {
            population[i][j] = nodes[nodeDist(rng)].id;
        }
        fitness[i] = 1.0 / (calculateObjectiveValue(population[i]) + 1e-10);
    }

    // ���Ž��ʼ��
    std::vector<int> bestSolution = population[0];
    double bestObj = fitness[0];
    convergenceHistory.push_back(bestObj);





    // ��ʼ����
    for (int iter = 0; iter < maxIterations; ++iter) {
        std::vector<NodeResource> currentNodeResources = originalNodeResources;

        // ����ÿ������Ľڵ���Դ����
        for (int i = 0; i < populationSize; ++i) {
            for (size_t j = 0; j < tasks.size(); ++j) {
                int nodeIdx = findNodeIndex(population[i][j]);
                if (nodeIdx >= 0) {
                    double scale = 1.0 / populationSize;
                    currentNodeResources[nodeIdx].currentCpu -= tasks[j].resourceDemand.cpuDemand * scale;
                    currentNodeResources[nodeIdx].currentMemory -= tasks[j].resourceDemand.memoryDemand * scale;
                    currentNodeResources[nodeIdx].currentBandwidth -= tasks[j].resourceDemand.bandwidthDemand * scale;
                }
            }
        }

        // ѡ�� alpha, beta, delta �������Ÿ���
        std::vector<std::pair<double, int>> fitnessIndices(populationSize);
        for (int i = 0; i < populationSize; ++i) fitnessIndices[i] = { fitness[i], i };
        std::sort(fitnessIndices.begin(), fitnessIndices.end(), std::greater<>());
        std::vector<int> alpha = population[fitnessIndices[0].second];
        std::vector<int> beta = population[fitnessIndices[1].second];
        std::vector<int> delta = population[fitnessIndices[2].second];

        // ���и��£����ǵ�̽����ģ���˻�Ľ���׼����
        for (int i = 0; i < populationSize; ++i) {
            std::vector<int> newSolution = population[i];
            double a = 2.0 - iter * (2.0 / maxIterations);  // ��̬����̽����Χ
            for (size_t j = 0; j < tasks.size(); ++j) {
                // ��ϻ��ǵĸ��²���
                double A1 = 2 * a * uniformDist(rng) - a;
                double C1 = 2 * uniformDist(rng);
                double D_alpha = std::abs(C1 * alpha[j] - newSolution[j]);
                double X1 = alpha[j] - A1 * D_alpha;

                double A2 = 2 * a * uniformDist(rng) - a;
                double C2 = 2 * uniformDist(rng);
                double D_beta = std::abs(C2 * beta[j] - newSolution[j]);
                double X2 = beta[j] - A2 * D_beta;

                double A3 = 2 * a * uniformDist(rng) - a;
                double C3 = 2 * uniformDist(rng);
                double D_delta = std::abs(C3 * delta[j] - newSolution[j]);
                double X3 = delta[j] - A3 * D_delta;

                // ���·��䷽��
                newSolution[j] = std::round((X1 + X2 + X3) / 3.0);
                newSolution[j] = std::max(1, std::min(newSolution[j], static_cast<int>(nodes.size())));
            }

            // �����µ�Ŀ��ֵ
            double newObj = calculateObjectiveValue(newSolution);
            double deltaObj = newObj - fitness[i];

            // ģ���˻�Ľ���׼��
            if (deltaObj < 0 || uniformDist(rng) < std::exp(-deltaObj / temperature)) {
                population[i] = newSolution;
                fitness[i] = newObj;
            }
            else {
                // ����������½⣬��ָ�����ǰ��
                population[i] = population[i];
                fitness[i] = fitness[i];
            }

            // ��¼��ѽ�
            if (fitness[i] > bestObj) {
                bestSolution = population[i];
                bestObj = fitness[i];
            }
        }

        // ÿ�ֵ������¶�˥��
        temperature *= coolingRate;
        convergenceHistory.push_back(bestObj);
    }

    // ����������ʷ
    lastConvergenceHistory = convergenceHistory;

    // ȷ�����񶼷���ɹ�
    if (std::count(bestSolution.begin(), bestSolution.end(), 0) > 0) {
        bestSolution = ensureAllTasksAllocated(bestSolution);
    }

    return bestSolution;
}




