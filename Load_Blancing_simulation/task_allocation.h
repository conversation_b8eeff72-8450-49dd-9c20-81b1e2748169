#ifndef TASK_ALLOCATION_H
#define TASK_ALLOCATION_H

#include "data.h"
#include <vector>
#include <string>
#include <unordered_map>
#include <chrono>
#include <map>

// Forward declaration for cross-referencing
class TaskColorInfo;
class TaskColorizer;
struct NodeGroup;

// ��������㷨����
enum AlgorithmType {
    ROUND_ROBIN,         // ��ѯ�㷨
    GENETIC,             // �Ŵ��㷨
    PARTICLE_SWARM,      // ����Ⱥ�㷨
    BEE_COLONY,          // ��Ⱥ�㷨
    ANT_COLONY,          // ��Ⱥ�㷨
    BAT,                 // �����㷨
    WHALE,               // �����㷨
    SIMULATED_ANNEALING, // ģ���˻��㷨
    GREY_WOLF,           // �����㷨
    HYBRID_GW_SA         // ����-ģ���˻����㷨
};

// Ŀ�꺯��Ȩ�ز���
struct ObjectiveWeights {
    double alpha = 0.2;  // ��Դ������Ȩ��
    double beta = 0.2;   // ��Ӧʱ��Ȩ��
    double gamma = 0.2;  // MakespanȨ��
    double epsilon = 0.2; // ���ؾ����Ȩ��
    double xi = 0.2;     // ������ʱ��Ȩ��
};

// ��Դ������Ȩ�ز���
struct ResourceWeights {
    double cpuWeight = 0.4;    // CPU��ԴȨ��
    double memWeight = 0.3;    // �ڴ���ԴȨ��
    double netWeight = 0.3;    // ������ԴȨ��
};

// �㷨����
struct AlgorithmParams {
    // ͨ�ò���
    int maxIterations = 50;      // ����������
    int populationSize = 50;      // ��Ⱥ/��������

    // �Ŵ��㷨����
    double crossoverRate = 0.8;   // ������
    double mutationRate = 0.1;    // ������

    // ����Ⱥ�㷨����
    double inertiaWeight = 0.7;   // ����Ȩ��
    double c1 = 1.5;              // ����ѧϰ����
    double c2 = 1.5;              // ���ѧϰ����

    // ��Ⱥ�㷨����
    double alpha = 1.0;           // ��Ϣ����Ҫ�̶�����
    double beta = 2.0;            // ����ʽ����
    double rho = 0.1;             // ��Ϣ������ϵ��
    double q0 = 100.0;            // ��Ϣ������

    // ģ���˻��㷨����
    double initialTemp = 100.0;   // ��ʼ�¶�
    double coolingRate = 0.95;    // ��ȴ��

    // ��Ⱥ�㷨����
    int scoutBeeCount = 10;       // ��������
    int eliteSiteCount = 5;       // ��Ӣλ������
    int eliteBeeCount = 10;       // ��Ӣ������

    // �����㷨����
    double pulseRate = 0.5;       // ������
    double loudness = 0.5;        // ���
    double frequencyMin = 0.0;    // ��СƵ��
    double frequencyMax = 2.0;    // ���Ƶ��

    // �����㷨����
    double a = 2.0;               // ���Ʋ���a
    double a2 = -1.0;             // ���Ʋ���a2

    // �����㷨����
    double decreaseFactor = 0.01; // ���Ʋ���˥������
};

// ����ִ��ʱ��ṹ��
struct TaskExecutionTime {
    double cpuTime;      // CPUִ��ʱ��
    double memTime;      // �ڴ����ʱ��
    double netTime;      // ���紫��ʱ��
    double totalTime;    // ��ִ��ʱ��
};

// ����������ṹ��
struct TaskAllocationResult {
    std::vector<int> nodeAssignment;                // ������䵽�Ľڵ�ID
    std::chrono::milliseconds executionTime;        // �㷨ִ��ʱ�� (����)
    double resourceUtilization;                     // ��Դ������ (%)
    double responseTime;                            // ��Ӧʱ�� (��)
    double makespan;                                // ������ʱ�� (ʱ�䵥λ)
    double loadBalanceDegree;                       // ���ؾ���� (��׼��)
    double totalTaskTime;                           // ������ִ��ʱ�� (ʱ�䵥λ)
    double normalizedRU;                            // ��һ����Դ������ (������)
    double normalizedRT;                            // ��һ����Ӧʱ�� (������)
    double normalizedMS;                            // ��һ��Makespan (������)
    double normalizedPsi;                           // ��һ�����ؾ���� (������)
    double normalizedTotalTime;                     // ��һ��������ʱ�� (������)
    double objectiveValue;                          // Ŀ�꺯��ֵ (������)
    std::map<int, std::vector<int>> nodeToTasks;    // �ڵ㵽�����ӳ��
    std::vector<double> convergenceHistory;         // ������ʷ
    std::vector<double> iterationTimes;             // ÿ�ε�����ʱ�� (����)
    double avgIterationTime;                        // ƽ������ʱ�� (����)
};

// �����������
class TaskAllocator {
public:
    // ���캯��
    TaskAllocator(const std::vector<Task>& tasks,
        const std::vector<Node>& nodes,
        const ObjectiveWeights& objWeights = ObjectiveWeights(),
        const ResourceWeights& resWeights = ResourceWeights(),
        const AlgorithmParams& algParams = AlgorithmParams());

    // ʹ��ָ���㷨��������
    TaskAllocationResult allocateTasks(AlgorithmType algorithm);

    // ������䷽����Ŀ�꺯��ֵ
    double calculateObjectiveValue(const std::vector<int>& nodeAssignment);

    // ��ȡ�㷨����
    static std::string getAlgorithmName(AlgorithmType algorithm);

    // �����������TXT�ļ�
    static bool saveAllocationResultToFile(const TaskAllocationResult& result,
        AlgorithmType algorithm,
        const std::string& filename);

    // ���������㷨�ıȽϽ����TXT�ļ�
    static bool saveComparisonResultsToFile(const std::map<AlgorithmType, TaskAllocationResult>& results,
        const std::string& filename);

    // �����㷨�������̵�TXT�ļ�
    static bool saveConvergenceToFile(const TaskAllocationResult& result,
        AlgorithmType algorithm,
        const std::string& filename);

    TaskAllocationResult evaluateAssignment(const std::vector<int>& assignment);

    // �����Դ��һ������
    double calculateNormalizedMetric(double value, double minVal, double maxVal) {
        return (value - minVal) / (maxVal - minVal);
    }

    // ���ʱЧ�Լ��
    bool isAssignmentValid(const std::vector<int>& assignment, int timeStep) {
        for (size_t i = 0; i < assignment.size(); ++i) {
            if (tasks[i].timeStep > timeStep) return false;
        }
        return true;
    }


private:
    std::vector<Task> tasks;       // �����б�
    std::vector<Node> nodes;       // �ڵ��б�
    ObjectiveWeights objWeights;   // Ŀ�꺯��Ȩ��
    ResourceWeights resWeights;    // ��ԴȨ��
    AlgorithmParams algParams;     // �㷨����

    // ��������ִ��ʱ��
    TaskExecutionTime calculateTaskExecutionTime(const Task& task, const Node& node);

    // ���ڵ��Ƿ��ܹ�����������Դ����
    bool canNodeSatisfyTask(const Task& task, const Node& node, const std::vector<int>& currentAllocation);

    // ������������Ƿ����㣨�������������ѷ��䣩
    bool areDependenciesSatisfied(const Task& task, const std::vector<int>& currentAllocation);

    // �������Ƿ�ᵼ��ѭ������
    bool wouldCreateCyclicDependency(const Task& task, int nodeId, const std::vector<int>& currentAllocation);

    // ��ȡ����������������У�����������ϵ��
    std::vector<size_t> getTaskTopologicalOrder();

    // ��֤������䷽���Ƿ���Ч����������Լ������ԴԼ����
    bool isValidAssignment(const std::vector<int>& assignment);

    // �޸���Ч��������䷽����ʹ����������Լ����
    std::vector<int> repairInvalidAssignment(const std::vector<int>& assignment);

    // ���ݷ��䷽���������ָ��
    TaskAllocationResult calculateMetrics(const std::vector<int>& nodeAssignment, std::chrono::milliseconds executionTime);

    // ȷ�����������ѷ���
    std::vector<int> ensureAllTasksAllocated(std::vector<int> initialAssignment);

    // ���ҽڵ�����
    int findNodeIndex(int nodeId);

    // ������Դѡ��ڵ�
    int selectNodeBasedOnResources(const Task& task, const std::vector<NodeResource>& resources);

    // ���½ڵ���Դ
    void updateNodeResources(NodeResource& resource, const Task& task, double scale);

    // ����ѡ��������Ⱥ�㷨��
    int rouletteSelect(const std::vector<double>& probabilities, double total);

    // ������Ϣ�أ�������Ⱥ�㷨��
    void updatePheromone(std::vector<std::vector<double>>& pheromone, const std::vector<std::vector<int>>& solutions, const std::vector<double>& fitness);

    // ������������㷨��ʵ��
    std::vector<int> roundRobinAllocation();
    std::vector<int> geneticAllocation();
    std::vector<int> particleSwarmAllocation();
    std::vector<int> beeColonyAllocation();
    std::vector<int> antColonyAllocation();
    std::vector<int> batAllocation();
    std::vector<int> whaleAllocation();
    std::vector<int> simulatedAnnealingAllocation();
    std::vector<int> greyWolfAllocation();
    std::vector<int> hybridGWSAAllocation();

    // ���ڻ�ϻ���-ģ���˻��㷨
    std::vector<int> hybridGWSAGroupAllocation(
        const std::vector<Task>& groupTasks,
        const std::vector<Node>& groupNodes,
        const std::vector<size_t>& taskIndices);

    // �޸�������ϵ
    std::vector<int> repairDependencies(const std::vector<int>& assignment,
        const std::vector<Task>& groupTasks,
        const std::vector<size_t>& taskIndices);

    // ȷ���������������ѷ���
    std::vector<int> ensureGroupTasksAllocated(const std::vector<int>& assignment,
        const std::vector<Task>& groupTasks,
        const std::vector<Node>& groupNodes);

    // ��������Ŀ�꺯��ֵ
    double calculateGroupObjectiveValue(const std::vector<int>& assignment,
        const std::vector<Task>& groupTasks,
        const std::vector<Node>& groupNodes);

    std::vector<double> lastConvergenceHistory; // ���һ��ִ�е�������ʷ
    std::vector<double> lastIterationTimes;     // ���һ��ִ�еĵ���ʱ��
    double avgIterationTime;

    std::vector<int> ensureValidAssignment(std::vector<int> assignment);
    // ���һ��ִ�е�ƽ������ʱ��
};

#endif // TASK_ALLOCATION_H