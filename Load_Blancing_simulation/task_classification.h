#ifndef TASK_CLASSIFICATION_H
#define TASK_CLASSIFICATION_H

#include "data.h"
#include "task_allocation.h"
#include <vector>

// ������ɫ����ö��
enum TaskColorType {
    CPU_INTENSIVE_COLOR,     // ��ɫ - CPU�ܼ���
    MEMORY_INTENSIVE_COLOR,  // ��ɫ - �ڴ��ܼ���
    NETWORK_INTENSIVE_COLOR, // ��ɫ - �����ܼ���
    GENERAL_COLOR            // ��ɫ - ͨ����
};

// �ڵ�������ö��
enum NodeGroupType {
    CPU_INTENSIVE_GROUP,     // CPU�ܼ�����
    MEMORY_INTENSIVE_GROUP,  // �ڴ��ܼ�����
    NETWORK_INTENSIVE_GROUP, // �����ܼ�����
    GENERAL_GROUP            // ͨ������
};

// �ڵ㸺��״̬ö�٣���λ�������
enum NodeLoadState {
    BALANCED = 0,          // ƽ��
    CPU_OVERLOAD = 1,      // CPU����
    MEMORY_OVERLOAD = 2,   // �ڴ����
    NETWORK_OVERLOAD = 4,  // �������
    CPU_UNDERLOAD = 8,     // CPU����
    MEMORY_UNDERLOAD = 16, // �ڴ����
    NETWORK_UNDERLOAD = 32 // �������
};

// ������ɫ��Ϣ�ṹ��
struct TaskColorInfo {
    int taskId;
    TaskColorType colorType;
    int colorShade = 0;    // ��ɫ��ǳ��0-5��
    int level = 0;         // ���񼶱�
    bool dependencyProcessed = false;
};

// �ڵ���Ϣ�ṹ��
struct NodeInfo {
    int nodeId;
    double performance;  // �ۺ�����ָ��
    double loadRate;     // ������
    NodeLoadState loadState;
};

// �ڵ���ṹ��
struct NodeGroup {
    NodeGroupType groupType;
    std::vector<NodeInfo> nodes;
    int monitorNodeId = -1;  // ��ؽڵ�ID
    double avgLoadRate = 0.0;  // ��ƽ��������
};

// ����������ɫ��
class TaskColorizer {
public:
    TaskColorizer(const std::vector<Task>& tasks,
        const ResourceWeights& resWeights = ResourceWeights());

    // ����������ۺ���Դ����
    double calculateResourceIndex(const Task& task);

    // ȷ���������ɫ����
    TaskColorType determineTaskColorType(const Task& task);

    // ������ɫ�㷨
    std::vector<TaskColorInfo> colorTasks();

private:
    std::vector<Task> tasks;
    ResourceWeights resWeights;
};

// �ڵ����ͷ�����
class NodeGrouper {
public:
    NodeGrouper(const std::vector<Node>& nodes,
        const ResourceWeights& resWeights = ResourceWeights());

    // ����ڵ���ۺ�����ָ��
    double calculatePerformanceIndex(const Node& node);

    // ȷ���ڵ��������
    NodeGroupType determineNodeGroupType(const Node& node);

    // ����ڵ���ۺϸ�����
    double calculateLoadRate(const Node& node);

    // ȷ���ڵ�ĸ���״̬
    NodeLoadState determineLoadState(const Node& node);

    // ����ڵ㲢ѡ�ټ�ؽڵ�
    std::vector<NodeGroup> groupNodes();

private:
    std::vector<Node> nodes;
    ResourceWeights resWeights;
};

#endif // TASK_CLASSIFICATION_H