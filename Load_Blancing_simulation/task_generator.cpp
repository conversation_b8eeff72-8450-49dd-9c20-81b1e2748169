#include "data.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <algorithm>
#include <unordered_map>
#include <queue>
#include <ctime>

// ������豸
static std::random_device rd;

// ��ȡ�������
static std::default_random_engine getRandomEngine() {
    // ʹ�õ�ǰʱ����Ϊ�����Ի�ø��õ������
    return std::default_random_engine(rd() ^ std::time(nullptr));
}

// ����ָ����Χ�ڵ��������
static int getRandomInt(std::default_random_engine& engine, int min, int max) {
    if (min > max) {
        std::swap(min, max);
    }
    std::uniform_int_distribution<int> dist(min, max);
    return dist(engine);
}

// ����ָ����Χ�ڵ����������
static double getRandomDouble(std::default_random_engine& engine, double min, double max) {
    std::uniform_real_distribution<double> dist(min, max);
    return dist(engine);
}

// ��Դ���������ѡ��ָ��������Ԫ��
static std::vector<int> getRandomSubset(std::default_random_engine& engine, const std::vector<int>& source, int num) {
    std::vector<int> subset = source;

    // ���������Ӽ���С����Դ���ϵĴ�С��������ΪԴ���ϵĴ�С
    if (num > subset.size()) {
        num = subset.size();
    }

    // ���Ҽ���
    std::shuffle(subset.begin(), subset.end(), engine);

    // ����ǰnum��Ԫ��
    subset.resize(num);
    return subset;
}

// ����Ƿ���ڴ���������ϵ
static bool hasTransitiveDependency(
    const std::unordered_map<int, std::unordered_set<int>>& dependencyMap,
    int from, int to, std::unordered_set<int>& visited, int currentLayer, int maxLayers) {

    // �����ǰ�����������������򷵻�false�Է�ֹ����ĵݹ�
    if (currentLayer > maxLayers) {
        return false;
    }

    // ����Ѿ����ʹ�����ڵ㣬����ѭ������
    if (visited.find(from) != visited.end()) {
        return false;
    }

    // ��ǵ�ǰ�ڵ�Ϊ�ѷ���
    visited.insert(from);

    // ����Ƿ����ֱ������
    auto it = dependencyMap.find(from);
    if (it != dependencyMap.end()) {
        const auto& deps = it->second;

        // �������ֱ��������ϵ
        if (deps.find(to) != deps.end()) {
            return true;
        }

        // ����Ƿ���ڴ���������ϵ
        for (int dep : deps) {
            if (hasTransitiveDependency(dependencyMap, dep, to, visited, currentLayer + 1, maxLayers)) {
                return true;
            }
        }
    }

    return false;
}

// �Ż�������ϵ���Ƴ���������
static std::unordered_set<int> optimizeDependencies(
    const std::unordered_map<int, std::unordered_set<int>>& dependencyMap,
    int taskId, int maxLayers) {

    std::unordered_set<int> result;
    auto it = dependencyMap.find(taskId);
    if (it == dependencyMap.end()) {
        return result;
    }

    const auto& directDeps = it->second;
    for (int dep : directDeps) {
        bool isRedundant = false;

        // ������������Ƿ���Դ��ݵ��������
        for (int otherDep : directDeps) {
            if (otherDep != dep) {
                std::unordered_set<int> visited;
                if (hasTransitiveDependency(dependencyMap, otherDep, dep, visited, 1, maxLayers)) {
                    isRedundant = true;
                    break;
                }
            }
        }

        // ���������������������ӵ������
        if (!isRedundant) {
            result.insert(dep);
        }
    }

    return result;
}

// ȷ����������
static TaskType determineTaskType(const TaskResourceDemand& demand, const TaskGenerationParams& params) {
    // ��������Դ����
    double totalDemand = params.cpuWeight * demand.cpuDemand +
        params.memoryWeight * demand.memoryDemand +
        params.networkWeight * demand.bandwidthDemand;

    // �������Դռ��
    double cpuRatio = (params.cpuWeight * demand.cpuDemand) / totalDemand;
    double memoryRatio = (params.memoryWeight * demand.memoryDemand) / totalDemand;
    double networkRatio = (params.networkWeight * demand.bandwidthDemand) / totalDemand;

    // �ж���Դ����
    if (cpuRatio > 0.6) {
        return TASK_CPU_INTENSIVE;
    }
    else if (memoryRatio > 0.6) {
        return TASK_MEMORY_INTENSIVE;
    }
    else if (networkRatio > 0.6) {
        return TASK_NETWORK_INTENSIVE;
    }
    else {
        return TASK_GENERAL;
    }
}

bool DataGenerator::generateTaskDataset(const std::string& filename, const TaskGenerationParams& params) {
    try {
        std::ofstream outFile(filename);
        if (!outFile.is_open()) {
            std::cerr << "�޷����ļ�: " << filename << std::endl;
            return false;
        }

        // д�������
        outFile << "������ ʱ�� CPU���� �ڴ����� �������� �������� ��������" << std::endl;

        auto engine = getRandomEngine();
        std::vector<std::vector<int>> tasksPerTimeStep(params.timeSteps + 1);

        // �洢���������������ϵ
        std::unordered_map<int, std::unordered_set<int>> dependencyMap;

        // ����ÿ�����͵�Ŀ����������
        int totalTasks = params.timeSteps * params.tasksPerTimeStep;
        int targetCpuIntensive = totalTasks * 0.3;  // 30%
        int targetMemoryIntensive = totalTasks * 0.3;  // 30%
        int targetNetworkIntensive = totalTasks * 0.3;  // 30%
        int targetGeneral = totalTasks * 0.3;  // 10%

        // ����ÿ�����͵�����
        int cpuIntensiveCount = 0;
        int memoryIntensiveCount = 0;
        int networkIntensiveCount = 0;
        int generalCount = 0;

        int taskId = 1;
        for (int timeStep = 1; timeStep <= params.timeSteps; ++timeStep) {
            std::vector<int>& currentTasks = tasksPerTimeStep[timeStep];

            for (int j = 0; j < params.tasksPerTimeStep; ++j) {
                // ������Դ����
                TaskResourceDemand demand;
                TaskType taskType;

                // ���ݵ�ǰ�����ɵĸ���������������������һ�����������
                if (cpuIntensiveCount < targetCpuIntensive &&
                    (memoryIntensiveCount >= targetMemoryIntensive ||
                        networkIntensiveCount >= targetNetworkIntensive ||
                        getRandomDouble(engine, 0.0, 1.0) < 0.4)) {
                    // ����CPU�ܼ�������
                    demand.cpuDemand = getRandomInt(engine, params.maxCpuDemand * 0.7, params.maxCpuDemand);
                    demand.memoryDemand = getRandomInt(engine, params.minMemoryDemand, params.maxMemoryDemand * 0.4);
                    demand.bandwidthDemand = getRandomInt(engine, params.minBandwidthDemand, params.maxBandwidthDemand * 0.4);
                    taskType = TASK_CPU_INTENSIVE;
                    cpuIntensiveCount++;
                }
                else if (memoryIntensiveCount < targetMemoryIntensive &&
                    (networkIntensiveCount >= targetNetworkIntensive ||
                        getRandomDouble(engine, 0.0, 1.0) < 0.5)) {
                    // �����ڴ��ܼ�������
                    demand.cpuDemand = getRandomInt(engine, params.minCpuDemand, params.maxCpuDemand * 0.4);
                    demand.memoryDemand = getRandomInt(engine, params.maxMemoryDemand * 0.7, params.maxMemoryDemand);
                    demand.bandwidthDemand = getRandomInt(engine, params.minBandwidthDemand, params.maxBandwidthDemand * 0.4);
                    taskType = TASK_MEMORY_INTENSIVE;
                    memoryIntensiveCount++;
                }
                else if (networkIntensiveCount < targetNetworkIntensive) {
                    // ����ͨ���ܼ�������
                    demand.cpuDemand = getRandomInt(engine, params.minCpuDemand, params.maxCpuDemand * 0.4);
                    demand.memoryDemand = getRandomInt(engine, params.minMemoryDemand, params.maxMemoryDemand * 0.4);
                    demand.bandwidthDemand = getRandomInt(engine, params.maxBandwidthDemand * 0.7, params.maxBandwidthDemand);
                    taskType = TASK_NETWORK_INTENSIVE;
                    networkIntensiveCount++;
                }
                else {
                    // ������ͨ����
                    demand.cpuDemand = getRandomInt(engine, params.minCpuDemand, params.maxCpuDemand * 0.6);
                    demand.memoryDemand = getRandomInt(engine, params.minMemoryDemand, params.maxMemoryDemand * 0.6);
                    demand.bandwidthDemand = getRandomInt(engine, params.minBandwidthDemand, params.maxBandwidthDemand * 0.6);
                    taskType = TASK_GENERAL;
                    generalCount++;
                }

                currentTasks.push_back(taskId);

                // ���������������
                std::unordered_set<int> dependencySet;

                if (getRandomDouble(engine, 0.0, 1.0) < params.dependencyProbability) {
                    // ֻ����������ǰʱ���Ѿ����ɵ�����
                    std::vector<int> availableTasks;
                    for (int t = 0; t < j; ++t) {  // ��ǰʱ��֮ǰ�Ѿ����ɵ�����
                        availableTasks.push_back(currentTasks[t]);
                    }

                    // ���ѡ����������
                    if (!availableTasks.empty()) {
                        int numDependencies = getRandomInt(engine, 1, std::min(params.maxDependencies, (int)availableTasks.size()));
                        std::vector<int> dependencies = getRandomSubset(engine, availableTasks, numDependencies);

                        // ��������ӵ����Ϻ�ӳ����
                        for (int dep : dependencies) {
                            dependencySet.insert(dep);
                        }
                        dependencyMap[taskId] = dependencySet;
                    }
                }

                taskId++;
            }
        }

        // ��������������������Ѿ����ɣ���ʼ�Ż���д���ļ�
        taskId = 1;
        for (int timeStep = 1; timeStep <= params.timeSteps; ++timeStep) {
            std::vector<int>& currentTasks = tasksPerTimeStep[timeStep];

            for (int j = 0; j < currentTasks.size(); ++j) {
                int currentTaskId = currentTasks[j];

                // ��ȡ�������Դ�����������ɱ���һ���ԣ�
                TaskResourceDemand demand;
                TaskType taskType;

                // ���ݵ�ǰ����ID��ģ��ȷ�����ͣ�������֮ǰ���ɵķֲ�һ��
                int typeIndex = (currentTaskId - 1) % 10; // �ֳ�10�ݣ���0-9��ģ����������
                if (typeIndex < 3) { // 0,1,2
                    // CPU�ܼ�������
                    demand.cpuDemand = getRandomInt(engine, params.maxCpuDemand * 0.7, params.maxCpuDemand);
                    demand.memoryDemand = getRandomInt(engine, params.minMemoryDemand, params.maxMemoryDemand * 0.4);
                    demand.bandwidthDemand = getRandomInt(engine, params.minBandwidthDemand, params.maxBandwidthDemand * 0.4);
                    taskType = TASK_CPU_INTENSIVE;
                }
                else if (typeIndex < 6) { // 3,4,5
                    // �ڴ��ܼ�������
                    demand.cpuDemand = getRandomInt(engine, params.minCpuDemand, params.maxCpuDemand * 0.4);
                    demand.memoryDemand = getRandomInt(engine, params.maxMemoryDemand * 0.7, params.maxMemoryDemand);
                    demand.bandwidthDemand = getRandomInt(engine, params.minBandwidthDemand, params.maxBandwidthDemand * 0.4);
                    taskType = TASK_MEMORY_INTENSIVE;
                }
                else if (typeIndex < 9) { // 6,7,8
                    // ͨ���ܼ�������
                    demand.cpuDemand = getRandomInt(engine, params.minCpuDemand, params.maxCpuDemand * 0.4);
                    demand.memoryDemand = getRandomInt(engine, params.minMemoryDemand, params.maxMemoryDemand * 0.4);
                    demand.bandwidthDemand = getRandomInt(engine, params.maxBandwidthDemand * 0.7, params.maxBandwidthDemand);
                    taskType = TASK_NETWORK_INTENSIVE;
                }
                else { // 9
                    // ��ͨ����
                    demand.cpuDemand = getRandomInt(engine, params.minCpuDemand, params.maxCpuDemand * 0.6);
                    demand.memoryDemand = getRandomInt(engine, params.minMemoryDemand, params.maxMemoryDemand * 0.6);
                    demand.bandwidthDemand = getRandomInt(engine, params.minBandwidthDemand, params.maxBandwidthDemand * 0.6);
                    taskType = TASK_GENERAL;
                }

                // �Ż�������ϵ���Ƴ���������
                std::unordered_set<int> optimizedDeps = optimizeDependencies(dependencyMap, currentTaskId, params.maxDependencyLayers);

                // ���������ַ���
                std::stringstream depStream;
                for (int dep : optimizedDeps) {
                    depStream << dep << " ";
                }

                // д��������Ϣ��������������֮����ӷָ���
                outFile << currentTaskId << " "
                    << timeStep << " "
                    << demand.cpuDemand << " "
                    << demand.memoryDemand << " "
                    << demand.bandwidthDemand << " "
                    << depStream.str() << "; " // ��ӷֺ���Ϊ�ָ���
                    << static_cast<int>(taskType) << std::endl;
            }
        }

        outFile.close();

        // �����������ͳ��
        std::cout << "��������ͳ�ƣ�" << std::endl;
        std::cout << "CPU�ܼ�����������: " << cpuIntensiveCount << " (" << (100.0 * cpuIntensiveCount / totalTasks) << "%)" << std::endl;
        std::cout << "�ڴ��ܼ�����������: " << memoryIntensiveCount << " (" << (100.0 * memoryIntensiveCount / totalTasks) << "%)" << std::endl;
        std::cout << "ͨ���ܼ�����������: " << networkIntensiveCount << " (" << (100.0 * networkIntensiveCount / totalTasks) << "%)" << std::endl;
        std::cout << "��ͨ��������: " << generalCount << " (" << (100.0 * generalCount / totalTasks) << "%)" << std::endl;

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "�����������ݼ�ʱ����: " << e.what() << std::endl;
        return false;
    }
}