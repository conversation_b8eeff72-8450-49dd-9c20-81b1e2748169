#include "timestep_classification_group.h"
#include <iostream>
#include <algorithm>
#include <unordered_map>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <numeric>  // Ϊstd::accumulate���
#include <cmath>    // Ϊstd::isfinite���
// ���ļ�ͷ���������������
#include "task_allocation.h"  // ȷ�������������ඨ��
// ��ӱ�Ҫͷ�ļ�
#include <string>
#include <sstream> 
#include "task_classification.h"  // ����������ͷ�ļ�
#include "multi_timestep_simulation.h"



// ���std::isfinite�ĺ��������ڼ����ֵ�Ƿ���Ч������NaNҲ���������
bool isValidNumber(double value) {
    return !std::isnan(value) && !std::isinf(value);
}

// ���캯��
MultiTimestepSimulation::MultiTimestepSimulation(const SimulationParams& params)
    : params(params) {
}

// ��������ͽڵ�����
bool MultiTimestepSimulation::loadData(const std::string& tasksFile, const std::string& nodesFile) {
    // ������������
    tasks.clear();
    std::ifstream taskStream(tasksFile);
    if (!taskStream.is_open()) {
        std::cerr << "�޷��������ļ�: " << tasksFile << std::endl;
        return false;
    }

    std::string line;
    // ����������
    std::getline(taskStream, line);

    while (std::getline(taskStream, line)) {
        std::istringstream iss(line);
        Task task;
        int taskId, timeStep, cpuDemand, memDemand, bwDemand;

        // ���������ֶ�
        if (!(iss >> taskId >> timeStep >> cpuDemand >> memDemand >> bwDemand)) {
            continue;
        }

        task.id = taskId;
        task.timeStep = timeStep;
        task.resourceDemand.cpuDemand = cpuDemand;
        task.resourceDemand.memoryDemand = memDemand;
        task.resourceDemand.bandwidthDemand = bwDemand;

        // �������������������
        std::string dependenciesAndType;
        std::getline(iss, dependenciesAndType);

        size_t semicolonPos = dependenciesAndType.find(';');
        if (semicolonPos != std::string::npos) {
            // ����������
            std::string dependenciesStr = dependenciesAndType.substr(0, semicolonPos);
            std::istringstream depIss(dependenciesStr);
            int depId;
            while (depIss >> depId) {
                task.dependencies.insert(depId);
            }

            // ������������
            std::string typeStr = dependenciesAndType.substr(semicolonPos + 1);
            std::istringstream typeIss(typeStr);
            int type;
            if (typeIss >> type) {
                task.type = static_cast<TaskType>(type);
            }
            else {
                task.type = TASK_GENERAL; // Ĭ������
            }
        }
        else {
            // ���ݾɸ�ʽ����
            std::istringstream fullIss(dependenciesAndType);
            std::string token;
            bool foundType = false;

            while (fullIss >> token) {
                try {
                    int value = std::stoi(token);
                    if (value >= 1 && value <= 4 && !foundType) {
                        task.type = static_cast<TaskType>(value);
                        foundType = true;
                    }
                    else {
                        task.dependencies.insert(value);
                    }
                }
                catch (...) {
                    // ���Է���������
                }
            }
            if (!foundType) {
                task.type = TASK_GENERAL;
            }
        }

        // �Ƴ�ʱ�䲽ɸѡ��ԭ������Ҫȫ��ʱ�䲽��
        tasks.push_back(task);
    }

    taskStream.close();

    // ����ʱ�䲽��������з���
    tasksByTimeStep.clear();
    for (const auto& task : tasks) {
        tasksByTimeStep[task.timeStep].push_back(task);
    }

    // ���ؽڵ�����
    nodes.clear();
    std::ifstream nodeStream(nodesFile);
    if (!nodeStream.is_open()) {
        std::cerr << "�޷��򿪽ڵ��ļ�: " << nodesFile << std::endl;
        return false;
    }

    // ����������
    std::getline(nodeStream, line);

    // �����ڵ�����
    int nodeId, nodeTimeStep, cpuCapacity, memCapacity, bwCapacity, currentCpu, currentMem, currentBw, nodeType;

    while (nodeStream >> nodeId >> nodeTimeStep >> cpuCapacity >> memCapacity >> bwCapacity >>
        currentCpu >> currentMem >> currentBw >> nodeType) {
        Node node;
        node.id = nodeId;
        node.timeStep = nodeTimeStep;
        node.resource.cpuCapacity = cpuCapacity;
        node.resource.memoryCapacity = memCapacity;
        node.resource.bandwidthCapacity = bwCapacity;
        node.resource.currentCpu = currentCpu;
        node.resource.currentMemory = currentMem;
        node.resource.currentBandwidth = currentBw;
        node.type = static_cast<NodeType>(nodeType);

        nodes.push_back(node);
    }

    nodeStream.close();

    // �����ʼ�ڵ�״̬
    originalNodes = nodes;

    std::cout << "�ɹ��������ݣ�" << tasks.size() << " ������"
        << nodes.size() << " ���ڵ�" << std::endl;
    std::cout << "����ʱ�䲽�ֲ���" << std::endl;

    for (const auto& pair : tasksByTimeStep) {
        std::cout << "  ʱ�䲽 " << pair.first << ": " << pair.second.size() << " ������" << std::endl;
    }

    return true;
}

// �����ı��浥��ʱ�䲽����ĺ���
void MultiTimestepSimulation::saveTimeStepResults(
    const std::map<AlgorithmType, TaskAllocationResult>& results,
    int timeStep) {

    std::string filename = "timestep_classification_group/timestep_" + std::to_string(timeStep) + ".csv";

    std::ofstream outFile(filename);
    if (!outFile.is_open()) {
        std::cerr << "�޷������ļ�: " << filename << std::endl;
        return;
    }

    // CSVͷ��
    outFile << "Algorithm,ResourceUtilization(%),ResponseTime(ms),Makespan,"
        << "LoadBalanceDegree,TotalTaskTime,ObjectiveValue\n";

    // �������
    for (const auto& pair : results) {
        AlgorithmType algorithm = pair.first;
        const TaskAllocationResult& result = pair.second;

        // ��鲢�����쳣ֵ
        double safeResourceUtilization = isValidNumber(result.resourceUtilization) ? result.resourceUtilization : 0.0;
        double safeResponseTime = isValidNumber(result.executionTime.count()) ? result.executionTime.count() : 0.0;
        double safeMakespan = isValidNumber(result.makespan) ? result.makespan : 0.1;
        double safeLoadBalance = isValidNumber(result.loadBalanceDegree) ? result.loadBalanceDegree : 0.0;
        double safeTotalTaskTime = isValidNumber(result.totalTaskTime) ? result.totalTaskTime : 0.0;
        double safeObjectiveValue = isValidNumber(result.objectiveValue) ? result.objectiveValue : 0.0;

        outFile << TaskAllocator::getAlgorithmName(algorithm) << ","
            << safeResourceUtilization << ","
            << safeResponseTime << ","
            << safeMakespan << ","
            << safeLoadBalance << ","
            << safeTotalTaskTime << ","
            << safeObjectiveValue << "\n";
    }

    outFile.close();
    std::cout << "ʱ�䲽 " << timeStep << " ����ѱ�����: " << filename << std::endl;
}

// ������ຯ��
std::map<TaskType, std::vector<Task>> MultiTimestepSimulation::classifyTasks(const std::vector<Task>& tasks) {
    std::map<TaskType, std::vector<Task>> tasksByType;

    // ʹ�������type����ֱ�ӷ���
    for (const auto& task : tasks) {
        tasksByType[task.type].push_back(task);
    }

    // �������ͳ��
    std::cout << "\n===== �������ͳ�� =====" << std::endl;
    for (int type = 1; type <= 4; type++) {
        TaskType taskType = static_cast<TaskType>(type);
        std::string typeName;
        switch (taskType) {
        case TASK_CPU_INTENSIVE: typeName = "CPU�ܼ���"; break;
        case TASK_MEMORY_INTENSIVE: typeName = "�ڴ��ܼ���"; break;
        case TASK_NETWORK_INTENSIVE: typeName = "ͨ���ܼ���"; break;
        case TASK_GENERAL: typeName = "ͨ����"; break;
        default: typeName = "δ֪����";
        }
        std::cout << typeName << ": " << tasksByType[taskType].size() << " ������" << std::endl;
    }

    return tasksByType;
}

// �ڵ���麯��
std::map<NodeType, std::vector<Node>> MultiTimestepSimulation::groupNodes(const std::vector<Node>& nodes) {
    std::map<NodeType, std::vector<Node>> nodesByType;

    // ʹ�ýڵ��type����ֱ�ӷ���
    for (const auto& node : nodes) {
        nodesByType[node.type].push_back(node);
    }

    // �������ͳ��
    std::cout << "\n===== �ڵ����ͳ�� =====" << std::endl;
    for (int type = 1; type <= 4; type++) {
        NodeType nodeType = static_cast<NodeType>(type);
        std::string typeName;
        switch (nodeType) {
        case CPU_INTENSIVE: typeName = "CPU�ܼ���"; break;
        case MEMORY_INTENSIVE: typeName = "�ڴ��ܼ���"; break;
        case NETWORK_INTENSIVE: typeName = "�����ܼ���"; break;
        case GENERAL: typeName = "ͨ����"; break;
        default: typeName = "δ֪����";
        }
        std::cout << typeName << ": " << nodesByType[nodeType].size() << " ���ڵ�" << std::endl;
    }

    return nodesByType;
}

// ���������������
TaskAllocationResult MultiTimestepSimulation::allocateTasksWithClassification(
    const std::vector<Task>& currentTasks,
    std::vector<Node>& currentNodes,
    AlgorithmType algorithm) {

    // ���û��������Ҫ���䣬ֱ�ӷ��ؿս��
    if (currentTasks.empty()) {
        TaskAllocationResult emptyResult;
        return emptyResult;
    }

    // ��������з���
    auto tasksByType = classifyTasks(currentTasks);

    // �Խڵ���з���
    auto nodesByType = groupNodes(currentNodes);

    // ��������ID��������ӳ��
    std::map<int, size_t> taskIdToIndex;
    for (size_t i = 0; i < currentTasks.size(); i++) {
        taskIdToIndex[currentTasks[i].id] = i;
    }

    // �������շ�����
    std::vector<int> finalAssignment(currentTasks.size(), 0);

    // ��¼���俪ʼʱ��
    auto startTime = std::chrono::high_resolution_clock::now();

    // ��ÿ���������ͽ��з���
    for (int type = 1; type <= 4; type++) {
        TaskType taskType = static_cast<TaskType>(type);
        NodeType nodeType = static_cast<NodeType>(type);

        // ���������û����������
        if (tasksByType[taskType].empty()) {
            std::cout << "���� " << type << " û����������" << std::endl;
            continue;
        }

        // ��ȡ�ڵ��б����������û�нڵ㣬����ʹ��ͨ�����ͽڵ�
        std::vector<Node> currentTypeNodes;
        if (nodesByType[nodeType].empty() && type != 4) {
            std::cout << "���� " << type << " û�нڵ㣬����ʹ��ͨ�ýڵ�(����4)" << std::endl;
            currentTypeNodes = nodesByType[GENERAL];
            if (currentTypeNodes.empty()) {
                std::cout << "ͨ�ýڵ㲻���ã��޷��������� " << type << " ������" << std::endl;
                continue;
            }
        }
        else {
            currentTypeNodes = nodesByType[nodeType];
        }

        std::cout << "\n===== ʹ�� " << TaskAllocator::getAlgorithmName(algorithm)
            << " Ϊ���� " << type << " �������� ("
            << tasksByType[taskType].size() << "������, "
            << currentTypeNodes.size() << "���ڵ�) =====" << std::endl;

        // ����������ʵ����ִ���������
        TaskAllocator allocator(tasksByType[taskType], currentTypeNodes,
            params.objectiveWeights, params.resourceWeights, params.algorithmParams);
        TaskAllocationResult typeResult = allocator.allocateTasks(algorithm);

        // �������ڷ����������
        int groupAssigned = 0;
        for (int nodeId : typeResult.nodeAssignment) {
            if (nodeId > 0) {
                groupAssigned++;
            }
        }

        std::cout << "���� " << type << " �ɹ����� " << groupAssigned << "/"
            << tasksByType[taskType].size() << " ������" << std::endl;

        // �����ڷ������ϲ���ȫ�ֽ��
        for (size_t i = 0; i < tasksByType[taskType].size() && i < typeResult.nodeAssignment.size(); i++) {
            if (typeResult.nodeAssignment[i] > 0) {
                int taskId = tasksByType[taskType][i].id;
                if (taskIdToIndex.find(taskId) != taskIdToIndex.end()) {
                    finalAssignment[taskIdToIndex[taskId]] = typeResult.nodeAssignment[i];
                }
            }
        }
    }

    // ��¼�������ʱ��
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    // ��������ָ��
    TaskAllocator finalAllocator(currentTasks, currentNodes,
        params.objectiveWeights, params.resourceWeights, params.algorithmParams);
    TaskAllocationResult finalResult = finalAllocator.evaluateAssignment(finalAssignment);
    finalResult.executionTime = duration;

    return finalResult;
}

// ִ�ж�ʱ�䲽����
void MultiTimestepSimulation::runSimulation() {
    std::cout << "��ʼִ�ж�ʱ�䲽����..." << std::endl;

    // ����֧�ֵ��㷨�б�
    const std::vector<AlgorithmType> algorithms = {
        AlgorithmType::ROUND_ROBIN,
        AlgorithmType::GENETIC,
        AlgorithmType::PARTICLE_SWARM,
        AlgorithmType::BAT,
        AlgorithmType::WHALE,
        AlgorithmType::SIMULATED_ANNEALING,
        AlgorithmType::GREY_WOLF,
        AlgorithmType::HYBRID_GW_SA
    };

    // ��ʼ�����ݽṹ
    std::map<AlgorithmType, std::vector<TaskAllocationResult>> algorithmResults;
    std::map<AlgorithmType, std::map<int, TaskExecutionInfo>> algorithmTaskInfo;
    std::map<int, std::map<AlgorithmType, std::vector<Node>>> algorithmNodeSnapshots;

    // ��ʼ���ڵ�״̬
    for (AlgorithmType alg : algorithms) {
        algorithmNodeSnapshots[0][alg] = originalNodes;
    }

    // ��ȡʱ�䲽������
    std::vector<int> timeSteps;
    for (const auto& pair : tasksByTimeStep) {
        timeSteps.push_back(pair.first);
    }
    std::sort(timeSteps.begin(), timeSteps.end());

    // ��ѭ��
    for (int timeStep : timeSteps) {
        const std::vector<Task>& currentTasks = tasksByTimeStep.at(timeStep); // ��ȫ����

        std::map<AlgorithmType, TaskAllocationResult> currentTimeStepResults;
        std::map<AlgorithmType, std::vector<Node>> currentAlgoNodes;

        for (AlgorithmType algorithm : algorithms) {
            // �ָ��㷨ר���Ľڵ�״̬
            std::vector<Node> algoNodes = algorithmNodeSnapshots[timeStep - 1][algorithm];

            // ������Դ
            updateNodeResourcesForAlgorithm(timeStep, algorithmTaskInfo[algorithm], algoNodes);
            printNodeResourceStatus(algoNodes);

            // �������
            TaskAllocationResult result;
            if (params.useClassificationAndGrouping) {
                result = allocateTasksWithClassification(currentTasks, algoNodes, algorithm);
            }
            else {
                TaskAllocator allocator(currentTasks, algoNodes,
                    params.objectiveWeights, params.resourceWeights, params.algorithmParams);
                result = allocator.allocateTasks(algorithm);
            }

            // ������
            algorithmResults[algorithm].push_back(result);
            currentAlgoNodes[algorithm] = algoNodes;
            currentTimeStepResults[algorithm] = result;
        }

        algorithmNodeSnapshots[timeStep] = currentAlgoNodes;
        saveTimeStepResults(currentTimeStepResults, timeStep);
    }

    // ������
    printSimulationResults(algorithmResults);
    saveSimulationResults(algorithmResults, "simulation_results.txt");
}



// �㷨ר������Դ���º���
void MultiTimestepSimulation::updateNodeResourcesForAlgorithm(
    int timeStep,
    std::map<int, TaskExecutionInfo>& taskInfo,
    std::vector<Node>& algoNodes) {  // �����㷨ר���Ľڵ�

    // ��ԭupdateNodeResources�еĴ���Ǩ�����ˣ�����node���������algoNodes
    for (auto& node : algoNodes) {
        // ... [ԭ��Դ�����߼�] ...
    }
}

// �㷨ר����������Ϣ���º���
void MultiTimestepSimulation::updateTaskExecutionInfoForAlgorithm(
    int timeStep,
    const std::vector<Task>& tasks,
    const TaskAllocationResult& result,
    std::map<int, TaskExecutionInfo>& taskInfo,
    std::vector<Node>& algoNodes) {  // ʹ���㷨ר���Ľڵ�

    // ��ԭupdateTaskExecutionInfo�еĽڵ����ø�ΪalgoNodes
    for (size_t i = 0; i < tasks.size(); ++i) {
        int nodeId = result.nodeAssignment[i];
        auto it = std::find_if(algoNodes.begin(), algoNodes.end(),
            [nodeId](const Node& n) { return n.id == nodeId; });
        // ... [�����߼�] ...
    }
}



double MultiTimestepSimulation::calculateLoadBalance(const std::vector<Node>& nodes) {
    if (nodes.empty()) return 0.0;

    double totalUtilization = 0.0;
    int validNodeCount = 0;

    for (const auto& node : nodes) {
        if (node.resource.cpuCapacity > 0) {  // ȷ����������
            double cpuUtil = 1.0 - (static_cast<double>(node.resource.currentCpu) / static_cast<double>(node.resource.cpuCapacity));
            // ȷ���������ں���Χ��
            cpuUtil = std::max(0.0, std::min(1.0, cpuUtil));
            totalUtilization += cpuUtil;
            validNodeCount++;
        }
    }

    return validNodeCount > 0 ? (totalUtilization / validNodeCount) : 0.0;
}

double MultiTimestepSimulation::calculateTotalTaskTime(int timestep) {
    double total = 0.0;
    if (timestepResults.count(timestep)) {
        for (const auto& pair : timestepResults[timestep]) {
            if (isValidNumber(pair.second.totalTaskTime) && pair.second.totalTaskTime >= 0) {
                total += pair.second.totalTaskTime;
            }
        }
    }
    return total;
}

// �޸ĺ��recalculateMetrics����
void MultiTimestepSimulation::recalculateMetrics(int currentTimeStep) {
    double maxMakespan = 0.0;

    // ��������ʱ�䲽�е����makespan
    for (int ts = 1; ts <= currentTimeStep; ts++) {
        if (timestepResults.count(ts)) {
            for (const auto& pair : timestepResults[ts]) {
                AlgorithmType alg = pair.first;
                const TaskAllocationResult& result = pair.second;
                if (isValidNumber(result.makespan)) {  // ����Ƿ�Ϊ����ֵ
                    maxMakespan = std::max<double>(maxMakespan, result.makespan);
                }
            }
        }
    }

    if (maxMakespan <= 0.0) maxMakespan = 0.1;  // ȷ����Ϊ��

    // ���㵱ǰ���ؾ����
    double loadBalance = calculateLoadBalance(nodes);
    if (!isValidNumber(loadBalance) || loadBalance < 0) loadBalance = 0.0;  // ����Ƿ�Ϊ�����ҷǸ�ֵ

    // ����������ʱ��
    double totalTaskTime = calculateTotalTaskTime(currentTimeStep);
    if (!isValidNumber(totalTaskTime) || totalTaskTime < 0) totalTaskTime = 0.0;  // ����Ƿ�Ϊ�����ҷǸ�ֵ

    // ���µ�ǰʱ�䲽�������㷨���
    if (timestepResults.count(currentTimeStep)) {
        for (auto& pair : timestepResults[currentTimeStep]) {
            AlgorithmType algorithm = pair.first;
            TaskAllocationResult& result = pair.second;

            // ����ָ�꣬ȷ��ֵ��Ч
            result.makespan = std::max(0.1, maxMakespan);
            result.loadBalanceDegree = std::max(0.0, loadBalance);
            result.totalTaskTime = std::max(0.0, totalTaskTime);

            // ȷ����һ��ֵ��[0,1]��Χ��
            result.normalizedRU = std::max(0.0, std::min(1.0, result.normalizedRU));
            result.normalizedRT = std::max(0.0, std::min(1.0, result.normalizedRT));
            result.normalizedMS = std::max(0.0, std::min(1.0, result.normalizedMS));
            result.normalizedPsi = std::max(0.0, std::min(1.0, result.normalizedPsi));
            result.normalizedTotalTime = std::max(0.0, std::min(1.0, result.normalizedTotalTime));

            // ���¼���Ŀ�꺯��ֵ
            result.objectiveValue =
                params.objectiveWeights.alpha * result.normalizedRU +
                params.objectiveWeights.beta * result.normalizedRT +
                params.objectiveWeights.gamma * result.normalizedMS +
                params.objectiveWeights.epsilon * result.normalizedPsi +
                params.objectiveWeights.xi * result.normalizedTotalTime;
        }
    }
}

// ��ӡ���������
void MultiTimestepSimulation::printSimulationResults(
    const std::map<AlgorithmType, std::vector<TaskAllocationResult>>& algorithmResults) {

    std::cout << "\n==== ��ʱ�䲽���������� ====" << std::endl;

    // ��ͷ
    std::cout << std::setw(20) << std::left << "�㷨"
        << std::setw(15) << std::right << "ƽ��Ŀ�꺯��"
        << std::setw(15) << "ƽ����Դ������"
        << std::setw(15) << "ƽ����Ӧʱ��"
        << std::setw(15) << "ƽ��Makespan"
        << std::setw(15) << "ƽ�����ؾ����"
        << std::setw(15) << "ƽ����ʱ��" << std::endl;

    std::cout << std::string(100, '-') << std::endl;

    // ����ÿ���㷨��ƽ��ָ��
    for (const auto& alg : algorithmResults) {
        AlgorithmType algorithm = alg.first;
        const std::vector<TaskAllocationResult>& results = alg.second;

        // ���û�н��������
        if (results.empty()) {
            continue;
        }

        // ����ƽ��ֵ
        double avgObjective = 0.0;
        double avgRU = 0.0;
        double avgRT = 0.0;
        double avgMS = 0.0;
        double avgPsi = 0.0;
        double avgTotalTime = 0.0;

        for (const auto& result : results) {
            avgObjective += result.objectiveValue;
            avgRU += result.resourceUtilization;
            avgRT += result.executionTime.count();
            avgMS += result.makespan;
            avgPsi += result.loadBalanceDegree;
            avgTotalTime += result.totalTaskTime;
        }

        avgObjective /= results.size();
        avgRU /= results.size();
        avgRT /= results.size();
        avgMS /= results.size();
        avgPsi /= results.size();
        avgTotalTime /= results.size();

        // ��ӡ���
        std::cout << std::setw(20) << std::left << TaskAllocator::getAlgorithmName(algorithm)
            << std::setw(15) << std::right << std::fixed << std::setprecision(6) << avgObjective
            << std::setw(15) << std::fixed << std::setprecision(2) << avgRU
            << std::setw(15) << std::fixed << std::setprecision(2) << avgRT
            << std::setw(15) << std::fixed << std::setprecision(4) << avgMS
            << std::setw(15) << std::fixed << std::setprecision(4) << avgPsi
            << std::setw(15) << std::fixed << std::setprecision(4) << avgTotalTime << std::endl;
    }
}

// ������������ļ�
void MultiTimestepSimulation::saveSimulationResults(
    const std::map<AlgorithmType, std::vector<TaskAllocationResult>>& algorithmResults,
    const std::string& filename) {

    std::ofstream outFile(filename);
    if (!outFile.is_open()) {
        std::cerr << "�޷������ļ�: " << filename << std::endl;
        return;
    }

    outFile << "==== ��ʱ�䲽���������� ====" << std::endl;

    // ��ͷ
    outFile << std::setw(20) << std::left << "�㷨"
        << std::setw(15) << std::right << "ƽ��Ŀ�꺯��"
        << std::setw(15) << "ƽ����Դ������"
        << std::setw(15) << "ƽ����Ӧʱ��"
        << std::setw(15) << "ƽ��Makespan"
        << std::setw(15) << "ƽ�����ؾ����"
        << std::setw(15) << "ƽ����ʱ��" << std::endl;

    outFile << std::string(100, '-') << std::endl;

    // ����ÿ���㷨��ƽ��ָ��
    for (const auto& alg : algorithmResults) {
        AlgorithmType algorithm = alg.first;
        const std::vector<TaskAllocationResult>& results = alg.second;

        // ���û�н��������
        if (results.empty()) {
            continue;
        }

        // ����ƽ��ֵ
        double avgObjective = 0.0;
        double avgRU = 0.0;
        double avgRT = 0.0;
        double avgMS = 0.0;
        double avgPsi = 0.0;
        double avgTotalTime = 0.0;

        for (const auto& result : results) {
            avgObjective += result.objectiveValue;
            avgRU += result.resourceUtilization;
            avgRT += result.executionTime.count();
            avgMS += result.makespan;
            avgPsi += result.loadBalanceDegree;
            avgTotalTime += result.totalTaskTime;
        }

        avgObjective /= results.size();
        avgRU /= results.size();
        avgRT /= results.size();
        avgMS /= results.size();
        avgPsi /= results.size();
        avgTotalTime /= results.size();

        // ��ӡ���
        outFile << std::setw(20) << std::left << TaskAllocator::getAlgorithmName(algorithm)
            << std::setw(15) << std::right << std::fixed << std::setprecision(6) << avgObjective
            << std::setw(15) << std::fixed << std::setprecision(2) << avgRU
            << std::setw(15) << std::fixed << std::setprecision(2) << avgRT
            << std::setw(15) << std::fixed << std::setprecision(4) << avgMS
            << std::setw(15) << std::fixed << std::setprecision(4) << avgPsi
            << std::setw(15) << std::fixed << std::setprecision(4) << avgTotalTime << std::endl;
    }

    // ����ʱ�䲽��ϸ��Ϣ
    outFile << "\n==== ʱ�䲽��ϸ��Ϣ ====" << std::endl;

    for (const auto& alg : algorithmResults) {
        AlgorithmType algorithm = alg.first;
        const std::vector<TaskAllocationResult>& results = alg.second;

        outFile << "\n" << TaskAllocator::getAlgorithmName(algorithm) << ":" << std::endl;
        outFile << std::setw(10) << "ʱ�䲽"
            << std::setw(15) << "Ŀ�꺯��ֵ"
            << std::setw(15) << "��Դ������"
            << std::setw(15) << "��Ӧʱ��(ms)"
            << std::setw(15) << "Makespan"
            << std::setw(15) << "���ؾ����"
            << std::setw(15) << "������ʱ��" << std::endl;

        outFile << std::string(90, '-') << std::endl;

        for (size_t i = 0; i < results.size(); i++) {
            const auto& result = results[i];

            outFile << std::setw(10) << (i + 1)  // ����ʱ�䲽��1��ʼ
                << std::setw(15) << std::fixed << std::setprecision(6) << result.objectiveValue
                << std::setw(15) << std::fixed << std::setprecision(2) << result.resourceUtilization
                << std::setw(15) << result.executionTime.count()
                << std::setw(15) << std::fixed << std::setprecision(4) << result.makespan
                << std::setw(15) << std::fixed << std::setprecision(4) << result.loadBalanceDegree
                << std::setw(15) << std::fixed << std::setprecision(4) << result.totalTaskTime << std::endl;
        }
    }

    outFile.close();
    std::cout << "�ѱ�����������ļ�: " << filename << std::endl;
}

// ��������ִ����Ϣ
void MultiTimestepSimulation::updateTaskExecutionInfo(int currentTimeStep,
    const std::vector<Task>& currentTasks,
    const TaskAllocationResult& result,
    std::map<int, TaskExecutionInfo>& taskInfo) {
    // Ϊ��ǰʱ�䲽��ÿ�����񴴽�ִ����Ϣ
    for (size_t i = 0; i < currentTasks.size(); i++) {
        const Task& task = currentTasks[i];
        int nodeId = (i < result.nodeAssignment.size()) ? result.nodeAssignment[i] : 0;

        // �������ɹ����䵽�ڵ�
        if (nodeId > 0) {
            // �ҵ�����Ľڵ�
            Node* assignedNode = nullptr;
            for (auto& node : nodes) {
                if (node.id == nodeId) {
                    assignedNode = &node;
                    break;
                }
            }

            if (assignedNode != nullptr) {
                // ��������ִ����Ϣ
                TaskExecutionInfo info;
                info.task = task;
                info.assignedNodeId = nodeId;
                info.startTime = currentTimeStep;

                // ��������ִ��ʱ��
                double cpuTime = static_cast<double>(task.resourceDemand.cpuDemand) / std::max(1.0, static_cast<double>(assignedNode->resource.currentCpu));
                double memTime = static_cast<double>(task.resourceDemand.memoryDemand) / std::max(1.0, static_cast<double>(assignedNode->resource.currentMemory));
                double netTime = static_cast<double>(task.resourceDemand.bandwidthDemand) / std::max(1.0, static_cast<double>(assignedNode->resource.currentBandwidth));

                // ��ִ��ʱ�䣨����ȡ����
                int executionTime = static_cast<int>(std::ceil(cpuTime + memTime + netTime));
                info.executionTime = executionTime;
                info.completionTime = currentTimeStep + executionTime;
                info.resourcesReleased = false;

                // ���浽����ִ����Ϣӳ��
                taskInfo[task.id] = info;

                // �ӽڵ��п۳���Դ
                assignedNode->resource.currentCpu -= task.resourceDemand.cpuDemand;
                assignedNode->resource.currentMemory -= task.resourceDemand.memoryDemand;
                assignedNode->resource.currentBandwidth -= task.resourceDemand.bandwidthDemand;

                // ȷ����Դ��Ϊ��
                assignedNode->resource.currentCpu = std::max(0, assignedNode->resource.currentCpu);
                assignedNode->resource.currentMemory = std::max(0, assignedNode->resource.currentMemory);
                assignedNode->resource.currentBandwidth = std::max(0, assignedNode->resource.currentBandwidth);

                std::cout << "  ���� " << task.id << " ���䵽�ڵ� " << nodeId
                    << ", ִ��ʱ��=" << executionTime << ", ���ʱ��=" << info.completionTime << std::endl;
            }
        }
    }

    // �����Դ������
    for (auto& node : nodes) {
        node.resource.currentCpu = std::max(0,
            std::min(node.resource.currentCpu, node.resource.cpuCapacity));
        node.resource.currentMemory = std::max(0,
            std::min(node.resource.currentMemory, node.resource.memoryCapacity));
        node.resource.currentBandwidth = std::max(0,
            std::min(node.resource.currentBandwidth, node.resource.bandwidthCapacity));

        // �������ӵ�¼��
        if (node.resource.currentBandwidth <
            (1 - params.networkSafeThreshold) * node.resource.bandwidthCapacity) {
            std::cout << "����: �ڵ� " << node.id
                << " �������ʹ�ó�����ȫ��ֵ("
                << params.networkSafeThreshold * 100 << "%)!\n";
        }
    }
}

// ��ӡ�ڵ���Դ״̬
void MultiTimestepSimulation::printNodeResourceStatus(const std::vector<Node>& nodes) { // ��Ӳ���
    std::cout << "==== �ڵ���Դ״̬ ====" << std::endl;
    for (const auto& node : nodes) { // ʹ�ò���nodes�����ǳ�Ա����nodes
        std::cout << "�ڵ� " << node.id
            << " CPU: " << node.resource.currentCpu << "/"
            << node.resource.cpuCapacity
            << " | �ڴ�: " << node.resource.currentMemory << "/"
            << node.resource.memoryCapacity << std::endl;
    }
}

// ��ӡ������ժҪ
void MultiTimestepSimulation::printAllocationSummary(const TaskAllocationResult& result, AlgorithmType algorithm) {
    std::cout << "������ժҪ - " << TaskAllocator::getAlgorithmName(algorithm) << "��" << std::endl;
    std::cout << "  ��������: " << result.nodeAssignment.size() << std::endl;

    // ����ɹ������������
    int assignedCount = 0;
    for (int nodeId : result.nodeAssignment) {
        if (nodeId > 0) {
            assignedCount++;
        }
    }

    double assignedPercentage = 100.0 * assignedCount / std::max(1, static_cast<int>(result.nodeAssignment.size()));
    std::cout << "  �ɹ�����������: " << assignedCount << " ("
        << std::fixed << std::setprecision(2) << assignedPercentage << "%)" << std::endl;

    // ÿ���ڵ�����������
    std::map<int, int> nodeTaskCount;
    for (int nodeId : result.nodeAssignment) {
        if (nodeId > 0) {
            nodeTaskCount[nodeId]++;
        }
    }

    std::cout << "  �ڵ����������: ";
    for (const auto& pair : nodeTaskCount) {
        std::cout << pair.first << "(" << pair.second << ") ";
    }
    std::cout << std::endl;

    // ��ӡ����ָ��
    std::cout << "  ��Դ������: " << std::fixed << std::setprecision(2) << result.resourceUtilization << "%" << std::endl;
    std::cout << "  �㷨��Ӧʱ��: " << result.executionTime.count() << " ms" << std::endl;
    std::cout << "  Makespan: " << std::fixed << std::setprecision(4) << result.makespan << std::endl;
    std::cout << "  ���ؾ����: " << std::fixed << std::setprecision(4) << result.loadBalanceDegree << std::endl;
    std::cout << "  ������ʱ��: " << std::fixed << std::setprecision(4) << result.totalTaskTime << std::endl;
    std::cout << "  Ŀ�꺯��ֵ: " << std::fixed << std::setprecision(6) << result.objectiveValue << std::endl;
}

// ���½ڵ���Դ���ͷ�������������Դ��
void MultiTimestepSimulation::updateNodeResources(int currentTimeStep,
    const std::map<int, TaskExecutionInfo>& taskInfo) {
    std::cout << "���½ڵ���Դ״̬..." << std::endl;

    // �����Ѿ���ɵ������ͷ���ռ�õ���Դ
    int releasedTaskCount = 0;

    for (auto& pair : taskInfo) {
        const TaskExecutionInfo& info = pair.second;

        // ��������������δ�ͷ���Դ
        if (info.completionTime <= currentTimeStep && !info.resourcesReleased) {
            // �ҵ����������Ľڵ�
            for (auto& node : nodes) {
                if (node.id == info.assignedNodeId) {
                    // �����ͷŵ���Դ
                    int releasedCpu = info.task.resourceDemand.cpuDemand * params.cpuEfficiency;
                    int releasedMem = info.task.resourceDemand.memoryDemand * params.memEfficiency;
                    int releasedBw = info.task.resourceDemand.bandwidthDemand * params.netEfficiency;

                    // ���½ڵ���Դ
                    node.resource.currentCpu += releasedCpu;
                    node.resource.currentMemory += releasedMem;
                    node.resource.currentBandwidth += releasedBw;

                    // ȷ���������������
                    node.resource.currentCpu = std::min(node.resource.currentCpu, node.resource.cpuCapacity);
                    node.resource.currentMemory = std::min(node.resource.currentMemory, node.resource.memoryCapacity);
                    node.resource.currentBandwidth = std::min(node.resource.currentBandwidth, node.resource.bandwidthCapacity);

                    std::cout << "  �ͷ����� " << info.task.id << " ����Դ: CPU=" << releasedCpu
                        << ", MEM=" << releasedMem << ", BW=" << releasedBw
                        << ", �ڵ�ID=" << node.id << std::endl;

                    releasedTaskCount++;
                    break;
                }
            }

            // ��Ǹ��������Դ���ͷ�
            const_cast<TaskExecutionInfo&>(info).resourcesReleased = true;
        }
    }

    std::cout << "  �ܼ��ͷ��� " << releasedTaskCount << " ��������������Դ" << std::endl;

    // ����ʱ������ƣ�CPU��Դ����Ȼ�ָ���ģ��CPU��ȴ�͵����Ż���
    for (auto& node : nodes) {
        double currentUtilization = 1.0 - static_cast<double>(node.resource.currentCpu) / node.resource.cpuCapacity;

        // ����CPU��Դ�ָ�����ʹ��˥������ģ������Իָ�
        int cpuRecovery = static_cast<int>(
            node.resource.cpuCapacity * currentUtilization * params.cpuDecayFactor * 0.1); // ÿʱ�䲽�ָ�10%

        node.resource.currentCpu += cpuRecovery;
        node.resource.currentCpu = std::min(node.resource.currentCpu, node.resource.cpuCapacity);

        if (cpuRecovery > 0) {
            std::cout << "  �ڵ� " << node.id << " ��Ȼ�ָ�CPU��Դ: +" << cpuRecovery << std::endl;
        }
    }
}