#ifndef MULTI_TIMESTEP_SIMULATION_H
#define MULTI_TIMESTEP_SIMULATION_H

#include "data.h"
#include "task_allocation.h"
#include "task_classification.h"
#include <vector>
#include <map>
#include <string>

// ��������ṹ��
struct SimulationParams {
    // Ŀ�꺯��Ȩ��
    ObjectiveWeights objectiveWeights;

    // ��ԴȨ��
    ResourceWeights resourceWeights;

    // �㷨����
    AlgorithmParams algorithmParams;

    // ��ԴЧ�ʲ���
    double cpuEfficiency = 0.85;  // CPU��Դ����Ч��
    double memEfficiency = 0.95;  // �ڴ���Դ����Ч��
    double netEfficiency = 0.90;  // ������Դ����Ч��

    // CPU����˥�����ӣ�ģ���Ƚ����ܵ��µķ�����˥����
    double cpuDecayFactor = 0.75;

    // ���簲ȫ��ֵ��������ֵ�ᵼ��ӵ�£�
    double networkSafeThreshold = 0.80;

    // �Ƿ�ʹ���������ͽڵ����
    bool useClassificationAndGrouping = false;
};

// ����ִ����Ϣ�ṹ��
struct TaskExecutionInfo {
    Task task;                // ����
    int assignedNodeId;       // ����Ľڵ�ID
    int startTime;            // ��ʼʱ��
    int executionTime;        // ִ��ʱ��
    int completionTime;       // ���ʱ��
    bool resourcesReleased;   // ��Դ�Ƿ����ͷ�
};

// ��ʱ�䲽������
class MultiTimestepSimulation {
public:
    // ���캯��
    MultiTimestepSimulation(const SimulationParams& params);

    // ��������ͽڵ�����
    bool loadData(const std::string& tasksFile, const std::string& nodesFile);

    // ִ�ж�ʱ�䲽����
    void runSimulation();

    // �����Ƿ�ʹ���������ͽڵ����
    void setUseClassificationAndGrouping(bool use) {
        params.useClassificationAndGrouping = use;
    }


    // �޸ĺ�Ľڵ�״̬��ӡ����
    void printNodeResourceStatus(const std::vector<Node>& nodes);


        // �޸ĺ����Դ���º�������
    void updateNodeResourcesForAlgorithm(int timeStep,
        std::map<int, TaskExecutionInfo>& taskInfo,
        std::vector<Node>& algoNodes);

private:
    // �������
    SimulationParams params;

    // ����ͽڵ�����
    std::vector<Task> tasks;
    std::vector<Node> nodes;
    std::vector<Node> originalNodes;  // �����ʼ�ڵ�״̬

    // ��ʱ�䲽���������
    std::map<int, std::vector<Task>> tasksByTimeStep;

    // ���½ڵ���Դ���ͷ�������������Դ��
    void updateNodeResources(int currentTimeStep,
        const std::map<int, TaskExecutionInfo>& taskInfo);

    // ��������ִ����Ϣ
    void updateTaskExecutionInfo(int currentTimeStep,
        const std::vector<Task>& currentTasks,
        const TaskAllocationResult& result,
        std::map<int, TaskExecutionInfo>& taskInfo);


    // ��ӡ������ժҪ
    void printAllocationSummary(const TaskAllocationResult& result, AlgorithmType algorithm);

    // ��ӡ���������
    void printSimulationResults(
        const std::map<AlgorithmType, std::vector<TaskAllocationResult>>& algorithmResults);

    // ������������ļ�
    void saveSimulationResults(
        const std::map<AlgorithmType, std::vector<TaskAllocationResult>>& algorithmResults,
        const std::string& filename);

    void saveTimeStepResults(const std::map<AlgorithmType, TaskAllocationResult>& results, int timeStep);

    // ʱ�䲽����洢
    std::map<int, std::map<AlgorithmType, TaskAllocationResult>> timestepResults;

    // �޸ĺ󣺰�[ʱ�䲽][�㷨����]�洢�ڵ�״̬
    std::map<int, std::map<AlgorithmType, std::vector<Node>>> algorithmNodeSnapshots;


    // ����ָ���ؼ��㺯��
    void recalculateMetrics(int currentTimestep);

    double calculateLoadBalance(const std::vector<Node>& nodes);
    double calculateTotalTaskTime(int timestep);

    // �������ͽڵ������غ���
    std::map<TaskType, std::vector<Task>> classifyTasks(const std::vector<Task>& tasks);
    std::map<NodeType, std::vector<Node>> groupNodes(const std::vector<Node>& nodes);
    TaskAllocationResult allocateTasksWithClassification(
        const std::vector<Task>& currentTasks,
        std::vector<Node>& currentNodes,
        AlgorithmType algorithm);


    


    void updateTaskExecutionInfoForAlgorithm(int timeStep, const std::vector<Task>& tasks, const TaskAllocationResult& result, std::map<int, TaskExecutionInfo>& taskInfo, std::vector<Node>& algoNodes);



};

#endif // MULTI_TIMESTEP_SIMULATION_H