# 负载均衡仿真系统详细说明文档

## 1. 项目概述

### 1.1 项目简介
本项目是一个基于C++开发的负载均衡仿真系统，主要用于研究和比较不同负载均衡算法在分布式计算环境中的性能表现。系统支持多种智能优化算法，包括遗传算法、粒子群算法、蚁群算法、蜂群算法、蝙蝠算法、鲸鱼算法、灰狼算法、模拟退火算法以及混合算法等。

### 1.2 主要特性
- **多算法支持**：集成10种不同的负载均衡算法
- **多时间步仿真**：支持动态任务分配和资源管理
- **任务分类与节点分组**：基于任务和节点特性的智能分组
- **性能指标评估**：全面的性能评估指标体系
- **可视化结果**：支持结果可视化和数据导出
- **可配置参数**：灵活的参数配置系统

### 1.3 应用场景
- 云计算环境下的任务调度
- 分布式计算系统的负载均衡
- 边缘计算资源分配
- 大规模并行计算优化

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据生成模块   │    │   任务分配模块   │    │   仿真执行模块   │
│  DataGenerator  │───▶│ TaskAllocator   │───▶│   Simulator     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据存储层     │    │   算法实现层     │    │   结果分析层     │
│   Data Files    │    │  Algorithms     │    │  Results        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心模块
1. **数据生成模块** (`data_generator.cpp/h`)
2. **任务分配模块** (`task_allocation.cpp/h`)
3. **仿真执行模块** (`simulation.cpp/h`)
4. **多时间步仿真** (`multi_timestep_simulation.h`)
5. **任务分类与节点分组** (`task_classification.h`)

## 3. 数据结构设计

### 3.1 任务结构 (Task)
```cpp
struct Task {
    int id;                              // 任务ID
    int timeStep;                        // 时间步
    TaskResourceDemand resourceDemand;   // 资源需求
    std::unordered_set<int> dependencies; // 依赖关系集合
    TaskType type;                       // 任务类型
};
```

### 3.2 节点结构 (Node)
```cpp
struct Node {
    int id;                 // 节点ID
    int timeStep;           // 时间步
    NodeResource resource;  // 节点资源
    NodeType type;          // 节点类型/特性
};
```

### 3.3 资源需求结构
```cpp
struct TaskResourceDemand {
    int cpuDemand;      // CPU需求(100-1000)
    int memoryDemand;   // 内存需求(100-1000)
    int bandwidthDemand; // 带宽需求(100-1000)
};
```

## 4. 算法实现

### 4.1 支持的算法列表
1. **轮询算法** (Round Robin)
2. **遗传算法** (Genetic Algorithm)
3. **粒子群算法** (Particle Swarm Optimization)
4. **蜂群算法** (Bee Colony Algorithm)
5. **蚁群算法** (Ant Colony Algorithm)
6. **蝙蝠算法** (Bat Algorithm)
7. **鲸鱼算法** (Whale Optimization Algorithm)
8. **灰狼算法** (Grey Wolf Optimizer)
9. **模拟退火算法** (Simulated Annealing)
10. **灰狼-模拟退火混合算法** (Hybrid GW-SA)

### 4.2 算法参数配置
```cpp
struct AlgorithmParams {
    int maxIterations = 50;      // 最大迭代次数
    int populationSize = 50;     // 种群/粒子数量
    double crossoverRate = 0.8;  // 交叉率
    double mutationRate = 0.1;   // 变异率
    double inertiaWeight = 0.7;  // 惯性权重
    double initialTemp = 100.0;  // 初始温度
    double coolingRate = 0.95;   // 冷却率
    // ... 其他参数
};
```

### 4.3 目标函数设计
系统采用多目标优化，综合考虑以下指标：
- **资源利用率** (Resource Utilization)
- **响应时间** (Response Time)
- **Makespan** (总完成时间)
- **负载均衡度** (Load Balance Degree)
- **总任务时间** (Total Task Time)

目标函数权重可配置：
```cpp
struct ObjectiveWeights {
    double alpha = 0.2;  // 资源利用率权重
    double beta = 0.2;   // 响应时间权重
    double gamma = 0.2;  // Makespan权重
    double epsilon = 0.2; // 负载均衡权重
    double xi = 0.2;     // 总任务时间权重
};
```

## 5. 功能模块详解

### 5.1 数据生成模块
**文件**: `main_data_generator.cpp`, `data_generator.cpp/h`

**功能**:
- 生成节点数据集
- 生成任务数据集
- 支持自定义参数配置
- 生成依赖关系

**主要参数**:
```cpp
struct NodeGenerationParams {
    int numNodes = 20;                 // 节点数量
    int minCpuCapacity = 10000;        // 最小CPU标准容量
    int maxCpuCapacity = 30000;        // 最大CPU标准容量
    // ... 其他参数
};

struct TaskGenerationParams {
    int timeSteps = 3;               // 时间步数
    int tasksPerTimeStep = 50;       // 每个时间步的任务数量
    double dependencyProbability = 0.3; // 依赖概率
    // ... 其他参数
};
```

### 5.2 任务分配模块
**文件**: `task_allocation.cpp/h`

**核心功能**:
- 实现10种负载均衡算法
- 处理任务依赖关系
- 资源约束检查
- 性能指标计算

**关键方法**:
```cpp
class TaskAllocator {
public:
    TaskAllocationResult allocateTasks(AlgorithmType algorithm);
    double calculateObjectiveValue(const std::vector<int>& nodeAssignment);
    bool isValidAssignment(const std::vector<int>& assignment);
    // ... 其他方法
};
```

### 5.3 多时间步仿真模块
**文件**: `main_Multi-Timestep.cpp`, `Multi-Timestep Task Allocation.cpp`

**功能特性**:
- 动态任务分配
- 资源释放和回收
- 时间序列数据记录
- 跨时间步的性能分析

### 5.4 任务分类与节点分组模块
**文件**: `main_Task Classification and Node Grouping.cpp`, `Task Classification and Node Grouping.cpp`

**功能**:
- 基于资源需求的任务分类
- 基于特性的节点分组
- 智能匹配策略
- 分组优化算法

## 6. 性能评估体系

### 6.1 评估指标
1. **资源利用率** (Resource Utilization)
   - 计算所有节点的平均资源使用率
   - 范围: 0-100%

2. **响应时间** (Response Time)
   - 任务从提交到开始执行的时间
   - 单位: 毫秒

3. **Makespan**
   - 所有任务完成的总时间
   - 反映系统的整体效率

4. **负载均衡度** (Load Balance Degree)
   - 使用标准差衡量负载分布的均匀性
   - 值越小表示负载越均衡

5. **总任务时间** (Total Task Time)
   - 所有任务执行时间的总和
   - 反映系统的处理能力

### 6.2 归一化处理
系统对所有指标进行归一化处理，确保不同量纲的指标可以公平比较：
```cpp
double calculateNormalizedMetric(double value, double minVal, double maxVal) {
    return (value - minVal) / (maxVal - minVal);
}
```

## 7. 使用指南

### 7.1 编译环境
- **编译器**: Visual Studio 2019/2022
- **语言标准**: C++17
- **平台**: Windows 10/11

### 7.2 运行方式
系统提供多个入口点，对应不同的功能：

1. **数据生成**: `main_data_generator.cpp`
   ```bash
   # 生成节点和任务数据
   ./main_data_generator
   ```

2. **算法比较实验**: `main_experiment.cpp.cpp`
   ```bash
   # 比较所有算法的性能
   ./main_experiment
   ```

3. **多时间步仿真**: `main_Multi-Timestep.cpp`
   ```bash
   # 执行多时间步仿真
   ./main_Multi-Timestep
   ```

4. **任务分类与节点分组**: `main_Task Classification and Node Grouping.cpp`
   ```bash
   # 执行分类分组实验
   ./main_Task_Classification_and_Node_Grouping
   ```

### 7.3 参数配置
用户可以通过修改以下文件来调整系统参数：
- `params.h`: 仿真参数
- `data.h`: 数据结构参数
- `task_allocation.h`: 算法参数

## 8. 输出结果

### 8.1 结果文件类型
1. **CSV文件**: 算法比较结果
2. **TXT文件**: 详细分配结果
3. **收敛曲线**: 算法收敛过程
4. **时间序列数据**: 多时间步仿真数据

### 8.2 结果分析
系统自动生成以下分析结果：
- 各算法的性能排名
- 收敛速度对比
- 资源利用效率分析
- 负载均衡效果评估

## 9. 扩展性设计

### 9.1 算法扩展
系统采用策略模式设计，新增算法只需：
1. 在`AlgorithmType`枚举中添加新类型
2. 在`TaskAllocator`类中实现对应的分配方法
3. 在`getAlgorithmName`方法中添加名称映射

### 9.2 指标扩展
新增评估指标只需：
1. 在`TaskAllocationResult`结构体中添加新字段
2. 在`calculateMetrics`方法中实现计算逻辑
3. 在目标函数中调整权重

### 9.3 数据结构扩展
系统支持灵活的数据结构扩展，可以轻松添加新的资源类型或约束条件。

## 10. 技术特点

### 10.1 设计优势
- **模块化设计**: 各模块职责清晰，易于维护
- **可扩展性**: 支持新算法和指标的快速集成
- **可配置性**: 丰富的参数配置选项
- **可重现性**: 完整的实验记录和结果保存

### 10.2 算法特色
- **混合算法**: 结合多种算法的优势
- **自适应参数**: 根据问题规模自动调整参数
- **约束处理**: 完善的依赖关系和资源约束处理
- **收敛优化**: 针对不同算法特点的收敛策略

### 10.3 性能优化
- **内存管理**: 高效的内存使用策略
- **计算优化**: 并行计算和算法优化
- **数据缓存**: 智能的数据缓存机制
- **I/O优化**: 批量文件操作和压缩存储

## 11. 总结

本负载均衡仿真系统是一个功能完整、设计先进的研究平台，具有以下特点：

1. **全面性**: 涵盖多种主流负载均衡算法
2. **实用性**: 提供完整的实验环境和分析工具
3. **先进性**: 采用最新的智能优化算法
4. **可靠性**: 经过充分测试和验证
5. **扩展性**: 支持功能扩展和算法改进

该系统为负载均衡算法的研究和应用提供了强有力的支持，可用于学术研究、工业应用和教学演示等多个场景。 