# 负载均衡仿真数据生成器

## 概述
本模块用于生成负载均衡仿真实验所需的任务和节点数据集，支持三种规模：小规模、中规模、大规模。生成的数据基于真实工作负载特征，包含完整的资源需求、依赖关系和调度信息。

## 文件结构
```
data_generation/
├── task_generate.py                    # 任务数据生成器
├── node_generate.py                    # 节点数据生成器
├── generate_datasets.py                # 主生成脚本
├── task_dependency_visualizer.py       # 任务依赖图可视化（pygraphviz版本）
├── task_dependency_visualizer_networkx.py  # 任务依赖图可视化（networkx版本）
├── visualize_example.py                # 可视化使用示例
├── README.md                           # 说明文档（本文件）
└── datasets/                           # 生成的数据集目录
    ├── tasks_small.json                # 小规模任务数据
    ├── tasks_medium.json               # 中规模任务数据
    ├── tasks_large.json                # 大规模任务数据
    ├── nodes_small.json                # 小规模节点数据
    ├── nodes_medium.json               # 中规模节点数据
    ├── nodes_large.json                # 大规模节点数据
    └── README.md                       # 数据集说明
```

## 使用方法

### 1. 生成数据集
```bash
cd data_generation
python generate_datasets.py
```

### 2. 可视化任务依赖关系
```bash
# 使用pygraphviz版本（需要先安装pygraphviz）
python task_dependency_visualizer.py

# 或使用networkx版本（推荐，依赖更少）
python task_dependency_visualizer_networkx.py

# 单独可视化某个数据集
python visualize_example.py
```

## 数据结构详解

### 任务数据格式 (tasks_*.json)

#### 文件整体结构
```json
{
    "metadata": {
        "total_tasks": 50,
        "generation_time": "2024-01-15T10:30:00",
        "scale": "small",
        "description": "小规模任务数据集，包含50个任务"
    },
    "tasks": [
        {
            "id": "task_0",
            "task_type": "cpu_intensive",
            "resource_demand": { ... },
            "execution_time": { ... },
            "dependencies": [ ... ],
            "scheduling_info": { ... }
        },
        ...
    ]
}
```

#### 单个任务结构详解
```json
{
    "id": "task_0",                          // 任务唯一标识符
    "task_type": "cpu_intensive",            // 任务类型（5种类型之一）
    
    // 资源需求 - 任务执行所需的计算资源
    "resource_demand": {
        "cpu_cores": 2.5,                   // CPU核心数需求（浮点数）
        "memory_mb": 4096.0,                // 内存需求（MB）
        "io_bandwidth_mbps": 150.0,         // I/O带宽需求（MB/s）
        "network_bandwidth_mbps": 256.7     // 网络带宽需求（MB/s）
    },
    
    // 执行时间信息
    "execution_time": {
        "base_runtime_seconds": 45.2,       // 基础执行时间（秒）
        "estimated_runtime_seconds": 48.5   // 预估执行时间（考虑系统开销）
    },
    
    // 任务依赖关系 - 必须在此任务之前完成的任务列表
    "dependencies": ["task_1", "task_3"],   // 依赖的任务ID列表
    
    // 调度信息 - 任务分配和执行状态
    "scheduling_info": {
        "assigned_node": "node_2",          // 分配的节点ID
        "start_time": 10.5,                 // 开始执行时间（秒）
        "end_time": 58.7,                   // 结束执行时间（秒）
        "actual_runtime": 48.2,             // 实际执行时间（秒）
        "status": "completed"               // 执行状态
    }
}
```

#### 任务类型说明
- **cpu_intensive**: CPU密集型任务，主要消耗CPU资源
- **memory_intensive**: 内存密集型任务，需要大量内存
- **io_intensive**: I/O密集型任务，频繁进行磁盘读写
- **network_intensive**: 网络密集型任务，需要大量网络传输
- **general**: 通用型任务，资源需求相对均衡

### 节点数据格式 (nodes_*.json)

#### 文件整体结构
```json
{
    "metadata": {
        "total_nodes": 10,
        "generation_time": "2024-01-15T10:30:00",
        "scale": "small",
        "description": "小规模节点数据集，包含10个异构计算节点"
    },
    "nodes": [
        {
            "id": "node_0",
            "node_type": "cpu_intensive",
            "resource_capacity": { ... },
            "current_usage": { ... },
            "performance_metrics": { ... },
            "hardware_info": { ... }
        },
        ...
    ]
}
```

#### 单个节点结构详解
```json
{
    "id": "node_0",                         // 节点唯一标识符
    "node_type": "cpu_intensive",           // 节点类型（4种类型之一）
    
    // 资源容量 - 节点的最大资源提供能力
    "resource_capacity": {
        "cpu_cores": 8.0,                  // CPU核心数总容量
        "memory_mb": 16384.0,              // 内存总容量（MB）
        "io_bandwidth_mbps": 1000.0,       // I/O带宽总容量（MB/s）
        "network_bandwidth_mbps": 1000.0   // 网络带宽总容量（MB/s）
    },
    
    // 当前使用情况 - 节点资源的实时使用状态
    "current_usage": {
        "cpu_cores": 3.2,                  // 当前CPU使用量
        "memory_mb": 6144.0,               // 当前内存使用量（MB）
        "io_bandwidth_mbps": 200.0,        // 当前I/O带宽使用量（MB/s）
        "network_bandwidth_mbps": 150.0,   // 当前网络带宽使用量（MB/s）
        "utilization_rate": 0.45           // 整体利用率（0-1）
    },
    
    // 可用资源 - 节点当前可分配的剩余资源
    "available_resources": {
        "cpu_cores": 4.8,                  // 可用CPU核心数
        "memory_mb": 10240.0,              // 可用内存（MB）
        "io_bandwidth_mbps": 800.0,        // 可用I/O带宽（MB/s）
        "network_bandwidth_mbps": 850.0    // 可用网络带宽（MB/s）
    },
    
    // 性能指标 - 节点的性能特征和效率指标
    "performance_metrics": {
        "cpu_performance_factor": 1.2,     // CPU性能因子（相对基准）
        "memory_access_speed": 0.95,       // 内存访问速度因子
        "io_performance_factor": 1.1,      // I/O性能因子
        "network_latency_ms": 2.5,         // 网络延迟（毫秒）
        "reliability_score": 0.92,         // 可靠性评分（0-1）
        "energy_efficiency": 0.85          // 能源效率评分（0-1）
    },
    
    // 硬件信息 - 节点的物理硬件特征
    "hardware_info": {
        "cpu_model": "Intel Xeon E5-2680",
        "memory_type": "DDR4-2400",
        "storage_type": "SSD",
        "network_interface": "10GbE",
        "power_consumption_watts": 150.0,
        "temperature_celsius": 45.2,
        "location": "Rack-A-01"
    }
}
```

#### 节点类型说明
- **cpu_intensive**: CPU密集型节点，拥有强大的CPU处理能力
- **memory_intensive**: 内存密集型节点，配备大容量高速内存
- **io_intensive**: I/O密集型节点，具有高速存储和I/O能力
- **general**: 通用型节点，各项资源配置均衡

## 数据规模配置

### 小规模 (Small Scale)
- **任务数量**: 50个
- **节点数量**: 10个
- **依赖关系**: 约30%的任务有依赖
- **适用场景**: 算法原型验证、快速测试

### 中规模 (Medium Scale)
- **任务数量**: 200个
- **节点数量**: 50个
- **依赖关系**: 约40%的任务有依赖
- **适用场景**: 算法性能评估、中等规模仿真

### 大规模 (Large Scale)
- **任务数量**: 500个
- **节点数量**: 100个
- **依赖关系**: 约50%的任务有依赖
- **适用场景**: 大规模系统仿真、性能压力测试

## 数据特征与质量保证

### 真实性保证
- **资源需求分布**: 基于真实工作负载统计特征
- **异构性模拟**: 节点间存在20-50%的性能差异
- **依赖关系**: 采用DAG结构，避免循环依赖
- **负载分布**: 模拟真实系统的负载变化模式

### 数据一致性
- **资源匹配**: 确保任务需求不超过系统总容量
- **时间一致性**: 开始时间、结束时间、执行时间保持逻辑一致
- **依赖完整性**: 所有依赖任务都存在于数据集中
- **数值精度**: 所有浮点数保留2位小数

### 统计特征
```
任务资源需求分布:
- CPU: 1.0-4.0 核心 (均值: 2.5)
- 内存: 1024-8192 MB (均值: 4096)
- I/O: 50-500 MB/s (均值: 200)
- 网络: 100-1000 MB/s (均值: 400)

节点资源容量分布:
- CPU: 4-16 核心 (均值: 8)
- 内存: 8192-32768 MB (均值: 16384)
- I/O: 500-2000 MB/s (均值: 1000)
- 网络: 1000-10000 MB/s (均值: 2000)
```

## 可视化功能

### 任务依赖图可视化
- **完整依赖图**: 显示所有任务的依赖关系
- **连通分量**: 显示最大连通子图
- **按类型分组**: 按任务类型生成子图
- **层次化布局**: 按依赖层级排列任务
- **颜色编码**: 不同任务类型使用不同颜色

### 生成的可视化文件
```
visualizations_small/
├── full_dependency_graph.png          # 完整依赖图
├── connected_component.png            # 连通分量图
├── tasks_cpu_intensive.png            # CPU密集型任务图
├── tasks_memory_intensive.png         # 内存密集型任务图
├── tasks_io_intensive.png             # I/O密集型任务图
├── tasks_network_intensive.png        # 网络密集型任务图
├── tasks_general.png                  # 通用型任务图
└── legend.png                         # 颜色图例
```

## 扩展功能

### 自定义数据生成
```python
from task_generate import TaskGenerator
from node_generate import NodeGenerator

# 自定义任务生成
task_gen = TaskGenerator()
custom_tasks = task_gen.generate_tasks(
    num_tasks=100,
    task_types=['cpu_intensive', 'memory_intensive'],
    dependency_ratio=0.3
)

# 自定义节点生成
node_gen = NodeGenerator()
custom_nodes = node_gen.generate_nodes(
    num_nodes=20,
    node_types=['cpu_intensive', 'general'],
    heterogeneity_factor=0.4
)
```

### 数据验证
```python
from data_validator import DataValidator

validator = DataValidator()
validation_result = validator.validate_dataset(
    tasks_file="datasets/tasks_small.json",
    nodes_file="datasets/nodes_small.json"
)
print(validation_result.summary())
```

## 依赖库要求

### 基础依赖
```
numpy>=1.19.0
matplotlib>=3.3.0
networkx>=2.5
```

### 可视化依赖（可选）
```
pygraphviz>=1.7  # 高质量图形渲染（推荐）
```

### 安装命令
```bash
# 基础依赖
pip install numpy matplotlib networkx

# 可视化依赖（可选）
conda install -c conda-forge pygraphviz
# 或
pip install pygraphviz
```

## 应用场景

### 负载均衡算法测试
- 轮询算法 (Round Robin)
- 最少连接算法 (Least Connections)
- 加权轮询算法 (Weighted Round Robin)
- 动态负载均衡算法

### 调度算法评估
- 先来先服务 (FCFS)
- 最短作业优先 (SJF)
- 优先级调度
- 多级反馈队列

### 性能指标分析
- 系统吞吐量
- 平均响应时间
- 资源利用率
- 负载均衡度
- Makespan

## 注意事项

1. **数据精度**: 所有数值保留2位小数，确保一致性
2. **依赖关系**: 生成的依赖图为DAG结构，无循环依赖
3. **资源约束**: 任务总需求不超过系统总容量的80%
4. **时间一致性**: 调度信息中的时间戳保持逻辑一致性
5. **类型分布**: 各种任务和节点类型分布相对均匀

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持三种规模数据集生成
- 包含完整的任务和节点数据结构
- 提供基础可视化功能

### v1.1.0 (2024-01-20)
- 添加任务依赖图可视化
- 支持pygraphviz和networkx两种渲染方式
- 改进数据质量验证
- 增加自定义生成参数

## 联系信息

如有问题或建议，请查看生成的示例数据或参考可视化结果。数据格式设计遵循负载均衡仿真的最佳实践，适用于各种调度和负载均衡算法的研究和测试。