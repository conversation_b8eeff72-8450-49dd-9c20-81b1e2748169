{"metadata": {"total_tasks": 500, "generated_at": "2025-08-02T23:12:14.744384", "dependency_probability": 0.4, "task_types": ["cpu_intensive", "memory_intensive", "io_intensive", "network_intensive", "general"]}, "tasks": [{"id": 1, "time_step": 5, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.68, "memory_mb": 4891.15, "io_bandwidth_mbps": 334.77, "network_bandwidth_mbps": 22.37}, "execution_time": {"base_runtime_seconds": 277.77, "estimated_runtime_seconds": 315.51}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 2, "time_step": 8, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.28, "memory_mb": 24064.26, "io_bandwidth_mbps": 291.25, "network_bandwidth_mbps": 25.9}, "execution_time": {"base_runtime_seconds": 417.1, "estimated_runtime_seconds": 480.56}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 3, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 1.78, "memory_mb": 2826.64, "io_bandwidth_mbps": 123.16, "network_bandwidth_mbps": 32.71}, "execution_time": {"base_runtime_seconds": 94.31, "estimated_runtime_seconds": 112.03}, "dependencies": [1], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 4, "time_step": 21, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.91, "memory_mb": 1809.71, "io_bandwidth_mbps": 1792.95, "network_bandwidth_mbps": 84.94}, "execution_time": {"base_runtime_seconds": 887.99, "estimated_runtime_seconds": 1059.95}, "dependencies": [3, 2, 1], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 5, "time_step": 8, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.78, "memory_mb": 5369.76, "io_bandwidth_mbps": 418.13, "network_bandwidth_mbps": 580.22}, "execution_time": {"base_runtime_seconds": 329.82, "estimated_runtime_seconds": 428.53}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 6, "time_step": 15, "task_type": "general", "resource_demand": {"cpu_cores": 1.41, "memory_mb": 12852.04, "io_bandwidth_mbps": 184.6, "network_bandwidth_mbps": 118.56}, "execution_time": {"base_runtime_seconds": 379.66, "estimated_runtime_seconds": 477.97}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 7, "time_step": 44, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.05, "memory_mb": 4078.8, "io_bandwidth_mbps": 1368.78, "network_bandwidth_mbps": 39.49}, "execution_time": {"base_runtime_seconds": 505.76, "estimated_runtime_seconds": 648.83}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 8, "time_step": 22, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.97, "memory_mb": 32366.89, "io_bandwidth_mbps": 234.84, "network_bandwidth_mbps": 16.32}, "execution_time": {"base_runtime_seconds": 502.16, "estimated_runtime_seconds": 572.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 9, "time_step": 7, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.69, "memory_mb": 3213.39, "io_bandwidth_mbps": 392.52, "network_bandwidth_mbps": 40.35}, "execution_time": {"base_runtime_seconds": 205.58, "estimated_runtime_seconds": 249.48}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 10, "time_step": 2, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.19, "memory_mb": 21462.58, "io_bandwidth_mbps": 115.53, "network_bandwidth_mbps": 10.61}, "execution_time": {"base_runtime_seconds": 407.28, "estimated_runtime_seconds": 467.91}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 11, "time_step": 11, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.26, "memory_mb": 2063.35, "io_bandwidth_mbps": 106.46, "network_bandwidth_mbps": 32.25}, "execution_time": {"base_runtime_seconds": 61.53, "estimated_runtime_seconds": 74.85}, "dependencies": [6, 5, 8], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 12, "time_step": 0, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.24, "memory_mb": 6388.52, "io_bandwidth_mbps": 483.94, "network_bandwidth_mbps": 90.31}, "execution_time": {"base_runtime_seconds": 256.28, "estimated_runtime_seconds": 313.45}, "dependencies": [2, 11, 3], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 13, "time_step": 9, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.46, "memory_mb": 2274.55, "io_bandwidth_mbps": 444.79, "network_bandwidth_mbps": 65.35}, "execution_time": {"base_runtime_seconds": 124.97, "estimated_runtime_seconds": 159.91}, "dependencies": [8], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 14, "time_step": 35, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.7, "memory_mb": 5319.05, "io_bandwidth_mbps": 256.06, "network_bandwidth_mbps": 827.95}, "execution_time": {"base_runtime_seconds": 177.85, "estimated_runtime_seconds": 230.86}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 15, "time_step": 27, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.6, "memory_mb": 3028.44, "io_bandwidth_mbps": 802.12, "network_bandwidth_mbps": 37.72}, "execution_time": {"base_runtime_seconds": 757.09, "estimated_runtime_seconds": 929.38}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 16, "time_step": 28, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.77, "memory_mb": 23763.66, "io_bandwidth_mbps": 84.81, "network_bandwidth_mbps": 36.79}, "execution_time": {"base_runtime_seconds": 429.45, "estimated_runtime_seconds": 514.43}, "dependencies": [13], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 17, "time_step": 40, "task_type": "general", "resource_demand": {"cpu_cores": 1.37, "memory_mb": 10460.48, "io_bandwidth_mbps": 382.93, "network_bandwidth_mbps": 179.63}, "execution_time": {"base_runtime_seconds": 330.05, "estimated_runtime_seconds": 405.01}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 18, "time_step": 28, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.74, "memory_mb": 2853.5, "io_bandwidth_mbps": 207.97, "network_bandwidth_mbps": 114.28}, "execution_time": {"base_runtime_seconds": 143.05, "estimated_runtime_seconds": 180.33}, "dependencies": [7, 10], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 19, "time_step": 46, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.07, "memory_mb": 15326.17, "io_bandwidth_mbps": 109.99, "network_bandwidth_mbps": 21.43}, "execution_time": {"base_runtime_seconds": 426.98, "estimated_runtime_seconds": 470.5}, "dependencies": [18, 2], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 20, "time_step": 50, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.19, "memory_mb": 1929.38, "io_bandwidth_mbps": 402.31, "network_bandwidth_mbps": 230.49}, "execution_time": {"base_runtime_seconds": 333.54, "estimated_runtime_seconds": 395.06}, "dependencies": [17, 5, 8], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 21, "time_step": 20, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.9, "memory_mb": 4161.89, "io_bandwidth_mbps": 216.42, "network_bandwidth_mbps": 60.08}, "execution_time": {"base_runtime_seconds": 63.8, "estimated_runtime_seconds": 82.33}, "dependencies": [5], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 22, "time_step": 31, "task_type": "general", "resource_demand": {"cpu_cores": 2.11, "memory_mb": 14125.16, "io_bandwidth_mbps": 122.99, "network_bandwidth_mbps": 80.79}, "execution_time": {"base_runtime_seconds": 419.84, "estimated_runtime_seconds": 524.69}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 23, "time_step": 25, "task_type": "general", "resource_demand": {"cpu_cores": 3.94, "memory_mb": 15591.79, "io_bandwidth_mbps": 604.17, "network_bandwidth_mbps": 68.97}, "execution_time": {"base_runtime_seconds": 121.79, "estimated_runtime_seconds": 139.63}, "dependencies": [17, 16], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 24, "time_step": 23, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.07, "memory_mb": 12551.97, "io_bandwidth_mbps": 251.37, "network_bandwidth_mbps": 23.4}, "execution_time": {"base_runtime_seconds": 381.65, "estimated_runtime_seconds": 477.94}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 25, "time_step": 36, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.86, "memory_mb": 6310.23, "io_bandwidth_mbps": 438.08, "network_bandwidth_mbps": 20.81}, "execution_time": {"base_runtime_seconds": 232.41, "estimated_runtime_seconds": 287.44}, "dependencies": [17, 15, 4], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 26, "time_step": 42, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.59, "memory_mb": 2789.07, "io_bandwidth_mbps": 1344.47, "network_bandwidth_mbps": 94.39}, "execution_time": {"base_runtime_seconds": 429.56, "estimated_runtime_seconds": 505.11}, "dependencies": [10], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 27, "time_step": 39, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.36, "memory_mb": 8432.47, "io_bandwidth_mbps": 299.09, "network_bandwidth_mbps": 35.41}, "execution_time": {"base_runtime_seconds": 490.0, "estimated_runtime_seconds": 607.58}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 28, "time_step": 14, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.37, "memory_mb": 2639.68, "io_bandwidth_mbps": 1259.56, "network_bandwidth_mbps": 88.61}, "execution_time": {"base_runtime_seconds": 338.24, "estimated_runtime_seconds": 408.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 29, "time_step": 32, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.3, "memory_mb": 3217.93, "io_bandwidth_mbps": 1938.11, "network_bandwidth_mbps": 56.87}, "execution_time": {"base_runtime_seconds": 566.68, "estimated_runtime_seconds": 662.32}, "dependencies": [17, 16, 15], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 30, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 6.15, "memory_mb": 15741.24, "io_bandwidth_mbps": 604.3, "network_bandwidth_mbps": 133.74}, "execution_time": {"base_runtime_seconds": 239.14, "estimated_runtime_seconds": 293.99}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 31, "time_step": 8, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.17, "memory_mb": 3792.14, "io_bandwidth_mbps": 1296.67, "network_bandwidth_mbps": 95.15}, "execution_time": {"base_runtime_seconds": 504.47, "estimated_runtime_seconds": 589.86}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 32, "time_step": 7, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.32, "memory_mb": 5206.02, "io_bandwidth_mbps": 450.82, "network_bandwidth_mbps": 804.74}, "execution_time": {"base_runtime_seconds": 152.54, "estimated_runtime_seconds": 195.96}, "dependencies": [30, 25, 5], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 33, "time_step": 24, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.98, "memory_mb": 2231.82, "io_bandwidth_mbps": 457.35, "network_bandwidth_mbps": 830.84}, "execution_time": {"base_runtime_seconds": 335.5, "estimated_runtime_seconds": 385.41}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 34, "time_step": 33, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.06, "memory_mb": 1598.11, "io_bandwidth_mbps": 284.17, "network_bandwidth_mbps": 738.05}, "execution_time": {"base_runtime_seconds": 440.1, "estimated_runtime_seconds": 526.28}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 35, "time_step": 22, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.65, "memory_mb": 3656.6, "io_bandwidth_mbps": 125.63, "network_bandwidth_mbps": 740.77}, "execution_time": {"base_runtime_seconds": 165.9, "estimated_runtime_seconds": 211.61}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 36, "time_step": 4, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.16, "memory_mb": 25463.65, "io_bandwidth_mbps": 160.64, "network_bandwidth_mbps": 21.89}, "execution_time": {"base_runtime_seconds": 460.23, "estimated_runtime_seconds": 594.04}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 37, "time_step": 28, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.91, "memory_mb": 28787.23, "io_bandwidth_mbps": 281.73, "network_bandwidth_mbps": 27.55}, "execution_time": {"base_runtime_seconds": 352.22, "estimated_runtime_seconds": 421.38}, "dependencies": [10, 13, 14], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 38, "time_step": 14, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.53, "memory_mb": 4520.64, "io_bandwidth_mbps": 304.04, "network_bandwidth_mbps": 535.46}, "execution_time": {"base_runtime_seconds": 282.3, "estimated_runtime_seconds": 339.41}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 39, "time_step": 23, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.27, "memory_mb": 13732.97, "io_bandwidth_mbps": 278.06, "network_bandwidth_mbps": 24.98}, "execution_time": {"base_runtime_seconds": 493.73, "estimated_runtime_seconds": 638.51}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 40, "time_step": 26, "task_type": "general", "resource_demand": {"cpu_cores": 2.93, "memory_mb": 8041.75, "io_bandwidth_mbps": 176.43, "network_bandwidth_mbps": 164.84}, "execution_time": {"base_runtime_seconds": 123.16, "estimated_runtime_seconds": 140.48}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 41, "time_step": 45, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.09, "memory_mb": 7709.26, "io_bandwidth_mbps": 383.57, "network_bandwidth_mbps": 466.71}, "execution_time": {"base_runtime_seconds": 188.16, "estimated_runtime_seconds": 220.92}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 42, "time_step": 46, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.24, "memory_mb": 8013.17, "io_bandwidth_mbps": 208.24, "network_bandwidth_mbps": 54.73}, "execution_time": {"base_runtime_seconds": 256.33, "estimated_runtime_seconds": 325.75}, "dependencies": [22], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 43, "time_step": 39, "task_type": "general", "resource_demand": {"cpu_cores": 7.21, "memory_mb": 11597.69, "io_bandwidth_mbps": 307.48, "network_bandwidth_mbps": 105.18}, "execution_time": {"base_runtime_seconds": 386.03, "estimated_runtime_seconds": 449.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 44, "time_step": 0, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.52, "memory_mb": 7064.98, "io_bandwidth_mbps": 126.19, "network_bandwidth_mbps": 20.57}, "execution_time": {"base_runtime_seconds": 147.93, "estimated_runtime_seconds": 191.77}, "dependencies": [16, 18, 33], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 45, "time_step": 27, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.74, "memory_mb": 11628.48, "io_bandwidth_mbps": 73.45, "network_bandwidth_mbps": 16.08}, "execution_time": {"base_runtime_seconds": 478.53, "estimated_runtime_seconds": 526.4}, "dependencies": [14], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 46, "time_step": 21, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.77, "memory_mb": 21493.45, "io_bandwidth_mbps": 232.34, "network_bandwidth_mbps": 38.78}, "execution_time": {"base_runtime_seconds": 323.82, "estimated_runtime_seconds": 362.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 47, "time_step": 14, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.81, "memory_mb": 3849.25, "io_bandwidth_mbps": 796.01, "network_bandwidth_mbps": 65.07}, "execution_time": {"base_runtime_seconds": 481.83, "estimated_runtime_seconds": 549.39}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 48, "time_step": 42, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.35, "memory_mb": 2849.68, "io_bandwidth_mbps": 1446.21, "network_bandwidth_mbps": 49.57}, "execution_time": {"base_runtime_seconds": 638.32, "estimated_runtime_seconds": 767.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 49, "time_step": 7, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.22, "memory_mb": 22309.21, "io_bandwidth_mbps": 88.88, "network_bandwidth_mbps": 47.6}, "execution_time": {"base_runtime_seconds": 166.97, "estimated_runtime_seconds": 187.91}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 50, "time_step": 16, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.89, "memory_mb": 3859.21, "io_bandwidth_mbps": 254.77, "network_bandwidth_mbps": 85.19}, "execution_time": {"base_runtime_seconds": 244.07, "estimated_runtime_seconds": 280.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 51, "time_step": 18, "task_type": "general", "resource_demand": {"cpu_cores": 4.17, "memory_mb": 11539.77, "io_bandwidth_mbps": 439.15, "network_bandwidth_mbps": 70.55}, "execution_time": {"base_runtime_seconds": 228.87, "estimated_runtime_seconds": 270.62}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 52, "time_step": 40, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.39, "memory_mb": 6511.15, "io_bandwidth_mbps": 299.92, "network_bandwidth_mbps": 541.82}, "execution_time": {"base_runtime_seconds": 108.95, "estimated_runtime_seconds": 136.03}, "dependencies": [50, 4, 16], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 53, "time_step": 17, "task_type": "general", "resource_demand": {"cpu_cores": 3.0, "memory_mb": 11650.29, "io_bandwidth_mbps": 547.21, "network_bandwidth_mbps": 126.54}, "execution_time": {"base_runtime_seconds": 456.13, "estimated_runtime_seconds": 536.16}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 54, "time_step": 2, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.83, "memory_mb": 7247.11, "io_bandwidth_mbps": 303.16, "network_bandwidth_mbps": 35.53}, "execution_time": {"base_runtime_seconds": 188.54, "estimated_runtime_seconds": 213.29}, "dependencies": [49, 14, 32], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 55, "time_step": 25, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.21, "memory_mb": 7925.86, "io_bandwidth_mbps": 178.46, "network_bandwidth_mbps": 45.22}, "execution_time": {"base_runtime_seconds": 299.34, "estimated_runtime_seconds": 355.32}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 56, "time_step": 47, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.35, "memory_mb": 9837.67, "io_bandwidth_mbps": 53.64, "network_bandwidth_mbps": 25.78}, "execution_time": {"base_runtime_seconds": 123.64, "estimated_runtime_seconds": 156.41}, "dependencies": [29, 7, 9], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 57, "time_step": 26, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.63, "memory_mb": 5417.34, "io_bandwidth_mbps": 181.0, "network_bandwidth_mbps": 18.33}, "execution_time": {"base_runtime_seconds": 196.01, "estimated_runtime_seconds": 219.23}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 58, "time_step": 10, "task_type": "general", "resource_demand": {"cpu_cores": 5.13, "memory_mb": 6878.43, "io_bandwidth_mbps": 480.99, "network_bandwidth_mbps": 15.52}, "execution_time": {"base_runtime_seconds": 151.07, "estimated_runtime_seconds": 187.98}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 59, "time_step": 15, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.37, "memory_mb": 1368.23, "io_bandwidth_mbps": 1942.7, "network_bandwidth_mbps": 35.88}, "execution_time": {"base_runtime_seconds": 865.16, "estimated_runtime_seconds": 971.71}, "dependencies": [49, 8], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 60, "time_step": 15, "task_type": "general", "resource_demand": {"cpu_cores": 7.92, "memory_mb": 12409.96, "io_bandwidth_mbps": 180.59, "network_bandwidth_mbps": 11.62}, "execution_time": {"base_runtime_seconds": 245.52, "estimated_runtime_seconds": 274.13}, "dependencies": [47], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 61, "time_step": 21, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.05, "memory_mb": 1693.82, "io_bandwidth_mbps": 990.92, "network_bandwidth_mbps": 31.71}, "execution_time": {"base_runtime_seconds": 787.16, "estimated_runtime_seconds": 956.8}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 62, "time_step": 50, "task_type": "general", "resource_demand": {"cpu_cores": 5.7, "memory_mb": 12821.27, "io_bandwidth_mbps": 727.42, "network_bandwidth_mbps": 74.7}, "execution_time": {"base_runtime_seconds": 170.9, "estimated_runtime_seconds": 219.81}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 63, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.37, "memory_mb": 23723.07, "io_bandwidth_mbps": 250.27, "network_bandwidth_mbps": 39.54}, "execution_time": {"base_runtime_seconds": 190.09, "estimated_runtime_seconds": 212.88}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 64, "time_step": 30, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.01, "memory_mb": 2496.31, "io_bandwidth_mbps": 611.22, "network_bandwidth_mbps": 80.93}, "execution_time": {"base_runtime_seconds": 865.21, "estimated_runtime_seconds": 1013.72}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 65, "time_step": 34, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.93, "memory_mb": 3413.06, "io_bandwidth_mbps": 170.92, "network_bandwidth_mbps": 46.91}, "execution_time": {"base_runtime_seconds": 80.84, "estimated_runtime_seconds": 103.72}, "dependencies": [44], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 66, "time_step": 50, "task_type": "general", "resource_demand": {"cpu_cores": 7.29, "memory_mb": 13594.54, "io_bandwidth_mbps": 626.34, "network_bandwidth_mbps": 127.23}, "execution_time": {"base_runtime_seconds": 307.13, "estimated_runtime_seconds": 343.93}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 67, "time_step": 31, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.36, "memory_mb": 4411.52, "io_bandwidth_mbps": 138.42, "network_bandwidth_mbps": 54.93}, "execution_time": {"base_runtime_seconds": 262.1, "estimated_runtime_seconds": 337.57}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 68, "time_step": 24, "task_type": "general", "resource_demand": {"cpu_cores": 6.58, "memory_mb": 3810.13, "io_bandwidth_mbps": 414.64, "network_bandwidth_mbps": 119.84}, "execution_time": {"base_runtime_seconds": 234.84, "estimated_runtime_seconds": 276.66}, "dependencies": [65, 17], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 69, "time_step": 23, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.03, "memory_mb": 2274.86, "io_bandwidth_mbps": 1989.31, "network_bandwidth_mbps": 16.59}, "execution_time": {"base_runtime_seconds": 188.47, "estimated_runtime_seconds": 239.12}, "dependencies": [62, 27, 14], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 70, "time_step": 35, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.12, "memory_mb": 6160.53, "io_bandwidth_mbps": 375.73, "network_bandwidth_mbps": 531.14}, "execution_time": {"base_runtime_seconds": 92.58, "estimated_runtime_seconds": 116.9}, "dependencies": [13, 60], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 71, "time_step": 13, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.05, "memory_mb": 6100.44, "io_bandwidth_mbps": 243.95, "network_bandwidth_mbps": 721.3}, "execution_time": {"base_runtime_seconds": 232.69, "estimated_runtime_seconds": 288.45}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 72, "time_step": 38, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.46, "memory_mb": 6443.01, "io_bandwidth_mbps": 402.22, "network_bandwidth_mbps": 776.76}, "execution_time": {"base_runtime_seconds": 397.33, "estimated_runtime_seconds": 516.3}, "dependencies": [26, 53, 68], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 73, "time_step": 30, "task_type": "general", "resource_demand": {"cpu_cores": 7.82, "memory_mb": 10789.03, "io_bandwidth_mbps": 180.43, "network_bandwidth_mbps": 24.93}, "execution_time": {"base_runtime_seconds": 466.87, "estimated_runtime_seconds": 533.05}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 74, "time_step": 20, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.44, "memory_mb": 30621.34, "io_bandwidth_mbps": 58.53, "network_bandwidth_mbps": 21.11}, "execution_time": {"base_runtime_seconds": 441.58, "estimated_runtime_seconds": 494.62}, "dependencies": [42, 38, 67], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 75, "time_step": 35, "task_type": "general", "resource_demand": {"cpu_cores": 1.9, "memory_mb": 15097.1, "io_bandwidth_mbps": 150.22, "network_bandwidth_mbps": 72.49}, "execution_time": {"base_runtime_seconds": 243.85, "estimated_runtime_seconds": 304.13}, "dependencies": [60], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 76, "time_step": 15, "task_type": "general", "resource_demand": {"cpu_cores": 2.22, "memory_mb": 3001.71, "io_bandwidth_mbps": 228.14, "network_bandwidth_mbps": 196.89}, "execution_time": {"base_runtime_seconds": 364.11, "estimated_runtime_seconds": 443.22}, "dependencies": [4, 71], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 77, "time_step": 28, "task_type": "general", "resource_demand": {"cpu_cores": 7.68, "memory_mb": 9020.01, "io_bandwidth_mbps": 789.77, "network_bandwidth_mbps": 94.04}, "execution_time": {"base_runtime_seconds": 209.23, "estimated_runtime_seconds": 239.34}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 78, "time_step": 45, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.51, "memory_mb": 24930.39, "io_bandwidth_mbps": 118.29, "network_bandwidth_mbps": 21.43}, "execution_time": {"base_runtime_seconds": 168.16, "estimated_runtime_seconds": 205.77}, "dependencies": [24], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 79, "time_step": 39, "task_type": "general", "resource_demand": {"cpu_cores": 2.2, "memory_mb": 15617.48, "io_bandwidth_mbps": 795.14, "network_bandwidth_mbps": 139.2}, "execution_time": {"base_runtime_seconds": 291.53, "estimated_runtime_seconds": 374.19}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 80, "time_step": 36, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.48, "memory_mb": 12041.98, "io_bandwidth_mbps": 95.3, "network_bandwidth_mbps": 27.01}, "execution_time": {"base_runtime_seconds": 549.43, "estimated_runtime_seconds": 675.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 81, "time_step": 19, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.53, "memory_mb": 3170.84, "io_bandwidth_mbps": 1146.34, "network_bandwidth_mbps": 30.48}, "execution_time": {"base_runtime_seconds": 681.74, "estimated_runtime_seconds": 794.47}, "dependencies": [79, 50, 76], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 82, "time_step": 4, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.95, "memory_mb": 19944.12, "io_bandwidth_mbps": 260.67, "network_bandwidth_mbps": 33.27}, "execution_time": {"base_runtime_seconds": 149.36, "estimated_runtime_seconds": 181.14}, "dependencies": [31], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 83, "time_step": 29, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.65, "memory_mb": 1215.38, "io_bandwidth_mbps": 359.47, "network_bandwidth_mbps": 835.37}, "execution_time": {"base_runtime_seconds": 95.54, "estimated_runtime_seconds": 113.79}, "dependencies": [66, 46], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 84, "time_step": 43, "task_type": "general", "resource_demand": {"cpu_cores": 3.82, "memory_mb": 7986.39, "io_bandwidth_mbps": 263.75, "network_bandwidth_mbps": 61.17}, "execution_time": {"base_runtime_seconds": 325.51, "estimated_runtime_seconds": 398.68}, "dependencies": [37, 20, 44], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 85, "time_step": 13, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.37, "memory_mb": 5529.18, "io_bandwidth_mbps": 357.93, "network_bandwidth_mbps": 60.93}, "execution_time": {"base_runtime_seconds": 185.44, "estimated_runtime_seconds": 216.54}, "dependencies": [41], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 86, "time_step": 8, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.0, "memory_mb": 2889.01, "io_bandwidth_mbps": 862.65, "network_bandwidth_mbps": 32.35}, "execution_time": {"base_runtime_seconds": 690.28, "estimated_runtime_seconds": 818.03}, "dependencies": [38, 73], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 87, "time_step": 27, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.89, "memory_mb": 6487.08, "io_bandwidth_mbps": 154.42, "network_bandwidth_mbps": 46.13}, "execution_time": {"base_runtime_seconds": 100.62, "estimated_runtime_seconds": 120.48}, "dependencies": [21], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 88, "time_step": 35, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.71, "memory_mb": 7151.84, "io_bandwidth_mbps": 256.47, "network_bandwidth_mbps": 97.07}, "execution_time": {"base_runtime_seconds": 140.7, "estimated_runtime_seconds": 170.72}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 89, "time_step": 33, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.43, "memory_mb": 4503.25, "io_bandwidth_mbps": 488.16, "network_bandwidth_mbps": 60.21}, "execution_time": {"base_runtime_seconds": 174.48, "estimated_runtime_seconds": 204.76}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 90, "time_step": 33, "task_type": "general", "resource_demand": {"cpu_cores": 7.67, "memory_mb": 9544.57, "io_bandwidth_mbps": 749.27, "network_bandwidth_mbps": 178.67}, "execution_time": {"base_runtime_seconds": 450.84, "estimated_runtime_seconds": 524.79}, "dependencies": [28, 58, 18], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 91, "time_step": 41, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.8, "memory_mb": 3664.49, "io_bandwidth_mbps": 1870.98, "network_bandwidth_mbps": 40.23}, "execution_time": {"base_runtime_seconds": 421.36, "estimated_runtime_seconds": 487.82}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 92, "time_step": 19, "task_type": "general", "resource_demand": {"cpu_cores": 2.57, "memory_mb": 2696.4, "io_bandwidth_mbps": 259.44, "network_bandwidth_mbps": 112.61}, "execution_time": {"base_runtime_seconds": 366.57, "estimated_runtime_seconds": 455.11}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 93, "time_step": 20, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.86, "memory_mb": 5323.62, "io_bandwidth_mbps": 363.54, "network_bandwidth_mbps": 34.48}, "execution_time": {"base_runtime_seconds": 125.54, "estimated_runtime_seconds": 156.83}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 94, "time_step": 29, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.22, "memory_mb": 2227.62, "io_bandwidth_mbps": 388.72, "network_bandwidth_mbps": 61.08}, "execution_time": {"base_runtime_seconds": 139.78, "estimated_runtime_seconds": 161.31}, "dependencies": [32, 10], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 95, "time_step": 46, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.64, "memory_mb": 7591.75, "io_bandwidth_mbps": 118.74, "network_bandwidth_mbps": 665.46}, "execution_time": {"base_runtime_seconds": 200.63, "estimated_runtime_seconds": 251.0}, "dependencies": [70], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 96, "time_step": 8, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.73, "memory_mb": 22463.62, "io_bandwidth_mbps": 238.69, "network_bandwidth_mbps": 12.63}, "execution_time": {"base_runtime_seconds": 343.55, "estimated_runtime_seconds": 436.58}, "dependencies": [55, 66], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 97, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.81, "memory_mb": 18071.85, "io_bandwidth_mbps": 201.81, "network_bandwidth_mbps": 40.32}, "execution_time": {"base_runtime_seconds": 226.23, "estimated_runtime_seconds": 258.06}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 98, "time_step": 18, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.81, "memory_mb": 3072.22, "io_bandwidth_mbps": 1318.92, "network_bandwidth_mbps": 62.16}, "execution_time": {"base_runtime_seconds": 604.98, "estimated_runtime_seconds": 712.16}, "dependencies": [6, 9, 84], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 99, "time_step": 42, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.15, "memory_mb": 2784.02, "io_bandwidth_mbps": 183.26, "network_bandwidth_mbps": 50.22}, "execution_time": {"base_runtime_seconds": 98.62, "estimated_runtime_seconds": 124.9}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 100, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.94, "memory_mb": 6808.63, "io_bandwidth_mbps": 222.79, "network_bandwidth_mbps": 36.53}, "execution_time": {"base_runtime_seconds": 170.75, "estimated_runtime_seconds": 214.69}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 101, "time_step": 19, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.95, "memory_mb": 6786.72, "io_bandwidth_mbps": 415.11, "network_bandwidth_mbps": 90.95}, "execution_time": {"base_runtime_seconds": 131.13, "estimated_runtime_seconds": 161.55}, "dependencies": [77], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 102, "time_step": 31, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.92, "memory_mb": 2897.21, "io_bandwidth_mbps": 1235.88, "network_bandwidth_mbps": 24.64}, "execution_time": {"base_runtime_seconds": 327.84, "estimated_runtime_seconds": 376.11}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 103, "time_step": 29, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.04, "memory_mb": 2932.78, "io_bandwidth_mbps": 887.85, "network_bandwidth_mbps": 48.54}, "execution_time": {"base_runtime_seconds": 452.69, "estimated_runtime_seconds": 541.44}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 104, "time_step": 27, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.36, "memory_mb": 21424.72, "io_bandwidth_mbps": 102.25, "network_bandwidth_mbps": 33.15}, "execution_time": {"base_runtime_seconds": 159.46, "estimated_runtime_seconds": 194.78}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 105, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.55, "memory_mb": 5575.75, "io_bandwidth_mbps": 279.2, "network_bandwidth_mbps": 530.78}, "execution_time": {"base_runtime_seconds": 151.59, "estimated_runtime_seconds": 195.75}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 106, "time_step": 8, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.05, "memory_mb": 3655.85, "io_bandwidth_mbps": 596.96, "network_bandwidth_mbps": 55.39}, "execution_time": {"base_runtime_seconds": 488.17, "estimated_runtime_seconds": 585.07}, "dependencies": [12, 68, 88], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 107, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.76, "memory_mb": 3066.74, "io_bandwidth_mbps": 217.11, "network_bandwidth_mbps": 393.39}, "execution_time": {"base_runtime_seconds": 134.97, "estimated_runtime_seconds": 165.83}, "dependencies": [36, 40, 70], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 108, "time_step": 32, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.8, "memory_mb": 5784.42, "io_bandwidth_mbps": 389.37, "network_bandwidth_mbps": 37.02}, "execution_time": {"base_runtime_seconds": 233.36, "estimated_runtime_seconds": 267.74}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 109, "time_step": 20, "task_type": "general", "resource_demand": {"cpu_cores": 7.62, "memory_mb": 5366.49, "io_bandwidth_mbps": 344.24, "network_bandwidth_mbps": 172.75}, "execution_time": {"base_runtime_seconds": 426.28, "estimated_runtime_seconds": 550.67}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 110, "time_step": 5, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.8, "memory_mb": 2672.61, "io_bandwidth_mbps": 486.97, "network_bandwidth_mbps": 446.59}, "execution_time": {"base_runtime_seconds": 345.3, "estimated_runtime_seconds": 402.65}, "dependencies": [3], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 111, "time_step": 44, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.52, "memory_mb": 7214.99, "io_bandwidth_mbps": 434.07, "network_bandwidth_mbps": 702.06}, "execution_time": {"base_runtime_seconds": 111.96, "estimated_runtime_seconds": 145.06}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 112, "time_step": 0, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.62, "memory_mb": 6315.22, "io_bandwidth_mbps": 165.19, "network_bandwidth_mbps": 86.55}, "execution_time": {"base_runtime_seconds": 165.25, "estimated_runtime_seconds": 207.37}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 113, "time_step": 40, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.16, "memory_mb": 3499.94, "io_bandwidth_mbps": 663.67, "network_bandwidth_mbps": 88.2}, "execution_time": {"base_runtime_seconds": 518.36, "estimated_runtime_seconds": 661.33}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 114, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.14, "memory_mb": 5471.11, "io_bandwidth_mbps": 473.91, "network_bandwidth_mbps": 57.08}, "execution_time": {"base_runtime_seconds": 292.14, "estimated_runtime_seconds": 336.1}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 115, "time_step": 11, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.54, "memory_mb": 4688.55, "io_bandwidth_mbps": 264.9, "network_bandwidth_mbps": 698.39}, "execution_time": {"base_runtime_seconds": 437.76, "estimated_runtime_seconds": 499.08}, "dependencies": [23], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 116, "time_step": 6, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.29, "memory_mb": 3693.01, "io_bandwidth_mbps": 1135.81, "network_bandwidth_mbps": 14.74}, "execution_time": {"base_runtime_seconds": 686.02, "estimated_runtime_seconds": 781.1}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 117, "time_step": 43, "task_type": "general", "resource_demand": {"cpu_cores": 2.39, "memory_mb": 5765.21, "io_bandwidth_mbps": 175.36, "network_bandwidth_mbps": 36.46}, "execution_time": {"base_runtime_seconds": 304.86, "estimated_runtime_seconds": 342.06}, "dependencies": [21, 5], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 118, "time_step": 17, "task_type": "general", "resource_demand": {"cpu_cores": 1.9, "memory_mb": 9715.54, "io_bandwidth_mbps": 349.63, "network_bandwidth_mbps": 26.05}, "execution_time": {"base_runtime_seconds": 193.17, "estimated_runtime_seconds": 221.78}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 119, "time_step": 47, "task_type": "general", "resource_demand": {"cpu_cores": 5.78, "memory_mb": 15293.88, "io_bandwidth_mbps": 423.67, "network_bandwidth_mbps": 161.47}, "execution_time": {"base_runtime_seconds": 449.82, "estimated_runtime_seconds": 509.44}, "dependencies": [107], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 120, "time_step": 37, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 12.25, "memory_mb": 2510.4, "io_bandwidth_mbps": 170.77, "network_bandwidth_mbps": 78.1}, "execution_time": {"base_runtime_seconds": 241.18, "estimated_runtime_seconds": 279.49}, "dependencies": [5, 90, 39], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 121, "time_step": 42, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.83, "memory_mb": 1395.22, "io_bandwidth_mbps": 1438.67, "network_bandwidth_mbps": 28.19}, "execution_time": {"base_runtime_seconds": 766.92, "estimated_runtime_seconds": 974.9}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 122, "time_step": 30, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.22, "memory_mb": 1491.76, "io_bandwidth_mbps": 1677.29, "network_bandwidth_mbps": 49.97}, "execution_time": {"base_runtime_seconds": 899.39, "estimated_runtime_seconds": 1082.46}, "dependencies": [21, 45], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 123, "time_step": 27, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.08, "memory_mb": 3963.52, "io_bandwidth_mbps": 825.37, "network_bandwidth_mbps": 52.27}, "execution_time": {"base_runtime_seconds": 469.46, "estimated_runtime_seconds": 542.0}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 124, "time_step": 36, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.61, "memory_mb": 27084.2, "io_bandwidth_mbps": 298.2, "network_bandwidth_mbps": 13.08}, "execution_time": {"base_runtime_seconds": 175.26, "estimated_runtime_seconds": 210.87}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 125, "time_step": 17, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.33, "memory_mb": 6368.9, "io_bandwidth_mbps": 490.55, "network_bandwidth_mbps": 350.05}, "execution_time": {"base_runtime_seconds": 161.71, "estimated_runtime_seconds": 188.16}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 126, "time_step": 38, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.27, "memory_mb": 29741.3, "io_bandwidth_mbps": 261.95, "network_bandwidth_mbps": 41.47}, "execution_time": {"base_runtime_seconds": 410.73, "estimated_runtime_seconds": 458.37}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 127, "time_step": 32, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.98, "memory_mb": 18008.05, "io_bandwidth_mbps": 73.31, "network_bandwidth_mbps": 17.88}, "execution_time": {"base_runtime_seconds": 202.12, "estimated_runtime_seconds": 229.7}, "dependencies": [24, 21], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 128, "time_step": 6, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.01, "memory_mb": 2161.77, "io_bandwidth_mbps": 536.49, "network_bandwidth_mbps": 10.85}, "execution_time": {"base_runtime_seconds": 441.06, "estimated_runtime_seconds": 516.24}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 129, "time_step": 18, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.6, "memory_mb": 5736.67, "io_bandwidth_mbps": 379.19, "network_bandwidth_mbps": 581.77}, "execution_time": {"base_runtime_seconds": 419.25, "estimated_runtime_seconds": 485.93}, "dependencies": [83], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 130, "time_step": 33, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.25, "memory_mb": 3510.29, "io_bandwidth_mbps": 182.0, "network_bandwidth_mbps": 192.79}, "execution_time": {"base_runtime_seconds": 438.55, "estimated_runtime_seconds": 538.37}, "dependencies": [29, 96], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 131, "time_step": 18, "task_type": "general", "resource_demand": {"cpu_cores": 7.92, "memory_mb": 15319.87, "io_bandwidth_mbps": 648.11, "network_bandwidth_mbps": 99.92}, "execution_time": {"base_runtime_seconds": 353.11, "estimated_runtime_seconds": 430.45}, "dependencies": [104], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 132, "time_step": 48, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.95, "memory_mb": 2703.4, "io_bandwidth_mbps": 1068.29, "network_bandwidth_mbps": 90.25}, "execution_time": {"base_runtime_seconds": 856.4, "estimated_runtime_seconds": 1022.04}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 133, "time_step": 34, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.61, "memory_mb": 3010.19, "io_bandwidth_mbps": 1898.84, "network_bandwidth_mbps": 89.99}, "execution_time": {"base_runtime_seconds": 571.86, "estimated_runtime_seconds": 629.92}, "dependencies": [101, 19], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 134, "time_step": 38, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.93, "memory_mb": 7655.36, "io_bandwidth_mbps": 128.63, "network_bandwidth_mbps": 66.16}, "execution_time": {"base_runtime_seconds": 204.85, "estimated_runtime_seconds": 245.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 135, "time_step": 38, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.85, "memory_mb": 6125.45, "io_bandwidth_mbps": 486.57, "network_bandwidth_mbps": 15.75}, "execution_time": {"base_runtime_seconds": 165.34, "estimated_runtime_seconds": 189.12}, "dependencies": [130, 53], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 136, "time_step": 11, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.28, "memory_mb": 6999.08, "io_bandwidth_mbps": 157.0, "network_bandwidth_mbps": 31.12}, "execution_time": {"base_runtime_seconds": 288.54, "estimated_runtime_seconds": 337.61}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 137, "time_step": 9, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.31, "memory_mb": 4906.56, "io_bandwidth_mbps": 443.36, "network_bandwidth_mbps": 937.16}, "execution_time": {"base_runtime_seconds": 356.28, "estimated_runtime_seconds": 444.36}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 138, "time_step": 8, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.13, "memory_mb": 2958.91, "io_bandwidth_mbps": 315.74, "network_bandwidth_mbps": 300.0}, "execution_time": {"base_runtime_seconds": 142.09, "estimated_runtime_seconds": 166.5}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 139, "time_step": 32, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.5, "memory_mb": 3536.72, "io_bandwidth_mbps": 108.3, "network_bandwidth_mbps": 846.12}, "execution_time": {"base_runtime_seconds": 315.15, "estimated_runtime_seconds": 367.95}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 140, "time_step": 21, "task_type": "general", "resource_demand": {"cpu_cores": 2.67, "memory_mb": 9285.43, "io_bandwidth_mbps": 396.91, "network_bandwidth_mbps": 84.13}, "execution_time": {"base_runtime_seconds": 60.13, "estimated_runtime_seconds": 68.75}, "dependencies": [86], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 141, "time_step": 12, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.17, "memory_mb": 3978.65, "io_bandwidth_mbps": 1422.35, "network_bandwidth_mbps": 51.35}, "execution_time": {"base_runtime_seconds": 497.29, "estimated_runtime_seconds": 626.58}, "dependencies": [36, 61], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 142, "time_step": 35, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.06, "memory_mb": 2820.85, "io_bandwidth_mbps": 432.24, "network_bandwidth_mbps": 82.92}, "execution_time": {"base_runtime_seconds": 244.39, "estimated_runtime_seconds": 290.04}, "dependencies": [114, 134], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 143, "time_step": 39, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.45, "memory_mb": 6395.96, "io_bandwidth_mbps": 471.75, "network_bandwidth_mbps": 60.46}, "execution_time": {"base_runtime_seconds": 296.68, "estimated_runtime_seconds": 360.47}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 144, "time_step": 37, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.7, "memory_mb": 4783.83, "io_bandwidth_mbps": 338.3, "network_bandwidth_mbps": 637.68}, "execution_time": {"base_runtime_seconds": 358.57, "estimated_runtime_seconds": 452.68}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 145, "time_step": 0, "task_type": "general", "resource_demand": {"cpu_cores": 1.48, "memory_mb": 8623.87, "io_bandwidth_mbps": 452.2, "network_bandwidth_mbps": 100.95}, "execution_time": {"base_runtime_seconds": 266.21, "estimated_runtime_seconds": 341.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 146, "time_step": 24, "task_type": "general", "resource_demand": {"cpu_cores": 3.24, "memory_mb": 2354.42, "io_bandwidth_mbps": 117.74, "network_bandwidth_mbps": 57.32}, "execution_time": {"base_runtime_seconds": 103.81, "estimated_runtime_seconds": 132.82}, "dependencies": [126, 76, 46], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 147, "time_step": 13, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.96, "memory_mb": 3311.32, "io_bandwidth_mbps": 388.71, "network_bandwidth_mbps": 51.84}, "execution_time": {"base_runtime_seconds": 296.62, "estimated_runtime_seconds": 371.5}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 148, "time_step": 36, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.64, "memory_mb": 4941.05, "io_bandwidth_mbps": 292.07, "network_bandwidth_mbps": 892.9}, "execution_time": {"base_runtime_seconds": 392.04, "estimated_runtime_seconds": 470.57}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 149, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.19, "memory_mb": 20978.94, "io_bandwidth_mbps": 247.61, "network_bandwidth_mbps": 25.07}, "execution_time": {"base_runtime_seconds": 300.48, "estimated_runtime_seconds": 381.27}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 150, "time_step": 39, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.47, "memory_mb": 23236.14, "io_bandwidth_mbps": 98.81, "network_bandwidth_mbps": 34.08}, "execution_time": {"base_runtime_seconds": 537.21, "estimated_runtime_seconds": 655.26}, "dependencies": [78, 84, 116], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 151, "time_step": 27, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.76, "memory_mb": 7256.49, "io_bandwidth_mbps": 228.66, "network_bandwidth_mbps": 29.55}, "execution_time": {"base_runtime_seconds": 83.41, "estimated_runtime_seconds": 97.76}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 152, "time_step": 4, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.05, "memory_mb": 4314.59, "io_bandwidth_mbps": 377.3, "network_bandwidth_mbps": 83.83}, "execution_time": {"base_runtime_seconds": 82.18, "estimated_runtime_seconds": 94.46}, "dependencies": [53, 69], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 153, "time_step": 50, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.15, "memory_mb": 5131.66, "io_bandwidth_mbps": 295.27, "network_bandwidth_mbps": 91.27}, "execution_time": {"base_runtime_seconds": 161.13, "estimated_runtime_seconds": 207.77}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 154, "time_step": 8, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.21, "memory_mb": 1379.33, "io_bandwidth_mbps": 1538.61, "network_bandwidth_mbps": 29.54}, "execution_time": {"base_runtime_seconds": 359.31, "estimated_runtime_seconds": 455.77}, "dependencies": [85, 108, 147], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 155, "time_step": 31, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.61, "memory_mb": 2601.98, "io_bandwidth_mbps": 392.31, "network_bandwidth_mbps": 44.25}, "execution_time": {"base_runtime_seconds": 250.12, "estimated_runtime_seconds": 297.57}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 156, "time_step": 28, "task_type": "general", "resource_demand": {"cpu_cores": 3.2, "memory_mb": 10403.83, "io_bandwidth_mbps": 223.02, "network_bandwidth_mbps": 98.16}, "execution_time": {"base_runtime_seconds": 280.45, "estimated_runtime_seconds": 315.39}, "dependencies": [97, 106, 8], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 157, "time_step": 24, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.48, "memory_mb": 7572.72, "io_bandwidth_mbps": 179.89, "network_bandwidth_mbps": 32.56}, "execution_time": {"base_runtime_seconds": 213.2, "estimated_runtime_seconds": 245.02}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 158, "time_step": 11, "task_type": "general", "resource_demand": {"cpu_cores": 7.97, "memory_mb": 5908.39, "io_bandwidth_mbps": 489.25, "network_bandwidth_mbps": 111.41}, "execution_time": {"base_runtime_seconds": 207.39, "estimated_runtime_seconds": 229.25}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 159, "time_step": 28, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.74, "memory_mb": 2666.06, "io_bandwidth_mbps": 112.04, "network_bandwidth_mbps": 951.34}, "execution_time": {"base_runtime_seconds": 424.48, "estimated_runtime_seconds": 470.75}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 160, "time_step": 29, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.25, "memory_mb": 1911.11, "io_bandwidth_mbps": 400.53, "network_bandwidth_mbps": 704.64}, "execution_time": {"base_runtime_seconds": 430.51, "estimated_runtime_seconds": 548.3}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 161, "time_step": 50, "task_type": "general", "resource_demand": {"cpu_cores": 3.09, "memory_mb": 8617.12, "io_bandwidth_mbps": 371.14, "network_bandwidth_mbps": 16.04}, "execution_time": {"base_runtime_seconds": 167.79, "estimated_runtime_seconds": 212.1}, "dependencies": [11], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 162, "time_step": 3, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.57, "memory_mb": 5666.82, "io_bandwidth_mbps": 213.33, "network_bandwidth_mbps": 108.98}, "execution_time": {"base_runtime_seconds": 438.53, "estimated_runtime_seconds": 505.97}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 163, "time_step": 3, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.15, "memory_mb": 4881.02, "io_bandwidth_mbps": 336.77, "network_bandwidth_mbps": 653.36}, "execution_time": {"base_runtime_seconds": 318.27, "estimated_runtime_seconds": 387.73}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 164, "time_step": 48, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.71, "memory_mb": 7029.19, "io_bandwidth_mbps": 404.58, "network_bandwidth_mbps": 58.29}, "execution_time": {"base_runtime_seconds": 144.83, "estimated_runtime_seconds": 176.76}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 165, "time_step": 37, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.77, "memory_mb": 2658.72, "io_bandwidth_mbps": 173.82, "network_bandwidth_mbps": 269.96}, "execution_time": {"base_runtime_seconds": 338.33, "estimated_runtime_seconds": 399.44}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 166, "time_step": 37, "task_type": "general", "resource_demand": {"cpu_cores": 7.93, "memory_mb": 9343.5, "io_bandwidth_mbps": 349.99, "network_bandwidth_mbps": 62.84}, "execution_time": {"base_runtime_seconds": 83.98, "estimated_runtime_seconds": 95.73}, "dependencies": [99, 58], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 167, "time_step": 23, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.12, "memory_mb": 1041.07, "io_bandwidth_mbps": 391.77, "network_bandwidth_mbps": 698.35}, "execution_time": {"base_runtime_seconds": 103.59, "estimated_runtime_seconds": 122.82}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 168, "time_step": 25, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.43, "memory_mb": 3402.91, "io_bandwidth_mbps": 1913.49, "network_bandwidth_mbps": 10.25}, "execution_time": {"base_runtime_seconds": 392.64, "estimated_runtime_seconds": 504.18}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 169, "time_step": 18, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.47, "memory_mb": 4042.83, "io_bandwidth_mbps": 1135.21, "network_bandwidth_mbps": 85.77}, "execution_time": {"base_runtime_seconds": 317.23, "estimated_runtime_seconds": 405.99}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 170, "time_step": 41, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.47, "memory_mb": 3991.62, "io_bandwidth_mbps": 1709.88, "network_bandwidth_mbps": 20.45}, "execution_time": {"base_runtime_seconds": 793.83, "estimated_runtime_seconds": 973.23}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 171, "time_step": 34, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.88, "memory_mb": 1922.42, "io_bandwidth_mbps": 345.82, "network_bandwidth_mbps": 783.83}, "execution_time": {"base_runtime_seconds": 269.42, "estimated_runtime_seconds": 320.86}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 172, "time_step": 21, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.02, "memory_mb": 2762.0, "io_bandwidth_mbps": 123.84, "network_bandwidth_mbps": 51.13}, "execution_time": {"base_runtime_seconds": 169.97, "estimated_runtime_seconds": 188.88}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 173, "time_step": 3, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.98, "memory_mb": 3288.67, "io_bandwidth_mbps": 181.82, "network_bandwidth_mbps": 199.85}, "execution_time": {"base_runtime_seconds": 306.74, "estimated_runtime_seconds": 391.09}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 174, "time_step": 24, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.76, "memory_mb": 2728.48, "io_bandwidth_mbps": 166.3, "network_bandwidth_mbps": 38.02}, "execution_time": {"base_runtime_seconds": 117.76, "estimated_runtime_seconds": 135.76}, "dependencies": [12], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 175, "time_step": 48, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.04, "memory_mb": 5419.38, "io_bandwidth_mbps": 499.11, "network_bandwidth_mbps": 91.89}, "execution_time": {"base_runtime_seconds": 234.77, "estimated_runtime_seconds": 276.48}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 176, "time_step": 45, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.71, "memory_mb": 7235.44, "io_bandwidth_mbps": 123.3, "network_bandwidth_mbps": 96.71}, "execution_time": {"base_runtime_seconds": 147.55, "estimated_runtime_seconds": 169.82}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 177, "time_step": 0, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.3, "memory_mb": 3056.63, "io_bandwidth_mbps": 363.96, "network_bandwidth_mbps": 76.99}, "execution_time": {"base_runtime_seconds": 92.05, "estimated_runtime_seconds": 118.46}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 178, "time_step": 41, "task_type": "general", "resource_demand": {"cpu_cores": 7.23, "memory_mb": 2314.56, "io_bandwidth_mbps": 616.49, "network_bandwidth_mbps": 114.15}, "execution_time": {"base_runtime_seconds": 401.67, "estimated_runtime_seconds": 456.6}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 179, "time_step": 24, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.32, "memory_mb": 1138.92, "io_bandwidth_mbps": 1834.6, "network_bandwidth_mbps": 48.27}, "execution_time": {"base_runtime_seconds": 762.93, "estimated_runtime_seconds": 974.63}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 180, "time_step": 10, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.15, "memory_mb": 28240.03, "io_bandwidth_mbps": 264.88, "network_bandwidth_mbps": 35.01}, "execution_time": {"base_runtime_seconds": 473.26, "estimated_runtime_seconds": 568.61}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 181, "time_step": 8, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.31, "memory_mb": 5263.55, "io_bandwidth_mbps": 390.63, "network_bandwidth_mbps": 71.61}, "execution_time": {"base_runtime_seconds": 240.76, "estimated_runtime_seconds": 303.86}, "dependencies": [23], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 182, "time_step": 31, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 12.62, "memory_mb": 2408.26, "io_bandwidth_mbps": 462.88, "network_bandwidth_mbps": 39.75}, "execution_time": {"base_runtime_seconds": 260.19, "estimated_runtime_seconds": 313.98}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 183, "time_step": 8, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.55, "memory_mb": 1302.13, "io_bandwidth_mbps": 1657.5, "network_bandwidth_mbps": 18.88}, "execution_time": {"base_runtime_seconds": 726.91, "estimated_runtime_seconds": 836.05}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 184, "time_step": 35, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.59, "memory_mb": 2945.34, "io_bandwidth_mbps": 368.55, "network_bandwidth_mbps": 27.65}, "execution_time": {"base_runtime_seconds": 269.85, "estimated_runtime_seconds": 347.35}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 185, "time_step": 14, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.95, "memory_mb": 1734.98, "io_bandwidth_mbps": 239.89, "network_bandwidth_mbps": 844.19}, "execution_time": {"base_runtime_seconds": 126.9, "estimated_runtime_seconds": 145.17}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 186, "time_step": 23, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.11, "memory_mb": 5571.28, "io_bandwidth_mbps": 165.36, "network_bandwidth_mbps": 585.02}, "execution_time": {"base_runtime_seconds": 344.91, "estimated_runtime_seconds": 393.0}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 187, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.49, "memory_mb": 18030.74, "io_bandwidth_mbps": 178.85, "network_bandwidth_mbps": 33.45}, "execution_time": {"base_runtime_seconds": 324.72, "estimated_runtime_seconds": 387.14}, "dependencies": [28, 103, 63], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 188, "time_step": 19, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.13, "memory_mb": 10780.47, "io_bandwidth_mbps": 99.39, "network_bandwidth_mbps": 17.95}, "execution_time": {"base_runtime_seconds": 173.53, "estimated_runtime_seconds": 194.53}, "dependencies": [100, 173, 149], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 189, "time_step": 29, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.29, "memory_mb": 5471.0, "io_bandwidth_mbps": 207.84, "network_bandwidth_mbps": 209.26}, "execution_time": {"base_runtime_seconds": 427.4, "estimated_runtime_seconds": 523.69}, "dependencies": [4, 160, 47], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 190, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.91, "memory_mb": 4599.45, "io_bandwidth_mbps": 405.13, "network_bandwidth_mbps": 634.21}, "execution_time": {"base_runtime_seconds": 313.21, "estimated_runtime_seconds": 373.41}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 191, "time_step": 47, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.44, "memory_mb": 4688.3, "io_bandwidth_mbps": 331.53, "network_bandwidth_mbps": 20.27}, "execution_time": {"base_runtime_seconds": 106.75, "estimated_runtime_seconds": 126.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 192, "time_step": 21, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.19, "memory_mb": 4468.78, "io_bandwidth_mbps": 334.87, "network_bandwidth_mbps": 687.6}, "execution_time": {"base_runtime_seconds": 399.85, "estimated_runtime_seconds": 503.61}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 193, "time_step": 22, "task_type": "general", "resource_demand": {"cpu_cores": 1.81, "memory_mb": 8283.58, "io_bandwidth_mbps": 182.21, "network_bandwidth_mbps": 166.41}, "execution_time": {"base_runtime_seconds": 89.4, "estimated_runtime_seconds": 115.44}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 194, "time_step": 23, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.52, "memory_mb": 28545.69, "io_bandwidth_mbps": 174.63, "network_bandwidth_mbps": 36.62}, "execution_time": {"base_runtime_seconds": 129.23, "estimated_runtime_seconds": 158.09}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 195, "time_step": 14, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.42, "memory_mb": 5610.98, "io_bandwidth_mbps": 433.16, "network_bandwidth_mbps": 39.12}, "execution_time": {"base_runtime_seconds": 135.22, "estimated_runtime_seconds": 156.61}, "dependencies": [60, 172, 57], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 196, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.02, "memory_mb": 30161.69, "io_bandwidth_mbps": 78.57, "network_bandwidth_mbps": 40.0}, "execution_time": {"base_runtime_seconds": 436.83, "estimated_runtime_seconds": 534.39}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 197, "time_step": 45, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.85, "memory_mb": 21536.01, "io_bandwidth_mbps": 72.9, "network_bandwidth_mbps": 47.92}, "execution_time": {"base_runtime_seconds": 165.08, "estimated_runtime_seconds": 210.38}, "dependencies": [132, 56], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 198, "time_step": 16, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.91, "memory_mb": 3498.85, "io_bandwidth_mbps": 1013.2, "network_bandwidth_mbps": 11.97}, "execution_time": {"base_runtime_seconds": 297.29, "estimated_runtime_seconds": 373.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 199, "time_step": 26, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.19, "memory_mb": 5874.08, "io_bandwidth_mbps": 128.03, "network_bandwidth_mbps": 158.41}, "execution_time": {"base_runtime_seconds": 362.14, "estimated_runtime_seconds": 442.35}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 200, "time_step": 30, "task_type": "general", "resource_demand": {"cpu_cores": 3.19, "memory_mb": 5949.69, "io_bandwidth_mbps": 183.98, "network_bandwidth_mbps": 20.35}, "execution_time": {"base_runtime_seconds": 373.63, "estimated_runtime_seconds": 477.46}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 201, "time_step": 1, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.9, "memory_mb": 3377.2, "io_bandwidth_mbps": 213.15, "network_bandwidth_mbps": 404.58}, "execution_time": {"base_runtime_seconds": 441.99, "estimated_runtime_seconds": 503.59}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 202, "time_step": 7, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.76, "memory_mb": 11026.04, "io_bandwidth_mbps": 97.99, "network_bandwidth_mbps": 21.66}, "execution_time": {"base_runtime_seconds": 518.31, "estimated_runtime_seconds": 648.03}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 203, "time_step": 43, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.88, "memory_mb": 2842.69, "io_bandwidth_mbps": 1968.54, "network_bandwidth_mbps": 35.66}, "execution_time": {"base_runtime_seconds": 674.06, "estimated_runtime_seconds": 803.31}, "dependencies": [47, 157], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 204, "time_step": 1, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.8, "memory_mb": 7565.08, "io_bandwidth_mbps": 161.51, "network_bandwidth_mbps": 873.96}, "execution_time": {"base_runtime_seconds": 372.61, "estimated_runtime_seconds": 471.96}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 205, "time_step": 4, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.8, "memory_mb": 1811.88, "io_bandwidth_mbps": 1555.39, "network_bandwidth_mbps": 71.29}, "execution_time": {"base_runtime_seconds": 613.89, "estimated_runtime_seconds": 679.03}, "dependencies": [176, 119, 83], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 206, "time_step": 8, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.12, "memory_mb": 13046.86, "io_bandwidth_mbps": 55.01, "network_bandwidth_mbps": 30.89}, "execution_time": {"base_runtime_seconds": 236.78, "estimated_runtime_seconds": 290.79}, "dependencies": [203], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 207, "time_step": 26, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.53, "memory_mb": 2943.66, "io_bandwidth_mbps": 761.75, "network_bandwidth_mbps": 32.55}, "execution_time": {"base_runtime_seconds": 262.23, "estimated_runtime_seconds": 320.47}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 208, "time_step": 23, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.23, "memory_mb": 32328.46, "io_bandwidth_mbps": 227.64, "network_bandwidth_mbps": 35.65}, "execution_time": {"base_runtime_seconds": 468.08, "estimated_runtime_seconds": 530.77}, "dependencies": [189, 110], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 209, "time_step": 27, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.52, "memory_mb": 3091.44, "io_bandwidth_mbps": 1681.51, "network_bandwidth_mbps": 58.86}, "execution_time": {"base_runtime_seconds": 259.47, "estimated_runtime_seconds": 324.17}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 210, "time_step": 46, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.67, "memory_mb": 2153.98, "io_bandwidth_mbps": 1123.79, "network_bandwidth_mbps": 74.38}, "execution_time": {"base_runtime_seconds": 735.49, "estimated_runtime_seconds": 826.67}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 211, "time_step": 7, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.19, "memory_mb": 2086.97, "io_bandwidth_mbps": 123.77, "network_bandwidth_mbps": 66.57}, "execution_time": {"base_runtime_seconds": 256.13, "estimated_runtime_seconds": 308.77}, "dependencies": [154, 102, 13], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 212, "time_step": 28, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.46, "memory_mb": 6504.54, "io_bandwidth_mbps": 452.31, "network_bandwidth_mbps": 865.47}, "execution_time": {"base_runtime_seconds": 165.07, "estimated_runtime_seconds": 198.82}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 213, "time_step": 37, "task_type": "general", "resource_demand": {"cpu_cores": 6.26, "memory_mb": 15863.03, "io_bandwidth_mbps": 634.79, "network_bandwidth_mbps": 168.57}, "execution_time": {"base_runtime_seconds": 117.73, "estimated_runtime_seconds": 132.2}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 214, "time_step": 42, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.15, "memory_mb": 4878.68, "io_bandwidth_mbps": 255.52, "network_bandwidth_mbps": 339.72}, "execution_time": {"base_runtime_seconds": 374.88, "estimated_runtime_seconds": 447.14}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 215, "time_step": 14, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.9, "memory_mb": 17092.25, "io_bandwidth_mbps": 51.6, "network_bandwidth_mbps": 39.63}, "execution_time": {"base_runtime_seconds": 549.74, "estimated_runtime_seconds": 657.17}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 216, "time_step": 22, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.61, "memory_mb": 28780.45, "io_bandwidth_mbps": 270.24, "network_bandwidth_mbps": 19.8}, "execution_time": {"base_runtime_seconds": 560.88, "estimated_runtime_seconds": 652.56}, "dependencies": [111], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 217, "time_step": 7, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.73, "memory_mb": 28049.92, "io_bandwidth_mbps": 294.62, "network_bandwidth_mbps": 32.93}, "execution_time": {"base_runtime_seconds": 594.07, "estimated_runtime_seconds": 657.7}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 218, "time_step": 39, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 8.91, "memory_mb": 3903.85, "io_bandwidth_mbps": 256.82, "network_bandwidth_mbps": 52.07}, "execution_time": {"base_runtime_seconds": 141.44, "estimated_runtime_seconds": 159.99}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 219, "time_step": 21, "task_type": "general", "resource_demand": {"cpu_cores": 7.75, "memory_mb": 12432.85, "io_bandwidth_mbps": 566.34, "network_bandwidth_mbps": 170.45}, "execution_time": {"base_runtime_seconds": 370.79, "estimated_runtime_seconds": 475.03}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 220, "time_step": 17, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.72, "memory_mb": 4063.89, "io_bandwidth_mbps": 1928.74, "network_bandwidth_mbps": 36.82}, "execution_time": {"base_runtime_seconds": 430.52, "estimated_runtime_seconds": 548.56}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 221, "time_step": 3, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.79, "memory_mb": 3130.62, "io_bandwidth_mbps": 1413.05, "network_bandwidth_mbps": 46.73}, "execution_time": {"base_runtime_seconds": 813.51, "estimated_runtime_seconds": 959.15}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 222, "time_step": 2, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.91, "memory_mb": 7755.73, "io_bandwidth_mbps": 423.65, "network_bandwidth_mbps": 656.47}, "execution_time": {"base_runtime_seconds": 166.03, "estimated_runtime_seconds": 192.2}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 223, "time_step": 14, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.25, "memory_mb": 3156.4, "io_bandwidth_mbps": 324.72, "network_bandwidth_mbps": 666.17}, "execution_time": {"base_runtime_seconds": 188.27, "estimated_runtime_seconds": 243.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 224, "time_step": 15, "task_type": "general", "resource_demand": {"cpu_cores": 1.67, "memory_mb": 5362.85, "io_bandwidth_mbps": 178.93, "network_bandwidth_mbps": 116.98}, "execution_time": {"base_runtime_seconds": 186.01, "estimated_runtime_seconds": 235.55}, "dependencies": [155], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 225, "time_step": 23, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.79, "memory_mb": 2699.19, "io_bandwidth_mbps": 506.25, "network_bandwidth_mbps": 75.25}, "execution_time": {"base_runtime_seconds": 191.71, "estimated_runtime_seconds": 235.38}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 226, "time_step": 36, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.73, "memory_mb": 2811.75, "io_bandwidth_mbps": 1790.82, "network_bandwidth_mbps": 43.58}, "execution_time": {"base_runtime_seconds": 250.83, "estimated_runtime_seconds": 300.93}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 227, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.38, "memory_mb": 1573.92, "io_bandwidth_mbps": 137.03, "network_bandwidth_mbps": 413.82}, "execution_time": {"base_runtime_seconds": 399.13, "estimated_runtime_seconds": 488.19}, "dependencies": [31, 155, 133], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 228, "time_step": 46, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.34, "memory_mb": 4007.92, "io_bandwidth_mbps": 1405.66, "network_bandwidth_mbps": 53.66}, "execution_time": {"base_runtime_seconds": 537.31, "estimated_runtime_seconds": 693.31}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 229, "time_step": 44, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.15, "memory_mb": 21931.0, "io_bandwidth_mbps": 241.95, "network_bandwidth_mbps": 41.65}, "execution_time": {"base_runtime_seconds": 330.97, "estimated_runtime_seconds": 395.88}, "dependencies": [133, 49], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 230, "time_step": 11, "task_type": "general", "resource_demand": {"cpu_cores": 5.55, "memory_mb": 7412.36, "io_bandwidth_mbps": 781.67, "network_bandwidth_mbps": 162.97}, "execution_time": {"base_runtime_seconds": 219.61, "estimated_runtime_seconds": 263.28}, "dependencies": [70, 129], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 231, "time_step": 30, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.84, "memory_mb": 22623.61, "io_bandwidth_mbps": 186.51, "network_bandwidth_mbps": 24.3}, "execution_time": {"base_runtime_seconds": 456.54, "estimated_runtime_seconds": 508.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 232, "time_step": 10, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.6, "memory_mb": 2392.18, "io_bandwidth_mbps": 595.03, "network_bandwidth_mbps": 45.02}, "execution_time": {"base_runtime_seconds": 212.58, "estimated_runtime_seconds": 250.42}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 233, "time_step": 46, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.95, "memory_mb": 7338.86, "io_bandwidth_mbps": 225.38, "network_bandwidth_mbps": 29.94}, "execution_time": {"base_runtime_seconds": 215.73, "estimated_runtime_seconds": 245.3}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 234, "time_step": 17, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.6, "memory_mb": 7098.54, "io_bandwidth_mbps": 343.97, "network_bandwidth_mbps": 385.75}, "execution_time": {"base_runtime_seconds": 396.49, "estimated_runtime_seconds": 500.66}, "dependencies": [169, 223, 19], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 235, "time_step": 2, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.83, "memory_mb": 13602.13, "io_bandwidth_mbps": 268.45, "network_bandwidth_mbps": 36.57}, "execution_time": {"base_runtime_seconds": 313.69, "estimated_runtime_seconds": 368.44}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 236, "time_step": 20, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.3, "memory_mb": 1038.41, "io_bandwidth_mbps": 1279.58, "network_bandwidth_mbps": 53.81}, "execution_time": {"base_runtime_seconds": 399.38, "estimated_runtime_seconds": 506.21}, "dependencies": [207], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 237, "time_step": 34, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.67, "memory_mb": 16352.94, "io_bandwidth_mbps": 225.25, "network_bandwidth_mbps": 45.65}, "execution_time": {"base_runtime_seconds": 427.57, "estimated_runtime_seconds": 541.89}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 238, "time_step": 29, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.65, "memory_mb": 29742.74, "io_bandwidth_mbps": 235.68, "network_bandwidth_mbps": 39.55}, "execution_time": {"base_runtime_seconds": 445.05, "estimated_runtime_seconds": 566.75}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 239, "time_step": 27, "task_type": "general", "resource_demand": {"cpu_cores": 6.14, "memory_mb": 15843.52, "io_bandwidth_mbps": 601.46, "network_bandwidth_mbps": 156.28}, "execution_time": {"base_runtime_seconds": 321.53, "estimated_runtime_seconds": 354.1}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 240, "time_step": 44, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.15, "memory_mb": 6125.77, "io_bandwidth_mbps": 473.32, "network_bandwidth_mbps": 38.14}, "execution_time": {"base_runtime_seconds": 89.47, "estimated_runtime_seconds": 100.81}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 241, "time_step": 30, "task_type": "general", "resource_demand": {"cpu_cores": 1.57, "memory_mb": 12085.21, "io_bandwidth_mbps": 713.63, "network_bandwidth_mbps": 154.05}, "execution_time": {"base_runtime_seconds": 310.04, "estimated_runtime_seconds": 399.54}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 242, "time_step": 50, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.19, "memory_mb": 3946.97, "io_bandwidth_mbps": 368.21, "network_bandwidth_mbps": 328.35}, "execution_time": {"base_runtime_seconds": 369.08, "estimated_runtime_seconds": 475.22}, "dependencies": [136, 123], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 243, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.52, "memory_mb": 6072.19, "io_bandwidth_mbps": 297.67, "network_bandwidth_mbps": 58.96}, "execution_time": {"base_runtime_seconds": 93.11, "estimated_runtime_seconds": 104.92}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 244, "time_step": 24, "task_type": "general", "resource_demand": {"cpu_cores": 4.42, "memory_mb": 16378.03, "io_bandwidth_mbps": 436.86, "network_bandwidth_mbps": 174.51}, "execution_time": {"base_runtime_seconds": 439.94, "estimated_runtime_seconds": 506.36}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 245, "time_step": 41, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.19, "memory_mb": 3111.48, "io_bandwidth_mbps": 553.47, "network_bandwidth_mbps": 68.2}, "execution_time": {"base_runtime_seconds": 646.74, "estimated_runtime_seconds": 791.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 246, "time_step": 36, "task_type": "general", "resource_demand": {"cpu_cores": 3.48, "memory_mb": 9587.54, "io_bandwidth_mbps": 228.03, "network_bandwidth_mbps": 150.0}, "execution_time": {"base_runtime_seconds": 314.17, "estimated_runtime_seconds": 351.16}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 247, "time_step": 6, "task_type": "general", "resource_demand": {"cpu_cores": 7.94, "memory_mb": 2755.39, "io_bandwidth_mbps": 649.77, "network_bandwidth_mbps": 74.32}, "execution_time": {"base_runtime_seconds": 369.4, "estimated_runtime_seconds": 446.72}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 248, "time_step": 0, "task_type": "general", "resource_demand": {"cpu_cores": 3.21, "memory_mb": 12501.26, "io_bandwidth_mbps": 130.35, "network_bandwidth_mbps": 192.03}, "execution_time": {"base_runtime_seconds": 435.91, "estimated_runtime_seconds": 529.74}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 249, "time_step": 17, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.08, "memory_mb": 14267.0, "io_bandwidth_mbps": 126.85, "network_bandwidth_mbps": 49.46}, "execution_time": {"base_runtime_seconds": 328.2, "estimated_runtime_seconds": 368.1}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 250, "time_step": 1, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.35, "memory_mb": 5718.89, "io_bandwidth_mbps": 333.91, "network_bandwidth_mbps": 928.02}, "execution_time": {"base_runtime_seconds": 386.83, "estimated_runtime_seconds": 489.17}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 251, "time_step": 49, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.94, "memory_mb": 6947.81, "io_bandwidth_mbps": 495.2, "network_bandwidth_mbps": 69.6}, "execution_time": {"base_runtime_seconds": 196.8, "estimated_runtime_seconds": 217.4}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 252, "time_step": 18, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.86, "memory_mb": 6687.08, "io_bandwidth_mbps": 430.6, "network_bandwidth_mbps": 65.95}, "execution_time": {"base_runtime_seconds": 274.56, "estimated_runtime_seconds": 313.96}, "dependencies": [41], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 253, "time_step": 4, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.58, "memory_mb": 7486.92, "io_bandwidth_mbps": 465.78, "network_bandwidth_mbps": 196.57}, "execution_time": {"base_runtime_seconds": 118.83, "estimated_runtime_seconds": 135.63}, "dependencies": [226, 238, 29], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 254, "time_step": 18, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.09, "memory_mb": 11164.87, "io_bandwidth_mbps": 159.0, "network_bandwidth_mbps": 23.53}, "execution_time": {"base_runtime_seconds": 512.91, "estimated_runtime_seconds": 654.41}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 255, "time_step": 16, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.93, "memory_mb": 2835.1, "io_bandwidth_mbps": 297.17, "network_bandwidth_mbps": 185.03}, "execution_time": {"base_runtime_seconds": 121.02, "estimated_runtime_seconds": 145.68}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 256, "time_step": 37, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.41, "memory_mb": 2951.95, "io_bandwidth_mbps": 878.88, "network_bandwidth_mbps": 15.06}, "execution_time": {"base_runtime_seconds": 877.7, "estimated_runtime_seconds": 1097.94}, "dependencies": [237, 211, 27], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 257, "time_step": 11, "task_type": "general", "resource_demand": {"cpu_cores": 2.01, "memory_mb": 15943.41, "io_bandwidth_mbps": 475.61, "network_bandwidth_mbps": 20.65}, "execution_time": {"base_runtime_seconds": 154.52, "estimated_runtime_seconds": 190.22}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 258, "time_step": 34, "task_type": "general", "resource_demand": {"cpu_cores": 7.56, "memory_mb": 2240.34, "io_bandwidth_mbps": 475.6, "network_bandwidth_mbps": 75.24}, "execution_time": {"base_runtime_seconds": 77.69, "estimated_runtime_seconds": 94.57}, "dependencies": [245, 93], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 259, "time_step": 6, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 8.86, "memory_mb": 2885.74, "io_bandwidth_mbps": 458.95, "network_bandwidth_mbps": 20.81}, "execution_time": {"base_runtime_seconds": 198.48, "estimated_runtime_seconds": 256.69}, "dependencies": [145, 144, 217], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 260, "time_step": 10, "task_type": "general", "resource_demand": {"cpu_cores": 6.57, "memory_mb": 4221.4, "io_bandwidth_mbps": 548.19, "network_bandwidth_mbps": 197.0}, "execution_time": {"base_runtime_seconds": 269.58, "estimated_runtime_seconds": 350.09}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 261, "time_step": 9, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.16, "memory_mb": 17113.19, "io_bandwidth_mbps": 116.4, "network_bandwidth_mbps": 38.68}, "execution_time": {"base_runtime_seconds": 456.66, "estimated_runtime_seconds": 549.75}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 262, "time_step": 11, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.7, "memory_mb": 17944.95, "io_bandwidth_mbps": 278.68, "network_bandwidth_mbps": 48.02}, "execution_time": {"base_runtime_seconds": 594.43, "estimated_runtime_seconds": 669.64}, "dependencies": [91], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 263, "time_step": 27, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.03, "memory_mb": 7391.72, "io_bandwidth_mbps": 304.8, "network_bandwidth_mbps": 76.79}, "execution_time": {"base_runtime_seconds": 190.3, "estimated_runtime_seconds": 220.09}, "dependencies": [180, 43, 191], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 264, "time_step": 0, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.36, "memory_mb": 5139.16, "io_bandwidth_mbps": 249.76, "network_bandwidth_mbps": 15.0}, "execution_time": {"base_runtime_seconds": 293.19, "estimated_runtime_seconds": 369.14}, "dependencies": [168], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 265, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.16, "memory_mb": 12090.95, "io_bandwidth_mbps": 223.76, "network_bandwidth_mbps": 41.48}, "execution_time": {"base_runtime_seconds": 340.6, "estimated_runtime_seconds": 414.62}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 266, "time_step": 34, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.25, "memory_mb": 2601.21, "io_bandwidth_mbps": 1868.98, "network_bandwidth_mbps": 30.8}, "execution_time": {"base_runtime_seconds": 554.02, "estimated_runtime_seconds": 693.96}, "dependencies": [182, 57], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 267, "time_step": 43, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.06, "memory_mb": 28697.1, "io_bandwidth_mbps": 294.13, "network_bandwidth_mbps": 10.45}, "execution_time": {"base_runtime_seconds": 523.97, "estimated_runtime_seconds": 644.77}, "dependencies": [172, 131, 149], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 268, "time_step": 28, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.67, "memory_mb": 6566.3, "io_bandwidth_mbps": 391.55, "network_bandwidth_mbps": 630.63}, "execution_time": {"base_runtime_seconds": 444.81, "estimated_runtime_seconds": 556.64}, "dependencies": [130, 260, 59], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 269, "time_step": 43, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.4, "memory_mb": 4258.07, "io_bandwidth_mbps": 116.41, "network_bandwidth_mbps": 606.59}, "execution_time": {"base_runtime_seconds": 389.64, "estimated_runtime_seconds": 481.49}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 270, "time_step": 18, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.67, "memory_mb": 31503.43, "io_bandwidth_mbps": 251.5, "network_bandwidth_mbps": 32.29}, "execution_time": {"base_runtime_seconds": 433.59, "estimated_runtime_seconds": 490.35}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 271, "time_step": 9, "task_type": "general", "resource_demand": {"cpu_cores": 3.86, "memory_mb": 2377.26, "io_bandwidth_mbps": 693.29, "network_bandwidth_mbps": 100.95}, "execution_time": {"base_runtime_seconds": 370.76, "estimated_runtime_seconds": 481.62}, "dependencies": [146], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 272, "time_step": 32, "task_type": "general", "resource_demand": {"cpu_cores": 4.72, "memory_mb": 5568.69, "io_bandwidth_mbps": 578.04, "network_bandwidth_mbps": 182.26}, "execution_time": {"base_runtime_seconds": 423.84, "estimated_runtime_seconds": 482.93}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 273, "time_step": 28, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.93, "memory_mb": 1867.36, "io_bandwidth_mbps": 725.5, "network_bandwidth_mbps": 78.12}, "execution_time": {"base_runtime_seconds": 810.23, "estimated_runtime_seconds": 1050.96}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 274, "time_step": 12, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.43, "memory_mb": 2424.01, "io_bandwidth_mbps": 259.75, "network_bandwidth_mbps": 92.47}, "execution_time": {"base_runtime_seconds": 132.69, "estimated_runtime_seconds": 151.77}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 275, "time_step": 16, "task_type": "general", "resource_demand": {"cpu_cores": 4.29, "memory_mb": 9480.6, "io_bandwidth_mbps": 665.57, "network_bandwidth_mbps": 152.68}, "execution_time": {"base_runtime_seconds": 353.11, "estimated_runtime_seconds": 454.0}, "dependencies": [98, 138], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 276, "time_step": 39, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.78, "memory_mb": 3753.99, "io_bandwidth_mbps": 1359.77, "network_bandwidth_mbps": 67.66}, "execution_time": {"base_runtime_seconds": 528.9, "estimated_runtime_seconds": 596.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 277, "time_step": 7, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.07, "memory_mb": 4725.29, "io_bandwidth_mbps": 131.73, "network_bandwidth_mbps": 70.6}, "execution_time": {"base_runtime_seconds": 270.79, "estimated_runtime_seconds": 319.99}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 278, "time_step": 13, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.24, "memory_mb": 6345.57, "io_bandwidth_mbps": 102.56, "network_bandwidth_mbps": 63.17}, "execution_time": {"base_runtime_seconds": 154.16, "estimated_runtime_seconds": 178.46}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 279, "time_step": 34, "task_type": "general", "resource_demand": {"cpu_cores": 6.03, "memory_mb": 12196.92, "io_bandwidth_mbps": 557.21, "network_bandwidth_mbps": 109.8}, "execution_time": {"base_runtime_seconds": 374.42, "estimated_runtime_seconds": 444.7}, "dependencies": [127, 179], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 280, "time_step": 29, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.92, "memory_mb": 4430.03, "io_bandwidth_mbps": 285.46, "network_bandwidth_mbps": 364.17}, "execution_time": {"base_runtime_seconds": 413.57, "estimated_runtime_seconds": 488.38}, "dependencies": [183, 10], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 281, "time_step": 17, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.7, "memory_mb": 2128.32, "io_bandwidth_mbps": 397.49, "network_bandwidth_mbps": 42.4}, "execution_time": {"base_runtime_seconds": 63.97, "estimated_runtime_seconds": 82.37}, "dependencies": [129, 52, 235], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 282, "time_step": 14, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 12.2, "memory_mb": 6352.82, "io_bandwidth_mbps": 270.37, "network_bandwidth_mbps": 66.88}, "execution_time": {"base_runtime_seconds": 266.04, "estimated_runtime_seconds": 312.7}, "dependencies": [113], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 283, "time_step": 18, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.77, "memory_mb": 1221.94, "io_bandwidth_mbps": 468.03, "network_bandwidth_mbps": 973.98}, "execution_time": {"base_runtime_seconds": 92.97, "estimated_runtime_seconds": 120.08}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 284, "time_step": 25, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.1, "memory_mb": 9249.74, "io_bandwidth_mbps": 240.12, "network_bandwidth_mbps": 47.42}, "execution_time": {"base_runtime_seconds": 126.06, "estimated_runtime_seconds": 154.13}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 285, "time_step": 8, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.02, "memory_mb": 2079.3, "io_bandwidth_mbps": 633.86, "network_bandwidth_mbps": 35.1}, "execution_time": {"base_runtime_seconds": 784.16, "estimated_runtime_seconds": 922.94}, "dependencies": [36, 73], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 286, "time_step": 1, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.84, "memory_mb": 3208.97, "io_bandwidth_mbps": 631.26, "network_bandwidth_mbps": 97.67}, "execution_time": {"base_runtime_seconds": 256.84, "estimated_runtime_seconds": 312.5}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 287, "time_step": 10, "task_type": "general", "resource_demand": {"cpu_cores": 2.36, "memory_mb": 14100.0, "io_bandwidth_mbps": 544.36, "network_bandwidth_mbps": 157.88}, "execution_time": {"base_runtime_seconds": 100.63, "estimated_runtime_seconds": 110.93}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 288, "time_step": 20, "task_type": "general", "resource_demand": {"cpu_cores": 2.9, "memory_mb": 11510.6, "io_bandwidth_mbps": 560.38, "network_bandwidth_mbps": 133.12}, "execution_time": {"base_runtime_seconds": 304.08, "estimated_runtime_seconds": 373.75}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 289, "time_step": 34, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.3, "memory_mb": 7463.01, "io_bandwidth_mbps": 423.24, "network_bandwidth_mbps": 81.96}, "execution_time": {"base_runtime_seconds": 168.85, "estimated_runtime_seconds": 202.01}, "dependencies": [247, 141], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 290, "time_step": 2, "task_type": "general", "resource_demand": {"cpu_cores": 7.52, "memory_mb": 13380.7, "io_bandwidth_mbps": 709.04, "network_bandwidth_mbps": 152.56}, "execution_time": {"base_runtime_seconds": 294.63, "estimated_runtime_seconds": 347.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 291, "time_step": 8, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.99, "memory_mb": 7541.68, "io_bandwidth_mbps": 418.55, "network_bandwidth_mbps": 86.13}, "execution_time": {"base_runtime_seconds": 135.94, "estimated_runtime_seconds": 175.36}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 292, "time_step": 12, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.1, "memory_mb": 6987.28, "io_bandwidth_mbps": 382.56, "network_bandwidth_mbps": 980.23}, "execution_time": {"base_runtime_seconds": 209.84, "estimated_runtime_seconds": 264.63}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 293, "time_step": 24, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.08, "memory_mb": 6812.84, "io_bandwidth_mbps": 186.42, "network_bandwidth_mbps": 621.54}, "execution_time": {"base_runtime_seconds": 406.95, "estimated_runtime_seconds": 458.83}, "dependencies": [201, 34], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 294, "time_step": 35, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.59, "memory_mb": 4251.42, "io_bandwidth_mbps": 270.7, "network_bandwidth_mbps": 33.64}, "execution_time": {"base_runtime_seconds": 77.68, "estimated_runtime_seconds": 86.72}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 295, "time_step": 11, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.08, "memory_mb": 24947.08, "io_bandwidth_mbps": 285.88, "network_bandwidth_mbps": 21.21}, "execution_time": {"base_runtime_seconds": 283.51, "estimated_runtime_seconds": 342.16}, "dependencies": [146], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 296, "time_step": 41, "task_type": "general", "resource_demand": {"cpu_cores": 3.65, "memory_mb": 15255.97, "io_bandwidth_mbps": 430.23, "network_bandwidth_mbps": 176.99}, "execution_time": {"base_runtime_seconds": 166.75, "estimated_runtime_seconds": 205.28}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 297, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 3.88, "memory_mb": 10043.17, "io_bandwidth_mbps": 754.94, "network_bandwidth_mbps": 24.46}, "execution_time": {"base_runtime_seconds": 455.75, "estimated_runtime_seconds": 535.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 298, "time_step": 11, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.38, "memory_mb": 1760.21, "io_bandwidth_mbps": 1234.4, "network_bandwidth_mbps": 67.83}, "execution_time": {"base_runtime_seconds": 591.9, "estimated_runtime_seconds": 740.05}, "dependencies": [34, 153, 86], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 299, "time_step": 29, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.78, "memory_mb": 6585.89, "io_bandwidth_mbps": 416.61, "network_bandwidth_mbps": 54.39}, "execution_time": {"base_runtime_seconds": 273.54, "estimated_runtime_seconds": 336.28}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 300, "time_step": 10, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.43, "memory_mb": 2241.66, "io_bandwidth_mbps": 243.87, "network_bandwidth_mbps": 255.3}, "execution_time": {"base_runtime_seconds": 168.89, "estimated_runtime_seconds": 202.14}, "dependencies": [216, 50], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 301, "time_step": 7, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.09, "memory_mb": 4022.36, "io_bandwidth_mbps": 1589.71, "network_bandwidth_mbps": 58.89}, "execution_time": {"base_runtime_seconds": 435.44, "estimated_runtime_seconds": 530.46}, "dependencies": [173, 238, 124], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 302, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.43, "memory_mb": 8080.35, "io_bandwidth_mbps": 265.59, "network_bandwidth_mbps": 327.7}, "execution_time": {"base_runtime_seconds": 424.78, "estimated_runtime_seconds": 504.5}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 303, "time_step": 9, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.55, "memory_mb": 3527.6, "io_bandwidth_mbps": 1170.35, "network_bandwidth_mbps": 23.13}, "execution_time": {"base_runtime_seconds": 464.06, "estimated_runtime_seconds": 601.98}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 304, "time_step": 13, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.02, "memory_mb": 1374.38, "io_bandwidth_mbps": 202.57, "network_bandwidth_mbps": 586.12}, "execution_time": {"base_runtime_seconds": 409.08, "estimated_runtime_seconds": 528.39}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 305, "time_step": 39, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.15, "memory_mb": 7602.02, "io_bandwidth_mbps": 456.29, "network_bandwidth_mbps": 84.69}, "execution_time": {"base_runtime_seconds": 289.7, "estimated_runtime_seconds": 341.0}, "dependencies": [89, 298], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 306, "time_step": 11, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.31, "memory_mb": 2347.55, "io_bandwidth_mbps": 116.9, "network_bandwidth_mbps": 300.42}, "execution_time": {"base_runtime_seconds": 374.85, "estimated_runtime_seconds": 444.98}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 307, "time_step": 37, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.73, "memory_mb": 2858.52, "io_bandwidth_mbps": 421.61, "network_bandwidth_mbps": 417.31}, "execution_time": {"base_runtime_seconds": 176.25, "estimated_runtime_seconds": 225.57}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 308, "time_step": 9, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.06, "memory_mb": 2624.37, "io_bandwidth_mbps": 1893.14, "network_bandwidth_mbps": 37.09}, "execution_time": {"base_runtime_seconds": 558.98, "estimated_runtime_seconds": 674.92}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 309, "time_step": 44, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.27, "memory_mb": 15880.3, "io_bandwidth_mbps": 218.4, "network_bandwidth_mbps": 43.68}, "execution_time": {"base_runtime_seconds": 517.91, "estimated_runtime_seconds": 671.76}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 310, "time_step": 40, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.88, "memory_mb": 1742.56, "io_bandwidth_mbps": 986.69, "network_bandwidth_mbps": 57.33}, "execution_time": {"base_runtime_seconds": 649.3, "estimated_runtime_seconds": 829.89}, "dependencies": [158, 101, 126], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 311, "time_step": 24, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.98, "memory_mb": 7162.67, "io_bandwidth_mbps": 485.11, "network_bandwidth_mbps": 96.71}, "execution_time": {"base_runtime_seconds": 140.39, "estimated_runtime_seconds": 160.22}, "dependencies": [281, 212, 309], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 312, "time_step": 38, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.52, "memory_mb": 6938.25, "io_bandwidth_mbps": 371.01, "network_bandwidth_mbps": 38.49}, "execution_time": {"base_runtime_seconds": 266.69, "estimated_runtime_seconds": 317.84}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 313, "time_step": 11, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.51, "memory_mb": 2274.21, "io_bandwidth_mbps": 1311.58, "network_bandwidth_mbps": 35.59}, "execution_time": {"base_runtime_seconds": 183.39, "estimated_runtime_seconds": 207.44}, "dependencies": [65, 241], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 314, "time_step": 47, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.95, "memory_mb": 7136.59, "io_bandwidth_mbps": 156.14, "network_bandwidth_mbps": 40.06}, "execution_time": {"base_runtime_seconds": 153.75, "estimated_runtime_seconds": 192.31}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 315, "time_step": 17, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.48, "memory_mb": 6908.07, "io_bandwidth_mbps": 275.5, "network_bandwidth_mbps": 258.8}, "execution_time": {"base_runtime_seconds": 242.61, "estimated_runtime_seconds": 304.46}, "dependencies": [189, 29], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 316, "time_step": 44, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.74, "memory_mb": 3407.14, "io_bandwidth_mbps": 216.95, "network_bandwidth_mbps": 635.95}, "execution_time": {"base_runtime_seconds": 303.59, "estimated_runtime_seconds": 380.47}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 317, "time_step": 34, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.8, "memory_mb": 1643.98, "io_bandwidth_mbps": 337.71, "network_bandwidth_mbps": 770.14}, "execution_time": {"base_runtime_seconds": 374.81, "estimated_runtime_seconds": 454.8}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 318, "time_step": 10, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.72, "memory_mb": 4200.89, "io_bandwidth_mbps": 374.52, "network_bandwidth_mbps": 299.39}, "execution_time": {"base_runtime_seconds": 431.55, "estimated_runtime_seconds": 517.12}, "dependencies": [2, 121, 202], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 319, "time_step": 14, "task_type": "general", "resource_demand": {"cpu_cores": 1.23, "memory_mb": 9797.23, "io_bandwidth_mbps": 615.31, "network_bandwidth_mbps": 135.67}, "execution_time": {"base_runtime_seconds": 332.55, "estimated_runtime_seconds": 370.53}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 320, "time_step": 18, "task_type": "general", "resource_demand": {"cpu_cores": 5.67, "memory_mb": 8192.73, "io_bandwidth_mbps": 631.25, "network_bandwidth_mbps": 145.25}, "execution_time": {"base_runtime_seconds": 84.82, "estimated_runtime_seconds": 102.91}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 321, "time_step": 22, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.86, "memory_mb": 27062.74, "io_bandwidth_mbps": 120.91, "network_bandwidth_mbps": 23.17}, "execution_time": {"base_runtime_seconds": 215.41, "estimated_runtime_seconds": 273.13}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 322, "time_step": 42, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.06, "memory_mb": 6666.21, "io_bandwidth_mbps": 121.3, "network_bandwidth_mbps": 357.92}, "execution_time": {"base_runtime_seconds": 440.34, "estimated_runtime_seconds": 525.41}, "dependencies": [267], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 323, "time_step": 48, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.11, "memory_mb": 6043.33, "io_bandwidth_mbps": 477.52, "network_bandwidth_mbps": 18.59}, "execution_time": {"base_runtime_seconds": 72.1, "estimated_runtime_seconds": 92.24}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 324, "time_step": 42, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.51, "memory_mb": 14581.75, "io_bandwidth_mbps": 96.93, "network_bandwidth_mbps": 29.27}, "execution_time": {"base_runtime_seconds": 378.45, "estimated_runtime_seconds": 437.06}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 325, "time_step": 35, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.96, "memory_mb": 7984.98, "io_bandwidth_mbps": 279.2, "network_bandwidth_mbps": 77.44}, "execution_time": {"base_runtime_seconds": 195.31, "estimated_runtime_seconds": 217.89}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 326, "time_step": 37, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.82, "memory_mb": 5464.8, "io_bandwidth_mbps": 498.02, "network_bandwidth_mbps": 28.61}, "execution_time": {"base_runtime_seconds": 185.02, "estimated_runtime_seconds": 232.5}, "dependencies": [262, 231, 53], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 327, "time_step": 5, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.35, "memory_mb": 28619.87, "io_bandwidth_mbps": 151.77, "network_bandwidth_mbps": 31.14}, "execution_time": {"base_runtime_seconds": 590.98, "estimated_runtime_seconds": 707.57}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 328, "time_step": 0, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.87, "memory_mb": 6679.06, "io_bandwidth_mbps": 142.83, "network_bandwidth_mbps": 27.0}, "execution_time": {"base_runtime_seconds": 168.23, "estimated_runtime_seconds": 207.87}, "dependencies": [327], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 329, "time_step": 43, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.16, "memory_mb": 1494.18, "io_bandwidth_mbps": 120.38, "network_bandwidth_mbps": 442.7}, "execution_time": {"base_runtime_seconds": 319.56, "estimated_runtime_seconds": 360.34}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 330, "time_step": 29, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.97, "memory_mb": 6070.99, "io_bandwidth_mbps": 122.08, "network_bandwidth_mbps": 21.62}, "execution_time": {"base_runtime_seconds": 254.12, "estimated_runtime_seconds": 300.5}, "dependencies": [65, 290, 238], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 331, "time_step": 16, "task_type": "general", "resource_demand": {"cpu_cores": 5.3, "memory_mb": 3522.94, "io_bandwidth_mbps": 489.32, "network_bandwidth_mbps": 58.89}, "execution_time": {"base_runtime_seconds": 291.65, "estimated_runtime_seconds": 370.2}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 332, "time_step": 17, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.73, "memory_mb": 30234.9, "io_bandwidth_mbps": 267.32, "network_bandwidth_mbps": 48.5}, "execution_time": {"base_runtime_seconds": 418.25, "estimated_runtime_seconds": 535.22}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 333, "time_step": 7, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.58, "memory_mb": 26062.8, "io_bandwidth_mbps": 296.11, "network_bandwidth_mbps": 23.88}, "execution_time": {"base_runtime_seconds": 512.35, "estimated_runtime_seconds": 644.8}, "dependencies": [246, 174], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 334, "time_step": 7, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.29, "memory_mb": 2354.75, "io_bandwidth_mbps": 285.39, "network_bandwidth_mbps": 29.56}, "execution_time": {"base_runtime_seconds": 98.12, "estimated_runtime_seconds": 118.52}, "dependencies": [120], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 335, "time_step": 13, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.49, "memory_mb": 2605.63, "io_bandwidth_mbps": 1964.51, "network_bandwidth_mbps": 32.26}, "execution_time": {"base_runtime_seconds": 436.76, "estimated_runtime_seconds": 561.88}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 336, "time_step": 1, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.56, "memory_mb": 3374.1, "io_bandwidth_mbps": 457.48, "network_bandwidth_mbps": 258.57}, "execution_time": {"base_runtime_seconds": 278.39, "estimated_runtime_seconds": 320.78}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 337, "time_step": 49, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.95, "memory_mb": 21107.58, "io_bandwidth_mbps": 194.59, "network_bandwidth_mbps": 37.39}, "execution_time": {"base_runtime_seconds": 168.01, "estimated_runtime_seconds": 212.56}, "dependencies": [329, 60], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 338, "time_step": 16, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.13, "memory_mb": 2490.37, "io_bandwidth_mbps": 333.74, "network_bandwidth_mbps": 15.53}, "execution_time": {"base_runtime_seconds": 133.16, "estimated_runtime_seconds": 166.63}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 339, "time_step": 22, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.96, "memory_mb": 2039.72, "io_bandwidth_mbps": 1986.42, "network_bandwidth_mbps": 34.81}, "execution_time": {"base_runtime_seconds": 773.63, "estimated_runtime_seconds": 971.95}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 340, "time_step": 47, "task_type": "general", "resource_demand": {"cpu_cores": 4.02, "memory_mb": 8773.69, "io_bandwidth_mbps": 779.58, "network_bandwidth_mbps": 164.47}, "execution_time": {"base_runtime_seconds": 429.23, "estimated_runtime_seconds": 482.59}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 341, "time_step": 6, "task_type": "general", "resource_demand": {"cpu_cores": 1.82, "memory_mb": 4457.84, "io_bandwidth_mbps": 292.6, "network_bandwidth_mbps": 17.52}, "execution_time": {"base_runtime_seconds": 157.97, "estimated_runtime_seconds": 195.23}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 342, "time_step": 0, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.19, "memory_mb": 5701.02, "io_bandwidth_mbps": 367.67, "network_bandwidth_mbps": 42.12}, "execution_time": {"base_runtime_seconds": 145.67, "estimated_runtime_seconds": 169.09}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 343, "time_step": 8, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.79, "memory_mb": 3590.32, "io_bandwidth_mbps": 1502.72, "network_bandwidth_mbps": 61.02}, "execution_time": {"base_runtime_seconds": 682.37, "estimated_runtime_seconds": 872.47}, "dependencies": [174], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 344, "time_step": 48, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.53, "memory_mb": 7872.41, "io_bandwidth_mbps": 170.25, "network_bandwidth_mbps": 429.14}, "execution_time": {"base_runtime_seconds": 352.16, "estimated_runtime_seconds": 451.94}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 345, "time_step": 24, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.25, "memory_mb": 12409.0, "io_bandwidth_mbps": 177.41, "network_bandwidth_mbps": 37.9}, "execution_time": {"base_runtime_seconds": 537.22, "estimated_runtime_seconds": 678.01}, "dependencies": [169, 95, 240], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 346, "time_step": 48, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.75, "memory_mb": 2622.15, "io_bandwidth_mbps": 320.7, "network_bandwidth_mbps": 91.72}, "execution_time": {"base_runtime_seconds": 174.86, "estimated_runtime_seconds": 214.88}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 347, "time_step": 22, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.26, "memory_mb": 4856.92, "io_bandwidth_mbps": 242.85, "network_bandwidth_mbps": 88.43}, "execution_time": {"base_runtime_seconds": 89.05, "estimated_runtime_seconds": 114.44}, "dependencies": [246, 133, 250], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 348, "time_step": 15, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.56, "memory_mb": 3058.93, "io_bandwidth_mbps": 234.11, "network_bandwidth_mbps": 75.1}, "execution_time": {"base_runtime_seconds": 195.21, "estimated_runtime_seconds": 245.67}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 349, "time_step": 44, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.72, "memory_mb": 6796.04, "io_bandwidth_mbps": 110.92, "network_bandwidth_mbps": 141.44}, "execution_time": {"base_runtime_seconds": 208.88, "estimated_runtime_seconds": 244.18}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 350, "time_step": 3, "task_type": "general", "resource_demand": {"cpu_cores": 4.31, "memory_mb": 8571.78, "io_bandwidth_mbps": 480.16, "network_bandwidth_mbps": 171.58}, "execution_time": {"base_runtime_seconds": 241.09, "estimated_runtime_seconds": 311.69}, "dependencies": [267, 46], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 351, "time_step": 39, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.96, "memory_mb": 10904.22, "io_bandwidth_mbps": 264.01, "network_bandwidth_mbps": 40.06}, "execution_time": {"base_runtime_seconds": 579.78, "estimated_runtime_seconds": 676.2}, "dependencies": [21], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 352, "time_step": 2, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.42, "memory_mb": 2895.23, "io_bandwidth_mbps": 521.42, "network_bandwidth_mbps": 84.57}, "execution_time": {"base_runtime_seconds": 895.31, "estimated_runtime_seconds": 1080.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 353, "time_step": 1, "task_type": "general", "resource_demand": {"cpu_cores": 6.84, "memory_mb": 12178.97, "io_bandwidth_mbps": 693.68, "network_bandwidth_mbps": 57.39}, "execution_time": {"base_runtime_seconds": 154.65, "estimated_runtime_seconds": 178.37}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 354, "time_step": 6, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.6, "memory_mb": 29227.73, "io_bandwidth_mbps": 230.47, "network_bandwidth_mbps": 40.83}, "execution_time": {"base_runtime_seconds": 181.55, "estimated_runtime_seconds": 232.6}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 355, "time_step": 41, "task_type": "general", "resource_demand": {"cpu_cores": 1.1, "memory_mb": 12391.64, "io_bandwidth_mbps": 445.95, "network_bandwidth_mbps": 180.06}, "execution_time": {"base_runtime_seconds": 171.42, "estimated_runtime_seconds": 210.76}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 356, "time_step": 27, "task_type": "general", "resource_demand": {"cpu_cores": 6.91, "memory_mb": 3434.63, "io_bandwidth_mbps": 225.57, "network_bandwidth_mbps": 167.89}, "execution_time": {"base_runtime_seconds": 358.99, "estimated_runtime_seconds": 424.45}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 357, "time_step": 21, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.51, "memory_mb": 18638.89, "io_bandwidth_mbps": 124.74, "network_bandwidth_mbps": 25.35}, "execution_time": {"base_runtime_seconds": 579.4, "estimated_runtime_seconds": 689.94}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 358, "time_step": 8, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.43, "memory_mb": 2689.01, "io_bandwidth_mbps": 1517.17, "network_bandwidth_mbps": 39.61}, "execution_time": {"base_runtime_seconds": 378.87, "estimated_runtime_seconds": 477.68}, "dependencies": [108, 45, 93], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 359, "time_step": 37, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 12.38, "memory_mb": 7941.98, "io_bandwidth_mbps": 138.97, "network_bandwidth_mbps": 71.44}, "execution_time": {"base_runtime_seconds": 230.99, "estimated_runtime_seconds": 266.08}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 360, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.58, "memory_mb": 13528.91, "io_bandwidth_mbps": 114.83, "network_bandwidth_mbps": 17.94}, "execution_time": {"base_runtime_seconds": 244.62, "estimated_runtime_seconds": 297.56}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 361, "time_step": 3, "task_type": "general", "resource_demand": {"cpu_cores": 3.43, "memory_mb": 8092.89, "io_bandwidth_mbps": 692.76, "network_bandwidth_mbps": 123.54}, "execution_time": {"base_runtime_seconds": 236.71, "estimated_runtime_seconds": 272.02}, "dependencies": [360, 212, 357], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 362, "time_step": 32, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.71, "memory_mb": 1537.75, "io_bandwidth_mbps": 511.11, "network_bandwidth_mbps": 70.49}, "execution_time": {"base_runtime_seconds": 638.26, "estimated_runtime_seconds": 818.99}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 363, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.21, "memory_mb": 15139.57, "io_bandwidth_mbps": 113.36, "network_bandwidth_mbps": 35.29}, "execution_time": {"base_runtime_seconds": 507.02, "estimated_runtime_seconds": 621.79}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 364, "time_step": 32, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.64, "memory_mb": 17629.64, "io_bandwidth_mbps": 223.24, "network_bandwidth_mbps": 33.98}, "execution_time": {"base_runtime_seconds": 539.29, "estimated_runtime_seconds": 678.05}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 365, "time_step": 25, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.04, "memory_mb": 1722.58, "io_bandwidth_mbps": 1939.3, "network_bandwidth_mbps": 61.0}, "execution_time": {"base_runtime_seconds": 480.85, "estimated_runtime_seconds": 549.97}, "dependencies": [269, 225, 348], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 366, "time_step": 50, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.62, "memory_mb": 31543.91, "io_bandwidth_mbps": 203.54, "network_bandwidth_mbps": 41.0}, "execution_time": {"base_runtime_seconds": 269.65, "estimated_runtime_seconds": 342.0}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 367, "time_step": 0, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.87, "memory_mb": 3466.7, "io_bandwidth_mbps": 670.83, "network_bandwidth_mbps": 37.38}, "execution_time": {"base_runtime_seconds": 675.67, "estimated_runtime_seconds": 835.08}, "dependencies": [134, 321], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 368, "time_step": 1, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.46, "memory_mb": 3399.88, "io_bandwidth_mbps": 717.42, "network_bandwidth_mbps": 18.03}, "execution_time": {"base_runtime_seconds": 658.52, "estimated_runtime_seconds": 741.46}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 369, "time_step": 23, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.89, "memory_mb": 4576.08, "io_bandwidth_mbps": 137.6, "network_bandwidth_mbps": 480.52}, "execution_time": {"base_runtime_seconds": 162.88, "estimated_runtime_seconds": 210.35}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 370, "time_step": 47, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.49, "memory_mb": 3824.23, "io_bandwidth_mbps": 369.78, "network_bandwidth_mbps": 223.89}, "execution_time": {"base_runtime_seconds": 236.82, "estimated_runtime_seconds": 277.95}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 371, "time_step": 14, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.52, "memory_mb": 3177.86, "io_bandwidth_mbps": 474.74, "network_bandwidth_mbps": 46.6}, "execution_time": {"base_runtime_seconds": 265.91, "estimated_runtime_seconds": 297.75}, "dependencies": [350, 36, 179], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 372, "time_step": 9, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.64, "memory_mb": 31993.84, "io_bandwidth_mbps": 75.99, "network_bandwidth_mbps": 45.37}, "execution_time": {"base_runtime_seconds": 262.69, "estimated_runtime_seconds": 319.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 373, "time_step": 6, "task_type": "general", "resource_demand": {"cpu_cores": 6.08, "memory_mb": 3694.05, "io_bandwidth_mbps": 289.07, "network_bandwidth_mbps": 97.0}, "execution_time": {"base_runtime_seconds": 442.51, "estimated_runtime_seconds": 497.69}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 374, "time_step": 26, "task_type": "general", "resource_demand": {"cpu_cores": 6.88, "memory_mb": 11169.98, "io_bandwidth_mbps": 695.24, "network_bandwidth_mbps": 64.16}, "execution_time": {"base_runtime_seconds": 451.18, "estimated_runtime_seconds": 581.39}, "dependencies": [56, 323, 270], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 375, "time_step": 24, "task_type": "general", "resource_demand": {"cpu_cores": 1.01, "memory_mb": 10824.44, "io_bandwidth_mbps": 146.46, "network_bandwidth_mbps": 155.91}, "execution_time": {"base_runtime_seconds": 231.21, "estimated_runtime_seconds": 293.3}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 376, "time_step": 15, "task_type": "general", "resource_demand": {"cpu_cores": 6.7, "memory_mb": 2060.59, "io_bandwidth_mbps": 184.64, "network_bandwidth_mbps": 50.47}, "execution_time": {"base_runtime_seconds": 453.22, "estimated_runtime_seconds": 499.7}, "dependencies": [77, 52], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 377, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.97, "memory_mb": 6357.85, "io_bandwidth_mbps": 213.54, "network_bandwidth_mbps": 49.0}, "execution_time": {"base_runtime_seconds": 116.63, "estimated_runtime_seconds": 138.84}, "dependencies": [259, 160, 270], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 378, "time_step": 15, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.3, "memory_mb": 6847.68, "io_bandwidth_mbps": 376.81, "network_bandwidth_mbps": 574.22}, "execution_time": {"base_runtime_seconds": 131.0, "estimated_runtime_seconds": 149.66}, "dependencies": [62], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 379, "time_step": 15, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.02, "memory_mb": 2423.56, "io_bandwidth_mbps": 1519.05, "network_bandwidth_mbps": 83.38}, "execution_time": {"base_runtime_seconds": 782.63, "estimated_runtime_seconds": 928.01}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 380, "time_step": 7, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.79, "memory_mb": 13177.78, "io_bandwidth_mbps": 151.15, "network_bandwidth_mbps": 41.0}, "execution_time": {"base_runtime_seconds": 395.01, "estimated_runtime_seconds": 474.71}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 381, "time_step": 29, "task_type": "general", "resource_demand": {"cpu_cores": 5.58, "memory_mb": 10133.76, "io_bandwidth_mbps": 632.55, "network_bandwidth_mbps": 131.8}, "execution_time": {"base_runtime_seconds": 96.51, "estimated_runtime_seconds": 108.09}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 382, "time_step": 42, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.79, "memory_mb": 8209.56, "io_bandwidth_mbps": 214.95, "network_bandwidth_mbps": 15.5}, "execution_time": {"base_runtime_seconds": 412.4, "estimated_runtime_seconds": 501.4}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 383, "time_step": 25, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.19, "memory_mb": 7174.64, "io_bandwidth_mbps": 326.06, "network_bandwidth_mbps": 372.48}, "execution_time": {"base_runtime_seconds": 281.66, "estimated_runtime_seconds": 359.89}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 384, "time_step": 16, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.82, "memory_mb": 22095.81, "io_bandwidth_mbps": 180.89, "network_bandwidth_mbps": 38.62}, "execution_time": {"base_runtime_seconds": 471.85, "estimated_runtime_seconds": 542.75}, "dependencies": [72, 85], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 385, "time_step": 2, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.49, "memory_mb": 2316.41, "io_bandwidth_mbps": 1815.18, "network_bandwidth_mbps": 61.26}, "execution_time": {"base_runtime_seconds": 301.32, "estimated_runtime_seconds": 367.7}, "dependencies": [113], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 386, "time_step": 4, "task_type": "general", "resource_demand": {"cpu_cores": 2.91, "memory_mb": 8494.31, "io_bandwidth_mbps": 484.18, "network_bandwidth_mbps": 79.17}, "execution_time": {"base_runtime_seconds": 448.86, "estimated_runtime_seconds": 499.9}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 387, "time_step": 28, "task_type": "general", "resource_demand": {"cpu_cores": 6.28, "memory_mb": 14596.93, "io_bandwidth_mbps": 563.02, "network_bandwidth_mbps": 134.28}, "execution_time": {"base_runtime_seconds": 240.46, "estimated_runtime_seconds": 306.72}, "dependencies": [297, 59], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 388, "time_step": 38, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.71, "memory_mb": 4085.53, "io_bandwidth_mbps": 1898.24, "network_bandwidth_mbps": 86.62}, "execution_time": {"base_runtime_seconds": 210.81, "estimated_runtime_seconds": 269.37}, "dependencies": [11], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 389, "time_step": 4, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.92, "memory_mb": 22989.64, "io_bandwidth_mbps": 220.8, "network_bandwidth_mbps": 33.47}, "execution_time": {"base_runtime_seconds": 242.85, "estimated_runtime_seconds": 288.58}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 390, "time_step": 18, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.4, "memory_mb": 9423.59, "io_bandwidth_mbps": 226.08, "network_bandwidth_mbps": 34.24}, "execution_time": {"base_runtime_seconds": 385.45, "estimated_runtime_seconds": 481.72}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 391, "time_step": 47, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.58, "memory_mb": 3705.67, "io_bandwidth_mbps": 256.07, "network_bandwidth_mbps": 703.04}, "execution_time": {"base_runtime_seconds": 376.8, "estimated_runtime_seconds": 466.26}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 392, "time_step": 11, "task_type": "general", "resource_demand": {"cpu_cores": 3.99, "memory_mb": 7450.04, "io_bandwidth_mbps": 342.01, "network_bandwidth_mbps": 190.45}, "execution_time": {"base_runtime_seconds": 119.38, "estimated_runtime_seconds": 135.95}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 393, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.18, "memory_mb": 2356.98, "io_bandwidth_mbps": 436.67, "network_bandwidth_mbps": 14.03}, "execution_time": {"base_runtime_seconds": 268.81, "estimated_runtime_seconds": 310.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 394, "time_step": 37, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.4, "memory_mb": 2509.8, "io_bandwidth_mbps": 707.86, "network_bandwidth_mbps": 20.32}, "execution_time": {"base_runtime_seconds": 599.46, "estimated_runtime_seconds": 740.94}, "dependencies": [120, 271], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 395, "time_step": 22, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.24, "memory_mb": 1311.57, "io_bandwidth_mbps": 398.42, "network_bandwidth_mbps": 618.03}, "execution_time": {"base_runtime_seconds": 104.03, "estimated_runtime_seconds": 124.91}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 396, "time_step": 27, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.01, "memory_mb": 17412.55, "io_bandwidth_mbps": 124.13, "network_bandwidth_mbps": 43.87}, "execution_time": {"base_runtime_seconds": 182.57, "estimated_runtime_seconds": 221.01}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 397, "time_step": 24, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.33, "memory_mb": 11574.68, "io_bandwidth_mbps": 100.56, "network_bandwidth_mbps": 32.66}, "execution_time": {"base_runtime_seconds": 524.71, "estimated_runtime_seconds": 598.16}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 398, "time_step": 31, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.31, "memory_mb": 8118.26, "io_bandwidth_mbps": 373.07, "network_bandwidth_mbps": 44.16}, "execution_time": {"base_runtime_seconds": 277.41, "estimated_runtime_seconds": 338.63}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 399, "time_step": 11, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.79, "memory_mb": 4841.87, "io_bandwidth_mbps": 414.57, "network_bandwidth_mbps": 444.96}, "execution_time": {"base_runtime_seconds": 407.6, "estimated_runtime_seconds": 467.96}, "dependencies": [346, 151, 49], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 400, "time_step": 9, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.46, "memory_mb": 2718.13, "io_bandwidth_mbps": 228.18, "network_bandwidth_mbps": 866.79}, "execution_time": {"base_runtime_seconds": 340.04, "estimated_runtime_seconds": 388.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 401, "time_step": 13, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.72, "memory_mb": 9185.46, "io_bandwidth_mbps": 167.17, "network_bandwidth_mbps": 12.66}, "execution_time": {"base_runtime_seconds": 564.62, "estimated_runtime_seconds": 652.31}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 402, "time_step": 21, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.93, "memory_mb": 2334.05, "io_bandwidth_mbps": 184.96, "network_bandwidth_mbps": 39.76}, "execution_time": {"base_runtime_seconds": 298.01, "estimated_runtime_seconds": 366.73}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 403, "time_step": 49, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.7, "memory_mb": 3020.09, "io_bandwidth_mbps": 736.05, "network_bandwidth_mbps": 23.19}, "execution_time": {"base_runtime_seconds": 188.61, "estimated_runtime_seconds": 221.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 404, "time_step": 5, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.23, "memory_mb": 3597.39, "io_bandwidth_mbps": 243.92, "network_bandwidth_mbps": 13.16}, "execution_time": {"base_runtime_seconds": 243.97, "estimated_runtime_seconds": 306.68}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 405, "time_step": 19, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.07, "memory_mb": 3965.87, "io_bandwidth_mbps": 1371.46, "network_bandwidth_mbps": 33.12}, "execution_time": {"base_runtime_seconds": 831.34, "estimated_runtime_seconds": 956.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 406, "time_step": 34, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.19, "memory_mb": 32461.88, "io_bandwidth_mbps": 57.6, "network_bandwidth_mbps": 33.4}, "execution_time": {"base_runtime_seconds": 364.5, "estimated_runtime_seconds": 420.5}, "dependencies": [174], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 407, "time_step": 10, "task_type": "general", "resource_demand": {"cpu_cores": 2.07, "memory_mb": 7373.28, "io_bandwidth_mbps": 694.79, "network_bandwidth_mbps": 104.17}, "execution_time": {"base_runtime_seconds": 374.42, "estimated_runtime_seconds": 415.61}, "dependencies": [73], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 408, "time_step": 40, "task_type": "general", "resource_demand": {"cpu_cores": 4.2, "memory_mb": 4259.21, "io_bandwidth_mbps": 674.77, "network_bandwidth_mbps": 34.77}, "execution_time": {"base_runtime_seconds": 384.02, "estimated_runtime_seconds": 448.94}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 409, "time_step": 17, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.53, "memory_mb": 3288.81, "io_bandwidth_mbps": 487.33, "network_bandwidth_mbps": 991.29}, "execution_time": {"base_runtime_seconds": 261.57, "estimated_runtime_seconds": 315.5}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 410, "time_step": 28, "task_type": "general", "resource_demand": {"cpu_cores": 6.67, "memory_mb": 12696.78, "io_bandwidth_mbps": 222.9, "network_bandwidth_mbps": 88.04}, "execution_time": {"base_runtime_seconds": 271.63, "estimated_runtime_seconds": 336.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 411, "time_step": 43, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.62, "memory_mb": 4011.35, "io_bandwidth_mbps": 859.8, "network_bandwidth_mbps": 90.63}, "execution_time": {"base_runtime_seconds": 355.51, "estimated_runtime_seconds": 461.95}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 412, "time_step": 18, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.27, "memory_mb": 7069.46, "io_bandwidth_mbps": 325.65, "network_bandwidth_mbps": 98.76}, "execution_time": {"base_runtime_seconds": 283.81, "estimated_runtime_seconds": 352.66}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 413, "time_step": 29, "task_type": "general", "resource_demand": {"cpu_cores": 3.73, "memory_mb": 7435.59, "io_bandwidth_mbps": 774.98, "network_bandwidth_mbps": 77.97}, "execution_time": {"base_runtime_seconds": 92.59, "estimated_runtime_seconds": 118.72}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 414, "time_step": 27, "task_type": "general", "resource_demand": {"cpu_cores": 4.84, "memory_mb": 15115.43, "io_bandwidth_mbps": 547.66, "network_bandwidth_mbps": 49.63}, "execution_time": {"base_runtime_seconds": 313.29, "estimated_runtime_seconds": 403.76}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 415, "time_step": 21, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.81, "memory_mb": 5786.51, "io_bandwidth_mbps": 111.63, "network_bandwidth_mbps": 76.94}, "execution_time": {"base_runtime_seconds": 181.25, "estimated_runtime_seconds": 206.07}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 416, "time_step": 45, "task_type": "general", "resource_demand": {"cpu_cores": 3.86, "memory_mb": 11163.24, "io_bandwidth_mbps": 741.71, "network_bandwidth_mbps": 157.21}, "execution_time": {"base_runtime_seconds": 215.39, "estimated_runtime_seconds": 242.11}, "dependencies": [170, 49, 144], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 417, "time_step": 23, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.54, "memory_mb": 5464.7, "io_bandwidth_mbps": 126.78, "network_bandwidth_mbps": 462.03}, "execution_time": {"base_runtime_seconds": 378.23, "estimated_runtime_seconds": 443.82}, "dependencies": [364, 261], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 418, "time_step": 30, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.48, "memory_mb": 2411.55, "io_bandwidth_mbps": 1498.93, "network_bandwidth_mbps": 40.14}, "execution_time": {"base_runtime_seconds": 463.51, "estimated_runtime_seconds": 601.21}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 419, "time_step": 43, "task_type": "general", "resource_demand": {"cpu_cores": 3.15, "memory_mb": 9020.71, "io_bandwidth_mbps": 618.59, "network_bandwidth_mbps": 184.43}, "execution_time": {"base_runtime_seconds": 227.75, "estimated_runtime_seconds": 285.16}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 420, "time_step": 13, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.41, "memory_mb": 28737.53, "io_bandwidth_mbps": 93.84, "network_bandwidth_mbps": 13.85}, "execution_time": {"base_runtime_seconds": 548.13, "estimated_runtime_seconds": 656.46}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 421, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.69, "memory_mb": 27603.61, "io_bandwidth_mbps": 257.12, "network_bandwidth_mbps": 20.03}, "execution_time": {"base_runtime_seconds": 590.34, "estimated_runtime_seconds": 700.81}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 422, "time_step": 50, "task_type": "general", "resource_demand": {"cpu_cores": 4.8, "memory_mb": 10618.64, "io_bandwidth_mbps": 452.02, "network_bandwidth_mbps": 84.5}, "execution_time": {"base_runtime_seconds": 417.15, "estimated_runtime_seconds": 511.89}, "dependencies": [87, 400], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 423, "time_step": 37, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.78, "memory_mb": 1867.32, "io_bandwidth_mbps": 1575.83, "network_bandwidth_mbps": 51.36}, "execution_time": {"base_runtime_seconds": 877.31, "estimated_runtime_seconds": 967.22}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 424, "time_step": 8, "task_type": "general", "resource_demand": {"cpu_cores": 4.96, "memory_mb": 5362.42, "io_bandwidth_mbps": 152.64, "network_bandwidth_mbps": 28.65}, "execution_time": {"base_runtime_seconds": 195.24, "estimated_runtime_seconds": 220.08}, "dependencies": [51], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 425, "time_step": 8, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.29, "memory_mb": 6952.84, "io_bandwidth_mbps": 468.44, "network_bandwidth_mbps": 85.79}, "execution_time": {"base_runtime_seconds": 289.34, "estimated_runtime_seconds": 336.49}, "dependencies": [221], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 426, "time_step": 9, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.75, "memory_mb": 22077.96, "io_bandwidth_mbps": 123.9, "network_bandwidth_mbps": 29.52}, "execution_time": {"base_runtime_seconds": 127.65, "estimated_runtime_seconds": 159.47}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 427, "time_step": 25, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.67, "memory_mb": 6324.72, "io_bandwidth_mbps": 125.2, "network_bandwidth_mbps": 90.27}, "execution_time": {"base_runtime_seconds": 177.08, "estimated_runtime_seconds": 210.84}, "dependencies": [387, 69, 258], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 428, "time_step": 10, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.82, "memory_mb": 1189.63, "io_bandwidth_mbps": 1070.49, "network_bandwidth_mbps": 59.22}, "execution_time": {"base_runtime_seconds": 814.76, "estimated_runtime_seconds": 911.39}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 429, "time_step": 14, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.33, "memory_mb": 3758.09, "io_bandwidth_mbps": 368.39, "network_bandwidth_mbps": 26.59}, "execution_time": {"base_runtime_seconds": 193.01, "estimated_runtime_seconds": 244.49}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 430, "time_step": 14, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.02, "memory_mb": 13902.96, "io_bandwidth_mbps": 264.82, "network_bandwidth_mbps": 37.84}, "execution_time": {"base_runtime_seconds": 404.22, "estimated_runtime_seconds": 447.05}, "dependencies": [51, 195, 352], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 431, "time_step": 31, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 8.85, "memory_mb": 4586.54, "io_bandwidth_mbps": 341.4, "network_bandwidth_mbps": 82.48}, "execution_time": {"base_runtime_seconds": 158.74, "estimated_runtime_seconds": 177.49}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 432, "time_step": 15, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.79, "memory_mb": 23206.96, "io_bandwidth_mbps": 185.06, "network_bandwidth_mbps": 23.37}, "execution_time": {"base_runtime_seconds": 447.73, "estimated_runtime_seconds": 548.91}, "dependencies": [428, 414, 59], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 433, "time_step": 42, "task_type": "general", "resource_demand": {"cpu_cores": 5.61, "memory_mb": 4730.32, "io_bandwidth_mbps": 594.09, "network_bandwidth_mbps": 96.32}, "execution_time": {"base_runtime_seconds": 370.61, "estimated_runtime_seconds": 416.64}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 434, "time_step": 31, "task_type": "general", "resource_demand": {"cpu_cores": 4.84, "memory_mb": 10967.23, "io_bandwidth_mbps": 164.49, "network_bandwidth_mbps": 48.71}, "execution_time": {"base_runtime_seconds": 334.17, "estimated_runtime_seconds": 428.46}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 435, "time_step": 18, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.43, "memory_mb": 7254.3, "io_bandwidth_mbps": 484.92, "network_bandwidth_mbps": 47.73}, "execution_time": {"base_runtime_seconds": 94.08, "estimated_runtime_seconds": 110.68}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 436, "time_step": 21, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.32, "memory_mb": 31206.86, "io_bandwidth_mbps": 168.58, "network_bandwidth_mbps": 35.48}, "execution_time": {"base_runtime_seconds": 494.81, "estimated_runtime_seconds": 598.57}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 437, "time_step": 23, "task_type": "general", "resource_demand": {"cpu_cores": 2.75, "memory_mb": 9485.86, "io_bandwidth_mbps": 569.39, "network_bandwidth_mbps": 38.99}, "execution_time": {"base_runtime_seconds": 118.9, "estimated_runtime_seconds": 142.83}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 438, "time_step": 32, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.98, "memory_mb": 3282.84, "io_bandwidth_mbps": 688.3, "network_bandwidth_mbps": 97.32}, "execution_time": {"base_runtime_seconds": 566.68, "estimated_runtime_seconds": 724.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 439, "time_step": 34, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.61, "memory_mb": 24052.23, "io_bandwidth_mbps": 222.32, "network_bandwidth_mbps": 18.79}, "execution_time": {"base_runtime_seconds": 367.58, "estimated_runtime_seconds": 468.41}, "dependencies": [259], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 440, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.72, "memory_mb": 25126.49, "io_bandwidth_mbps": 93.5, "network_bandwidth_mbps": 25.36}, "execution_time": {"base_runtime_seconds": 386.16, "estimated_runtime_seconds": 458.56}, "dependencies": [292, 121, 232], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 441, "time_step": 10, "task_type": "general", "resource_demand": {"cpu_cores": 3.44, "memory_mb": 14289.31, "io_bandwidth_mbps": 300.26, "network_bandwidth_mbps": 109.22}, "execution_time": {"base_runtime_seconds": 324.19, "estimated_runtime_seconds": 417.31}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 442, "time_step": 29, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.63, "memory_mb": 8994.36, "io_bandwidth_mbps": 122.83, "network_bandwidth_mbps": 26.88}, "execution_time": {"base_runtime_seconds": 236.86, "estimated_runtime_seconds": 304.05}, "dependencies": [268, 425], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 443, "time_step": 37, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.35, "memory_mb": 3784.4, "io_bandwidth_mbps": 1955.94, "network_bandwidth_mbps": 30.87}, "execution_time": {"base_runtime_seconds": 780.16, "estimated_runtime_seconds": 949.88}, "dependencies": [171, 90], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 444, "time_step": 49, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.73, "memory_mb": 2708.44, "io_bandwidth_mbps": 164.22, "network_bandwidth_mbps": 22.51}, "execution_time": {"base_runtime_seconds": 275.21, "estimated_runtime_seconds": 320.14}, "dependencies": [269], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 445, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.03, "memory_mb": 20950.28, "io_bandwidth_mbps": 102.56, "network_bandwidth_mbps": 35.5}, "execution_time": {"base_runtime_seconds": 283.13, "estimated_runtime_seconds": 335.56}, "dependencies": [284, 240, 134], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 446, "time_step": 11, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.22, "memory_mb": 3727.03, "io_bandwidth_mbps": 1791.28, "network_bandwidth_mbps": 20.09}, "execution_time": {"base_runtime_seconds": 433.04, "estimated_runtime_seconds": 548.93}, "dependencies": [53, 328], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 447, "time_step": 41, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.08, "memory_mb": 6708.09, "io_bandwidth_mbps": 175.33, "network_bandwidth_mbps": 65.77}, "execution_time": {"base_runtime_seconds": 217.65, "estimated_runtime_seconds": 258.03}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 448, "time_step": 8, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.37, "memory_mb": 14829.35, "io_bandwidth_mbps": 164.9, "network_bandwidth_mbps": 17.68}, "execution_time": {"base_runtime_seconds": 309.05, "estimated_runtime_seconds": 368.19}, "dependencies": [440], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 449, "time_step": 2, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.76, "memory_mb": 25716.97, "io_bandwidth_mbps": 100.24, "network_bandwidth_mbps": 12.98}, "execution_time": {"base_runtime_seconds": 276.52, "estimated_runtime_seconds": 333.36}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 450, "time_step": 38, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.74, "memory_mb": 7625.71, "io_bandwidth_mbps": 337.59, "network_bandwidth_mbps": 765.89}, "execution_time": {"base_runtime_seconds": 183.5, "estimated_runtime_seconds": 211.45}, "dependencies": [358], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 451, "time_step": 47, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.12, "memory_mb": 3160.77, "io_bandwidth_mbps": 819.69, "network_bandwidth_mbps": 46.93}, "execution_time": {"base_runtime_seconds": 284.42, "estimated_runtime_seconds": 334.11}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 452, "time_step": 23, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.4, "memory_mb": 4797.1, "io_bandwidth_mbps": 101.2, "network_bandwidth_mbps": 480.52}, "execution_time": {"base_runtime_seconds": 151.64, "estimated_runtime_seconds": 167.88}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 453, "time_step": 2, "task_type": "general", "resource_demand": {"cpu_cores": 6.9, "memory_mb": 9680.46, "io_bandwidth_mbps": 626.79, "network_bandwidth_mbps": 14.47}, "execution_time": {"base_runtime_seconds": 368.6, "estimated_runtime_seconds": 453.83}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 454, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.67, "memory_mb": 28229.05, "io_bandwidth_mbps": 151.98, "network_bandwidth_mbps": 38.4}, "execution_time": {"base_runtime_seconds": 261.36, "estimated_runtime_seconds": 331.17}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 455, "time_step": 26, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.56, "memory_mb": 1502.15, "io_bandwidth_mbps": 370.87, "network_bandwidth_mbps": 613.06}, "execution_time": {"base_runtime_seconds": 150.27, "estimated_runtime_seconds": 187.26}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 456, "time_step": 11, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.56, "memory_mb": 2033.64, "io_bandwidth_mbps": 969.44, "network_bandwidth_mbps": 60.53}, "execution_time": {"base_runtime_seconds": 537.08, "estimated_runtime_seconds": 673.29}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 457, "time_step": 9, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.63, "memory_mb": 6934.96, "io_bandwidth_mbps": 134.56, "network_bandwidth_mbps": 899.75}, "execution_time": {"base_runtime_seconds": 235.38, "estimated_runtime_seconds": 303.11}, "dependencies": [200, 7], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 458, "time_step": 4, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.81, "memory_mb": 7390.57, "io_bandwidth_mbps": 266.87, "network_bandwidth_mbps": 31.32}, "execution_time": {"base_runtime_seconds": 261.13, "estimated_runtime_seconds": 323.39}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 459, "time_step": 18, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.32, "memory_mb": 3334.84, "io_bandwidth_mbps": 247.41, "network_bandwidth_mbps": 183.67}, "execution_time": {"base_runtime_seconds": 249.41, "estimated_runtime_seconds": 279.36}, "dependencies": [420], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 460, "time_step": 46, "task_type": "general", "resource_demand": {"cpu_cores": 4.95, "memory_mb": 5253.12, "io_bandwidth_mbps": 215.81, "network_bandwidth_mbps": 63.9}, "execution_time": {"base_runtime_seconds": 395.69, "estimated_runtime_seconds": 488.66}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 461, "time_step": 11, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.3, "memory_mb": 2575.41, "io_bandwidth_mbps": 1011.3, "network_bandwidth_mbps": 68.46}, "execution_time": {"base_runtime_seconds": 576.69, "estimated_runtime_seconds": 660.07}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 462, "time_step": 26, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.19, "memory_mb": 3880.45, "io_bandwidth_mbps": 178.81, "network_bandwidth_mbps": 428.62}, "execution_time": {"base_runtime_seconds": 282.28, "estimated_runtime_seconds": 318.45}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 463, "time_step": 13, "task_type": "general", "resource_demand": {"cpu_cores": 3.35, "memory_mb": 11925.15, "io_bandwidth_mbps": 655.03, "network_bandwidth_mbps": 46.4}, "execution_time": {"base_runtime_seconds": 474.88, "estimated_runtime_seconds": 591.78}, "dependencies": [317, 255], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 464, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.75, "memory_mb": 1684.25, "io_bandwidth_mbps": 437.38, "network_bandwidth_mbps": 212.51}, "execution_time": {"base_runtime_seconds": 126.21, "estimated_runtime_seconds": 143.05}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 465, "time_step": 32, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.45, "memory_mb": 1453.81, "io_bandwidth_mbps": 1806.78, "network_bandwidth_mbps": 23.86}, "execution_time": {"base_runtime_seconds": 242.13, "estimated_runtime_seconds": 308.73}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 466, "time_step": 37, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.65, "memory_mb": 3489.01, "io_bandwidth_mbps": 422.26, "network_bandwidth_mbps": 900.53}, "execution_time": {"base_runtime_seconds": 173.89, "estimated_runtime_seconds": 197.27}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 467, "time_step": 19, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.56, "memory_mb": 3170.27, "io_bandwidth_mbps": 565.06, "network_bandwidth_mbps": 77.88}, "execution_time": {"base_runtime_seconds": 428.86, "estimated_runtime_seconds": 497.24}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 468, "time_step": 22, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.0, "memory_mb": 5256.71, "io_bandwidth_mbps": 293.58, "network_bandwidth_mbps": 145.25}, "execution_time": {"base_runtime_seconds": 266.58, "estimated_runtime_seconds": 331.06}, "dependencies": [219], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 469, "time_step": 47, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.66, "memory_mb": 4053.77, "io_bandwidth_mbps": 419.08, "network_bandwidth_mbps": 45.85}, "execution_time": {"base_runtime_seconds": 222.76, "estimated_runtime_seconds": 278.53}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 470, "time_step": 35, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.75, "memory_mb": 24132.98, "io_bandwidth_mbps": 221.19, "network_bandwidth_mbps": 19.83}, "execution_time": {"base_runtime_seconds": 326.55, "estimated_runtime_seconds": 373.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 471, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.02, "memory_mb": 23439.26, "io_bandwidth_mbps": 145.77, "network_bandwidth_mbps": 21.46}, "execution_time": {"base_runtime_seconds": 181.6, "estimated_runtime_seconds": 205.33}, "dependencies": [141], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 472, "time_step": 38, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.1, "memory_mb": 2795.51, "io_bandwidth_mbps": 1083.38, "network_bandwidth_mbps": 88.29}, "execution_time": {"base_runtime_seconds": 895.49, "estimated_runtime_seconds": 1149.16}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 473, "time_step": 16, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.33, "memory_mb": 3109.76, "io_bandwidth_mbps": 432.74, "network_bandwidth_mbps": 94.4}, "execution_time": {"base_runtime_seconds": 298.72, "estimated_runtime_seconds": 385.82}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 474, "time_step": 10, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.35, "memory_mb": 2608.76, "io_bandwidth_mbps": 479.8, "network_bandwidth_mbps": 598.59}, "execution_time": {"base_runtime_seconds": 382.74, "estimated_runtime_seconds": 491.53}, "dependencies": [203, 174], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 475, "time_step": 29, "task_type": "general", "resource_demand": {"cpu_cores": 5.43, "memory_mb": 9507.24, "io_bandwidth_mbps": 350.04, "network_bandwidth_mbps": 195.69}, "execution_time": {"base_runtime_seconds": 468.83, "estimated_runtime_seconds": 533.67}, "dependencies": [135, 332], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 476, "time_step": 42, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.33, "memory_mb": 23548.89, "io_bandwidth_mbps": 252.73, "network_bandwidth_mbps": 37.91}, "execution_time": {"base_runtime_seconds": 344.1, "estimated_runtime_seconds": 442.29}, "dependencies": [124, 249, 31], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 477, "time_step": 36, "task_type": "general", "resource_demand": {"cpu_cores": 7.21, "memory_mb": 13539.78, "io_bandwidth_mbps": 510.14, "network_bandwidth_mbps": 32.59}, "execution_time": {"base_runtime_seconds": 307.99, "estimated_runtime_seconds": 378.58}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 478, "time_step": 29, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.12, "memory_mb": 4778.77, "io_bandwidth_mbps": 134.05, "network_bandwidth_mbps": 30.6}, "execution_time": {"base_runtime_seconds": 90.18, "estimated_runtime_seconds": 100.13}, "dependencies": [441], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 479, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.99, "memory_mb": 22511.5, "io_bandwidth_mbps": 260.6, "network_bandwidth_mbps": 35.82}, "execution_time": {"base_runtime_seconds": 483.42, "estimated_runtime_seconds": 575.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 480, "time_step": 25, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.93, "memory_mb": 2031.16, "io_bandwidth_mbps": 874.08, "network_bandwidth_mbps": 60.05}, "execution_time": {"base_runtime_seconds": 482.7, "estimated_runtime_seconds": 570.4}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 481, "time_step": 29, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.1, "memory_mb": 7399.41, "io_bandwidth_mbps": 489.49, "network_bandwidth_mbps": 336.33}, "execution_time": {"base_runtime_seconds": 164.77, "estimated_runtime_seconds": 210.63}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 482, "time_step": 50, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.61, "memory_mb": 6634.98, "io_bandwidth_mbps": 436.99, "network_bandwidth_mbps": 452.94}, "execution_time": {"base_runtime_seconds": 106.27, "estimated_runtime_seconds": 120.83}, "dependencies": [4, 277], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 483, "time_step": 43, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.28, "memory_mb": 2314.21, "io_bandwidth_mbps": 463.2, "network_bandwidth_mbps": 446.43}, "execution_time": {"base_runtime_seconds": 262.14, "estimated_runtime_seconds": 327.47}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 484, "time_step": 20, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.44, "memory_mb": 1097.06, "io_bandwidth_mbps": 1611.6, "network_bandwidth_mbps": 55.11}, "execution_time": {"base_runtime_seconds": 686.34, "estimated_runtime_seconds": 812.52}, "dependencies": [176], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 485, "time_step": 8, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.25, "memory_mb": 1317.4, "io_bandwidth_mbps": 421.51, "network_bandwidth_mbps": 884.46}, "execution_time": {"base_runtime_seconds": 254.52, "estimated_runtime_seconds": 280.88}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 486, "time_step": 15, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 8.05, "memory_mb": 8120.13, "io_bandwidth_mbps": 237.52, "network_bandwidth_mbps": 41.29}, "execution_time": {"base_runtime_seconds": 133.21, "estimated_runtime_seconds": 171.5}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 487, "time_step": 43, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.42, "memory_mb": 5945.48, "io_bandwidth_mbps": 490.67, "network_bandwidth_mbps": 49.06}, "execution_time": {"base_runtime_seconds": 205.26, "estimated_runtime_seconds": 246.72}, "dependencies": [126, 34], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 488, "time_step": 46, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 8.04, "memory_mb": 3225.55, "io_bandwidth_mbps": 402.79, "network_bandwidth_mbps": 22.58}, "execution_time": {"base_runtime_seconds": 297.12, "estimated_runtime_seconds": 330.32}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 489, "time_step": 41, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.15, "memory_mb": 26834.17, "io_bandwidth_mbps": 285.11, "network_bandwidth_mbps": 49.41}, "execution_time": {"base_runtime_seconds": 493.71, "estimated_runtime_seconds": 622.59}, "dependencies": [171], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 490, "time_step": 45, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.07, "memory_mb": 3901.07, "io_bandwidth_mbps": 640.87, "network_bandwidth_mbps": 10.69}, "execution_time": {"base_runtime_seconds": 428.11, "estimated_runtime_seconds": 498.14}, "dependencies": [483, 19], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 491, "time_step": 27, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.25, "memory_mb": 10589.62, "io_bandwidth_mbps": 176.85, "network_bandwidth_mbps": 17.34}, "execution_time": {"base_runtime_seconds": 453.72, "estimated_runtime_seconds": 532.12}, "dependencies": [451], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 492, "time_step": 47, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.97, "memory_mb": 3768.67, "io_bandwidth_mbps": 244.33, "network_bandwidth_mbps": 27.52}, "execution_time": {"base_runtime_seconds": 134.76, "estimated_runtime_seconds": 156.51}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 493, "time_step": 27, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.81, "memory_mb": 4573.33, "io_bandwidth_mbps": 320.68, "network_bandwidth_mbps": 87.62}, "execution_time": {"base_runtime_seconds": 177.33, "estimated_runtime_seconds": 198.77}, "dependencies": [2, 199], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 494, "time_step": 38, "task_type": "general", "resource_demand": {"cpu_cores": 2.58, "memory_mb": 5395.89, "io_bandwidth_mbps": 563.3, "network_bandwidth_mbps": 57.15}, "execution_time": {"base_runtime_seconds": 156.34, "estimated_runtime_seconds": 181.71}, "dependencies": [72, 155, 180], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 495, "time_step": 4, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.61, "memory_mb": 2723.59, "io_bandwidth_mbps": 391.81, "network_bandwidth_mbps": 326.39}, "execution_time": {"base_runtime_seconds": 190.18, "estimated_runtime_seconds": 231.93}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 496, "time_step": 21, "task_type": "general", "resource_demand": {"cpu_cores": 3.56, "memory_mb": 3928.78, "io_bandwidth_mbps": 109.2, "network_bandwidth_mbps": 93.16}, "execution_time": {"base_runtime_seconds": 295.98, "estimated_runtime_seconds": 346.58}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 497, "time_step": 0, "task_type": "general", "resource_demand": {"cpu_cores": 6.94, "memory_mb": 8436.09, "io_bandwidth_mbps": 190.34, "network_bandwidth_mbps": 95.8}, "execution_time": {"base_runtime_seconds": 410.28, "estimated_runtime_seconds": 490.25}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 498, "time_step": 22, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.03, "memory_mb": 3648.38, "io_bandwidth_mbps": 697.76, "network_bandwidth_mbps": 94.02}, "execution_time": {"base_runtime_seconds": 611.55, "estimated_runtime_seconds": 749.6}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 499, "time_step": 34, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.42, "memory_mb": 4883.94, "io_bandwidth_mbps": 414.32, "network_bandwidth_mbps": 238.93}, "execution_time": {"base_runtime_seconds": 255.36, "estimated_runtime_seconds": 298.33}, "dependencies": [374, 231], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 500, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 5.56, "memory_mb": 6531.28, "io_bandwidth_mbps": 280.99, "network_bandwidth_mbps": 154.69}, "execution_time": {"base_runtime_seconds": 277.17, "estimated_runtime_seconds": 343.7}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}]}