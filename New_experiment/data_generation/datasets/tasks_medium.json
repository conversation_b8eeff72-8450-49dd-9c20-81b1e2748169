{"metadata": {"total_tasks": 200, "generated_at": "2025-08-02T23:12:14.731816", "dependency_probability": 0.3, "task_types": ["cpu_intensive", "memory_intensive", "io_intensive", "network_intensive", "general"]}, "tasks": [{"id": 1, "time_step": 13, "task_type": "general", "resource_demand": {"cpu_cores": 7.52, "memory_mb": 13153.95, "io_bandwidth_mbps": 730.49, "network_bandwidth_mbps": 11.19}, "execution_time": {"base_runtime_seconds": 204.91, "estimated_runtime_seconds": 237.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 2, "time_step": 14, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.49, "memory_mb": 6141.71, "io_bandwidth_mbps": 490.1, "network_bandwidth_mbps": 600.27}, "execution_time": {"base_runtime_seconds": 146.07, "estimated_runtime_seconds": 162.56}, "dependencies": [1], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 3, "time_step": 1, "task_type": "general", "resource_demand": {"cpu_cores": 6.33, "memory_mb": 8022.65, "io_bandwidth_mbps": 719.75, "network_bandwidth_mbps": 20.85}, "execution_time": {"base_runtime_seconds": 238.34, "estimated_runtime_seconds": 292.5}, "dependencies": [2], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 4, "time_step": 8, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.25, "memory_mb": 7653.61, "io_bandwidth_mbps": 309.57, "network_bandwidth_mbps": 805.4}, "execution_time": {"base_runtime_seconds": 409.59, "estimated_runtime_seconds": 522.09}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 5, "time_step": 10, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.5, "memory_mb": 2927.71, "io_bandwidth_mbps": 355.97, "network_bandwidth_mbps": 34.38}, "execution_time": {"base_runtime_seconds": 206.47, "estimated_runtime_seconds": 228.78}, "dependencies": [2, 3], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 6, "time_step": 12, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.07, "memory_mb": 17950.45, "io_bandwidth_mbps": 142.69, "network_bandwidth_mbps": 47.35}, "execution_time": {"base_runtime_seconds": 511.87, "estimated_runtime_seconds": 607.05}, "dependencies": [1, 2, 3], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 7, "time_step": 4, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.36, "memory_mb": 2906.4, "io_bandwidth_mbps": 442.47, "network_bandwidth_mbps": 107.9}, "execution_time": {"base_runtime_seconds": 296.6, "estimated_runtime_seconds": 331.75}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 8, "time_step": 15, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.15, "memory_mb": 12940.96, "io_bandwidth_mbps": 199.78, "network_bandwidth_mbps": 48.73}, "execution_time": {"base_runtime_seconds": 381.62, "estimated_runtime_seconds": 435.56}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 9, "time_step": 16, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.74, "memory_mb": 5124.04, "io_bandwidth_mbps": 116.33, "network_bandwidth_mbps": 96.4}, "execution_time": {"base_runtime_seconds": 186.92, "estimated_runtime_seconds": 230.24}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 10, "time_step": 3, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.26, "memory_mb": 1648.34, "io_bandwidth_mbps": 1240.13, "network_bandwidth_mbps": 37.75}, "execution_time": {"base_runtime_seconds": 627.43, "estimated_runtime_seconds": 750.19}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 11, "time_step": 2, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.9, "memory_mb": 3904.83, "io_bandwidth_mbps": 1284.79, "network_bandwidth_mbps": 42.17}, "execution_time": {"base_runtime_seconds": 665.39, "estimated_runtime_seconds": 760.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 12, "time_step": 11, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.94, "memory_mb": 1629.72, "io_bandwidth_mbps": 1949.49, "network_bandwidth_mbps": 76.46}, "execution_time": {"base_runtime_seconds": 613.34, "estimated_runtime_seconds": 793.77}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 13, "time_step": 6, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.67, "memory_mb": 28461.52, "io_bandwidth_mbps": 166.52, "network_bandwidth_mbps": 33.94}, "execution_time": {"base_runtime_seconds": 224.08, "estimated_runtime_seconds": 246.89}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 14, "time_step": 20, "task_type": "general", "resource_demand": {"cpu_cores": 7.03, "memory_mb": 8260.81, "io_bandwidth_mbps": 499.84, "network_bandwidth_mbps": 149.14}, "execution_time": {"base_runtime_seconds": 386.94, "estimated_runtime_seconds": 433.25}, "dependencies": [8, 10], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 15, "time_step": 13, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.44, "memory_mb": 3457.8, "io_bandwidth_mbps": 184.72, "network_bandwidth_mbps": 88.14}, "execution_time": {"base_runtime_seconds": 160.31, "estimated_runtime_seconds": 189.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 16, "time_step": 16, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.88, "memory_mb": 1377.22, "io_bandwidth_mbps": 539.72, "network_bandwidth_mbps": 55.77}, "execution_time": {"base_runtime_seconds": 367.47, "estimated_runtime_seconds": 462.06}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 17, "time_step": 16, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.3, "memory_mb": 6242.19, "io_bandwidth_mbps": 409.64, "network_bandwidth_mbps": 93.29}, "execution_time": {"base_runtime_seconds": 142.84, "estimated_runtime_seconds": 164.08}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 18, "time_step": 12, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.32, "memory_mb": 14061.0, "io_bandwidth_mbps": 243.05, "network_bandwidth_mbps": 41.89}, "execution_time": {"base_runtime_seconds": 234.34, "estimated_runtime_seconds": 272.34}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 19, "time_step": 4, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.92, "memory_mb": 7589.42, "io_bandwidth_mbps": 136.05, "network_bandwidth_mbps": 93.43}, "execution_time": {"base_runtime_seconds": 183.34, "estimated_runtime_seconds": 213.83}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 20, "time_step": 6, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.86, "memory_mb": 5668.88, "io_bandwidth_mbps": 310.14, "network_bandwidth_mbps": 63.57}, "execution_time": {"base_runtime_seconds": 198.15, "estimated_runtime_seconds": 256.23}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 21, "time_step": 0, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.61, "memory_mb": 7016.68, "io_bandwidth_mbps": 435.58, "network_bandwidth_mbps": 34.21}, "execution_time": {"base_runtime_seconds": 151.45, "estimated_runtime_seconds": 168.68}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 22, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 8.09, "memory_mb": 2928.89, "io_bandwidth_mbps": 165.87, "network_bandwidth_mbps": 92.1}, "execution_time": {"base_runtime_seconds": 256.94, "estimated_runtime_seconds": 332.97}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 23, "time_step": 6, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.44, "memory_mb": 21552.54, "io_bandwidth_mbps": 262.08, "network_bandwidth_mbps": 18.91}, "execution_time": {"base_runtime_seconds": 537.93, "estimated_runtime_seconds": 603.06}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 24, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.19, "memory_mb": 29507.35, "io_bandwidth_mbps": 287.96, "network_bandwidth_mbps": 18.96}, "execution_time": {"base_runtime_seconds": 216.71, "estimated_runtime_seconds": 274.79}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 25, "time_step": 9, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.03, "memory_mb": 3145.48, "io_bandwidth_mbps": 362.39, "network_bandwidth_mbps": 512.05}, "execution_time": {"base_runtime_seconds": 105.87, "estimated_runtime_seconds": 120.82}, "dependencies": [17, 23, 13], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 26, "time_step": 11, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.19, "memory_mb": 1806.8, "io_bandwidth_mbps": 630.76, "network_bandwidth_mbps": 19.78}, "execution_time": {"base_runtime_seconds": 718.88, "estimated_runtime_seconds": 820.34}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 27, "time_step": 8, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.71, "memory_mb": 27249.55, "io_bandwidth_mbps": 296.04, "network_bandwidth_mbps": 13.26}, "execution_time": {"base_runtime_seconds": 335.71, "estimated_runtime_seconds": 394.24}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 28, "time_step": 12, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.53, "memory_mb": 6101.47, "io_bandwidth_mbps": 330.46, "network_bandwidth_mbps": 75.26}, "execution_time": {"base_runtime_seconds": 212.11, "estimated_runtime_seconds": 241.17}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 29, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.9, "memory_mb": 6285.28, "io_bandwidth_mbps": 265.34, "network_bandwidth_mbps": 77.08}, "execution_time": {"base_runtime_seconds": 196.87, "estimated_runtime_seconds": 236.18}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 30, "time_step": 18, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.53, "memory_mb": 5107.36, "io_bandwidth_mbps": 468.17, "network_bandwidth_mbps": 454.32}, "execution_time": {"base_runtime_seconds": 228.63, "estimated_runtime_seconds": 295.33}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 31, "time_step": 10, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.85, "memory_mb": 3537.5, "io_bandwidth_mbps": 391.01, "network_bandwidth_mbps": 20.52}, "execution_time": {"base_runtime_seconds": 224.26, "estimated_runtime_seconds": 265.61}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 32, "time_step": 0, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.6, "memory_mb": 1534.85, "io_bandwidth_mbps": 170.99, "network_bandwidth_mbps": 128.67}, "execution_time": {"base_runtime_seconds": 238.23, "estimated_runtime_seconds": 308.31}, "dependencies": [14], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 33, "time_step": 16, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.4, "memory_mb": 6373.11, "io_bandwidth_mbps": 225.21, "network_bandwidth_mbps": 48.69}, "execution_time": {"base_runtime_seconds": 184.53, "estimated_runtime_seconds": 209.98}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 34, "time_step": 17, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.82, "memory_mb": 3957.73, "io_bandwidth_mbps": 290.27, "network_bandwidth_mbps": 61.35}, "execution_time": {"base_runtime_seconds": 208.24, "estimated_runtime_seconds": 247.76}, "dependencies": [20, 32, 10], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 35, "time_step": 14, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.02, "memory_mb": 7941.64, "io_bandwidth_mbps": 216.52, "network_bandwidth_mbps": 46.62}, "execution_time": {"base_runtime_seconds": 131.65, "estimated_runtime_seconds": 155.89}, "dependencies": [32], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 36, "time_step": 12, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.06, "memory_mb": 2142.89, "io_bandwidth_mbps": 414.28, "network_bandwidth_mbps": 515.07}, "execution_time": {"base_runtime_seconds": 105.51, "estimated_runtime_seconds": 130.57}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 37, "time_step": 10, "task_type": "general", "resource_demand": {"cpu_cores": 6.87, "memory_mb": 8224.76, "io_bandwidth_mbps": 205.72, "network_bandwidth_mbps": 125.9}, "execution_time": {"base_runtime_seconds": 167.62, "estimated_runtime_seconds": 201.53}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 38, "time_step": 8, "task_type": "general", "resource_demand": {"cpu_cores": 7.23, "memory_mb": 4847.93, "io_bandwidth_mbps": 142.79, "network_bandwidth_mbps": 174.07}, "execution_time": {"base_runtime_seconds": 479.13, "estimated_runtime_seconds": 566.4}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 39, "time_step": 7, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.74, "memory_mb": 3888.14, "io_bandwidth_mbps": 740.98, "network_bandwidth_mbps": 67.52}, "execution_time": {"base_runtime_seconds": 812.22, "estimated_runtime_seconds": 912.38}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 40, "time_step": 12, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.95, "memory_mb": 3179.52, "io_bandwidth_mbps": 472.61, "network_bandwidth_mbps": 51.36}, "execution_time": {"base_runtime_seconds": 98.23, "estimated_runtime_seconds": 125.53}, "dependencies": [2, 33, 15], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 41, "time_step": 19, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.53, "memory_mb": 20662.98, "io_bandwidth_mbps": 211.85, "network_bandwidth_mbps": 16.57}, "execution_time": {"base_runtime_seconds": 575.91, "estimated_runtime_seconds": 680.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 42, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 7.72, "memory_mb": 7719.95, "io_bandwidth_mbps": 423.17, "network_bandwidth_mbps": 186.01}, "execution_time": {"base_runtime_seconds": 222.5, "estimated_runtime_seconds": 288.98}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 43, "time_step": 15, "task_type": "general", "resource_demand": {"cpu_cores": 1.66, "memory_mb": 13382.17, "io_bandwidth_mbps": 511.26, "network_bandwidth_mbps": 91.06}, "execution_time": {"base_runtime_seconds": 268.58, "estimated_runtime_seconds": 305.02}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 44, "time_step": 11, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.12, "memory_mb": 5660.43, "io_bandwidth_mbps": 199.93, "network_bandwidth_mbps": 545.61}, "execution_time": {"base_runtime_seconds": 126.88, "estimated_runtime_seconds": 151.93}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 45, "time_step": 17, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.76, "memory_mb": 4173.38, "io_bandwidth_mbps": 198.69, "network_bandwidth_mbps": 206.78}, "execution_time": {"base_runtime_seconds": 371.28, "estimated_runtime_seconds": 468.75}, "dependencies": [36], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 46, "time_step": 6, "task_type": "general", "resource_demand": {"cpu_cores": 1.12, "memory_mb": 7463.33, "io_bandwidth_mbps": 111.4, "network_bandwidth_mbps": 135.34}, "execution_time": {"base_runtime_seconds": 305.7, "estimated_runtime_seconds": 368.27}, "dependencies": [39, 19], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 47, "time_step": 13, "task_type": "general", "resource_demand": {"cpu_cores": 1.45, "memory_mb": 8935.12, "io_bandwidth_mbps": 646.23, "network_bandwidth_mbps": 195.91}, "execution_time": {"base_runtime_seconds": 458.33, "estimated_runtime_seconds": 506.5}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 48, "time_step": 19, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.31, "memory_mb": 6319.25, "io_bandwidth_mbps": 145.57, "network_bandwidth_mbps": 98.78}, "execution_time": {"base_runtime_seconds": 231.77, "estimated_runtime_seconds": 260.3}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 49, "time_step": 4, "task_type": "general", "resource_demand": {"cpu_cores": 1.65, "memory_mb": 6274.56, "io_bandwidth_mbps": 765.47, "network_bandwidth_mbps": 163.42}, "execution_time": {"base_runtime_seconds": 67.84, "estimated_runtime_seconds": 83.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 50, "time_step": 20, "task_type": "general", "resource_demand": {"cpu_cores": 6.38, "memory_mb": 11452.12, "io_bandwidth_mbps": 407.51, "network_bandwidth_mbps": 41.84}, "execution_time": {"base_runtime_seconds": 155.82, "estimated_runtime_seconds": 177.99}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 51, "time_step": 2, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.24, "memory_mb": 9553.46, "io_bandwidth_mbps": 133.38, "network_bandwidth_mbps": 39.82}, "execution_time": {"base_runtime_seconds": 403.0, "estimated_runtime_seconds": 452.79}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 52, "time_step": 1, "task_type": "general", "resource_demand": {"cpu_cores": 5.68, "memory_mb": 8703.36, "io_bandwidth_mbps": 140.09, "network_bandwidth_mbps": 57.07}, "execution_time": {"base_runtime_seconds": 350.16, "estimated_runtime_seconds": 416.47}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 53, "time_step": 8, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.44, "memory_mb": 6432.13, "io_bandwidth_mbps": 126.42, "network_bandwidth_mbps": 91.72}, "execution_time": {"base_runtime_seconds": 229.15, "estimated_runtime_seconds": 255.49}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 54, "time_step": 10, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.25, "memory_mb": 4571.93, "io_bandwidth_mbps": 448.72, "network_bandwidth_mbps": 702.24}, "execution_time": {"base_runtime_seconds": 127.77, "estimated_runtime_seconds": 156.34}, "dependencies": [3, 1], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 55, "time_step": 20, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.65, "memory_mb": 6006.3, "io_bandwidth_mbps": 486.92, "network_bandwidth_mbps": 761.55}, "execution_time": {"base_runtime_seconds": 182.02, "estimated_runtime_seconds": 231.78}, "dependencies": [12, 54, 45], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 56, "time_step": 0, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.59, "memory_mb": 2546.01, "io_bandwidth_mbps": 392.65, "network_bandwidth_mbps": 80.1}, "execution_time": {"base_runtime_seconds": 203.66, "estimated_runtime_seconds": 244.74}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 57, "time_step": 11, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.3, "memory_mb": 8321.83, "io_bandwidth_mbps": 289.18, "network_bandwidth_mbps": 46.3}, "execution_time": {"base_runtime_seconds": 150.7, "estimated_runtime_seconds": 166.86}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 58, "time_step": 13, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.78, "memory_mb": 4079.72, "io_bandwidth_mbps": 1238.19, "network_bandwidth_mbps": 91.87}, "execution_time": {"base_runtime_seconds": 835.36, "estimated_runtime_seconds": 949.73}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 59, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.24, "memory_mb": 10886.91, "io_bandwidth_mbps": 262.58, "network_bandwidth_mbps": 17.56}, "execution_time": {"base_runtime_seconds": 554.35, "estimated_runtime_seconds": 644.05}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 60, "time_step": 11, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.87, "memory_mb": 1389.48, "io_bandwidth_mbps": 682.21, "network_bandwidth_mbps": 93.71}, "execution_time": {"base_runtime_seconds": 614.43, "estimated_runtime_seconds": 736.86}, "dependencies": [2, 24], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 61, "time_step": 4, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.03, "memory_mb": 2115.68, "io_bandwidth_mbps": 1315.27, "network_bandwidth_mbps": 31.45}, "execution_time": {"base_runtime_seconds": 540.61, "estimated_runtime_seconds": 665.42}, "dependencies": [3, 32], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 62, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.81, "memory_mb": 2642.86, "io_bandwidth_mbps": 490.09, "network_bandwidth_mbps": 275.67}, "execution_time": {"base_runtime_seconds": 443.94, "estimated_runtime_seconds": 509.05}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 63, "time_step": 5, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.73, "memory_mb": 25696.76, "io_bandwidth_mbps": 70.81, "network_bandwidth_mbps": 39.81}, "execution_time": {"base_runtime_seconds": 180.69, "estimated_runtime_seconds": 214.32}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 64, "time_step": 2, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.92, "memory_mb": 32455.7, "io_bandwidth_mbps": 100.09, "network_bandwidth_mbps": 14.37}, "execution_time": {"base_runtime_seconds": 351.05, "estimated_runtime_seconds": 395.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 65, "time_step": 5, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 8.68, "memory_mb": 3042.63, "io_bandwidth_mbps": 236.37, "network_bandwidth_mbps": 21.18}, "execution_time": {"base_runtime_seconds": 174.44, "estimated_runtime_seconds": 219.08}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 66, "time_step": 15, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.82, "memory_mb": 7862.27, "io_bandwidth_mbps": 419.24, "network_bandwidth_mbps": 70.5}, "execution_time": {"base_runtime_seconds": 88.04, "estimated_runtime_seconds": 114.21}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 67, "time_step": 15, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.15, "memory_mb": 31308.84, "io_bandwidth_mbps": 110.39, "network_bandwidth_mbps": 49.63}, "execution_time": {"base_runtime_seconds": 523.27, "estimated_runtime_seconds": 651.78}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 68, "time_step": 7, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.04, "memory_mb": 4627.14, "io_bandwidth_mbps": 221.86, "network_bandwidth_mbps": 890.23}, "execution_time": {"base_runtime_seconds": 372.89, "estimated_runtime_seconds": 475.67}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 69, "time_step": 1, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.29, "memory_mb": 1351.26, "io_bandwidth_mbps": 684.39, "network_bandwidth_mbps": 30.44}, "execution_time": {"base_runtime_seconds": 847.6, "estimated_runtime_seconds": 975.52}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 70, "time_step": 19, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.09, "memory_mb": 32130.47, "io_bandwidth_mbps": 130.01, "network_bandwidth_mbps": 32.03}, "execution_time": {"base_runtime_seconds": 495.88, "estimated_runtime_seconds": 636.45}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 71, "time_step": 20, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.43, "memory_mb": 7319.43, "io_bandwidth_mbps": 380.89, "network_bandwidth_mbps": 49.51}, "execution_time": {"base_runtime_seconds": 138.39, "estimated_runtime_seconds": 164.05}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 72, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.2, "memory_mb": 3077.06, "io_bandwidth_mbps": 470.55, "network_bandwidth_mbps": 39.65}, "execution_time": {"base_runtime_seconds": 280.31, "estimated_runtime_seconds": 357.3}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 73, "time_step": 1, "task_type": "general", "resource_demand": {"cpu_cores": 5.78, "memory_mb": 12208.98, "io_bandwidth_mbps": 114.04, "network_bandwidth_mbps": 178.09}, "execution_time": {"base_runtime_seconds": 412.87, "estimated_runtime_seconds": 459.03}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 74, "time_step": 13, "task_type": "general", "resource_demand": {"cpu_cores": 5.02, "memory_mb": 3280.14, "io_bandwidth_mbps": 339.25, "network_bandwidth_mbps": 52.17}, "execution_time": {"base_runtime_seconds": 292.11, "estimated_runtime_seconds": 373.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 75, "time_step": 9, "task_type": "general", "resource_demand": {"cpu_cores": 6.98, "memory_mb": 14544.24, "io_bandwidth_mbps": 567.86, "network_bandwidth_mbps": 73.07}, "execution_time": {"base_runtime_seconds": 130.59, "estimated_runtime_seconds": 168.08}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 76, "time_step": 2, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.86, "memory_mb": 4431.13, "io_bandwidth_mbps": 181.51, "network_bandwidth_mbps": 89.5}, "execution_time": {"base_runtime_seconds": 229.36, "estimated_runtime_seconds": 281.32}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 77, "time_step": 17, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.8, "memory_mb": 3274.37, "io_bandwidth_mbps": 347.77, "network_bandwidth_mbps": 428.38}, "execution_time": {"base_runtime_seconds": 311.58, "estimated_runtime_seconds": 401.06}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 78, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.39, "memory_mb": 7924.04, "io_bandwidth_mbps": 249.27, "network_bandwidth_mbps": 87.62}, "execution_time": {"base_runtime_seconds": 149.24, "estimated_runtime_seconds": 189.45}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 79, "time_step": 11, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.21, "memory_mb": 2733.49, "io_bandwidth_mbps": 294.17, "network_bandwidth_mbps": 885.29}, "execution_time": {"base_runtime_seconds": 300.73, "estimated_runtime_seconds": 359.47}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 80, "time_step": 4, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.5, "memory_mb": 2349.59, "io_bandwidth_mbps": 592.0, "network_bandwidth_mbps": 47.25}, "execution_time": {"base_runtime_seconds": 231.05, "estimated_runtime_seconds": 280.06}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 81, "time_step": 12, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.17, "memory_mb": 2844.15, "io_bandwidth_mbps": 731.63, "network_bandwidth_mbps": 49.57}, "execution_time": {"base_runtime_seconds": 298.22, "estimated_runtime_seconds": 378.44}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 82, "time_step": 4, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.87, "memory_mb": 6064.44, "io_bandwidth_mbps": 143.83, "network_bandwidth_mbps": 68.89}, "execution_time": {"base_runtime_seconds": 161.25, "estimated_runtime_seconds": 202.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 83, "time_step": 13, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.63, "memory_mb": 2939.82, "io_bandwidth_mbps": 174.13, "network_bandwidth_mbps": 997.09}, "execution_time": {"base_runtime_seconds": 109.17, "estimated_runtime_seconds": 129.93}, "dependencies": [52, 50, 39], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 84, "time_step": 7, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.67, "memory_mb": 1568.38, "io_bandwidth_mbps": 1075.84, "network_bandwidth_mbps": 41.68}, "execution_time": {"base_runtime_seconds": 891.51, "estimated_runtime_seconds": 1067.84}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 85, "time_step": 19, "task_type": "general", "resource_demand": {"cpu_cores": 4.66, "memory_mb": 6284.93, "io_bandwidth_mbps": 427.59, "network_bandwidth_mbps": 173.4}, "execution_time": {"base_runtime_seconds": 310.34, "estimated_runtime_seconds": 342.44}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 86, "time_step": 16, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.65, "memory_mb": 11323.91, "io_bandwidth_mbps": 230.93, "network_bandwidth_mbps": 31.02}, "execution_time": {"base_runtime_seconds": 517.5, "estimated_runtime_seconds": 579.86}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 87, "time_step": 19, "task_type": "general", "resource_demand": {"cpu_cores": 1.06, "memory_mb": 11976.06, "io_bandwidth_mbps": 475.84, "network_bandwidth_mbps": 17.59}, "execution_time": {"base_runtime_seconds": 138.42, "estimated_runtime_seconds": 159.63}, "dependencies": [28, 3, 76], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 88, "time_step": 20, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.51, "memory_mb": 18379.78, "io_bandwidth_mbps": 293.74, "network_bandwidth_mbps": 30.62}, "execution_time": {"base_runtime_seconds": 279.06, "estimated_runtime_seconds": 324.38}, "dependencies": [11], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 89, "time_step": 14, "task_type": "general", "resource_demand": {"cpu_cores": 3.29, "memory_mb": 4352.92, "io_bandwidth_mbps": 208.87, "network_bandwidth_mbps": 189.87}, "execution_time": {"base_runtime_seconds": 66.91, "estimated_runtime_seconds": 78.69}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 90, "time_step": 11, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 12.01, "memory_mb": 6240.38, "io_bandwidth_mbps": 394.4, "network_bandwidth_mbps": 72.28}, "execution_time": {"base_runtime_seconds": 70.0, "estimated_runtime_seconds": 82.77}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 91, "time_step": 15, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.6, "memory_mb": 21564.14, "io_bandwidth_mbps": 58.77, "network_bandwidth_mbps": 42.52}, "execution_time": {"base_runtime_seconds": 367.13, "estimated_runtime_seconds": 440.08}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 92, "time_step": 14, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.96, "memory_mb": 30672.21, "io_bandwidth_mbps": 282.65, "network_bandwidth_mbps": 45.66}, "execution_time": {"base_runtime_seconds": 399.09, "estimated_runtime_seconds": 450.09}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 93, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 7.93, "memory_mb": 12900.05, "io_bandwidth_mbps": 741.17, "network_bandwidth_mbps": 113.98}, "execution_time": {"base_runtime_seconds": 322.13, "estimated_runtime_seconds": 374.88}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 94, "time_step": 8, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.21, "memory_mb": 12823.05, "io_bandwidth_mbps": 121.27, "network_bandwidth_mbps": 41.46}, "execution_time": {"base_runtime_seconds": 473.41, "estimated_runtime_seconds": 565.5}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 95, "time_step": 9, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.87, "memory_mb": 2274.42, "io_bandwidth_mbps": 432.04, "network_bandwidth_mbps": 62.76}, "execution_time": {"base_runtime_seconds": 155.09, "estimated_runtime_seconds": 200.59}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 96, "time_step": 14, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.63, "memory_mb": 3063.29, "io_bandwidth_mbps": 127.02, "network_bandwidth_mbps": 79.37}, "execution_time": {"base_runtime_seconds": 255.43, "estimated_runtime_seconds": 296.02}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 97, "time_step": 4, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.31, "memory_mb": 19603.75, "io_bandwidth_mbps": 81.94, "network_bandwidth_mbps": 16.42}, "execution_time": {"base_runtime_seconds": 587.85, "estimated_runtime_seconds": 656.15}, "dependencies": [81, 54], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 98, "time_step": 4, "task_type": "general", "resource_demand": {"cpu_cores": 4.13, "memory_mb": 9603.25, "io_bandwidth_mbps": 628.44, "network_bandwidth_mbps": 129.19}, "execution_time": {"base_runtime_seconds": 123.06, "estimated_runtime_seconds": 138.71}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 99, "time_step": 9, "task_type": "general", "resource_demand": {"cpu_cores": 6.97, "memory_mb": 4928.76, "io_bandwidth_mbps": 559.25, "network_bandwidth_mbps": 51.32}, "execution_time": {"base_runtime_seconds": 222.25, "estimated_runtime_seconds": 251.54}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 100, "time_step": 7, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.76, "memory_mb": 3334.31, "io_bandwidth_mbps": 1207.0, "network_bandwidth_mbps": 79.49}, "execution_time": {"base_runtime_seconds": 810.23, "estimated_runtime_seconds": 905.79}, "dependencies": [4], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 101, "time_step": 16, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.16, "memory_mb": 3460.01, "io_bandwidth_mbps": 1157.93, "network_bandwidth_mbps": 85.21}, "execution_time": {"base_runtime_seconds": 541.93, "estimated_runtime_seconds": 656.71}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 102, "time_step": 16, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.75, "memory_mb": 6087.99, "io_bandwidth_mbps": 499.82, "network_bandwidth_mbps": 160.45}, "execution_time": {"base_runtime_seconds": 256.34, "estimated_runtime_seconds": 293.71}, "dependencies": [80, 20, 73], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 103, "time_step": 13, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.44, "memory_mb": 4042.71, "io_bandwidth_mbps": 1393.15, "network_bandwidth_mbps": 85.02}, "execution_time": {"base_runtime_seconds": 725.0, "estimated_runtime_seconds": 914.93}, "dependencies": [60], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 104, "time_step": 1, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.04, "memory_mb": 2564.8, "io_bandwidth_mbps": 1174.74, "network_bandwidth_mbps": 55.43}, "execution_time": {"base_runtime_seconds": 457.23, "estimated_runtime_seconds": 585.37}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 105, "time_step": 18, "task_type": "general", "resource_demand": {"cpu_cores": 3.94, "memory_mb": 13345.47, "io_bandwidth_mbps": 745.26, "network_bandwidth_mbps": 19.75}, "execution_time": {"base_runtime_seconds": 187.99, "estimated_runtime_seconds": 228.27}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 106, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.94, "memory_mb": 2391.46, "io_bandwidth_mbps": 101.04, "network_bandwidth_mbps": 487.42}, "execution_time": {"base_runtime_seconds": 282.04, "estimated_runtime_seconds": 363.23}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 107, "time_step": 1, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.39, "memory_mb": 1644.64, "io_bandwidth_mbps": 492.96, "network_bandwidth_mbps": 463.63}, "execution_time": {"base_runtime_seconds": 406.84, "estimated_runtime_seconds": 457.3}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 108, "time_step": 13, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.97, "memory_mb": 8054.83, "io_bandwidth_mbps": 271.28, "network_bandwidth_mbps": 21.53}, "execution_time": {"base_runtime_seconds": 138.28, "estimated_runtime_seconds": 176.02}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 109, "time_step": 12, "task_type": "general", "resource_demand": {"cpu_cores": 2.82, "memory_mb": 9592.75, "io_bandwidth_mbps": 556.94, "network_bandwidth_mbps": 59.11}, "execution_time": {"base_runtime_seconds": 304.5, "estimated_runtime_seconds": 375.16}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 110, "time_step": 1, "task_type": "general", "resource_demand": {"cpu_cores": 2.6, "memory_mb": 6471.54, "io_bandwidth_mbps": 572.15, "network_bandwidth_mbps": 92.24}, "execution_time": {"base_runtime_seconds": 120.26, "estimated_runtime_seconds": 140.6}, "dependencies": [65, 74], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 111, "time_step": 14, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 12.02, "memory_mb": 6143.89, "io_bandwidth_mbps": 291.75, "network_bandwidth_mbps": 17.25}, "execution_time": {"base_runtime_seconds": 109.22, "estimated_runtime_seconds": 121.78}, "dependencies": [18, 72], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 112, "time_step": 16, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.31, "memory_mb": 2174.11, "io_bandwidth_mbps": 396.98, "network_bandwidth_mbps": 415.45}, "execution_time": {"base_runtime_seconds": 289.63, "estimated_runtime_seconds": 335.29}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 113, "time_step": 17, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.42, "memory_mb": 1534.78, "io_bandwidth_mbps": 1221.58, "network_bandwidth_mbps": 49.86}, "execution_time": {"base_runtime_seconds": 783.91, "estimated_runtime_seconds": 881.83}, "dependencies": [79, 98], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 114, "time_step": 11, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.26, "memory_mb": 30846.65, "io_bandwidth_mbps": 99.04, "network_bandwidth_mbps": 48.46}, "execution_time": {"base_runtime_seconds": 576.72, "estimated_runtime_seconds": 723.25}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 115, "time_step": 7, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.31, "memory_mb": 7324.06, "io_bandwidth_mbps": 467.43, "network_bandwidth_mbps": 990.1}, "execution_time": {"base_runtime_seconds": 106.9, "estimated_runtime_seconds": 124.75}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 116, "time_step": 17, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.86, "memory_mb": 6248.61, "io_bandwidth_mbps": 381.47, "network_bandwidth_mbps": 49.45}, "execution_time": {"base_runtime_seconds": 232.67, "estimated_runtime_seconds": 266.69}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 117, "time_step": 14, "task_type": "general", "resource_demand": {"cpu_cores": 2.27, "memory_mb": 4449.97, "io_bandwidth_mbps": 562.99, "network_bandwidth_mbps": 55.16}, "execution_time": {"base_runtime_seconds": 352.65, "estimated_runtime_seconds": 448.33}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 118, "time_step": 17, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.23, "memory_mb": 8725.19, "io_bandwidth_mbps": 219.97, "network_bandwidth_mbps": 14.1}, "execution_time": {"base_runtime_seconds": 283.39, "estimated_runtime_seconds": 315.33}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 119, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 6.57, "memory_mb": 5229.96, "io_bandwidth_mbps": 795.44, "network_bandwidth_mbps": 23.15}, "execution_time": {"base_runtime_seconds": 346.85, "estimated_runtime_seconds": 447.67}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 120, "time_step": 11, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.59, "memory_mb": 2754.4, "io_bandwidth_mbps": 268.73, "network_bandwidth_mbps": 34.22}, "execution_time": {"base_runtime_seconds": 201.69, "estimated_runtime_seconds": 225.81}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 121, "time_step": 17, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.57, "memory_mb": 2646.31, "io_bandwidth_mbps": 363.41, "network_bandwidth_mbps": 980.27}, "execution_time": {"base_runtime_seconds": 405.62, "estimated_runtime_seconds": 454.53}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 122, "time_step": 10, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.35, "memory_mb": 30839.23, "io_bandwidth_mbps": 287.54, "network_bandwidth_mbps": 12.11}, "execution_time": {"base_runtime_seconds": 231.62, "estimated_runtime_seconds": 266.93}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 123, "time_step": 16, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.96, "memory_mb": 6463.96, "io_bandwidth_mbps": 318.95, "network_bandwidth_mbps": 74.29}, "execution_time": {"base_runtime_seconds": 237.43, "estimated_runtime_seconds": 295.89}, "dependencies": [33], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 124, "time_step": 0, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.02, "memory_mb": 2703.1, "io_bandwidth_mbps": 1317.32, "network_bandwidth_mbps": 39.55}, "execution_time": {"base_runtime_seconds": 667.2, "estimated_runtime_seconds": 849.07}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 125, "time_step": 15, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.63, "memory_mb": 8076.6, "io_bandwidth_mbps": 407.29, "network_bandwidth_mbps": 41.9}, "execution_time": {"base_runtime_seconds": 190.8, "estimated_runtime_seconds": 234.17}, "dependencies": [7, 28, 90], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 126, "time_step": 3, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.49, "memory_mb": 2573.23, "io_bandwidth_mbps": 1167.84, "network_bandwidth_mbps": 84.56}, "execution_time": {"base_runtime_seconds": 602.75, "estimated_runtime_seconds": 696.32}, "dependencies": [62, 93], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 127, "time_step": 18, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.89, "memory_mb": 25171.65, "io_bandwidth_mbps": 262.53, "network_bandwidth_mbps": 28.14}, "execution_time": {"base_runtime_seconds": 426.04, "estimated_runtime_seconds": 520.05}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 128, "time_step": 11, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.03, "memory_mb": 2582.05, "io_bandwidth_mbps": 476.03, "network_bandwidth_mbps": 56.39}, "execution_time": {"base_runtime_seconds": 178.3, "estimated_runtime_seconds": 197.75}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 129, "time_step": 7, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.99, "memory_mb": 1771.37, "io_bandwidth_mbps": 1259.81, "network_bandwidth_mbps": 67.11}, "execution_time": {"base_runtime_seconds": 736.4, "estimated_runtime_seconds": 913.32}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 130, "time_step": 16, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.56, "memory_mb": 2063.85, "io_bandwidth_mbps": 1780.69, "network_bandwidth_mbps": 38.24}, "execution_time": {"base_runtime_seconds": 688.84, "estimated_runtime_seconds": 873.64}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 131, "time_step": 7, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.52, "memory_mb": 13750.24, "io_bandwidth_mbps": 100.64, "network_bandwidth_mbps": 26.5}, "execution_time": {"base_runtime_seconds": 353.22, "estimated_runtime_seconds": 416.69}, "dependencies": [113, 104], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 132, "time_step": 10, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 8.43, "memory_mb": 5752.25, "io_bandwidth_mbps": 256.14, "network_bandwidth_mbps": 35.72}, "execution_time": {"base_runtime_seconds": 217.72, "estimated_runtime_seconds": 253.65}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 133, "time_step": 9, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.25, "memory_mb": 3285.22, "io_bandwidth_mbps": 1999.99, "network_bandwidth_mbps": 87.49}, "execution_time": {"base_runtime_seconds": 410.26, "estimated_runtime_seconds": 491.25}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 134, "time_step": 10, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.1, "memory_mb": 2931.23, "io_bandwidth_mbps": 640.92, "network_bandwidth_mbps": 67.15}, "execution_time": {"base_runtime_seconds": 236.7, "estimated_runtime_seconds": 297.8}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 135, "time_step": 12, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.68, "memory_mb": 8155.09, "io_bandwidth_mbps": 444.9, "network_bandwidth_mbps": 52.5}, "execution_time": {"base_runtime_seconds": 127.9, "estimated_runtime_seconds": 163.61}, "dependencies": [42, 110, 92], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 136, "time_step": 14, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.13, "memory_mb": 2411.93, "io_bandwidth_mbps": 466.43, "network_bandwidth_mbps": 264.23}, "execution_time": {"base_runtime_seconds": 429.63, "estimated_runtime_seconds": 516.4}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 137, "time_step": 15, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.21, "memory_mb": 11248.28, "io_bandwidth_mbps": 182.29, "network_bandwidth_mbps": 46.28}, "execution_time": {"base_runtime_seconds": 513.32, "estimated_runtime_seconds": 621.39}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 138, "time_step": 11, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.12, "memory_mb": 24576.71, "io_bandwidth_mbps": 148.31, "network_bandwidth_mbps": 34.26}, "execution_time": {"base_runtime_seconds": 565.69, "estimated_runtime_seconds": 685.48}, "dependencies": [12, 104], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 139, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.34, "memory_mb": 8111.76, "io_bandwidth_mbps": 201.09, "network_bandwidth_mbps": 268.01}, "execution_time": {"base_runtime_seconds": 173.47, "estimated_runtime_seconds": 203.66}, "dependencies": [44], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 140, "time_step": 19, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.79, "memory_mb": 6125.51, "io_bandwidth_mbps": 240.83, "network_bandwidth_mbps": 468.84}, "execution_time": {"base_runtime_seconds": 314.74, "estimated_runtime_seconds": 379.32}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 141, "time_step": 17, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.4, "memory_mb": 7429.35, "io_bandwidth_mbps": 277.72, "network_bandwidth_mbps": 59.66}, "execution_time": {"base_runtime_seconds": 173.28, "estimated_runtime_seconds": 203.25}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 142, "time_step": 3, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.64, "memory_mb": 1099.82, "io_bandwidth_mbps": 210.2, "network_bandwidth_mbps": 123.35}, "execution_time": {"base_runtime_seconds": 267.35, "estimated_runtime_seconds": 318.9}, "dependencies": [94], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 143, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 3.36, "memory_mb": 16135.52, "io_bandwidth_mbps": 671.54, "network_bandwidth_mbps": 42.7}, "execution_time": {"base_runtime_seconds": 459.77, "estimated_runtime_seconds": 557.44}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 144, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.89, "memory_mb": 26843.98, "io_bandwidth_mbps": 109.17, "network_bandwidth_mbps": 41.48}, "execution_time": {"base_runtime_seconds": 513.94, "estimated_runtime_seconds": 581.04}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 145, "time_step": 10, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.31, "memory_mb": 1102.66, "io_bandwidth_mbps": 1354.24, "network_bandwidth_mbps": 68.58}, "execution_time": {"base_runtime_seconds": 648.47, "estimated_runtime_seconds": 807.98}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 146, "time_step": 8, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 7.14, "memory_mb": 3780.45, "io_bandwidth_mbps": 377.93, "network_bandwidth_mbps": 15.93}, "execution_time": {"base_runtime_seconds": 186.53, "estimated_runtime_seconds": 220.48}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 147, "time_step": 12, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.89, "memory_mb": 7718.69, "io_bandwidth_mbps": 488.24, "network_bandwidth_mbps": 539.63}, "execution_time": {"base_runtime_seconds": 172.63, "estimated_runtime_seconds": 201.18}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 148, "time_step": 15, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.62, "memory_mb": 16198.8, "io_bandwidth_mbps": 177.54, "network_bandwidth_mbps": 33.45}, "execution_time": {"base_runtime_seconds": 365.83, "estimated_runtime_seconds": 413.86}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 149, "time_step": 20, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.06, "memory_mb": 2880.68, "io_bandwidth_mbps": 1150.51, "network_bandwidth_mbps": 95.65}, "execution_time": {"base_runtime_seconds": 370.82, "estimated_runtime_seconds": 435.79}, "dependencies": [83, 94], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 150, "time_step": 0, "task_type": "general", "resource_demand": {"cpu_cores": 3.21, "memory_mb": 10032.71, "io_bandwidth_mbps": 662.22, "network_bandwidth_mbps": 91.82}, "execution_time": {"base_runtime_seconds": 192.15, "estimated_runtime_seconds": 231.7}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 151, "time_step": 0, "task_type": "general", "resource_demand": {"cpu_cores": 1.4, "memory_mb": 12623.33, "io_bandwidth_mbps": 379.64, "network_bandwidth_mbps": 65.29}, "execution_time": {"base_runtime_seconds": 333.06, "estimated_runtime_seconds": 393.36}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 152, "time_step": 15, "task_type": "general", "resource_demand": {"cpu_cores": 2.08, "memory_mb": 9046.93, "io_bandwidth_mbps": 404.83, "network_bandwidth_mbps": 36.02}, "execution_time": {"base_runtime_seconds": 445.67, "estimated_runtime_seconds": 511.41}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 153, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.15, "memory_mb": 20631.41, "io_bandwidth_mbps": 167.45, "network_bandwidth_mbps": 26.89}, "execution_time": {"base_runtime_seconds": 423.1, "estimated_runtime_seconds": 508.29}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 154, "time_step": 18, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.16, "memory_mb": 2304.0, "io_bandwidth_mbps": 325.21, "network_bandwidth_mbps": 97.75}, "execution_time": {"base_runtime_seconds": 281.36, "estimated_runtime_seconds": 360.93}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 155, "time_step": 13, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.8, "memory_mb": 23644.01, "io_bandwidth_mbps": 50.83, "network_bandwidth_mbps": 14.82}, "execution_time": {"base_runtime_seconds": 493.17, "estimated_runtime_seconds": 571.3}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 156, "time_step": 1, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.62, "memory_mb": 2185.47, "io_bandwidth_mbps": 874.18, "network_bandwidth_mbps": 78.61}, "execution_time": {"base_runtime_seconds": 740.86, "estimated_runtime_seconds": 824.71}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 157, "time_step": 8, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.95, "memory_mb": 7688.61, "io_bandwidth_mbps": 314.42, "network_bandwidth_mbps": 983.39}, "execution_time": {"base_runtime_seconds": 215.58, "estimated_runtime_seconds": 277.89}, "dependencies": [63, 101], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 158, "time_step": 6, "task_type": "general", "resource_demand": {"cpu_cores": 6.15, "memory_mb": 3930.13, "io_bandwidth_mbps": 397.4, "network_bandwidth_mbps": 154.36}, "execution_time": {"base_runtime_seconds": 284.78, "estimated_runtime_seconds": 323.71}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 159, "time_step": 4, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 9.66, "memory_mb": 3808.41, "io_bandwidth_mbps": 472.99, "network_bandwidth_mbps": 51.99}, "execution_time": {"base_runtime_seconds": 72.21, "estimated_runtime_seconds": 80.56}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 160, "time_step": 7, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 5.05, "memory_mb": 7375.31, "io_bandwidth_mbps": 184.67, "network_bandwidth_mbps": 79.0}, "execution_time": {"base_runtime_seconds": 123.59, "estimated_runtime_seconds": 151.43}, "dependencies": [144, 74], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 161, "time_step": 15, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.17, "memory_mb": 2069.47, "io_bandwidth_mbps": 310.19, "network_bandwidth_mbps": 633.03}, "execution_time": {"base_runtime_seconds": 225.59, "estimated_runtime_seconds": 265.98}, "dependencies": [90, 143, 20], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 162, "time_step": 4, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 8.37, "memory_mb": 4407.56, "io_bandwidth_mbps": 245.55, "network_bandwidth_mbps": 13.09}, "execution_time": {"base_runtime_seconds": 68.51, "estimated_runtime_seconds": 85.38}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 163, "time_step": 4, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.98, "memory_mb": 5901.5, "io_bandwidth_mbps": 303.43, "network_bandwidth_mbps": 854.58}, "execution_time": {"base_runtime_seconds": 231.74, "estimated_runtime_seconds": 271.37}, "dependencies": [1, 116, 26], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 164, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.89, "memory_mb": 27833.22, "io_bandwidth_mbps": 208.63, "network_bandwidth_mbps": 16.77}, "execution_time": {"base_runtime_seconds": 560.32, "estimated_runtime_seconds": 673.17}, "dependencies": [36, 69, 87], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 165, "time_step": 18, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.93, "memory_mb": 10908.91, "io_bandwidth_mbps": 286.19, "network_bandwidth_mbps": 17.94}, "execution_time": {"base_runtime_seconds": 200.95, "estimated_runtime_seconds": 226.43}, "dependencies": [127, 151, 114], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 166, "time_step": 13, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.11, "memory_mb": 23546.44, "io_bandwidth_mbps": 50.91, "network_bandwidth_mbps": 14.32}, "execution_time": {"base_runtime_seconds": 227.75, "estimated_runtime_seconds": 264.07}, "dependencies": [59], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 167, "time_step": 17, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.5, "memory_mb": 6882.35, "io_bandwidth_mbps": 193.09, "network_bandwidth_mbps": 78.97}, "execution_time": {"base_runtime_seconds": 244.89, "estimated_runtime_seconds": 289.19}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 168, "time_step": 2, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.91, "memory_mb": 2192.91, "io_bandwidth_mbps": 247.69, "network_bandwidth_mbps": 67.59}, "execution_time": {"base_runtime_seconds": 64.07, "estimated_runtime_seconds": 76.01}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 169, "time_step": 9, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.55, "memory_mb": 5390.18, "io_bandwidth_mbps": 197.47, "network_bandwidth_mbps": 99.24}, "execution_time": {"base_runtime_seconds": 98.6, "estimated_runtime_seconds": 109.57}, "dependencies": [98, 152, 122], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 170, "time_step": 14, "task_type": "general", "resource_demand": {"cpu_cores": 4.78, "memory_mb": 12972.34, "io_bandwidth_mbps": 571.18, "network_bandwidth_mbps": 192.4}, "execution_time": {"base_runtime_seconds": 245.91, "estimated_runtime_seconds": 270.82}, "dependencies": [99, 36, 163], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 171, "time_step": 6, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.26, "memory_mb": 3675.76, "io_bandwidth_mbps": 172.68, "network_bandwidth_mbps": 17.0}, "execution_time": {"base_runtime_seconds": 175.93, "estimated_runtime_seconds": 208.35}, "dependencies": [78, 21], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 172, "time_step": 5, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.06, "memory_mb": 4436.65, "io_bandwidth_mbps": 338.82, "network_bandwidth_mbps": 312.9}, "execution_time": {"base_runtime_seconds": 151.56, "estimated_runtime_seconds": 173.71}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 173, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.63, "memory_mb": 21831.58, "io_bandwidth_mbps": 191.5, "network_bandwidth_mbps": 33.29}, "execution_time": {"base_runtime_seconds": 294.76, "estimated_runtime_seconds": 331.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 174, "time_step": 13, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 4.49, "memory_mb": 3695.43, "io_bandwidth_mbps": 227.04, "network_bandwidth_mbps": 40.77}, "execution_time": {"base_runtime_seconds": 120.47, "estimated_runtime_seconds": 156.33}, "dependencies": [171, 39, 168], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 175, "time_step": 4, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.45, "memory_mb": 6219.87, "io_bandwidth_mbps": 475.36, "network_bandwidth_mbps": 197.14}, "execution_time": {"base_runtime_seconds": 129.94, "estimated_runtime_seconds": 165.78}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 176, "time_step": 7, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.26, "memory_mb": 4342.07, "io_bandwidth_mbps": 197.7, "network_bandwidth_mbps": 190.1}, "execution_time": {"base_runtime_seconds": 140.16, "estimated_runtime_seconds": 159.23}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 177, "time_step": 10, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.47, "memory_mb": 7661.16, "io_bandwidth_mbps": 300.37, "network_bandwidth_mbps": 15.49}, "execution_time": {"base_runtime_seconds": 260.55, "estimated_runtime_seconds": 320.45}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 178, "time_step": 6, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.32, "memory_mb": 23198.85, "io_bandwidth_mbps": 69.63, "network_bandwidth_mbps": 15.7}, "execution_time": {"base_runtime_seconds": 249.66, "estimated_runtime_seconds": 308.59}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 179, "time_step": 0, "task_type": "general", "resource_demand": {"cpu_cores": 1.71, "memory_mb": 3170.02, "io_bandwidth_mbps": 568.34, "network_bandwidth_mbps": 89.93}, "execution_time": {"base_runtime_seconds": 332.3, "estimated_runtime_seconds": 411.08}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 180, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.91, "memory_mb": 20821.44, "io_bandwidth_mbps": 140.55, "network_bandwidth_mbps": 39.11}, "execution_time": {"base_runtime_seconds": 182.74, "estimated_runtime_seconds": 218.07}, "dependencies": [149], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 181, "time_step": 6, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.54, "memory_mb": 5960.04, "io_bandwidth_mbps": 319.27, "network_bandwidth_mbps": 219.99}, "execution_time": {"base_runtime_seconds": 417.28, "estimated_runtime_seconds": 468.15}, "dependencies": [40, 150], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 182, "time_step": 12, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.36, "memory_mb": 20231.74, "io_bandwidth_mbps": 129.45, "network_bandwidth_mbps": 29.36}, "execution_time": {"base_runtime_seconds": 591.29, "estimated_runtime_seconds": 763.39}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 183, "time_step": 19, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.4, "memory_mb": 3272.24, "io_bandwidth_mbps": 1756.92, "network_bandwidth_mbps": 59.09}, "execution_time": {"base_runtime_seconds": 802.71, "estimated_runtime_seconds": 918.08}, "dependencies": [117, 84, 87], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 184, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.43, "memory_mb": 20251.89, "io_bandwidth_mbps": 124.99, "network_bandwidth_mbps": 13.0}, "execution_time": {"base_runtime_seconds": 159.05, "estimated_runtime_seconds": 180.86}, "dependencies": [80], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 185, "time_step": 15, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.93, "memory_mb": 1240.04, "io_bandwidth_mbps": 776.07, "network_bandwidth_mbps": 71.85}, "execution_time": {"base_runtime_seconds": 884.9, "estimated_runtime_seconds": 1102.36}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 186, "time_step": 6, "task_type": "general", "resource_demand": {"cpu_cores": 4.94, "memory_mb": 13211.21, "io_bandwidth_mbps": 376.22, "network_bandwidth_mbps": 12.01}, "execution_time": {"base_runtime_seconds": 312.65, "estimated_runtime_seconds": 352.03}, "dependencies": [100], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 187, "time_step": 9, "task_type": "general", "resource_demand": {"cpu_cores": 6.94, "memory_mb": 6399.67, "io_bandwidth_mbps": 241.45, "network_bandwidth_mbps": 133.06}, "execution_time": {"base_runtime_seconds": 194.61, "estimated_runtime_seconds": 227.29}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 188, "time_step": 18, "task_type": "general", "resource_demand": {"cpu_cores": 7.87, "memory_mb": 3314.72, "io_bandwidth_mbps": 576.46, "network_bandwidth_mbps": 180.72}, "execution_time": {"base_runtime_seconds": 206.89, "estimated_runtime_seconds": 229.55}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 189, "time_step": 16, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 10.72, "memory_mb": 3179.59, "io_bandwidth_mbps": 182.65, "network_bandwidth_mbps": 90.64}, "execution_time": {"base_runtime_seconds": 98.38, "estimated_runtime_seconds": 113.79}, "dependencies": [91], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 190, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.21, "memory_mb": 21077.63, "io_bandwidth_mbps": 216.43, "network_bandwidth_mbps": 48.41}, "execution_time": {"base_runtime_seconds": 540.34, "estimated_runtime_seconds": 668.72}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 191, "time_step": 11, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.42, "memory_mb": 5873.39, "io_bandwidth_mbps": 150.21, "network_bandwidth_mbps": 79.32}, "execution_time": {"base_runtime_seconds": 251.59, "estimated_runtime_seconds": 285.07}, "dependencies": [131], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 192, "time_step": 16, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.84, "memory_mb": 2019.68, "io_bandwidth_mbps": 929.77, "network_bandwidth_mbps": 83.11}, "execution_time": {"base_runtime_seconds": 381.5, "estimated_runtime_seconds": 472.27}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 193, "time_step": 13, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.25, "memory_mb": 5200.04, "io_bandwidth_mbps": 387.76, "network_bandwidth_mbps": 27.44}, "execution_time": {"base_runtime_seconds": 121.73, "estimated_runtime_seconds": 140.81}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 194, "time_step": 16, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.81, "memory_mb": 5173.46, "io_bandwidth_mbps": 431.43, "network_bandwidth_mbps": 903.15}, "execution_time": {"base_runtime_seconds": 385.48, "estimated_runtime_seconds": 450.38}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 195, "time_step": 12, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.04, "memory_mb": 1458.2, "io_bandwidth_mbps": 1378.13, "network_bandwidth_mbps": 44.65}, "execution_time": {"base_runtime_seconds": 539.37, "estimated_runtime_seconds": 632.85}, "dependencies": [120, 31], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 196, "time_step": 20, "task_type": "general", "resource_demand": {"cpu_cores": 7.55, "memory_mb": 7639.3, "io_bandwidth_mbps": 412.76, "network_bandwidth_mbps": 82.74}, "execution_time": {"base_runtime_seconds": 360.99, "estimated_runtime_seconds": 464.12}, "dependencies": [40, 189], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 197, "time_step": 16, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.64, "memory_mb": 3848.74, "io_bandwidth_mbps": 148.82, "network_bandwidth_mbps": 85.56}, "execution_time": {"base_runtime_seconds": 295.17, "estimated_runtime_seconds": 377.09}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 198, "time_step": 13, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.34, "memory_mb": 2926.81, "io_bandwidth_mbps": 923.76, "network_bandwidth_mbps": 96.39}, "execution_time": {"base_runtime_seconds": 257.89, "estimated_runtime_seconds": 330.7}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 199, "time_step": 11, "task_type": "general", "resource_demand": {"cpu_cores": 7.88, "memory_mb": 15339.11, "io_bandwidth_mbps": 401.15, "network_bandwidth_mbps": 167.96}, "execution_time": {"base_runtime_seconds": 410.79, "estimated_runtime_seconds": 505.01}, "dependencies": [82, 173, 50], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 200, "time_step": 2, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 15.46, "memory_mb": 7151.1, "io_bandwidth_mbps": 287.83, "network_bandwidth_mbps": 58.18}, "execution_time": {"base_runtime_seconds": 144.22, "estimated_runtime_seconds": 166.84}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}]}