{"metadata": {"total_tasks": 50, "generated_at": "2025-08-02T23:12:14.728303", "dependency_probability": 0.2, "task_types": ["cpu_intensive", "memory_intensive", "io_intensive", "network_intensive", "general"]}, "tasks": [{"id": 1, "time_step": 5, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.65, "memory_mb": 2166.32, "io_bandwidth_mbps": 1274.13, "network_bandwidth_mbps": 24.97}, "execution_time": {"base_runtime_seconds": 623.42, "estimated_runtime_seconds": 690.56}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 2, "time_step": 5, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 14.23, "memory_mb": 3132.91, "io_bandwidth_mbps": 312.05, "network_bandwidth_mbps": 98.28}, "execution_time": {"base_runtime_seconds": 87.95, "estimated_runtime_seconds": 100.99}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 3, "time_step": 5, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.33, "memory_mb": 5471.19, "io_bandwidth_mbps": 273.37, "network_bandwidth_mbps": 860.02}, "execution_time": {"base_runtime_seconds": 443.4, "estimated_runtime_seconds": 558.36}, "dependencies": [2], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 4, "time_step": 4, "task_type": "general", "resource_demand": {"cpu_cores": 1.7, "memory_mb": 7114.29, "io_bandwidth_mbps": 354.21, "network_bandwidth_mbps": 22.57}, "execution_time": {"base_runtime_seconds": 468.57, "estimated_runtime_seconds": 553.35}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 5, "time_step": 0, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.68, "memory_mb": 28528.48, "io_bandwidth_mbps": 157.61, "network_bandwidth_mbps": 20.82}, "execution_time": {"base_runtime_seconds": 324.61, "estimated_runtime_seconds": 414.18}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 6, "time_step": 4, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.11, "memory_mb": 3797.36, "io_bandwidth_mbps": 1961.91, "network_bandwidth_mbps": 43.43}, "execution_time": {"base_runtime_seconds": 663.02, "estimated_runtime_seconds": 781.19}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 7, "time_step": 5, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.99, "memory_mb": 3898.45, "io_bandwidth_mbps": 1399.72, "network_bandwidth_mbps": 46.07}, "execution_time": {"base_runtime_seconds": 195.96, "estimated_runtime_seconds": 232.47}, "dependencies": [1], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 8, "time_step": 4, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.6, "memory_mb": 2859.03, "io_bandwidth_mbps": 407.47, "network_bandwidth_mbps": 912.18}, "execution_time": {"base_runtime_seconds": 231.18, "estimated_runtime_seconds": 276.21}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 9, "time_step": 2, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.93, "memory_mb": 11459.42, "io_bandwidth_mbps": 178.87, "network_bandwidth_mbps": 33.93}, "execution_time": {"base_runtime_seconds": 374.64, "estimated_runtime_seconds": 437.19}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 10, "time_step": 4, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.65, "memory_mb": 2903.62, "io_bandwidth_mbps": 132.5, "network_bandwidth_mbps": 38.04}, "execution_time": {"base_runtime_seconds": 133.58, "estimated_runtime_seconds": 168.12}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 11, "time_step": 4, "task_type": "general", "resource_demand": {"cpu_cores": 7.69, "memory_mb": 14858.52, "io_bandwidth_mbps": 795.56, "network_bandwidth_mbps": 108.11}, "execution_time": {"base_runtime_seconds": 439.99, "estimated_runtime_seconds": 525.46}, "dependencies": [9, 2, 6], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 12, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 1.61, "memory_mb": 10495.31, "io_bandwidth_mbps": 562.05, "network_bandwidth_mbps": 195.7}, "execution_time": {"base_runtime_seconds": 207.81, "estimated_runtime_seconds": 249.87}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 13, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.74, "memory_mb": 12299.42, "io_bandwidth_mbps": 288.08, "network_bandwidth_mbps": 16.04}, "execution_time": {"base_runtime_seconds": 413.85, "estimated_runtime_seconds": 498.84}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 14, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.02, "memory_mb": 13047.35, "io_bandwidth_mbps": 226.55, "network_bandwidth_mbps": 34.45}, "execution_time": {"base_runtime_seconds": 478.17, "estimated_runtime_seconds": 602.28}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 15, "time_step": 2, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 13.83, "memory_mb": 3265.02, "io_bandwidth_mbps": 392.97, "network_bandwidth_mbps": 97.17}, "execution_time": {"base_runtime_seconds": 236.32, "estimated_runtime_seconds": 260.8}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 16, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.43, "memory_mb": 13444.08, "io_bandwidth_mbps": 100.92, "network_bandwidth_mbps": 35.27}, "execution_time": {"base_runtime_seconds": 200.66, "estimated_runtime_seconds": 241.99}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 17, "time_step": 4, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.57, "memory_mb": 2242.65, "io_bandwidth_mbps": 1277.7, "network_bandwidth_mbps": 51.2}, "execution_time": {"base_runtime_seconds": 776.06, "estimated_runtime_seconds": 928.47}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 18, "time_step": 3, "task_type": "general", "resource_demand": {"cpu_cores": 7.15, "memory_mb": 11590.57, "io_bandwidth_mbps": 204.62, "network_bandwidth_mbps": 50.37}, "execution_time": {"base_runtime_seconds": 466.82, "estimated_runtime_seconds": 595.84}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 19, "time_step": 4, "task_type": "general", "resource_demand": {"cpu_cores": 1.07, "memory_mb": 2619.41, "io_bandwidth_mbps": 352.9, "network_bandwidth_mbps": 125.45}, "execution_time": {"base_runtime_seconds": 151.59, "estimated_runtime_seconds": 173.78}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 20, "time_step": 0, "task_type": "general", "resource_demand": {"cpu_cores": 3.12, "memory_mb": 14273.53, "io_bandwidth_mbps": 491.3, "network_bandwidth_mbps": 44.89}, "execution_time": {"base_runtime_seconds": 286.39, "estimated_runtime_seconds": 363.33}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 21, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.59, "memory_mb": 16100.45, "io_bandwidth_mbps": 165.6, "network_bandwidth_mbps": 17.76}, "execution_time": {"base_runtime_seconds": 433.05, "estimated_runtime_seconds": 503.23}, "dependencies": [4, 5], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 22, "time_step": 3, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.44, "memory_mb": 1749.57, "io_bandwidth_mbps": 612.22, "network_bandwidth_mbps": 18.05}, "execution_time": {"base_runtime_seconds": 770.04, "estimated_runtime_seconds": 960.89}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 23, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 1.45, "memory_mb": 31961.45, "io_bandwidth_mbps": 135.36, "network_bandwidth_mbps": 30.16}, "execution_time": {"base_runtime_seconds": 229.96, "estimated_runtime_seconds": 268.39}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 24, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 6.38, "memory_mb": 5503.17, "io_bandwidth_mbps": 435.6, "network_bandwidth_mbps": 76.76}, "execution_time": {"base_runtime_seconds": 219.44, "estimated_runtime_seconds": 280.17}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 25, "time_step": 3, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.45, "memory_mb": 7001.35, "io_bandwidth_mbps": 352.91, "network_bandwidth_mbps": 450.99}, "execution_time": {"base_runtime_seconds": 392.77, "estimated_runtime_seconds": 494.92}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 26, "time_step": 0, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.48, "memory_mb": 6549.0, "io_bandwidth_mbps": 107.5, "network_bandwidth_mbps": 759.22}, "execution_time": {"base_runtime_seconds": 239.97, "estimated_runtime_seconds": 291.43}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 27, "time_step": 3, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.05, "memory_mb": 21025.52, "io_bandwidth_mbps": 245.84, "network_bandwidth_mbps": 42.21}, "execution_time": {"base_runtime_seconds": 249.11, "estimated_runtime_seconds": 297.6}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 28, "time_step": 4, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.54, "memory_mb": 3124.36, "io_bandwidth_mbps": 1786.05, "network_bandwidth_mbps": 11.85}, "execution_time": {"base_runtime_seconds": 629.92, "estimated_runtime_seconds": 755.77}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 29, "time_step": 0, "task_type": "general", "resource_demand": {"cpu_cores": 7.26, "memory_mb": 11326.91, "io_bandwidth_mbps": 714.52, "network_bandwidth_mbps": 118.51}, "execution_time": {"base_runtime_seconds": 62.16, "estimated_runtime_seconds": 68.83}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 30, "time_step": 4, "task_type": "general", "resource_demand": {"cpu_cores": 7.77, "memory_mb": 7468.67, "io_bandwidth_mbps": 233.78, "network_bandwidth_mbps": 117.22}, "execution_time": {"base_runtime_seconds": 196.09, "estimated_runtime_seconds": 252.66}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 31, "time_step": 5, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.71, "memory_mb": 2850.0, "io_bandwidth_mbps": 1711.21, "network_bandwidth_mbps": 44.23}, "execution_time": {"base_runtime_seconds": 552.74, "estimated_runtime_seconds": 638.44}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 32, "time_step": 5, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.92, "memory_mb": 1919.77, "io_bandwidth_mbps": 703.41, "network_bandwidth_mbps": 83.01}, "execution_time": {"base_runtime_seconds": 369.61, "estimated_runtime_seconds": 432.6}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 33, "time_step": 3, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.95, "memory_mb": 1523.51, "io_bandwidth_mbps": 894.84, "network_bandwidth_mbps": 98.88}, "execution_time": {"base_runtime_seconds": 791.98, "estimated_runtime_seconds": 891.97}, "dependencies": [2, 21], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 34, "time_step": 2, "task_type": "general", "resource_demand": {"cpu_cores": 7.74, "memory_mb": 15514.52, "io_bandwidth_mbps": 659.28, "network_bandwidth_mbps": 20.09}, "execution_time": {"base_runtime_seconds": 453.66, "estimated_runtime_seconds": 510.86}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 35, "time_step": 2, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 2.08, "memory_mb": 1192.13, "io_bandwidth_mbps": 472.26, "network_bandwidth_mbps": 659.03}, "execution_time": {"base_runtime_seconds": 206.25, "estimated_runtime_seconds": 228.02}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 36, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.14, "memory_mb": 9518.16, "io_bandwidth_mbps": 103.37, "network_bandwidth_mbps": 10.5}, "execution_time": {"base_runtime_seconds": 531.35, "estimated_runtime_seconds": 601.86}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 37, "time_step": 3, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 1.4, "memory_mb": 7715.1, "io_bandwidth_mbps": 371.13, "network_bandwidth_mbps": 956.92}, "execution_time": {"base_runtime_seconds": 111.98, "estimated_runtime_seconds": 143.94}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 38, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 3.95, "memory_mb": 8620.15, "io_bandwidth_mbps": 149.88, "network_bandwidth_mbps": 14.89}, "execution_time": {"base_runtime_seconds": 196.87, "estimated_runtime_seconds": 241.85}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 39, "time_step": 2, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.54, "memory_mb": 7949.69, "io_bandwidth_mbps": 129.95, "network_bandwidth_mbps": 728.19}, "execution_time": {"base_runtime_seconds": 326.34, "estimated_runtime_seconds": 417.51}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 40, "time_step": 2, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.81, "memory_mb": 3987.29, "io_bandwidth_mbps": 1742.11, "network_bandwidth_mbps": 13.54}, "execution_time": {"base_runtime_seconds": 740.89, "estimated_runtime_seconds": 957.06}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 41, "time_step": 1, "task_type": "network_intensive", "resource_demand": {"cpu_cores": 3.01, "memory_mb": 4640.38, "io_bandwidth_mbps": 392.33, "network_bandwidth_mbps": 140.54}, "execution_time": {"base_runtime_seconds": 405.19, "estimated_runtime_seconds": 477.88}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 42, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 2.74, "memory_mb": 3242.54, "io_bandwidth_mbps": 326.46, "network_bandwidth_mbps": 25.66}, "execution_time": {"base_runtime_seconds": 116.59, "estimated_runtime_seconds": 138.1}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 43, "time_step": 4, "task_type": "general", "resource_demand": {"cpu_cores": 3.56, "memory_mb": 12911.17, "io_bandwidth_mbps": 236.62, "network_bandwidth_mbps": 57.66}, "execution_time": {"base_runtime_seconds": 275.79, "estimated_runtime_seconds": 316.02}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 44, "time_step": 2, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.18, "memory_mb": 3551.77, "io_bandwidth_mbps": 779.32, "network_bandwidth_mbps": 79.36}, "execution_time": {"base_runtime_seconds": 503.98, "estimated_runtime_seconds": 635.3}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 45, "time_step": 1, "task_type": "memory_intensive", "resource_demand": {"cpu_cores": 2.03, "memory_mb": 31122.82, "io_bandwidth_mbps": 224.62, "network_bandwidth_mbps": 36.63}, "execution_time": {"base_runtime_seconds": 294.25, "estimated_runtime_seconds": 343.5}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 46, "time_step": 5, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.23, "memory_mb": 1863.68, "io_bandwidth_mbps": 1755.38, "network_bandwidth_mbps": 31.45}, "execution_time": {"base_runtime_seconds": 637.81, "estimated_runtime_seconds": 820.14}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 47, "time_step": 3, "task_type": "cpu_intensive", "resource_demand": {"cpu_cores": 11.92, "memory_mb": 5968.3, "io_bandwidth_mbps": 316.14, "network_bandwidth_mbps": 97.84}, "execution_time": {"base_runtime_seconds": 223.48, "estimated_runtime_seconds": 290.21}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 48, "time_step": 3, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.5, "memory_mb": 1117.71, "io_bandwidth_mbps": 1179.07, "network_bandwidth_mbps": 32.25}, "execution_time": {"base_runtime_seconds": 588.76, "estimated_runtime_seconds": 732.96}, "dependencies": [9], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 49, "time_step": 5, "task_type": "general", "resource_demand": {"cpu_cores": 1.42, "memory_mb": 11809.58, "io_bandwidth_mbps": 582.99, "network_bandwidth_mbps": 124.01}, "execution_time": {"base_runtime_seconds": 89.67, "estimated_runtime_seconds": 106.43}, "dependencies": [35, 47, 5], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}, {"id": 50, "time_step": 3, "task_type": "io_intensive", "resource_demand": {"cpu_cores": 1.39, "memory_mb": 3879.96, "io_bandwidth_mbps": 1817.92, "network_bandwidth_mbps": 21.71}, "execution_time": {"base_runtime_seconds": 590.85, "estimated_runtime_seconds": 681.9}, "dependencies": [], "scheduling_info": {"start_time": 0.0, "end_time": 0.0, "assigned_node": null}}]}