import os
from task_generate import generate_all_scales as generate_tasks
from node_generate import generate_all_scales as generate_nodes

def main():
    """生成所有规模的任务和节点数据集"""
    print("=" * 60)
    print("负载均衡仿真数据集生成器")
    print("=" * 60)
    
    # 创建主输出目录
    os.makedirs("datasets", exist_ok=True)
    
    print("开始生成任务数据集...")
    generate_tasks()
    
    print("\n开始生成节点数据集...")
    generate_nodes()
    
    print("\n" + "=" * 60)
    print("数据集生成完成！")
    print("生成的文件:")
    print("- datasets/tasks_small.json (50个任务)")
    print("- datasets/tasks_medium.json (200个任务)")
    print("- datasets/tasks_large.json (500个任务)")
    print("- datasets/nodes_small.json (10个节点)")
    print("- datasets/nodes_medium.json (50个节点)")
    print("- datasets/nodes_large.json (100个节点)")
    print("=" * 60)

if __name__ == "__main__":
    main()