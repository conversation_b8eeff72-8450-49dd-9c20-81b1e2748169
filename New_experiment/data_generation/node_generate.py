import json
import random
import os
from datetime import datetime
from typing import Dict, List, Any
from enum import Enum

class NodeType(Enum):
    CPU_INTENSIVE = "cpu_intensive"
    MEMORY_INTENSIVE = "memory_intensive"
    IO_INTENSIVE = "io_intensive"
    NETWORK_INTENSIVE = "network_intensive"
    GENERAL = "general"

class NodeGenerator:
    def __init__(self):
        # 节点类型配置 - 定义每种类型的资源容量范围
        self.node_configs = {
            NodeType.CPU_INTENSIVE: {
                "cpu_range": (16, 64),          # CPU核心数
                "memory_range": (16384, 65536), # MB
                "io_range": (1000, 3000),       # MB/s
                "network_range": (1000, 5000)   # MB/s
            },
            NodeType.MEMORY_INTENSIVE: {
                "cpu_range": (8, 32),
                "memory_range": (65536, 262144),
                "io_range": (500, 2000),
                "network_range": (1000, 3000)
            },
            NodeType.IO_INTENSIVE: {
                "cpu_range": (4, 16),
                "memory_range": (8192, 32768),
                "io_range": (3000, 10000),
                "network_range": (1000, 5000)
            },
            NodeType.NETWORK_INTENSIVE: {
                "cpu_range": (8, 32),
                "memory_range": (16384, 65536),
                "io_range": (1000, 4000),
                "network_range": (5000, 20000)
            },
            NodeType.GENERAL: {
                "cpu_range": (8, 32),
                "memory_range": (16384, 65536),
                "io_range": (1000, 4000),
                "network_range": (1000, 5000)
            }
        }
    
    def generate_node(self, node_id: int, node_type: NodeType) -> Dict[str, Any]:
        """生成单个节点"""
        config = self.node_configs[node_type]
        
        # 生成总容量
        total_cpu = round(random.uniform(*config["cpu_range"]), 2)
        total_memory = round(random.uniform(*config["memory_range"]), 2)
        total_io = round(random.uniform(*config["io_range"]), 2)
        total_network = round(random.uniform(*config["network_range"]), 2)
        
        # 生成当前使用量（10%-80%的使用率）
        usage_ratio = random.uniform(0.1, 0.8)
        
        current_cpu = round(total_cpu * usage_ratio, 2)
        current_memory = round(total_memory * usage_ratio, 2)
        current_io = round(total_io * usage_ratio, 2)
        current_network = round(total_network * usage_ratio, 2)
        
        # 计算可用容量
        available_cpu = round(total_cpu - current_cpu, 2)
        available_memory = round(total_memory - current_memory, 2)
        available_io = round(total_io - current_io, 2)
        available_network = round(total_network - current_network, 2)
        
        return {
            "id": node_id,
            "node_type": node_type.value,
            "total_capacity": {
                "cpu_cores": total_cpu,
                "memory_mb": total_memory,
                "io_bandwidth_mbps": total_io,
                "network_bandwidth_mbps": total_network
            },
            "current_usage": {
                "cpu_cores": current_cpu,
                "memory_mb": current_memory,
                "io_bandwidth_mbps": current_io,
                "network_bandwidth_mbps": current_network
            },
            "available_capacity": {
                "cpu_cores": available_cpu,
                "memory_mb": available_memory,
                "io_bandwidth_mbps": available_io,
                "network_bandwidth_mbps": available_network
            },
            "utilization_ratio": round(usage_ratio, 3),
            "status": "active",
            "performance_factor": round(random.uniform(0.8, 1.2), 2)  # 性能因子
        }
    
    def generate_dataset(self, num_nodes: int) -> Dict[str, Any]:
        """生成完整的节点数据集"""
        nodes = []
        node_types = list(NodeType)
        
        # 按比例分配节点类型
        type_distribution = [0.2, 0.2, 0.2, 0.2, 0.2]  # 每种类型20%
        
        for i in range(num_nodes):
            # 根据分布选择节点类型
            type_idx = random.choices(range(len(node_types)), weights=type_distribution)[0]
            node_type = node_types[type_idx]
            
            node = self.generate_node(i + 1, node_type)
            nodes.append(node)
        
        # 创建数据集结构
        dataset = {
            "metadata": {
                "total_nodes": num_nodes,
                "generated_at": datetime.now().isoformat(),
                "node_types": [t.value for t in NodeType]
            },
            "nodes": nodes
        }
        
        return dataset

def generate_all_scales():
    """生成三种规模的数据集"""
    generator = NodeGenerator()
    
    scales = {
        "small": {"num_nodes": 10},
        "medium": {"num_nodes": 50},
        "large": {"num_nodes": 100}
    }
    
    # 创建输出目录
    output_dir = "datasets"
    os.makedirs(output_dir, exist_ok=True)
    
    for scale_name, params in scales.items():
        print(f"生成{scale_name}规模节点数据集...")
        dataset = generator.generate_dataset(**params)
        
        # 保存文件
        filename = os.path.join(output_dir, f"nodes_{scale_name}.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)
        
        print(f"已保存: {filename}")
        print(f"节点数量: {params['num_nodes']}")
        print("-" * 40)

if __name__ == "__main__":
    generate_all_scales()