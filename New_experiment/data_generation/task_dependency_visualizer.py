import pygraphviz as pgv
import matplotlib.pyplot as plt
from collections import defaultdict
import json
import os
import numpy as np
import matplotlib.colors as mcolors
from matplotlib.patches import Patch

# 基础颜色配置 - 对应5种任务类型
BASE_COLORS = {
    'cpu_intensive': ['#FFC8C8', '#FF0000'],      # 红色渐变
    'memory_intensive': ['#C8FFC8', '#00FF00'],   # 绿色渐变
    'io_intensive': ['#C8C8FF', '#0000FF'],       # 蓝色渐变
    'network_intensive': ['#FFFFC8', '#FFFF00'], # 黄色渐变
    'general': ['#E8D4FF', '#8000FF']             # 紫色渐变
}

class TaskDependencyVisualizer:
    def __init__(self):
        self.tasks = {}
        self.color_gradients = {}
        
    def load_task_data(self, json_file_path):
        """加载JSON格式的任务数据"""
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 转换为内部格式
        self.tasks = {}
        for task in data['tasks']:
            task_id = task['id']
            self.tasks[task_id] = {
                'cpu': task['resource_demand']['cpu_cores'],
                'memory': task['resource_demand']['memory_mb'],
                'io': task['resource_demand']['io_bandwidth_mbps'],
                'network': task['resource_demand']['network_bandwidth_mbps'],
                'deps': task['dependencies'],
                'type': task['task_type'],
                'base_runtime': task['execution_time']['base_runtime_seconds'],
                'estimated_runtime': task['execution_time']['estimated_runtime_seconds']
            }
        
        print(f"已加载 {len(self.tasks)} 个任务")
        return self.tasks

    def generate_color_gradients(self, max_level):
        """根据最大层级动态生成颜色梯度"""
        max_level = max(max_level, 2)
        
        color_gradients = {}
        for task_type, colors in BASE_COLORS.items():
            start_color = mcolors.to_rgb(colors[0])
            end_color = mcolors.to_rgb(colors[1])
            
            gradient = []
            for i in range(max_level):
                t = i / (max_level - 1)
                r = start_color[0] + (end_color[0] - start_color[0]) * t
                g = start_color[1] + (end_color[1] - start_color[1]) * t
                b = start_color[2] + (end_color[2] - start_color[2]) * t
                
                color = mcolors.to_hex((r, g, b))
                gradient.append(color)
            
            color_gradients[task_type] = gradient
        
        return color_gradients

    def calculate_dependency_levels(self, tasks):
        """计算任务层级"""
        levels = {}
        
        # 初始化没有依赖的任务
        for task_id in tasks:
            if not tasks[task_id]['deps']:
                levels[task_id] = 1
        
        # 迭代计算层级
        updated = True
        while updated:
            updated = False
            for task_id in tasks:
                if task_id in levels:
                    continue
                deps = tasks[task_id]['deps']
                if all(dep in levels for dep in deps):
                    levels[task_id] = max(levels[dep] for dep in deps) + 1
                    updated = True
        
        # 处理可能的循环依赖
        for task_id in tasks:
            if task_id not in levels:
                levels[task_id] = 1
        
        return levels

    def get_connected_component(self, tasks, start_task_id, max_nodes=50):
        """获取与指定任务相连的子图"""
        component = {}
        visited = set()
        queue = [start_task_id]
        
        while queue and len(component) < max_nodes:
            task_id = queue.pop(0)
            if task_id in visited or task_id not in tasks:
                continue
            
            visited.add(task_id)
            component[task_id] = tasks[task_id]
            
            # 将所有依赖和被依赖的任务加入队列
            for dep in tasks[task_id]['deps']:
                if dep not in visited and len(component) < max_nodes:
                    queue.append(dep)
            
            # 获取依赖当前任务的所有任务
            for other_id, other_task in tasks.items():
                if task_id in other_task['deps'] and other_id not in visited and len(component) < max_nodes:
                    queue.append(other_id)
        
        return component

    def get_type_subset(self, tasks, task_type, max_nodes=50):
        """获取特定类型的任务子集"""
        matching_tasks = {task_id: task for task_id, task in tasks.items() 
                         if task['type'] == task_type}
        
        if len(matching_tasks) <= max_nodes:
            return matching_tasks
        
        # 如果匹配的任务过多，随机选择
        import random
        selected_ids = random.sample(list(matching_tasks.keys()), max_nodes)
        return {task_id: tasks[task_id] for task_id in selected_ids}

    def visualize_full_dependency_graph(self, output_file="full_dependency_graph.png"):
        """可视化完整的任务依赖图"""
        if not self.tasks:
            print("请先加载任务数据")
            return False
        
        # 计算层级
        levels = self.calculate_dependency_levels(self.tasks)
        max_level = max(levels.values(), default=1)
        
        # 生成颜色梯度
        color_gradients = self.generate_color_gradients(max_level)
        
        print(f"最大依赖层级: {max_level}")
        
        # 创建图
        G = pgv.AGraph(directed=True, strict=False)
        
        # 按层级对节点进行分组
        nodes_by_level = defaultdict(list)
        for task_id, task in self.tasks.items():
            level = levels.get(task_id, 1)
            nodes_by_level[level].append(task_id)
        
        # 设置图的全局属性
        G.graph_attr.update({
            'rankdir': 'TB',
            'dpi': '300',
            'splines': 'polyline',
            'overlap': 'false',
            'ranksep': '0.5',
            'nodesep': '0.3',
            'compound': 'true',
            'fontname': 'Arial'
        })
        
        # 添加节点 - 按层级添加
        for level in range(1, max_level + 1):
            with G.subgraph(name=f'level_{level}') as subg:
                subg.graph_attr.update({'rank': 'same'})
                
                for task_id in nodes_by_level[level]:
                    task = self.tasks[task_id]
                    gradient = color_gradients[task['type']]
                    color_index = level - 1
                    color = gradient[color_index]
                    
                    # 创建节点标签
                    label = (f"Task {task_id}\n"
                           f"Type: {task['type']}\n"
                           f"CPU: {task['cpu']:.1f}\n"
                           f"Mem: {task['memory']:.0f}MB\n"
                           f"Level: {level}")
                    
                    subg.add_node(task_id,
                                fillcolor=color,
                                style='filled',
                                shape='box',
                                fontname='Arial',
                                fontsize='10',
                                label=label)
        
        # 添加边
        for task_id, task in self.tasks.items():
            for dep in task['deps']:
                if dep in self.tasks:
                    G.add_edge(dep, task_id)
        
        # 添加隐形边来强制层级关系
        for level in range(1, max_level):
            if nodes_by_level[level] and nodes_by_level[level + 1]:
                G.add_edge(nodes_by_level[level][0], nodes_by_level[level + 1][0],
                          style='invis', constraint='true', weight='10')
        
        try:
            G.layout(prog='dot', args='-Gordering=out')
            G.draw(output_file)
            print(f"已生成完整依赖图: {output_file}")
            return True
        except Exception as e:
            print(f"生成图像失败: {e}")
            return False

    def visualize_connected_component(self, output_file="connected_component.png"):
        """可视化连通分量子图"""
        if not self.tasks:
            print("请先加载任务数据")
            return False
        
        # 计算层级
        levels = self.calculate_dependency_levels(self.tasks)
        max_level = max(levels.values(), default=1)
        
        # 生成颜色梯度
        color_gradients = self.generate_color_gradients(max_level)
        
        # 找出依赖最多的任务
        most_deps_task = max(self.tasks.items(), 
                           key=lambda x: len(x[1]['deps']), 
                           default=(None, None))[0]
        
        if not most_deps_task:
            print("没有找到有依赖关系的任务")
            return False
        
        # 获取连通分量
        component_subset = self.get_connected_component(self.tasks, most_deps_task, 30)
        
        return self._visualize_subset(component_subset, levels, output_file,
                                    f"Connected Component (Starting: Task {most_deps_task})",
                                    color_gradients)

    def visualize_by_type(self, task_type, output_file=None):
        """按任务类型可视化"""
        if not self.tasks:
            print("请先加载任务数据")
            return False
        
        if output_file is None:
            output_file = f"tasks_type_{task_type}.png"
        
        # 计算层级
        levels = self.calculate_dependency_levels(self.tasks)
        max_level = max(levels.values(), default=1)
        
        # 生成颜色梯度
        color_gradients = self.generate_color_gradients(max_level)
        
        # 获取特定类型的任务
        type_subset = self.get_type_subset(self.tasks, task_type, 50)
        
        if not type_subset:
            print(f"没有找到类型为 {task_type} 的任务")
            return False
        
        return self._visualize_subset(type_subset, levels, output_file,
                                    f"{task_type.upper()} Tasks",
                                    color_gradients)

    def _visualize_subset(self, tasks_subset, levels, output_file, title, color_gradients):
        """可视化任务子集的通用方法"""
        G = pgv.AGraph(directed=True, strict=False)
        
        # 按层级对节点进行分组
        nodes_by_level = defaultdict(list)
        for task_id, task in tasks_subset.items():
            level = levels.get(task_id, 1)
            nodes_by_level[level].append(task_id)
        
        max_level = max(nodes_by_level.keys()) if nodes_by_level else 1
        
        # 设置图的全局属性
        G.graph_attr.update({
            'label': title,
            'labelloc': 't',
            'fontsize': '16',
            'fontname': 'Arial',
            'dpi': '300',
            'rankdir': 'TB',
            'ranksep': '0.8',
            'nodesep': '0.4',
            'splines': 'polyline',
            'overlap': 'false'
        })
        
        # 添加节点
        for level in range(1, max_level + 1):
            with G.subgraph(name=f'level_{level}') as subg:
                subg.graph_attr.update({'rank': 'same'})
                
                for task_id in nodes_by_level[level]:
                    task = tasks_subset[task_id]
                    gradient = color_gradients[task['type']]
                    color_index = min(level - 1, len(gradient) - 1)
                    color = gradient[color_index]
                    
                    # 创建简化的节点标签
                    label = (f"T{task_id}\n"
                           f"{task['type'][:3].upper()}\n"
                           f"L{level}")
                    
                    subg.add_node(task_id,
                                fillcolor=color,
                                style='filled',
                                shape='box',
                                fontname='Arial',
                                fontsize='12',
                                label=label)
        
        # 添加边
        for task_id, task in tasks_subset.items():
            for dep in task['deps']:
                if dep in tasks_subset:
                    G.add_edge(dep, task_id)
        
        try:
            G.layout(prog='dot')
            G.draw(output_file)
            print(f"已生成图像: {output_file}")
            return True
        except Exception as e:
            print(f"生成图像失败: {e}")
            return False

    def create_legend(self, output_file="legend.png"):
        """创建图例"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 任务类型图例
        legend_elements = []
        type_names = {
            'cpu_intensive': 'CPU Intensive',
            'memory_intensive': 'Memory Intensive', 
            'io_intensive': 'I/O Intensive',
            'network_intensive': 'Network Intensive',
            'general': 'General'
        }
        
        for task_type, name in type_names.items():
            color = BASE_COLORS[task_type][1]  # 使用最深色
            legend_elements.append(Patch(facecolor=color, label=name))
        
        ax.legend(handles=legend_elements, loc='center', fontsize=14,
                 title="Task Types", title_fontsize=16)
        ax.axis('off')
        
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"已生成图例: {output_file}")

    def generate_all_visualizations(self, dataset_name):
        """生成所有可视化图表"""
        output_dir = f"visualizations_{dataset_name}"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"为数据集 {dataset_name} 生成可视化图表...")
        
        # 1. 完整依赖图
        self.visualize_full_dependency_graph(
            os.path.join(output_dir, "full_dependency_graph.png"))
        
        # 2. 连通分量
        self.visualize_connected_component(
            os.path.join(output_dir, "connected_component.png"))
        
        # 3. 按类型分组
        task_types = ['cpu_intensive', 'memory_intensive', 'io_intensive', 
                     'network_intensive', 'general']
        
        for task_type in task_types:
            self.visualize_by_type(task_type, 
                os.path.join(output_dir, f"tasks_{task_type}.png"))
        
        # 4. 图例
        self.create_legend(os.path.join(output_dir, "legend.png"))
        
        print(f"所有可视化图表已生成到目录: {output_dir}")

def main():
    """主函数 - 为所有数据集生成可视化"""
    visualizer = TaskDependencyVisualizer()
    
    datasets = ['small', 'medium', 'large']
    
    for dataset in datasets:
        json_file = f"datasets/tasks_{dataset}.json"
        
        if os.path.exists(json_file):
            print(f"\n处理数据集: {dataset}")
            visualizer.load_task_data(json_file)
            visualizer.generate_all_visualizations(dataset)
        else:
            print(f"数据集文件不存在: {json_file}")

if __name__ == "__main__":
    main()