import json
import random
import os
from datetime import datetime
from typing import Dict, List, Set, Any
from enum import Enum

class TaskType(Enum):
    CPU_INTENSIVE = "cpu_intensive"
    MEMORY_INTENSIVE = "memory_intensive"
    IO_INTENSIVE = "io_intensive"
    NETWORK_INTENSIVE = "network_intensive"
    GENERAL = "general"

class TaskGenerator:
    def __init__(self):
        # 任务类型配置 - 定义每种类型的资源需求范围
        self.task_configs = {
            TaskType.CPU_INTENSIVE: {
                "cpu_range": (4.0, 16.0),      # CPU核心数
                "memory_range": (2048, 8192),   # MB
                "io_range": (100, 500),         # MB/s
                "network_range": (10, 100),     # MB/s
                "runtime_range": (60, 300)      # 秒
            },
            TaskType.MEMORY_INTENSIVE: {
                "cpu_range": (1.0, 4.0),
                "memory_range": (8192, 32768),
                "io_range": (50, 300),
                "network_range": (10, 50),
                "runtime_range": (120, 600)
            },
            TaskType.IO_INTENSIVE: {
                "cpu_range": (1.0, 2.0),
                "memory_range": (1024, 4096),
                "io_range": (500, 2000),
                "network_range": (10, 100),
                "runtime_range": (180, 900)
            },
            TaskType.NETWORK_INTENSIVE: {
                "cpu_range": (1.0, 4.0),
                "memory_range": (1024, 8192),
                "io_range": (100, 500),
                "network_range": (100, 1000),
                "runtime_range": (90, 450)
            },
            TaskType.GENERAL: {
                "cpu_range": (1.0, 8.0),
                "memory_range": (2048, 16384),
                "io_range": (100, 800),
                "network_range": (10, 200),
                "runtime_range": (60, 480)
            }
        }
    
    def generate_task(self, task_id: int, task_type: TaskType, time_step: int = 0) -> Dict[str, Any]:
        """生成单个任务"""
        config = self.task_configs[task_type]
        
        # 生成资源需求
        cpu_requirement = round(random.uniform(*config["cpu_range"]), 2)
        memory_requirement = round(random.uniform(*config["memory_range"]), 2)
        io_requirement = round(random.uniform(*config["io_range"]), 2)
        network_requirement = round(random.uniform(*config["network_range"]), 2)
        
        # 生成执行时间
        base_runtime = round(random.uniform(*config["runtime_range"]), 2)
        # 预估执行时间增加10-30%的不确定性
        estimated_runtime = round(base_runtime * random.uniform(1.1, 1.3), 2)
        
        return {
            "id": task_id,
            "time_step": time_step,
            "task_type": task_type.value,
            "resource_demand": {
                "cpu_cores": cpu_requirement,
                "memory_mb": memory_requirement,
                "io_bandwidth_mbps": io_requirement,
                "network_bandwidth_mbps": network_requirement
            },
            "execution_time": {
                "base_runtime_seconds": base_runtime,
                "estimated_runtime_seconds": estimated_runtime
            },
            "dependencies": [],  # 稍后添加依赖关系
            "scheduling_info": {
                "start_time": 0.0,
                "end_time": 0.0,
                "assigned_node": None
            }
        }
    
    def add_dependencies(self, tasks: List[Dict], dependency_probability: float = 0.3):
        """为任务添加依赖关系"""
        for i, task in enumerate(tasks):
            if i == 0:  # 第一个任务没有依赖
                continue
            
            # 随机决定是否有依赖
            if random.random() < dependency_probability:
                # 随机选择前面的任务作为依赖
                num_deps = random.randint(1, min(3, i))  # 最多3个依赖
                possible_deps = list(range(i))
                dependencies = random.sample(possible_deps, num_deps)
                task["dependencies"] = [tasks[dep_idx]["id"] for dep_idx in dependencies]
    
    def generate_dataset(self, num_tasks: int, dependency_prob: float = 0.3) -> Dict[str, Any]:
        """生成完整的任务数据集"""
        tasks = []
        task_types = list(TaskType)
        
        # 按比例分配任务类型
        type_distribution = [0.2, 0.2, 0.2, 0.2, 0.2]  # 每种类型20%
        
        for i in range(num_tasks):
            # 根据分布选择任务类型
            type_idx = random.choices(range(len(task_types)), weights=type_distribution)[0]
            task_type = task_types[type_idx]
            
            # 生成时间步（模拟任务到达时间）
            time_step = random.randint(0, max(1, num_tasks // 10))
            
            task = self.generate_task(i + 1, task_type, time_step)
            tasks.append(task)
        
        # 添加依赖关系
        self.add_dependencies(tasks, dependency_prob)
        
        # 创建数据集结构
        dataset = {
            "metadata": {
                "total_tasks": num_tasks,
                "generated_at": datetime.now().isoformat(),
                "dependency_probability": dependency_prob,
                "task_types": [t.value for t in TaskType]
            },
            "tasks": tasks
        }
        
        return dataset

def generate_all_scales():
    """生成三种规模的数据集"""
    generator = TaskGenerator()
    
    scales = {
        "small": {"num_tasks": 50, "dependency_prob": 0.2},
        "medium": {"num_tasks": 200, "dependency_prob": 0.3},
        "large": {"num_tasks": 500, "dependency_prob": 0.4}
    }
    
    # 创建输出目录
    output_dir = "datasets"
    os.makedirs(output_dir, exist_ok=True)
    
    for scale_name, params in scales.items():
        print(f"生成{scale_name}规模数据集...")
        dataset = generator.generate_dataset(**params)
        
        # 保存文件
        filename = os.path.join(output_dir, f"tasks_{scale_name}.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)
        
        print(f"已保存: {filename}")
        print(f"任务数量: {params['num_tasks']}")
        print(f"依赖概率: {params['dependency_prob']}")
        print("-" * 40)

if __name__ == "__main__":
    generate_all_scales()