from task_dependency_visualizer import TaskDependencyVisualizer

def visualize_single_dataset(dataset_name):
    """可视化单个数据集的示例"""
    visualizer = TaskDependencyVisualizer()
    
    # 加载数据
    json_file = f"datasets/tasks_{dataset_name}.json"
    visualizer.load_task_data(json_file)
    
    # 生成完整依赖图
    visualizer.visualize_full_dependency_graph(f"{dataset_name}_full_graph.png")
    
    # 生成连通分量图
    visualizer.visualize_connected_component(f"{dataset_name}_connected.png")
    
    # 生成CPU密集型任务图
    visualizer.visualize_by_type('cpu_intensive', f"{dataset_name}_cpu_tasks.png")
    
    # 生成图例
    visualizer.create_legend(f"{dataset_name}_legend.png")
    
    print(f"数据集 {dataset_name} 的可视化完成")

if __name__ == "__main__":
    # 可视化小规模数据集
    visualize_single_dataset('small')