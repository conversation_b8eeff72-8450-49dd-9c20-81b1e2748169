# ABC算法负载均衡数据生成器

本项目实现了基于人工蜂群算法(Artificial Bee Colony, ABC)的任务调度优化系统，用于生成GNN训练数据。

## 项目概述

该系统将原本的C++版本ABC算法完全替换为Python实现，提供了更好的集成性和可维护性。主要用于解决云计算环境中的任务调度和负载均衡问题。

## 核心功能

### 1. ABC算法实现 (`abc_algorithm_wrapper.py`)
- **完整的人工蜂群算法**：包含雇佣蜂、观察蜂、侦察蜂三个阶段
- **多目标优化**：综合考虑资源利用率、负载均衡度、完成时间
- **约束处理**：智能处理资源约束违反情况
- **自适应搜索**：根据解的质量动态调整搜索策略

### 2. 数据生成器 (`gnn_training_data_generator.py`)
- **GNN训练数据生成**：为图神经网络模型生成高质量训练样本
- **图着色预处理**：支持图着色算法优化任务依赖关系
- **多样化场景**：生成不同规模和复杂度的调度场景
- **性能指标计算**：自动计算各种负载均衡性能指标

## 安装要求

```bash
pip install numpy networkx
```

注意：如果需要运行完整的GNN训练数据生成，还需要安装：
```bash
pip install torch torch-geometric
```

## 快速开始

### 1. 基本使用

```python
from abc_algorithm_wrapper import ABCAlgorithmWrapper
import networkx as nx

# 创建ABC算法实例
abc_wrapper = ABCAlgorithmWrapper(
    colony_size=50,      # 蜂群大小
    max_iterations=100,  # 最大迭代次数
    limit=10            # 放弃阈值
)

# 定义任务和节点
tasks = [
    {
        'id': 0,
        'cpu_req': 2.0,
        'memory_req': 4096,
        'io_req': 100,
        'network_req': 200,
        'type': 'cpu_intensive',
        'execution_time': 15
    }
    # ... 更多任务
]

nodes = [
    {
        'id': 0,
        'cpu_cap': 8.0,
        'memory_cap': 16384,
        'io_cap': 1000,
        'network_cap': 1000,
        'type': 'general',
        'current_load': 0.3
    }
    # ... 更多节点
]

# 创建任务依赖图
dag = nx.DiGraph()
dag.add_nodes_from([0, 1, 2])
dag.add_edge(0, 2)  # 任务0依赖任务2

# 执行任务分配
result = abc_wrapper.allocate_tasks(tasks, nodes, dag)
print(f"分配方案: {result['allocation']}")
print(f"目标函数值: {result['objective_value']}")
```

### 2. 生成训练数据

```python
from gnn_training_data_generator import GNNTrainingDataGenerator, GNNDataConfig

# 配置数据生成参数
config = GNNDataConfig(
    num_samples=1000,           # 生成样本数量
    min_tasks=10,              # 最小任务数
    max_tasks=50,              # 最大任务数
    min_nodes=5,               # 最小节点数
    max_nodes=20,              # 最大节点数
    abc_colony_size=50,        # ABC蜂群大小
    abc_max_iterations=100,    # ABC最大迭代次数
    abc_limit=10              # ABC放弃阈值
)

# 创建数据生成器
generator = GNNTrainingDataGenerator(config)

# 生成单个样本
sample = generator.generate_single_sample()
print(f"生成的样本: {sample}")
```

## 算法参数说明

### ABC算法参数
- **colony_size**: 蜂群大小，影响搜索的广度，建议20-100
- **max_iterations**: 最大迭代次数，影响搜索深度，建议50-200
- **limit**: 放弃阈值，控制何时放弃当前解，建议5-20

### 数据生成参数
- **num_samples**: 生成的训练样本数量
- **min_tasks/max_tasks**: 任务数量范围
- **min_nodes/max_nodes**: 节点数量范围
- **dependency_ratio**: 任务依赖关系密度
- **enable_coloring**: 是否启用图着色预处理

## 测试和验证

### 1. 运行基本测试
```bash
python test_abc_integration.py
```

### 2. 运行数据生成测试
```bash
python test_data_generation.py
```

### 3. 生成完整训练数据
```bash
python generate_gnn_training_data.py --num_samples 5000
```

## 性能指标

系统会自动计算以下性能指标：

1. **资源利用率** (Resource Utilization)
   - 衡量系统资源的使用效率
   - 范围：0-1，越高越好

2. **负载均衡度** (Load Balance)
   - 衡量各节点负载的均匀程度
   - 范围：0-1，越高越好

3. **完成时间** (Makespan)
   - 所有任务完成所需的总时间
   - 越小越好

4. **目标函数值** (Objective Value)
   - 综合性能指标
   - 范围：0-1，越高越好

## 文件结构

```
New_experiment/src/data_generation/
├── abc_algorithm_wrapper.py          # ABC算法核心实现
├── gnn_training_data_generator.py    # GNN数据生成器
├── generate_gnn_training_data.py     # 数据生成主脚本
├── test_abc_integration.py           # ABC算法测试
├── test_data_generation.py           # 数据生成测试
├── gnn_data_loader.py                # 数据加载器
└── README.md                         # 本文档
```

## 算法特性

### 1. 智能初始化
- 优先选择能满足任务资源需求的节点
- 避免生成大量不可行解

### 2. 约束处理
- 对违反资源约束的解进行惩罚而非直接丢弃
- 计算约束违反程度，指导搜索方向

### 3. 多目标优化
- 平衡资源利用率、负载均衡、完成时间
- 可调整权重适应不同优化目标

### 4. 自适应搜索
- 根据解的质量动态调整搜索策略
- 雇佣蜂和观察蜂采用不同的邻域搜索方法

## 常见问题

### Q: 为什么有些样本的目标函数值为0？
A: 这通常表示生成的解违反了资源约束。可以通过以下方式改善：
- 增加节点资源容量
- 减少任务资源需求
- 调整ABC算法参数

### Q: 如何提高算法性能？
A: 可以尝试：
- 增加蜂群大小 (colony_size)
- 增加迭代次数 (max_iterations)
- 调整放弃阈值 (limit)

### Q: 如何自定义适应度函数？
A: 修改 `abc_algorithm_wrapper.py` 中的 `_calculate_fitness` 方法，调整各指标的权重。

## 更新日志

### v2.0.0 (当前版本)
- ✅ 完全替换C++实现为Python版本
- ✅ 改进约束处理机制
- ✅ 增强算法鲁棒性
- ✅ 添加详细测试套件

### v1.0.0 (原版本)
- 基于C++可执行文件的ABC算法调用
- 基本的任务调度功能

## 贡献指南

欢迎提交Issue和Pull Request来改进本项目。在提交代码前，请确保：
1. 运行所有测试用例
2. 遵循Python代码规范
3. 添加必要的文档说明

## 算法详细说明

### ABC算法工作流程

1. **初始化阶段**
   ```
   生成初始蜂群 → 计算适应度 → 初始化试验计数器
   ```

2. **主循环**
   ```
   for iteration in range(max_iterations):
       雇佣蜂阶段()    # 在当前解邻域搜索
       观察蜂阶段()    # 基于概率选择优质解进行搜索
       侦察蜂阶段()    # 替换停滞的解
       更新最佳解()
   ```

3. **终止条件**
   - 达到最大迭代次数
   - 找到满意解
   - 算法收敛

### 适应度函数设计

```python
fitness = 0.4 * resource_utilization +
          0.4 * load_balance +
          0.2 * (1.0 / (1.0 + makespan / 100))
```

权重分配说明：
- **资源利用率 (40%)**：确保系统资源得到充分利用
- **负载均衡 (40%)**：保证各节点负载分布均匀
- **完成时间 (20%)**：优化任务执行效率

## 扩展功能

### 1. 自定义任务类型

```python
# 定义新的任务类型
custom_task_types = {
    'gpu_intensive': {
        'cpu_weight': 0.2,
        'memory_weight': 0.3,
        'gpu_weight': 0.4,
        'network_weight': 0.1
    }
}
```

### 2. 动态负载调整

```python
# 支持动态更新节点负载
def update_node_load(node_id, new_load):
    nodes[node_id]['current_load'] = new_load
```

### 3. 多约束优化

系统支持以下约束类型：
- CPU容量约束
- 内存容量约束
- I/O带宽约束
- 网络带宽约束
- 任务依赖约束
- 节点类型匹配约束

## 性能优化建议

### 1. 参数调优
```python
# 小规模问题 (< 20任务)
abc_wrapper = ABCAlgorithmWrapper(
    colony_size=20,
    max_iterations=50,
    limit=5
)

# 中等规模问题 (20-100任务)
abc_wrapper = ABCAlgorithmWrapper(
    colony_size=50,
    max_iterations=100,
    limit=10
)

# 大规模问题 (> 100任务)
abc_wrapper = ABCAlgorithmWrapper(
    colony_size=100,
    max_iterations=200,
    limit=20
)
```

### 2. 内存优化
- 对于大规模问题，考虑分批处理
- 使用生成器模式减少内存占用
- 定期清理中间结果

### 3. 并行化
```python
# 可以并行化的部分：
# - 适应度计算
# - 邻域搜索
# - 多个独立实验
```

## 实验结果示例

### 测试场景1：小规模 (8任务, 4节点)
```
目标函数值: 0.7360
资源利用率: 0.5722
负载均衡度: 0.9300
完成时间: 48.00
约束违反: 0
```

### 测试场景2：中等规模 (15任务, 8节点)
```
目标函数值: 0.6630
资源利用率: 0.6957
负载均衡度: 0.8206
完成时间: 65.00
约束违反: 0
```

## 故障排除

### 常见错误及解决方案

1. **ModuleNotFoundError: No module named 'torch'**
   ```bash
   # 解决方案：安装PyTorch
   pip install torch
   ```

2. **目标函数值始终为0**
   ```python
   # 检查资源配置是否合理
   total_task_resources = sum(task['cpu_req'] for task in tasks)
   total_node_capacity = sum(node['cpu_cap'] for node in nodes)
   if total_task_resources > total_node_capacity:
       print("资源不足，需要增加节点容量或减少任务需求")
   ```

3. **算法收敛过慢**
   ```python
   # 调整参数
   abc_wrapper = ABCAlgorithmWrapper(
       colony_size=100,  # 增加蜂群大小
       limit=5          # 减少放弃阈值
   )
   ```

## API参考

### ABCAlgorithmWrapper类

#### 构造函数
```python
ABCAlgorithmWrapper(colony_size=50, max_iterations=100, limit=10)
```

#### 主要方法
- `allocate_tasks(tasks, nodes, dag)`: 执行任务分配
- `_calculate_fitness(solution)`: 计算解的适应度
- `_is_feasible(allocation)`: 检查解的可行性

#### 返回值格式
```python
{
    'allocation': {task_id: node_id, ...},
    'objective_value': float,
    'resource_utilization': float,
    'load_balance': float,
    'makespan': float,
    'performance_metrics': {...}
}
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
