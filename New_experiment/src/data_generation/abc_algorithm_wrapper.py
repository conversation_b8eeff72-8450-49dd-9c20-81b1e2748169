import json
import numpy as np
from typing import Dict, List, Any, <PERSON>ple
import networkx as nx
import random
import copy

class ABCAlgorithmWrapper:
    """ABC算法Python实现"""

    def __init__(self, colony_size: int = 50, max_iterations: int = 100, limit: int = 10):
        """
        初始化ABC算法参数

        Args:
            colony_size: 蜂群大小
            max_iterations: 最大迭代次数
            limit: 放弃阈值
        """
        self.colony_size = colony_size
        self.max_iterations = max_iterations
        self.limit = limit
        self.employed_bees = colony_size // 2
        self.onlooker_bees = colony_size // 2

    def allocate_tasks(self, tasks: List[Dict], nodes: List[Dict],
                      dag: nx.DiGraph) -> Dict[str, Any]:
        """执行任务分配"""
        return self._abc_algorithm(tasks, nodes, dag)

    def _abc_algorithm(self, tasks: List[Dict], nodes: List[Dict],
                      dag: nx.DiGraph) -> Dict[str, Any]:
        """ABC算法主函数"""

        # 初始化
        self.tasks = tasks
        self.nodes = nodes
        self.dag = dag
        self.num_tasks = len(tasks)
        self.num_nodes = len(nodes)

        # 计算任务的拓扑排序
        try:
            self.topo_order = list(nx.topological_sort(dag))
        except:
            self.topo_order = [task['id'] for task in tasks]

        # 初始化蜂群
        population = self._initialize_population()
        fitness_values = [self._calculate_fitness(solution) for solution in population]
        trial_counters = [0] * self.colony_size

        best_solution = None
        best_fitness = float('-inf')

        # 主循环
        for iteration in range(self.max_iterations):
            # 雇佣蜂阶段
            for i in range(self.employed_bees):
                new_solution = self._employed_bee_phase(population[i])
                new_fitness = self._calculate_fitness(new_solution)

                if new_fitness > fitness_values[i]:
                    population[i] = new_solution
                    fitness_values[i] = new_fitness
                    trial_counters[i] = 0
                else:
                    trial_counters[i] += 1

            # 观察蜂阶段
            probabilities = self._calculate_probabilities(fitness_values)
            for i in range(self.onlooker_bees):
                selected_index = self._roulette_wheel_selection(probabilities)
                new_solution = self._onlooker_bee_phase(population[selected_index])
                new_fitness = self._calculate_fitness(new_solution)

                if new_fitness > fitness_values[selected_index]:
                    population[selected_index] = new_solution
                    fitness_values[selected_index] = new_fitness
                    trial_counters[selected_index] = 0
                else:
                    trial_counters[selected_index] += 1

            # 侦察蜂阶段
            for i in range(self.colony_size):
                if trial_counters[i] > self.limit:
                    population[i] = self._generate_random_solution()
                    fitness_values[i] = self._calculate_fitness(population[i])
                    trial_counters[i] = 0

            # 更新最佳解
            current_best_idx = np.argmax(fitness_values)
            if fitness_values[current_best_idx] > best_fitness:
                best_fitness = fitness_values[current_best_idx]
                best_solution = copy.deepcopy(population[current_best_idx])

        # 转换解格式并计算性能指标
        allocation = {task_id: best_solution[i] for i, task_id in enumerate(self.topo_order)}
        metrics = self._calculate_performance_metrics(allocation)

        return {
            'allocation': allocation,
            'objective_value': best_fitness,
            'resource_utilization': metrics['resource_utilization'],
            'load_balance': metrics['load_balance'],
            'makespan': metrics['makespan'],
            'performance_metrics': metrics
        }

    def _initialize_population(self) -> List[List[int]]:
        """初始化蜂群种群"""
        population = []
        for _ in range(self.colony_size):
            solution = self._generate_random_solution()
            population.append(solution)
        return population

    def _generate_random_solution(self) -> List[int]:
        """生成随机解（任务到节点的分配）"""
        solution = []
        for i, task_id in enumerate(self.topo_order):
            task = self.tasks[task_id]

            # 找到能够满足任务资源需求的节点
            feasible_nodes = []
            for j, node in enumerate(self.nodes):
                if (task['cpu_req'] <= node['cpu_cap'] and
                    task['memory_req'] <= node['memory_cap'] and
                    task['io_req'] <= node['io_cap'] and
                    task['network_req'] <= node['network_cap']):
                    feasible_nodes.append(j)

            # 如果有可行节点，随机选择一个；否则选择资源最大的节点
            if feasible_nodes:
                node_id = random.choice(feasible_nodes)
            else:
                # 选择总资源容量最大的节点
                node_id = max(range(self.num_nodes),
                             key=lambda j: (self.nodes[j]['cpu_cap'] +
                                          self.nodes[j]['memory_cap'] +
                                          self.nodes[j]['io_cap'] +
                                          self.nodes[j]['network_cap']))

            solution.append(node_id)
        return solution

    def _calculate_fitness(self, solution: List[int]) -> float:
        """计算解的适应度"""
        # 将解转换为分配字典
        allocation = {task_id: solution[i] for i, task_id in enumerate(self.topo_order)}

        # 计算性能指标
        metrics = self._calculate_performance_metrics(allocation)

        # 检查资源约束，如果不可行则给予惩罚
        if not self._is_feasible(allocation):
            # 计算资源超限程度作为惩罚
            penalty = self._calculate_constraint_penalty(allocation)
            return max(0.0, 0.1 - penalty)  # 给不可行解一个很小的适应度

        # 综合适应度函数
        fitness = (
            0.4 * metrics['resource_utilization'] +
            0.4 * metrics['load_balance'] +
            0.2 * (1.0 / (1.0 + metrics['makespan'] / 100))
        )

        return fitness

    def _calculate_constraint_penalty(self, allocation: Dict[str, int]) -> float:
        """计算约束违反的惩罚值"""
        penalty = 0.0

        # 初始化节点负载
        node_loads = {node['id']: {
            'cpu': node['current_load'] * node['cpu_cap'],
            'memory': node['current_load'] * node['memory_cap'],
            'io': node['current_load'] * node['io_cap'],
            'network': node['current_load'] * node['network_cap']
        } for node in self.nodes}

        # 累加任务资源需求
        for task in self.tasks:
            task_id = task['id']
            node_id = allocation[task_id]
            node = self.nodes[node_id]

            node_loads[node_id]['cpu'] += task['cpu_req']
            node_loads[node_id]['memory'] += task['memory_req']
            node_loads[node_id]['io'] += task['io_req']
            node_loads[node_id]['network'] += task['network_req']

            # 计算超限程度
            if node_loads[node_id]['cpu'] > node['cpu_cap']:
                penalty += (node_loads[node_id]['cpu'] - node['cpu_cap']) / node['cpu_cap']
            if node_loads[node_id]['memory'] > node['memory_cap']:
                penalty += (node_loads[node_id]['memory'] - node['memory_cap']) / node['memory_cap']
            if node_loads[node_id]['io'] > node['io_cap']:
                penalty += (node_loads[node_id]['io'] - node['io_cap']) / node['io_cap']
            if node_loads[node_id]['network'] > node['network_cap']:
                penalty += (node_loads[node_id]['network'] - node['network_cap']) / node['network_cap']

        return penalty

    def _is_feasible(self, allocation: Dict[str, int]) -> bool:
        """检查解是否满足资源约束"""
        # 初始化节点负载
        node_loads = {node['id']: {
            'cpu': node['current_load'] * node['cpu_cap'],
            'memory': node['current_load'] * node['memory_cap'],
            'io': node['current_load'] * node['io_cap'],
            'network': node['current_load'] * node['network_cap']
        } for node in self.nodes}

        # 累加任务资源需求
        for task in self.tasks:
            task_id = task['id']
            node_id = allocation[task_id]
            node = self.nodes[node_id]

            node_loads[node_id]['cpu'] += task['cpu_req']
            node_loads[node_id]['memory'] += task['memory_req']
            node_loads[node_id]['io'] += task['io_req']
            node_loads[node_id]['network'] += task['network_req']

            # 检查是否超出容量
            if (node_loads[node_id]['cpu'] > node['cpu_cap'] or
                node_loads[node_id]['memory'] > node['memory_cap'] or
                node_loads[node_id]['io'] > node['io_cap'] or
                node_loads[node_id]['network'] > node['network_cap']):
                return False

        return True

    def _employed_bee_phase(self, solution: List[int]) -> List[int]:
        """雇佣蜂阶段：在当前解的邻域中搜索"""
        new_solution = copy.deepcopy(solution)

        # 随机选择一个任务位置进行修改
        task_index = random.randint(0, len(solution) - 1)

        # 随机选择一个不同的节点
        current_node = solution[task_index]
        available_nodes = [i for i in range(self.num_nodes) if i != current_node]

        if available_nodes:
            new_solution[task_index] = random.choice(available_nodes)

        return new_solution

    def _onlooker_bee_phase(self, solution: List[int]) -> List[int]:
        """观察蜂阶段：基于概率选择的邻域搜索"""
        new_solution = copy.deepcopy(solution)

        # 随机选择1-3个任务位置进行修改
        num_changes = random.randint(1, min(3, len(solution)))
        task_indices = random.sample(range(len(solution)), num_changes)

        for task_index in task_indices:
            # 随机选择一个节点
            new_solution[task_index] = random.randint(0, self.num_nodes - 1)

        return new_solution

    def _calculate_probabilities(self, fitness_values: List[float]) -> List[float]:
        """计算选择概率"""
        min_fitness = min(fitness_values)
        adjusted_fitness = [f - min_fitness + 1e-10 for f in fitness_values]
        total_fitness = sum(adjusted_fitness)

        if total_fitness == 0:
            return [1.0 / len(fitness_values)] * len(fitness_values)

        probabilities = [f / total_fitness for f in adjusted_fitness]
        return probabilities

    def _roulette_wheel_selection(self, probabilities: List[float]) -> int:
        """轮盘赌选择"""
        r = random.random()
        cumulative_prob = 0.0

        for i, prob in enumerate(probabilities):
            cumulative_prob += prob
            if r <= cumulative_prob:
                return i

        return len(probabilities) - 1

    def _calculate_performance_metrics(self, allocation: Dict[str, int]) -> Dict[str, float]:
        """计算性能指标"""

        # 初始化节点负载
        node_loads = {node['id']: {
            'cpu': node['current_load'] * node['cpu_cap'],
            'memory': node['current_load'] * node['memory_cap'],
            'io': node['current_load'] * node['io_cap'],
            'network': node['current_load'] * node['network_cap']
        } for node in self.nodes}

        # 累加任务资源需求
        for task in self.tasks:
            task_id = task['id']
            node_id = allocation[task_id]

            node_loads[node_id]['cpu'] += task['cpu_req']
            node_loads[node_id]['memory'] += task['memory_req']
            node_loads[node_id]['io'] += task['io_req']
            node_loads[node_id]['network'] += task['network_req']

        # 资源利用率
        total_utilization = 0
        for node in self.nodes:
            node_id = node['id']
            utilization = (
                node_loads[node_id]['cpu'] / node['cpu_cap'] +
                node_loads[node_id]['memory'] / node['memory_cap'] +
                node_loads[node_id]['io'] / node['io_cap'] +
                node_loads[node_id]['network'] / node['network_cap']
            ) / 4
            total_utilization += utilization

        resource_utilization = total_utilization / len(self.nodes)

        # 负载均衡度
        node_utilizations = []
        for node in self.nodes:
            node_id = node['id']
            total_load = sum(node_loads[node_id].values())
            total_capacity = sum([
                node['cpu_cap'], node['memory_cap'],
                node['io_cap'], node['network_cap']
            ])
            utilization = total_load / total_capacity if total_capacity > 0 else 0
            node_utilizations.append(utilization)

        if len(node_utilizations) > 1 and np.mean(node_utilizations) > 0:
            load_balance = 1.0 - (np.std(node_utilizations) / np.mean(node_utilizations))
        else:
            load_balance = 1.0

        # Makespan（简化计算）
        node_completion_times = {}
        for node in self.nodes:
            node_id = node['id']
            assigned_tasks = [t for t in self.tasks if allocation.get(t['id']) == node_id]
            total_time = sum(t.get('execution_time', 10) for t in assigned_tasks)
            node_completion_times[node_id] = total_time

        makespan = max(node_completion_times.values()) if node_completion_times else 0

        return {
            'resource_utilization': resource_utilization,
            'load_balance': max(0, load_balance),
            'makespan': makespan
        }

# 测试代码
if __name__ == "__main__":
    # 创建测试数据
    test_tasks = [
        {'id': 0, 'cpu_req': 2.0, 'memory_req': 4096, 'io_req': 100, 'network_req': 200, 'type': 'cpu_intensive', 'execution_time': 15},
        {'id': 1, 'cpu_req': 1.0, 'memory_req': 8192, 'io_req': 50, 'network_req': 100, 'type': 'memory_intensive', 'execution_time': 12},
        {'id': 2, 'cpu_req': 1.5, 'memory_req': 2048, 'io_req': 300, 'network_req': 150, 'type': 'io_intensive', 'execution_time': 18}
    ]

    test_nodes = [
        {'id': 0, 'cpu_cap': 8.0, 'memory_cap': 16384, 'io_cap': 1000, 'network_cap': 1000, 'type': 'general', 'current_load': 0.3},
        {'id': 1, 'cpu_cap': 4.0, 'memory_cap': 32768, 'io_cap': 500, 'network_cap': 800, 'type': 'memory_optimized', 'current_load': 0.2}
    ]

    test_dag = nx.DiGraph()
    test_dag.add_nodes_from([0, 1, 2])
    test_dag.add_edge(0, 2)

    # 测试ABC算法包装器
    print("开始ABC算法测试...")
    abc_wrapper = ABCAlgorithmWrapper(colony_size=20, max_iterations=50, limit=5)
    result = abc_wrapper.allocate_tasks(test_tasks, test_nodes, test_dag)

    print("分配结果:")
    print(json.dumps(result, indent=2))