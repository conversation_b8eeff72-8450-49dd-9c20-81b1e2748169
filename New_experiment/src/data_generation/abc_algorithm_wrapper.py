import json
import subprocess
import tempfile
import os
import numpy as np
from typing import Dict, List, Any
import networkx as nx

class ABCAlgorithmWrapper:
    """ABC算法Python包装器"""
    
    def __init__(self, cpp_executable_path: str = "./abc_allocator"):
        self.cpp_executable_path = cpp_executable_path
        self.check_executable()
    
    def check_executable(self):
        """检查C++可执行文件是否存在"""
        if not os.path.exists(self.cpp_executable_path):
            print(f"警告: C++可执行文件不存在: {self.cpp_executable_path}")
            print("将使用Python备用实现")
    
    def allocate_tasks(self, tasks: List[Dict], nodes: List[Dict], 
                      dag: nx.DiGraph) -> Dict[str, Any]:
        """执行任务分配"""
        if os.path.exists(self.cpp_executable_path):
            return self._call_cpp_abc(tasks, nodes, dag)
        else:
            return self._python_fallback_allocation(tasks, nodes, dag)
    
    def _call_cpp_abc(self, tasks: List[Dict], nodes: List[Dict], 
                     dag: nx.DiGraph) -> Dict[str, Any]:
        """调用C++ ABC算法"""
        try:
            # 准备输入数据
            input_data = {
                'tasks': [
                    {
                        'id': task['id'],
                        'cpu_req': task['cpu_req'],
                        'memory_req': task['memory_req'],
                        'io_req': task['io_req'],
                        'network_req': task['network_req'],
                        'type': task['type']
                    }
                    for task in tasks
                ],
                'nodes': [
                    {
                        'id': node['id'],
                        'cpu_cap': node['cpu_cap'],
                        'memory_cap': node['memory_cap'],
                        'io_cap': node['io_cap'],
                        'network_cap': node['network_cap'],
                        'type': node['type'],
                        'current_load': node['current_load']
                    }
                    for node in nodes
                ],
                'dependencies': list(dag.edges()),
                'parameters': {
                    'colony_size': 50,
                    'max_iterations': 100,
                    'limit': 10
                }
            }
            
            # 创建临时输入文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(input_data, f, indent=2)
                input_file = f.name
            
            # 调用C++程序
            result = subprocess.run(
                [self.cpp_executable_path, input_file],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            # 清理临时文件
            os.unlink(input_file)
            
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                print(f"C++ ABC算法执行失败: {result.stderr}")
                return self._python_fallback_allocation(tasks, nodes, dag)
                
        except Exception as e:
            print(f"调用C++ ABC算法时出错: {e}")
            return self._python_fallback_allocation(tasks, nodes, dag)
    
    def _python_fallback_allocation(self, tasks: List[Dict], nodes: List[Dict], 
                                  dag: nx.DiGraph) -> Dict[str, Any]:
        """Python备用分配算法（简化的启发式算法）"""
        
        # 计算任务的拓扑排序
        try:
            topo_order = list(nx.topological_sort(dag))
        except:
            topo_order = [task['id'] for task in tasks]
        
        # 初始化节点负载
        node_loads = {node['id']: {
            'cpu': node['current_load'] * node['cpu_cap'],
            'memory': node['current_load'] * node['memory_cap'],
            'io': node['current_load'] * node['io_cap'],
            'network': node['current_load'] * node['network_cap']
        } for node in nodes}
        
        allocation = {}
        
        # 按拓扑顺序分配任务
        for task_id in topo_order:
            task = next(t for t in tasks if t['id'] == task_id)
            
            best_node = None
            best_score = float('inf')
            
            for node in nodes:
                node_id = node['id']
                
                # 检查资源约束
                if (node_loads[node_id]['cpu'] + task['cpu_req'] <= node['cpu_cap'] and
                    node_loads[node_id]['memory'] + task['memory_req'] <= node['memory_cap'] and
                    node_loads[node_id]['io'] + task['io_req'] <= node['io_cap'] and
                    node_loads[node_id]['network'] + task['network_req'] <= node['network_cap']):
                    
                    # 计算分配得分（负载均衡 + 资源匹配）
                    load_score = (
                        (node_loads[node_id]['cpu'] + task['cpu_req']) / node['cpu_cap'] +
                        (node_loads[node_id]['memory'] + task['memory_req']) / node['memory_cap'] +
                        (node_loads[node_id]['io'] + task['io_req']) / node['io_cap'] +
                        (node_loads[node_id]['network'] + task['network_req']) / node['network_cap']
                    ) / 4
                    
                    # 资源匹配得分
                    match_score = self._calculate_resource_match_score(task, node)
                    
                    total_score = load_score + (1 - match_score)
                    
                    if total_score < best_score:
                        best_score = total_score
                        best_node = node_id
            
            # 分配任务到最佳节点
            if best_node:
                allocation[task_id] = best_node
                node_loads[best_node]['cpu'] += task['cpu_req']
                node_loads[best_node]['memory'] += task['memory_req']
                node_loads[best_node]['io'] += task['io_req']
                node_loads[best_node]['network'] += task['network_req']
            else:
                # 如果没有合适的节点，分配到负载最小的节点
                min_load_node = min(nodes, key=lambda n: sum(node_loads[n['id']].values()))
                allocation[task_id] = min_load_node['id']
        
        # 计算性能指标
        metrics = self._calculate_performance_metrics(tasks, nodes, allocation, node_loads)
        
        return {
            'allocation': allocation,
            'objective_value': metrics['objective_value'],
            'resource_utilization': metrics['resource_utilization'],
            'load_balance': metrics['load_balance'],
            'makespan': metrics['makespan'],
            'performance_metrics': metrics
        }
    
    def _calculate_resource_match_score(self, task: Dict, node: Dict) -> float:
        """计算任务和节点的资源匹配得分"""
        task_profile = np.array([
            task['cpu_req'], task['memory_req'], 
            task['io_req'], task['network_req']
        ])
        
        node_profile = np.array([
            node['cpu_cap'], node['memory_cap'], 
            node['io_cap'], node['network_cap']
        ])
        
        # 归一化
        task_profile = task_profile / np.sum(task_profile)
        node_profile = node_profile / np.sum(node_profile)
        
        # 计算余弦相似度
        dot_product = np.dot(task_profile, node_profile)
        norm_product = np.linalg.norm(task_profile) * np.linalg.norm(node_profile)
        
        if norm_product == 0:
            return 0.0
        
        return dot_product / norm_product
    
    def _calculate_performance_metrics(self, tasks: List[Dict], nodes: List[Dict], 
                                     allocation: Dict, node_loads: Dict) -> Dict[str, float]:
        """计算性能指标"""
        
        # 资源利用率
        total_utilization = 0
        for node in nodes:
            node_id = node['id']
            utilization = (
                node_loads[node_id]['cpu'] / node['cpu_cap'] +
                node_loads[node_id]['memory'] / node['memory_cap'] +
                node_loads[node_id]['io'] / node['io_cap'] +
                node_loads[node_id]['network'] / node['network_cap']
            ) / 4
            total_utilization += utilization
        
        resource_utilization = total_utilization / len(nodes)
        
        # 负载均衡度
        node_utilizations = []
        for node in nodes:
            node_id = node['id']
            utilization = sum(node_loads[node_id].values()) / sum([
                node['cpu_cap'], node['memory_cap'], 
                node['io_cap'], node['network_cap']
            ])
            node_utilizations.append(utilization)
        
        if len(node_utilizations) > 1:
            load_balance = 1.0 - (np.std(node_utilizations) / np.mean(node_utilizations))
        else:
            load_balance = 1.0
        
        # Makespan（简化计算）
        node_completion_times = {}
        for node in nodes:
            node_id = node['id']
            assigned_tasks = [t for t in tasks if allocation.get(t['id']) == node_id]
            total_time = sum(t.get('execution_time', 10) for t in assigned_tasks)
            node_completion_times[node_id] = total_time
        
        makespan = max(node_completion_times.values()) if node_completion_times else 0
        
        # 目标函数值（综合指标）
        objective_value = 0.4 * resource_utilization + 0.4 * load_balance + 0.2 * (1.0 / (1.0 + makespan / 100))
        
        return {
            'resource_utilization': resource_utilization,
            'load_balance': max(0, load_balance),
            'makespan': makespan,
            'objective_value': objective_value
        }

# 测试代码
if __name__ == "__main__":
    # 创建测试数据
    test_tasks = [
        {'id': 0, 'cpu_req': 2.0, 'memory_req': 4096, 'io_req': 100, 'network_req': 200, 'type': 'cpu_intensive'},
        {'id': 1, 'cpu_req': 1.0, 'memory_req': 8192, 'io_req': 50, 'network_req': 100, 'type': 'memory_intensive'},
        {'id': 2, 'cpu_req': 1.5, 'memory_req': 2048, 'io_req': 300, 'network_req': 150, 'type': 'io_intensive'}
    ]
    
    test_nodes = [
        {'id': 0, 'cpu_cap': 8.0, 'memory_cap': 16384, 'io_cap': 1000, 'network_cap': 1000, 'type': 'general', 'current_load': 0.3},
        {'id': 1, 'cpu_cap': 4.0, 'memory_cap': 32768, 'io_cap': 500, 'network_cap': 800, 'type': 'memory_optimized', 'current_load': 0.2}
    ]
    
    test_dag = nx.DiGraph()
    test_dag.add_nodes_from([0, 1, 2])
    test_dag.add_edge(0, 2)
    
    # 测试ABC算法包装器
    abc_wrapper = ABCAlgorithmWrapper()
    result = abc_wrapper.allocate_tasks(test_tasks, test_nodes, test_dag)
    
    print("分配结果:")
    print(json.dumps(result, indent=2))