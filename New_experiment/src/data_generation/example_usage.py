#!/usr/bin/env python3
"""
ABC算法使用示例
演示如何使用新的Python版本ABC算法进行任务调度
"""

import json
import networkx as nx
from abc_algorithm_wrapper import ABCAlgorithmWrapper

def example_basic_usage():
    """基本使用示例"""
    print("=== ABC算法基本使用示例 ===\n")
    
    # 1. 创建ABC算法实例
    print("1. 创建ABC算法实例")
    abc_wrapper = ABCAlgorithmWrapper(
        colony_size=30,      # 蜂群大小
        max_iterations=50,   # 最大迭代次数
        limit=5             # 放弃阈值
    )
    print("   ✅ ABC算法实例创建成功")
    
    # 2. 定义任务
    print("\n2. 定义任务")
    tasks = [
        {
            'id': 0,
            'cpu_req': 2.0,
            'memory_req': 4096,
            'io_req': 100,
            'network_req': 200,
            'type': 'cpu_intensive',
            'execution_time': 15
        },
        {
            'id': 1,
            'cpu_req': 1.0,
            'memory_req': 8192,
            'io_req': 50,
            'network_req': 100,
            'type': 'memory_intensive',
            'execution_time': 12
        },
        {
            'id': 2,
            'cpu_req': 1.5,
            'memory_req': 2048,
            'io_req': 300,
            'network_req': 150,
            'type': 'io_intensive',
            'execution_time': 18
        }
    ]
    print(f"   ✅ 定义了 {len(tasks)} 个任务")
    
    # 3. 定义节点
    print("\n3. 定义计算节点")
    nodes = [
        {
            'id': 0,
            'cpu_cap': 8.0,
            'memory_cap': 16384,
            'io_cap': 1000,
            'network_cap': 1000,
            'type': 'general',
            'current_load': 0.3
        },
        {
            'id': 1,
            'cpu_cap': 4.0,
            'memory_cap': 32768,
            'io_cap': 500,
            'network_cap': 800,
            'type': 'memory_optimized',
            'current_load': 0.2
        },
        {
            'id': 2,
            'cpu_cap': 6.0,
            'memory_cap': 8192,
            'io_cap': 2000,
            'network_cap': 600,
            'type': 'io_optimized',
            'current_load': 0.1
        }
    ]
    print(f"   ✅ 定义了 {len(nodes)} 个计算节点")
    
    # 4. 创建任务依赖图
    print("\n4. 创建任务依赖关系")
    dag = nx.DiGraph()
    dag.add_nodes_from([0, 1, 2])
    dag.add_edge(0, 2)  # 任务0依赖任务2
    print(f"   ✅ 创建了包含 {len(dag.edges())} 个依赖关系的DAG")
    
    # 5. 执行任务分配
    print("\n5. 执行ABC算法进行任务分配")
    print("   正在运行ABC算法...")
    result = abc_wrapper.allocate_tasks(tasks, nodes, dag)
    print("   ✅ 任务分配完成")
    
    # 6. 显示结果
    print("\n6. 分配结果")
    print(f"   分配方案: {result['allocation']}")
    print(f"   目标函数值: {result['objective_value']:.4f}")
    print(f"   资源利用率: {result['resource_utilization']:.4f}")
    print(f"   负载均衡度: {result['load_balance']:.4f}")
    print(f"   完成时间: {result['makespan']:.2f}")
    
    return result

def example_parameter_tuning():
    """参数调优示例"""
    print("\n\n=== ABC算法参数调优示例 ===\n")
    
    # 创建测试数据
    tasks = [
        {'id': i, 'cpu_req': 1.5, 'memory_req': 2048, 'io_req': 100, 
         'network_req': 150, 'type': 'general', 'execution_time': 10}
        for i in range(6)
    ]
    
    nodes = [
        {'id': i, 'cpu_cap': 6.0, 'memory_cap': 8192, 'io_cap': 800, 
         'network_cap': 800, 'type': 'general', 'current_load': 0.2}
        for i in range(3)
    ]
    
    dag = nx.DiGraph()
    dag.add_nodes_from(range(6))
    
    # 测试不同参数配置
    configs = [
        {"name": "快速配置", "colony_size": 20, "max_iterations": 30, "limit": 3},
        {"name": "平衡配置", "colony_size": 50, "max_iterations": 50, "limit": 5},
        {"name": "精确配置", "colony_size": 80, "max_iterations": 100, "limit": 10}
    ]
    
    print("测试不同参数配置的性能:")
    for config in configs:
        print(f"\n{config['name']}:")
        print(f"  参数: 蜂群大小={config['colony_size']}, "
              f"迭代次数={config['max_iterations']}, 放弃阈值={config['limit']}")
        
        abc_wrapper = ABCAlgorithmWrapper(
            colony_size=config['colony_size'],
            max_iterations=config['max_iterations'],
            limit=config['limit']
        )
        
        result = abc_wrapper.allocate_tasks(tasks, nodes, dag)
        print(f"  结果: 目标值={result['objective_value']:.4f}, "
              f"利用率={result['resource_utilization']:.4f}, "
              f"均衡度={result['load_balance']:.4f}")

def example_constraint_handling():
    """约束处理示例"""
    print("\n\n=== 约束处理示例 ===\n")
    
    # 创建资源紧张的场景
    print("1. 创建资源紧张的测试场景")
    tasks = [
        {'id': 0, 'cpu_req': 3.0, 'memory_req': 8192, 'io_req': 400, 
         'network_req': 500, 'type': 'heavy', 'execution_time': 20},
        {'id': 1, 'cpu_req': 2.5, 'memory_req': 6144, 'io_req': 300, 
         'network_req': 400, 'type': 'heavy', 'execution_time': 18},
        {'id': 2, 'cpu_req': 2.0, 'memory_req': 4096, 'io_req': 200, 
         'network_req': 300, 'type': 'medium', 'execution_time': 15}
    ]
    
    # 资源有限的节点
    nodes = [
        {'id': 0, 'cpu_cap': 4.0, 'memory_cap': 8192, 'io_cap': 500, 
         'network_cap': 600, 'type': 'limited', 'current_load': 0.3},
        {'id': 1, 'cpu_cap': 3.0, 'memory_cap': 6144, 'io_cap': 400, 
         'network_cap': 500, 'type': 'limited', 'current_load': 0.2}
    ]
    
    dag = nx.DiGraph()
    dag.add_nodes_from([0, 1, 2])
    
    print("   任务总需求 > 节点总容量 (模拟资源紧张)")
    
    # 执行分配
    print("\n2. 执行ABC算法")
    abc_wrapper = ABCAlgorithmWrapper(colony_size=40, max_iterations=60, limit=8)
    result = abc_wrapper.allocate_tasks(tasks, nodes, dag)
    
    print("\n3. 约束处理结果")
    print(f"   目标函数值: {result['objective_value']:.4f}")
    if result['objective_value'] > 0:
        print("   ✅ 找到可行解")
    else:
        print("   ⚠️  解违反约束，算法进行了惩罚处理")
    
    print(f"   分配方案: {result['allocation']}")
    print(f"   资源利用率: {result['resource_utilization']:.4f}")

def example_performance_analysis():
    """性能分析示例"""
    print("\n\n=== 性能分析示例 ===\n")
    
    import time
    import random
    
    # 生成不同规模的测试用例
    test_cases = [
        {"tasks": 5, "nodes": 3, "name": "小规模"},
        {"tasks": 10, "nodes": 5, "name": "中等规模"},
        {"tasks": 20, "nodes": 8, "name": "大规模"}
    ]
    
    abc_wrapper = ABCAlgorithmWrapper(colony_size=30, max_iterations=50, limit=5)
    
    print("性能测试结果:")
    print("-" * 60)
    print(f"{'规模':<10} {'任务数':<8} {'节点数':<8} {'耗时(秒)':<10} {'目标值':<10}")
    print("-" * 60)
    
    for case in test_cases:
        # 生成测试数据
        tasks = []
        for i in range(case['tasks']):
            tasks.append({
                'id': i,
                'cpu_req': random.uniform(1.0, 3.0),
                'memory_req': random.randint(2048, 8192),
                'io_req': random.randint(100, 500),
                'network_req': random.randint(200, 600),
                'type': 'test',
                'execution_time': random.randint(10, 25)
            })
        
        nodes = []
        for i in range(case['nodes']):
            nodes.append({
                'id': i,
                'cpu_cap': random.uniform(8.0, 16.0),
                'memory_cap': random.randint(16384, 32768),
                'io_cap': random.randint(1000, 2000),
                'network_cap': random.randint(1000, 2000),
                'type': 'test',
                'current_load': random.uniform(0.1, 0.3)
            })
        
        dag = nx.DiGraph()
        dag.add_nodes_from(range(case['tasks']))
        
        # 测量执行时间
        start_time = time.time()
        result = abc_wrapper.allocate_tasks(tasks, nodes, dag)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        print(f"{case['name']:<10} {case['tasks']:<8} {case['nodes']:<8} "
              f"{execution_time:<10.3f} {result['objective_value']:<10.4f}")

def main():
    """主函数"""
    print("ABC算法Python实现 - 使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_basic_usage()
    example_parameter_tuning()
    example_constraint_handling()
    example_performance_analysis()
    
    print("\n" + "=" * 50)
    print("🎉 所有示例运行完成!")
    print("\n💡 提示:")
    print("- 根据问题规模调整ABC算法参数")
    print("- 确保节点资源容量大于任务总需求")
    print("- 可以通过修改适应度函数权重来调整优化目标")

if __name__ == "__main__":
    main()
