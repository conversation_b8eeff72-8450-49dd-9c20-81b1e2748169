#!/usr/bin/env python3
"""
GNN训练数据生成主脚本
用于生成三层GNN模型的训练数据，支持图着色预处理对比实验
"""

import os
import sys
import argparse
from gnn_training_data_generator import GNNTrainingDataGenerator, GNNDataConfig
from gnn_data_loader import create_gnn_dataloaders, compare_datasets

def main():
    parser = argparse.ArgumentParser(description='生成GNN训练数据')
    parser.add_argument('--num_samples', type=int, default=5000, help='生成样本数量')
    parser.add_argument('--min_tasks', type=int, default=10, help='最小任务数')
    parser.add_argument('--max_tasks', type=int, default=50, help='最大任务数')
    parser.add_argument('--min_nodes', type=int, default=5, help='最小节点数')
    parser.add_argument('--max_nodes', type=int, default=20, help='最大节点数')
    parser.add_argument('--output_dir', type=str, default='gnn_training_data', help='输出目录')
    parser.add_argument('--abc_executable', type=str, default='./abc_allocator', help='ABC算法可执行文件路径')
    parser.add_argument('--test_only', action='store_true', help='仅测试数据加载')
    
    args = parser.parse_args()
    
    if args.test_only:
        test_data_loading(args.output_dir)
        return
    
    # 配置数据生成参数
    config = GNNDataConfig(
        num_samples=args.num_samples,
        min_tasks=args.min_tasks,
        max_tasks=args.max_tasks,
        min_nodes=args.min_nodes,
        max_nodes=args.max_nodes,
        output_dir=args.output_dir,
        abc_executable_path=args.abc_executable
    )
    
    print("GNN训练数据生成配置:")
    print(f"  样本数量: {config.num_samples}")
    print(f"  任务数量范围: {config.min_tasks}-{config.max_tasks}")
    print(f"  节点数量范围: {config.min_nodes}-{config.max_nodes}")
    print(f"  输出目录: {config.output_dir}")
    print(f"  ABC可执行文件: {config.abc_executable_path}")
    
    # 创建数据生成器
    generator = GNNTrainingDataGenerator(config)
    
    # 生成数据集
    print("\n开始生成数据集...")
    dataset_with_coloring, dataset_without_coloring = generator.generate_dataset()
    
    # 保存数据集
    print("\n保存数据集...")
    generator.save_dataset(dataset_with_coloring, dataset_without_coloring)
    
    # 测试数据加载
    print("\n测试数据加载...")
    test_data_loading(args.output_dir)
    
    print("\n数据生成完成!")
    print(f"数据集保存在: {args.output_dir}")
    print("包含两个子目录:")
    print(f"  - with_coloring/: 包含图着色特征的数据集")
    print(f"  - without_coloring/: 不包含图着色特征的数据集")

def test_data_loading(data_dir: str):
    """测试数据加载功能"""
    if not os.path.exists(data_dir):
        print(f"数据目录不存在: {data_dir}")
        return
    
    try:
        # 比较数据集
        print("\n数据集比较:")
        comparison = compare_datasets(data_dir)
        
        for dataset_type, info in comparison.items():
            print(f"\n{dataset_type}:")
            for key, value in info.items():
                print(f"  {key}: {value}")
        
        # 测试数据加载器
        print("\n测试数据加载器...")
        
        # 有颜色特征的数据加载器
        print("创建有颜色特征的数据加载器...")
        train_loader_with, val_loader_with, test_loader_with = create_gnn_dataloaders(
            data_dir, batch_size=8, coloring_type='with_coloring'
        )
        
        # 无颜色特征的数据加载器
        print("创建无颜色特征的数据加载器...")
        train_loader_without, val_loader_without, test_loader_without = create_gnn_dataloaders(
            data_dir, batch_size=8, coloring_type='without_coloring'
        )
        
        # 测试批次数据
        print("\n测试批次数据:")
        
        print("有颜色特征数据:")
        for batch in train_loader_with:
            print(f"  任务特征: {batch['task_features'].shape}")
            print(f"  节点特征: {batch['node_features'].shape}")
            print(f"  全局特征: {batch['global_features'].shape}")
            print(f"  分配标签: {batch['allocation_labels'].shape}")
            break
        
        print("\n无颜色特征数据:")
        for batch in train_loader_without:
            print(f"  任务特征: {batch['task_features'].shape}")
            print(f"  节点特征: {batch['node_features'].shape}")
            print(f"  全局特征: {batch['global_features'].shape}")
            print(f"  分配标签: {batch['allocation_labels'].shape}")
            break
        
        print("\n数据加载测试成功!")
        
    except Exception as e:
        print(f"数据加载测试失败: {e}")

if __name__ == "__main__":
    main()