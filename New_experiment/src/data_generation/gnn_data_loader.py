import torch
from torch.utils.data import Dataset, DataLoader
import os
import json
from typing import Dict, Tuple, Optional

class GNNTaskAllocationDataset(Dataset):
    """GNN任务分配数据集"""
    
    def __init__(self, data_dir: str, split: str = 'train', 
                 coloring_type: str = 'with_coloring'):
        """
        Args:
            data_dir: 数据集根目录
            split: 'train', 'val', 或 'test'
            coloring_type: 'with_coloring' 或 'without_coloring'
        """
        self.data_dir = data_dir
        self.split = split
        self.coloring_type = coloring_type
        
        # 加载数据
        data_path = os.path.join(data_dir, coloring_type, f"{split}.pt")
        if not os.path.exists(data_path):
            raise FileNotFoundError(f"数据文件不存在: {data_path}")
        
        self.data = torch.load(data_path)
        
        # 加载元数据
        metadata_path = os.path.join(data_dir, coloring_type, "metadata.json")
        with open(metadata_path, 'r') as f:
            self.metadata = json.load(f)
        
        self.num_samples = self.data['task_features'].size(0)
        
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """获取单个样本"""
        return {
            'task_features': self.data['task_features'][idx],
            'node_features': self.data['node_features'][idx],
            'global_features': self.data['global_features'][idx],
            'allocation_labels': self.data['allocation_labels'][idx]
        }
    
    def get_feature_dims(self) -> Tuple[int, int, int]:
        """获取特征维度"""
        task_dim = self.data['task_features'].size(-1)
        node_dim = self.data['node_features'].size(-1)
        global_dim = self.data['global_features'].size(-1)
        return task_dim, node_dim, global_dim

def create_gnn_dataloaders(data_dir: str, batch_size: int = 32, 
                          coloring_type: str = 'with_coloring',
                          num_workers: int = 4) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """创建GNN数据加载器"""
    
    # 创建数据集
    train_dataset = GNNTaskAllocationDataset(data_dir, 'train', coloring_type)
    val_dataset = GNNTaskAllocationDataset(data_dir, 'val', coloring_type)
    test_dataset = GNNTaskAllocationDataset(data_dir, 'test', coloring_type)
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size, 
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset, 
        batch_size=batch_size, 
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader, test_loader

def compare_datasets(data_dir: str) -> Dict[str, any]:
    """比较有无颜色特征的数据集"""
    
    # 加载两种数据集的元数据
    with_coloring_meta_path = os.path.join(data_dir, "with_coloring", "metadata.json")
    without_coloring_meta_path = os.path.join(data_dir, "without_coloring", "metadata.json")
    
    with open(with_coloring_meta_path, 'r') as f:
        with_coloring_meta = json.load(f)
    
    with open(without_coloring_meta_path, 'r') as f:
        without_coloring_meta = json.load(f)
    
    # 加载样本数据进行比较
    with_coloring_train = torch.load(os.path.join(data_dir, "with_coloring", "train.pt"))
    without_coloring_train = torch.load(os.path.join(data_dir, "without_coloring", "train.pt"))
    
    comparison = {
        'with_coloring': {
            'num_samples': with_coloring_meta['num_samples'],
            'task_feature_dim': with_coloring_train['task_features'].size(-1),
            'node_feature_dim': with_coloring_train['node_features'].size(-1),
            'global_feature_dim': with_coloring_train['global_features'].size(-1)
        },
        'without_coloring': {
            'num_samples': without_coloring_meta['num_samples'],
            'task_feature_dim': without_coloring_train['task_features'].size(-1),
            'node_feature_dim': without_coloring_train['node_features'].size(-1),
            'global_feature_dim': without_coloring_train['global_features'].size(-1)
        }
    }
    
    return comparison

# 使用示例
if __name__ == "__main__":
    # 创建数据加载器
    data_dir = "gnn_training_data"
    
    print("创建有颜色特征的数据加载器...")
    train_loader_with, val_loader_with, test_loader_with = create_gnn_dataloaders(
        data_dir, batch_size=16, coloring_type='with_coloring'
    )
    
    print("创建无颜色特征的数据加载器...")
    train_loader_without, val_loader_without, test_loader_without = create_gnn_dataloaders(
        data_dir, batch_size=16, coloring_type='without_coloring'
    )
    
    # 测试数据加载
    print("\n测试数据加载...")
    for batch in train_loader_with:
        print(f"任务特征形状: {batch['task_features'].shape}")
        print(f"节点特征形状: {batch['node_features'].shape}")
        print(f"全局特征形状: {batch['global_features'].shape}")
        print(f"分配标签形状: {batch['allocation_labels'].shape}")
        break
    
    # 比较数据集
    print("\n数据集比较:")
    comparison = compare_datasets(data_dir)
    for dataset_type, info in comparison.items():
        print(f"{dataset_type}:")
        for key, value in info.items():
            print(f"  {key}: {value}")