
import torch
import numpy as np
import networkx as nx
import json
import os
import random
import sys
import traceback
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod
import subprocess
import tempfile
from datetime import datetime
from abc_algorithm_wrapper import ABCAlgorithmWrapper

# 设置随机种子以确保可重现性
random.seed(42)
np.random.seed(42)
torch.manual_seed(42)


@dataclass
class GNNDataConfig:
    """GNN训练数据配置"""
    # 数据规模
    num_samples: int = 10000
    min_tasks: int = 10
    max_tasks: int = 50
    min_nodes: int = 5
    max_nodes: int = 20

    # 特征维度
    task_feature_dim: int = 16
    node_feature_dim: int = 12
    global_feature_dim: int = 8

    # 图着色配置
    enable_coloring: bool = True
    max_colors: int = 8
    enable_priority_levels: bool = True
    max_priority_levels: int = 5

    # 依赖关系配置
    dependency_ratio: float = 0.4
    max_dependencies: int = 3

    # ABC算法配置
    abc_colony_size: int = 50
    abc_max_iterations: int = 100
    abc_limit: int = 10

    # 输出配置
    output_dir: str = "gnn_training_data"
    train_ratio: float = 0.7
    val_ratio: float = 0.15
    test_ratio: float = 0.15


class GraphColoringAlgorithm:
    """改进的图着色算法"""

    def __init__(self, max_colors: int = 8):
        self.max_colors = max_colors

    def color_graph(self, dag: nx.DiGraph) -> Dict[int, int]:
        """对DAG进行图着色"""
        # 转换为无向图进行着色
        undirected_graph = dag.to_undirected()

        # 使用贪心着色算法
        coloring = nx.greedy_color(undirected_graph, strategy='largest_first')

        # 限制颜色数量
        max_color = max(coloring.values()) if coloring else 0
        if max_color >= self.max_colors:
            # 重新映射颜色到允许范围内
            color_mapping = {}
            unique_colors = sorted(set(coloring.values()))
            for i, color in enumerate(unique_colors):
                color_mapping[color] = i % self.max_colors

            coloring = {node: color_mapping[color] for node, color in coloring.items()}

        return coloring

    def assign_priority_levels(self, dag: nx.DiGraph, max_levels: int = 5) -> Dict[int, int]:
        """基于拓扑结构分配优先级等级"""
        try:
            # 计算拓扑层级
            levels = {}

            # 找到入度为0的节点作为第一层
            in_degrees = dict(dag.in_degree())
            current_level_nodes = [node for node, degree in in_degrees.items() if degree == 0]
            level = 0

            # 如果没有入度为0的节点，说明图为空或有问题，给所有节点分配默认等级
            if not current_level_nodes:
                return {node: 0 for node in dag.nodes()}

            while current_level_nodes and level < max_levels:
                for node in current_level_nodes:
                    levels[node] = level

                # 找到下一层节点
                next_level_nodes = []
                for node in current_level_nodes:
                    for successor in dag.successors(node):
                        if successor not in levels:
                            # 检查所有前驱是否都已分配等级
                            if all(pred in levels for pred in dag.predecessors(successor)):
                                next_level_nodes.append(successor)

                current_level_nodes = list(set(next_level_nodes))
                level += 1

            # 为未分配等级的节点分配默认等级
            for node in dag.nodes():
                if node not in levels:
                    levels[node] = min(max_levels - 1, level)

            return levels

        except Exception as e:
            print(f"优先级等级分配失败: {e}")
            # 返回默认等级
            return {node: 0 for node in dag.nodes()}


# 移除旧的ABCAllocatorInterface类，现在使用ABCAlgorithmWrapper


class GNNTrainingDataGenerator:
    """GNN训练数据生成器"""

    def __init__(self, config: GNNDataConfig):
        self.config = config
        self.graph_coloring = GraphColoringAlgorithm(config.max_colors)
        self.abc_allocator = ABCAlgorithmWrapper(
            colony_size=config.abc_colony_size,
            max_iterations=config.abc_max_iterations,
            limit=config.abc_limit
        )

        # 任务类型配置
        self.task_types = {
            'cpu_intensive': {'cpu_weight': 0.6, 'memory_weight': 0.2, 'io_weight': 0.1, 'network_weight': 0.1},
            'memory_intensive': {'cpu_weight': 0.2, 'memory_weight': 0.6, 'io_weight': 0.1, 'network_weight': 0.1},
            'io_intensive': {'cpu_weight': 0.1, 'memory_weight': 0.2, 'io_weight': 0.6, 'network_weight': 0.1},
            'network_intensive': {'cpu_weight': 0.1, 'memory_weight': 0.1, 'io_weight': 0.1, 'network_weight': 0.7},
            'general': {'cpu_weight': 0.25, 'memory_weight': 0.25, 'io_weight': 0.25, 'network_weight': 0.25}
        }

        # 节点类型配置
        self.node_types = {
            'cpu_optimized': {'cpu_factor': 1.5, 'memory_factor': 1.0, 'io_factor': 1.0, 'network_factor': 1.0},
            'memory_optimized': {'cpu_factor': 1.0, 'memory_factor': 1.5, 'io_factor': 1.0, 'network_factor': 1.0},
            'io_optimized': {'cpu_factor': 1.0, 'memory_factor': 1.0, 'io_factor': 1.5, 'network_factor': 1.0},
            'general': {'cpu_factor': 1.0, 'memory_factor': 1.0, 'io_factor': 1.0, 'network_factor': 1.0}
        }

    def generate_dag_structure(self, num_tasks: int) -> nx.DiGraph:
        """生成DAG结构"""
        dag = nx.DiGraph()
        dag.add_nodes_from(range(num_tasks))

        # 生成依赖关系
        for task_id in range(num_tasks):
            # 第一个任务(task_id=0)不能有依赖，跳过
            if task_id == 0:
                continue

            if random.random() < self.config.dependency_ratio:
                # 确保依赖数量范围有效
                max_possible_deps = min(self.config.max_dependencies, task_id)
                if max_possible_deps > 0:
                    num_deps = random.randint(1, max_possible_deps)
                    possible_deps = list(range(task_id))
                    dependencies = random.sample(possible_deps, num_deps)

                    for dep in dependencies:
                        dag.add_edge(dep, task_id)

        return dag

    def generate_task_features(self, task_id: int, task_type: str,
                               dag: nx.DiGraph, colors: Optional[Dict] = None,
                               priority_levels: Optional[Dict] = None) -> Dict:
        """生成任务特征"""
        type_config = self.task_types[task_type]

        # 基础资源需求
        base_cpu = random.uniform(1.0, 4.0)
        base_memory = random.uniform(1024, 8192)
        base_io = random.uniform(50, 500)
        base_network = random.uniform(100, 1000)

        # 根据任务类型调整
        cpu_req = base_cpu * type_config['cpu_weight'] * random.uniform(0.8, 1.2)
        memory_req = base_memory * type_config['memory_weight'] * random.uniform(0.8, 1.2)
        io_req = base_io * type_config['io_weight'] * random.uniform(0.8, 1.2)
        network_req = base_network * type_config['network_weight'] * random.uniform(0.8, 1.2)

        # 计算图结构特征
        in_degree = dag.in_degree(task_id) if dag.has_node(task_id) else 0
        out_degree = dag.out_degree(task_id) if dag.has_node(task_id) else 0

        # 计算资源比例（避免除零错误）
        total_resource = cpu_req + memory_req + io_req + network_req
        if total_resource == 0:
            total_resource = 1.0

        # 基础特征向量 (12维)
        task_features = [
            cpu_req, memory_req, io_req, network_req,  # 资源需求 (4维)
            random.uniform(10, 100),  # 执行时间
            random.uniform(1, 5),  # 优先级
            float(in_degree), float(out_degree),  # 图结构特征 (2维)
            cpu_req / total_resource,  # CPU比例
            memory_req / total_resource,  # 内存比例
            io_req / total_resource,  # I/O比例
            network_req / total_resource  # 网络比例
        ]

        # 添加颜色和等级特征 (4维)
        if colors is not None and priority_levels is not None:
            color = colors.get(task_id, 0)
            level = priority_levels.get(task_id, 0)
            # 颜色one-hot编码 (简化为2维)
            color_feature1 = 1.0 if color % 2 == 0 else 0.0
            color_feature2 = 1.0 if color // 2 % 2 == 0 else 0.0
            # 等级归一化
            max_level = max(1, self.config.max_priority_levels - 1)
            level_feature = float(level) / float(max_level)
            # 颜色-等级交互特征
            interaction_feature = color_feature1 * level_feature

            task_features.extend([color_feature1, color_feature2, level_feature, interaction_feature])
        else:
            # 无颜色特征时用0填充
            task_features.extend([0.0, 0.0, 0.0, 0.0])

        return {
            'id': task_id,
            'type': task_type,
            'cpu_req': cpu_req,
            'memory_req': memory_req,
            'io_req': io_req,
            'network_req': network_req,
            'task_features': task_features
        }

    def generate_node_features(self, node_id: int, node_type: str) -> Dict:
        """生成节点特征"""
        type_config = self.node_types[node_type]

        # 基础资源容量
        base_cpu = random.uniform(8.0, 32.0)
        base_memory = random.uniform(16384, 65536)
        base_io = random.uniform(1000, 5000)
        base_network = random.uniform(1000, 10000)

        # 根据节点类型调整
        cpu_cap = base_cpu * type_config['cpu_factor']
        memory_cap = base_memory * type_config['memory_factor']
        io_cap = base_io * type_config['io_factor']
        network_cap = base_network * type_config['network_factor']

        # 当前负载（随机）
        current_load = random.uniform(0.1, 0.8)

        # 可用资源
        cpu_available = cpu_cap * (1 - current_load)
        memory_available = memory_cap * (1 - current_load)
        io_available = io_cap * (1 - current_load)
        network_available = network_cap * (1 - current_load)

        # 节点特征向量 (12维)
        node_features = [
            cpu_cap, memory_cap, io_cap, network_cap,  # 总容量 (4维)
            cpu_available, memory_available, io_available, network_available,  # 可用资源 (4维)
            current_load,  # 当前负载
            random.uniform(0.9, 0.99),  # 可靠性
            random.uniform(0.8, 1.2),  # 性能因子
            random.uniform(30, 70)  # 温度
        ]

        return {
            'id': node_id,
            'type': node_type,
            'cpu_cap': cpu_cap,
            'memory_cap': memory_cap,
            'io_cap': io_cap,
            'network_cap': network_cap,
            'current_load': current_load,
            'node_features': node_features
        }

    def prepare_gnn_data(self, tasks: List[Dict], nodes: List[Dict],
                         dag: nx.DiGraph, allocation_result: Dict) -> Dict[str, torch.Tensor]:
        """准备GNN训练数据"""
        num_tasks = len(tasks)
        num_nodes = len(nodes)

        # 任务特征 [num_tasks, task_feature_dim]
        task_features = torch.tensor([task['task_features'] for task in tasks], dtype=torch.float32)

        # 节点特征 [num_nodes, node_feature_dim]
        node_features = torch.tensor([node['node_features'] for node in nodes], dtype=torch.float32)

        # 任务依赖边 [2, num_edges]
        task_edges = list(dag.edges())
        if task_edges:
            task_edge_index = torch.tensor(task_edges, dtype=torch.long).t().contiguous()
        else:
            task_edge_index = torch.empty((2, 0), dtype=torch.long)

        # 全局特征 [global_feature_dim]
        total_cpu_demand = sum(task['cpu_req'] for task in tasks)
        total_memory_demand = sum(task['memory_req'] for task in tasks)
        total_cpu_capacity = sum(node['cpu_cap'] for node in nodes)
        total_memory_capacity = sum(node['memory_cap'] for node in nodes)

        global_features = torch.tensor([
            num_tasks, num_nodes,  # 数量信息
            num_tasks / num_nodes,  # 任务节点比
            total_cpu_demand / total_cpu_capacity,  # CPU需求比
            total_memory_demand / total_memory_capacity,  # 内存需求比
            len(task_edges) / max(1, num_tasks),  # 依赖密度
            allocation_result.get('resource_utilization', 0.7),  # 资源利用率
            allocation_result.get('load_balance', 0.8)  # 负载均衡度
        ], dtype=torch.float32)

        # 分配标签 [num_tasks, num_nodes] (one-hot)
        allocation_labels = torch.zeros(num_tasks, num_nodes)
        allocation = allocation_result.get('allocation', {})
        for task_idx, task in enumerate(tasks):
            task_id = task['id']
            if task_id in allocation:
                assigned_node_id = allocation[task_id]
                # 找到节点索引
                for node_idx, node in enumerate(nodes):
                    if node['id'] == assigned_node_id:
                        allocation_labels[task_idx, node_idx] = 1.0
                        break

        return {
            'task_features': task_features,
            'node_features': node_features,
            'task_edge_index': task_edge_index,
            'global_features': global_features,
            'allocation_labels': allocation_labels,
            'metadata': {
                'num_tasks': num_tasks,
                'num_nodes': num_nodes,
                'has_coloring': any('color' in str(task.get('task_features', [])) for task in tasks)
            }
        }

    def generate_single_sample(self, enable_coloring: bool = True) -> Dict[str, torch.Tensor]:
        """生成单个训练样本"""
        # 随机生成任务和节点数量
        num_tasks = random.randint(self.config.min_tasks, self.config.max_tasks)
        num_nodes = random.randint(self.config.min_nodes, self.config.max_nodes)

        # 生成DAG结构
        dag = self.generate_dag_structure(num_tasks)

        # 生成图着色和优先级等级（如果启用）
        colors = None
        priority_levels = None
        if enable_coloring:
            colors = self.graph_coloring.color_graph(dag)
            priority_levels = self.graph_coloring.assign_priority_levels(dag, self.config.max_priority_levels)

        # 生成任务
        tasks = []
        task_types = list(self.task_types.keys())
        for task_id in range(num_tasks):
            task_type = random.choice(task_types)
            task = self.generate_task_features(task_id, task_type, dag, colors, priority_levels)
            tasks.append(task)

        # 生成节点
        nodes = []
        node_types = list(self.node_types.keys())
        for node_id in range(num_nodes):
            node_type = random.choice(node_types)
            node = self.generate_node_features(node_id, node_type)
            nodes.append(node)

        # 使用ABC算法生成分配方案
        allocation_result = self.abc_allocator.allocate_tasks(tasks, nodes, dag)

        # 准备GNN数据
        gnn_data = self.prepare_gnn_data(tasks, nodes, dag, allocation_result)

        return gnn_data

    def generate_dataset(self) -> Tuple[Dict, Dict]:
        """生成完整的训练数据集"""
        print(f"开始生成 {self.config.num_samples} 个训练样本...")

        # 生成两组数据：有颜色特征和无颜色特征
        samples_with_coloring = []
        samples_without_coloring = []

        for i in range(self.config.num_samples):
            if i % 100 == 0:
                print(f"已生成 {i}/{self.config.num_samples} 个样本")

            try:
                # 生成有颜色特征的样本
                sample_with = self.generate_single_sample(enable_coloring=True)
                samples_with_coloring.append(sample_with)

                # 生成无颜色特征的样本（相同的任务和节点配置）
                sample_without = self.generate_single_sample(enable_coloring=False)
                samples_without_coloring.append(sample_without)

            except Exception as e:
                print(f"生成第 {i} 个样本时出错: {e}")
                traceback.print_exc()
                continue

        # 组织数据集
        dataset_with_coloring = self._organize_dataset(samples_with_coloring, "with_coloring")
        dataset_without_coloring = self._organize_dataset(samples_without_coloring, "without_coloring")

        return dataset_with_coloring, dataset_without_coloring

    def _organize_dataset(self, samples: List[Dict], dataset_type: str) -> Dict:
        """组织数据集格式"""
        # 分割训练/验证/测试集
        num_samples = len(samples)
        num_train = int(num_samples * self.config.train_ratio)
        num_val = int(num_samples * self.config.val_ratio)

        train_samples = samples[:num_train]
        val_samples = samples[num_train:num_train + num_val]
        test_samples = samples[num_train + num_val:]

        dataset = {
            'train': self._batch_samples(train_samples),
            'val': self._batch_samples(val_samples),
            'test': self._batch_samples(test_samples),
            'metadata': {
                'dataset_type': dataset_type,
                'num_samples': num_samples,
                'num_train': len(train_samples),
                'num_val': len(val_samples),
                'num_test': len(test_samples),
                'config': self.config.__dict__,
                'generation_time': datetime.now().isoformat()
            }
        }

        return dataset

    def _batch_samples(self, samples: List[Dict]) -> Dict[str, torch.Tensor]:
        """将样本批处理"""
        if not samples:
            return {}

        # 找到最大尺寸
        max_tasks = max(sample['task_features'].size(0) for sample in samples)
        max_nodes = max(sample['node_features'].size(0) for sample in samples)
        max_edges = max(sample['task_edge_index'].size(1) for sample in samples)

        batch_size = len(samples)

        # 初始化批处理张量
        batch_task_features = torch.zeros(batch_size, max_tasks, self.config.task_feature_dim)
        batch_node_features = torch.zeros(batch_size, max_nodes, self.config.node_feature_dim)
        batch_global_features = torch.zeros(batch_size, self.config.global_feature_dim)
        batch_allocation_labels = torch.zeros(batch_size, max_tasks, max_nodes)

        # 填充数据
        for i, sample in enumerate(samples):
            num_tasks = sample['task_features'].size(0)
            num_nodes = sample['node_features'].size(0)

            batch_task_features[i, :num_tasks] = sample['task_features']
            batch_node_features[i, :num_nodes] = sample['node_features']
            batch_global_features[i] = sample['global_features']
            batch_allocation_labels[i, :num_tasks, :num_nodes] = sample['allocation_labels']

        return {
            'task_features': batch_task_features,
            'node_features': batch_node_features,
            'global_features': batch_global_features,
            'allocation_labels': batch_allocation_labels
        }

    def save_dataset(self, dataset_with: Dict, dataset_without: Dict):
        """保存数据集"""
        os.makedirs(self.config.output_dir, exist_ok=True)

        # 保存有颜色特征的数据集
        with_coloring_dir = os.path.join(self.config.output_dir, "with_coloring")
        os.makedirs(with_coloring_dir, exist_ok=True)

        for split in ['train', 'val', 'test']:
            if split in dataset_with:
                torch.save(dataset_with[split],
                           os.path.join(with_coloring_dir, f"{split}.pt"))

        # 保存元数据
        with open(os.path.join(with_coloring_dir, "metadata.json"), 'w') as f:
            json.dump(dataset_with['metadata'], f, indent=2)

        # 保存无颜色特征的数据集
        without_coloring_dir = os.path.join(self.config.output_dir, "without_coloring")
        os.makedirs(without_coloring_dir, exist_ok=True)

        for split in ['train', 'val', 'test']:
            if split in dataset_without:
                torch.save(dataset_without[split],
                           os.path.join(without_coloring_dir, f"{split}.pt"))

        # 保存元数据
        with open(os.path.join(without_coloring_dir, "metadata.json"), 'w') as f:
            json.dump(dataset_without['metadata'], f, indent=2)

        print(f"数据集已保存到: {self.config.output_dir}")


def main():
    """主函数"""
    config = GNNDataConfig(
        num_samples=1000,  # 生成1000个样本用于测试
        min_tasks=10,
        max_tasks=30,
        min_nodes=5,
        max_nodes=15,
        output_dir="gnn_training_data"
    )

    generator = GNNTrainingDataGenerator(config)

    try:
        # 生成数据集
        dataset_with, dataset_without = generator.generate_dataset()

        # 保存数据集
        generator.save_dataset(dataset_with, dataset_without)

        print("数据集生成完成!")
        print(f"有颜色特征数据集: {config.output_dir}/with_coloring/")
        print(f"无颜色特征数据集: {config.output_dir}/without_coloring/")

    except Exception as e:
        print(f"数据集生成失败: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
