#!/usr/bin/env python3
"""
测试ABC算法集成
"""

import sys
import os
import json
import networkx as nx
from abc_algorithm_wrapper import ABCAlgorithmWrapper

def test_abc_algorithm():
    """测试ABC算法"""
    print("开始测试ABC算法集成...")
    
    # 创建测试数据
    tasks = [
        {
            'id': 0, 
            'cpu_req': 2.0, 
            'memory_req': 4096, 
            'io_req': 100, 
            'network_req': 200, 
            'type': 'cpu_intensive',
            'execution_time': 15
        },
        {
            'id': 1, 
            'cpu_req': 1.0, 
            'memory_req': 8192, 
            'io_req': 50, 
            'network_req': 100, 
            'type': 'memory_intensive',
            'execution_time': 12
        },
        {
            'id': 2, 
            'cpu_req': 1.5, 
            'memory_req': 2048, 
            'io_req': 300, 
            'network_req': 150, 
            'type': 'io_intensive',
            'execution_time': 18
        },
        {
            'id': 3, 
            'cpu_req': 0.8, 
            'memory_req': 1024, 
            'io_req': 80, 
            'network_req': 400, 
            'type': 'network_intensive',
            'execution_time': 10
        }
    ]
    
    nodes = [
        {
            'id': 0, 
            'cpu_cap': 8.0, 
            'memory_cap': 16384, 
            'io_cap': 1000, 
            'network_cap': 1000, 
            'type': 'general', 
            'current_load': 0.3
        },
        {
            'id': 1, 
            'cpu_cap': 4.0, 
            'memory_cap': 32768, 
            'io_cap': 500, 
            'network_cap': 800, 
            'type': 'memory_optimized', 
            'current_load': 0.2
        },
        {
            'id': 2, 
            'cpu_cap': 6.0, 
            'memory_cap': 8192, 
            'io_cap': 2000, 
            'network_cap': 600, 
            'type': 'io_optimized', 
            'current_load': 0.1
        }
    ]
    
    # 创建DAG
    dag = nx.DiGraph()
    dag.add_nodes_from([0, 1, 2, 3])
    dag.add_edge(0, 2)  # 任务0依赖任务2
    dag.add_edge(1, 3)  # 任务1依赖任务3
    
    # 测试ABC算法
    print(f"任务数量: {len(tasks)}")
    print(f"节点数量: {len(nodes)}")
    print(f"依赖关系: {list(dag.edges())}")
    
    # 创建ABC算法实例
    abc_wrapper = ABCAlgorithmWrapper(
        colony_size=30, 
        max_iterations=50, 
        limit=5
    )
    
    # 执行任务分配
    print("正在执行ABC算法...")
    result = abc_wrapper.allocate_tasks(tasks, nodes, dag)
    
    # 输出结果
    print("\n=== ABC算法分配结果 ===")
    print(f"分配方案: {result['allocation']}")
    print(f"目标函数值: {result['objective_value']:.4f}")
    print(f"资源利用率: {result['resource_utilization']:.4f}")
    print(f"负载均衡度: {result['load_balance']:.4f}")
    print(f"完成时间: {result['makespan']:.2f}")
    
    # 验证分配结果
    print("\n=== 分配结果验证 ===")
    allocation = result['allocation']
    
    # 检查所有任务是否都被分配
    all_tasks_assigned = all(task['id'] in allocation for task in tasks)
    print(f"所有任务都被分配: {all_tasks_assigned}")

    # 检查分配的节点是否有效
    valid_nodes = all(allocation[task['id']] in [node['id'] for node in nodes]
                     for task in tasks)
    print(f"分配的节点都有效: {valid_nodes}")

    # 计算每个节点的负载
    node_loads = {node['id']: {'cpu': 0, 'memory': 0, 'io': 0, 'network': 0}
                  for node in nodes}

    for task in tasks:
        node_id = allocation[task['id']]
        node_loads[node_id]['cpu'] += task['cpu_req']
        node_loads[node_id]['memory'] += task['memory_req']
        node_loads[node_id]['io'] += task['io_req']
        node_loads[node_id]['network'] += task['network_req']
    
    print("\n=== 节点负载情况 ===")
    for node in nodes:
        node_id = node['id']
        load = node_loads[node_id]
        print(f"节点 {node_id} ({node['type']}):")
        print(f"  CPU: {load['cpu']:.1f}/{node['cpu_cap']:.1f} "
              f"({load['cpu']/node['cpu_cap']*100:.1f}%)")
        print(f"  内存: {load['memory']}/{node['memory_cap']} "
              f"({load['memory']/node['memory_cap']*100:.1f}%)")
        print(f"  I/O: {load['io']}/{node['io_cap']} "
              f"({load['io']/node['io_cap']*100:.1f}%)")
        print(f"  网络: {load['network']}/{node['network_cap']} "
              f"({load['network']/node['network_cap']*100:.1f}%)")
    
    # 检查资源约束
    constraint_violations = 0
    for node in nodes:
        node_id = node['id']
        load = node_loads[node_id]
        if (load['cpu'] > node['cpu_cap'] or 
            load['memory'] > node['memory_cap'] or
            load['io'] > node['io_cap'] or 
            load['network'] > node['network_cap']):
            constraint_violations += 1
            print(f"警告: 节点 {node_id} 资源超限!")
    
    print(f"\n资源约束违反数量: {constraint_violations}")
    
    if constraint_violations == 0 and all_tasks_assigned and valid_nodes:
        print("✅ ABC算法测试通过!")
        return True
    else:
        print("❌ ABC算法测试失败!")
        return False

def test_multiple_scenarios():
    """测试多种场景"""
    print("\n" + "="*50)
    print("测试多种场景")
    print("="*50)
    
    scenarios = [
        {"tasks": 5, "nodes": 3, "name": "小规模"},
        {"tasks": 10, "nodes": 5, "name": "中等规模"},
        {"tasks": 15, "nodes": 8, "name": "大规模"}
    ]
    
    abc_wrapper = ABCAlgorithmWrapper(colony_size=20, max_iterations=30, limit=3)
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ({scenario['tasks']}任务, {scenario['nodes']}节点) ---")
        
        # 生成随机任务
        import random
        tasks = []
        for i in range(scenario['tasks']):
            tasks.append({
                'id': i,
                'cpu_req': random.uniform(0.5, 3.0),
                'memory_req': random.randint(1024, 8192),
                'io_req': random.randint(50, 500),
                'network_req': random.randint(100, 600),
                'type': random.choice(['cpu_intensive', 'memory_intensive', 'io_intensive']),
                'execution_time': random.randint(5, 20)
            })
        
        # 生成随机节点
        nodes = []
        for i in range(scenario['nodes']):
            nodes.append({
                'id': i,
                'cpu_cap': random.uniform(4.0, 12.0),
                'memory_cap': random.randint(8192, 32768),
                'io_cap': random.randint(500, 2000),
                'network_cap': random.randint(600, 1200),
                'type': random.choice(['general', 'memory_optimized', 'io_optimized']),
                'current_load': random.uniform(0.1, 0.4)
            })
        
        # 创建简单DAG
        dag = nx.DiGraph()
        dag.add_nodes_from(range(scenario['tasks']))
        
        try:
            result = abc_wrapper.allocate_tasks(tasks, nodes, dag)
            print(f"  目标函数值: {result['objective_value']:.4f}")
            print(f"  资源利用率: {result['resource_utilization']:.4f}")
            print(f"  负载均衡度: {result['load_balance']:.4f}")
            print(f"  ✅ 成功")
        except Exception as e:
            print(f"  ❌ 失败: {e}")

if __name__ == "__main__":
    success = test_abc_algorithm()
    if success:
        test_multiple_scenarios()
    
    print("\n测试完成!")
