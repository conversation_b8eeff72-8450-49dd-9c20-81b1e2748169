#!/usr/bin/env python3
"""
测试数据生成功能（不依赖torch）
"""

import sys
import os
import json
import networkx as nx
import numpy as np
import random
from abc_algorithm_wrapper import ABCAlgorithmWrapper

# 模拟GNNDataConfig类
class SimpleDataConfig:
    def __init__(self):
        self.abc_colony_size = 30
        self.abc_max_iterations = 50
        self.abc_limit = 5
        self.min_tasks = 5
        self.max_tasks = 15
        self.min_nodes = 3
        self.max_nodes = 8

def generate_random_tasks(num_tasks):
    """生成随机任务"""
    tasks = []
    task_types = ['cpu_intensive', 'memory_intensive', 'io_intensive', 'network_intensive']
    
    for i in range(num_tasks):
        task_type = random.choice(task_types)
        
        # 根据任务类型生成不同的资源需求
        if task_type == 'cpu_intensive':
            cpu_req = random.uniform(2.0, 4.0)
            memory_req = random.randint(2048, 8192)
            io_req = random.randint(50, 200)
            network_req = random.randint(100, 300)
        elif task_type == 'memory_intensive':
            cpu_req = random.uniform(0.5, 2.0)
            memory_req = random.randint(8192, 16384)
            io_req = random.randint(50, 150)
            network_req = random.randint(100, 250)
        elif task_type == 'io_intensive':
            cpu_req = random.uniform(1.0, 2.5)
            memory_req = random.randint(1024, 4096)
            io_req = random.randint(300, 800)
            network_req = random.randint(100, 200)
        else:  # network_intensive
            cpu_req = random.uniform(0.5, 1.5)
            memory_req = random.randint(1024, 4096)
            io_req = random.randint(50, 150)
            network_req = random.randint(400, 1000)
        
        task = {
            'id': i,
            'cpu_req': cpu_req,
            'memory_req': memory_req,
            'io_req': io_req,
            'network_req': network_req,
            'type': task_type,
            'execution_time': random.randint(5, 25)
        }
        tasks.append(task)
    
    return tasks

def generate_random_nodes(num_nodes):
    """生成随机节点"""
    nodes = []
    node_types = ['general', 'cpu_optimized', 'memory_optimized', 'io_optimized']
    
    for i in range(num_nodes):
        node_type = random.choice(node_types)
        
        # 根据节点类型生成不同的资源容量（增加容量以确保可行性）
        if node_type == 'cpu_optimized':
            cpu_cap = random.uniform(12.0, 20.0)
            memory_cap = random.randint(16384, 32768)
            io_cap = random.randint(800, 1500)
            network_cap = random.randint(1000, 1500)
        elif node_type == 'memory_optimized':
            cpu_cap = random.uniform(8.0, 12.0)
            memory_cap = random.randint(32768, 65536)
            io_cap = random.randint(600, 1200)
            network_cap = random.randint(1000, 1500)
        elif node_type == 'io_optimized':
            cpu_cap = random.uniform(8.0, 12.0)
            memory_cap = random.randint(16384, 32768)
            io_cap = random.randint(1500, 3000)
            network_cap = random.randint(800, 1200)
        else:  # general
            cpu_cap = random.uniform(10.0, 16.0)
            memory_cap = random.randint(16384, 32768)
            io_cap = random.randint(1000, 2000)
            network_cap = random.randint(1000, 2000)
        
        node = {
            'id': i,
            'cpu_cap': cpu_cap,
            'memory_cap': memory_cap,
            'io_cap': io_cap,
            'network_cap': network_cap,
            'type': node_type,
            'current_load': random.uniform(0.1, 0.4)
        }
        nodes.append(node)
    
    return nodes

def generate_random_dag(num_tasks, dependency_ratio=0.3):
    """生成随机DAG"""
    dag = nx.DiGraph()
    dag.add_nodes_from(range(num_tasks))
    
    # 添加随机依赖关系
    max_edges = int(num_tasks * dependency_ratio)
    edges_added = 0
    
    for i in range(num_tasks):
        for j in range(i + 1, num_tasks):
            if edges_added >= max_edges:
                break
            if random.random() < 0.3:  # 30%的概率添加边
                dag.add_edge(i, j)
                edges_added += 1
    
    # 确保DAG是无环的
    if not nx.is_directed_acyclic_graph(dag):
        # 如果有环，移除一些边
        while not nx.is_directed_acyclic_graph(dag):
            edges = list(dag.edges())
            if edges:
                edge_to_remove = random.choice(edges)
                dag.remove_edge(*edge_to_remove)
    
    return dag

def test_single_sample():
    """测试生成单个样本"""
    print("=== 测试生成单个样本 ===")
    
    # 生成随机数据
    num_tasks = random.randint(8, 12)
    num_nodes = random.randint(4, 6)
    
    tasks = generate_random_tasks(num_tasks)
    nodes = generate_random_nodes(num_nodes)
    dag = generate_random_dag(num_tasks)
    
    print(f"任务数量: {num_tasks}")
    print(f"节点数量: {num_nodes}")
    print(f"依赖关系数量: {len(dag.edges())}")
    
    # 使用ABC算法进行分配
    config = SimpleDataConfig()
    abc_wrapper = ABCAlgorithmWrapper(
        colony_size=config.abc_colony_size,
        max_iterations=config.abc_max_iterations,
        limit=config.abc_limit
    )
    
    print("正在执行ABC算法...")
    result = abc_wrapper.allocate_tasks(tasks, nodes, dag)
    
    print(f"分配结果: {result['allocation']}")
    print(f"目标函数值: {result['objective_value']:.4f}")
    print(f"资源利用率: {result['resource_utilization']:.4f}")
    print(f"负载均衡度: {result['load_balance']:.4f}")
    print(f"完成时间: {result['makespan']:.2f}")
    
    return result

def test_batch_generation():
    """测试批量生成"""
    print("\n=== 测试批量生成 ===")
    
    config = SimpleDataConfig()
    abc_wrapper = ABCAlgorithmWrapper(
        colony_size=config.abc_colony_size,
        max_iterations=config.abc_max_iterations,
        limit=config.abc_limit
    )
    
    num_samples = 10
    successful_samples = 0
    total_objective = 0.0
    total_utilization = 0.0
    total_balance = 0.0
    
    print(f"开始生成 {num_samples} 个训练样本...")
    
    for i in range(num_samples):
        try:
            # 生成随机数据
            num_tasks = random.randint(config.min_tasks, config.max_tasks)
            num_nodes = random.randint(config.min_nodes, config.max_nodes)
            
            tasks = generate_random_tasks(num_tasks)
            nodes = generate_random_nodes(num_nodes)
            dag = generate_random_dag(num_tasks)
            
            # 使用ABC算法进行分配
            result = abc_wrapper.allocate_tasks(tasks, nodes, dag)
            
            successful_samples += 1
            total_objective += result['objective_value']
            total_utilization += result['resource_utilization']
            total_balance += result['load_balance']
            
            print(f"已生成 {successful_samples}/{num_samples} 个样本 "
                  f"(任务:{num_tasks}, 节点:{num_nodes}, 目标值:{result['objective_value']:.3f})")
            
        except Exception as e:
            print(f"生成第 {i+1} 个样本时出错: {e}")
    
    if successful_samples > 0:
        avg_objective = total_objective / successful_samples
        avg_utilization = total_utilization / successful_samples
        avg_balance = total_balance / successful_samples
        
        print(f"\n=== 批量生成统计 ===")
        print(f"成功生成样本数: {successful_samples}/{num_samples}")
        print(f"平均目标函数值: {avg_objective:.4f}")
        print(f"平均资源利用率: {avg_utilization:.4f}")
        print(f"平均负载均衡度: {avg_balance:.4f}")
        
        return True
    else:
        print("❌ 批量生成失败!")
        return False

def main():
    """主函数"""
    print("开始测试数据生成功能...")
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    # 测试单个样本生成
    single_result = test_single_sample()
    
    if single_result['objective_value'] > 0:
        print("✅ 单个样本生成测试通过!")
        
        # 测试批量生成
        batch_success = test_batch_generation()
        
        if batch_success:
            print("✅ 批量生成测试通过!")
            print("\n🎉 ABC算法已成功替换，可以正常生成训练数据!")
        else:
            print("❌ 批量生成测试失败!")
    else:
        print("❌ 单个样本生成测试失败!")

if __name__ == "__main__":
    main()
