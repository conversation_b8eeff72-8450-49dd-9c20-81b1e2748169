import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import torch
import networkx as nx
from typing import Dict, List, Tuple, Any
import os
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches


class WorkflowVisualizer:
    """工作流调度可视化器"""
    
    def __init__(self, output_dir: str = "./visualizations"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        # Set English font and professional color palette
        import matplotlib
        matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
        matplotlib.rcParams['axes.unicode_minus'] = False
        matplotlib.rcParams['font.size'] = 12
        matplotlib.rcParams['axes.titlesize'] = 14
        matplotlib.rcParams['axes.labelsize'] = 12
        matplotlib.rcParams['axes.prop_cycle'] = matplotlib.cycler(color=[
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#7f7f7f'])
        # Professional color palette
        self.colors = {
            'transformer': '#1f77b4',
            'pinn': '#2ca02c',
            'gat': '#9467bd',
            'tasks': '#ff7f0e',
            'nodes': '#8c564b',
            'assignment': '#d62728'
        }
    
    def visualize_layer_outputs(self, debug_info: Dict[str, Any], workflow_data: Dict) -> None:
        """可视化各层输出"""
        print("📊 生成层输出可视化...")
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Three-layer GNN Model Layer Output Visualization', fontsize=16, fontweight='bold')
        
        # 1. Transformer层输出
        if 'transformer' in debug_info['layer_outputs']:
            transformer_output = debug_info['layer_outputs']['transformer'].detach().cpu().numpy()
            self._plot_feature_heatmap(axes[0, 0], transformer_output[0], 
                                     'DAG Transformer Layer Output', 'Task', 'Feature Dimension')
        
        # 2. PINN约束层输出
        if 'constraint_enhanced' in debug_info['layer_outputs']:
            constraint_output = debug_info['layer_outputs']['constraint_enhanced'].detach().cpu().numpy()
            self._plot_feature_heatmap(axes[0, 1], constraint_output[0],
                                     'PINN Constraint Enhanced Layer Output', 'Task', 'Feature Dimension')
        
        # 3. 约束损失
        if 'constraint_losses' in debug_info['losses']:
            constraint_losses = debug_info['losses']['constraint_losses']
            self._plot_constraint_losses(axes[1, 0], constraint_losses)
        
        # 4. GAT层输出
        if 'gat_output' in debug_info['layer_outputs']:
            gat_output = debug_info['layer_outputs']['gat_output'].detach().cpu().numpy()
            self._plot_assignment_matrix(axes[1, 1], gat_output[0],
                                       'GAT Decision Layer Output', 'Task', 'Node')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'layer_outputs.png'), dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Layer output visualization saved to: {self.output_dir}/layer_outputs.png")
    
    def visualize_workflow_dag(self, workflow_data: Dict, output_path: str = None) -> None:
        """可视化工作流DAG"""
        print("📊 生成工作流DAG可视化...")
        
        # 从workflow_data中提取DAG信息
        dag_data = workflow_data['dag']
        dag = nx.node_link_graph(dag_data)
        
        # 创建图形
        plt.figure(figsize=(12, 8))
        
        # 使用层次布局
        pos = nx.spring_layout(dag, k=3, iterations=50)
        
        # 绘制节点
        nx.draw_networkx_nodes(dag, pos, 
                              node_color=self.colors['tasks'],
                              node_size=1000,
                              alpha=0.8)
        
        # 绘制边
        nx.draw_networkx_edges(dag, pos, 
                              edge_color='gray',
                              arrows=True,
                              arrowsize=20,
                              arrowstyle='->')
        
        # 添加节点标签
        nx.draw_networkx_labels(dag, pos, 
                               font_size=10,
                               font_weight='bold')
        
        plt.title(f'Workflow DAG Structure ({workflow_data["type"]})', fontsize=16, fontweight='bold')
        plt.axis('off')
        
        if output_path is None:
            output_path = os.path.join(self.output_dir, 'workflow_dag.png')
        
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Workflow DAG visualization saved to: {output_path}")
    
    def visualize_node_characteristics(self, node_features: torch.Tensor, output_path: str = None) -> None:
        """可视化节点特征"""
        print("📊 生成节点特征可视化...")
        
        node_features_np = node_features.detach().cpu().numpy()
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('Computational Node Characteristics Analysis', fontsize=16, fontweight='bold')
        
        # 1. CPU容量分布
        axes[0, 0].bar(range(len(node_features_np)), node_features_np[:, 0])
        axes[0, 0].set_title('CPU Capacity Distribution')
        axes[0, 0].set_xlabel('Node ID')
        axes[0, 0].set_ylabel('CPU Capacity (GFLOPS)')
        
        # 2. 内存容量分布
        axes[0, 1].bar(range(len(node_features_np)), node_features_np[:, 1])
        axes[0, 1].set_title('Memory Capacity Distribution')
        axes[0, 1].set_xlabel('Node ID')
        axes[0, 1].set_ylabel('Memory Capacity (GB)')
        
        # 3. I/O容量分布
        axes[1, 0].bar(range(len(node_features_np)), node_features_np[:, 2])
        axes[1, 0].set_title('I/O Capacity Distribution')
        axes[1, 0].set_xlabel('Node ID')
        axes[1, 0].set_ylabel('I/O Capacity (MB/s)')
        
        # 4. 网络容量分布
        axes[1, 1].bar(range(len(node_features_np)), node_features_np[:, 3])
        axes[1, 1].set_title('Network Capacity Distribution')
        axes[1, 1].set_xlabel('Node ID')
        axes[1, 1].set_ylabel('Network Capacity (Mbps)')
        
        plt.tight_layout()
        
        if output_path is None:
            output_path = os.path.join(self.output_dir, 'node_characteristics.png')
        
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Node characteristics visualization saved to: {output_path}")
    
    def visualize_final_assignment(self, assignment_probs: torch.Tensor, 
                                 workflow_data: Dict, output_path: str = None) -> None:
        """可视化最终分配结果"""
        print("📊 生成最终分配结果可视化...")
        
        assignment_np = assignment_probs.detach().cpu().numpy()
        
        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Workflow Task Assignment Results', fontsize=16, fontweight='bold')
        
        # 1. 分配概率热力图
        im1 = axes[0, 0].imshow(assignment_np[0], cmap='YlOrRd', aspect='auto')
        axes[0, 0].set_title('Task-Node Assignment Probability')
        axes[0, 0].set_xlabel('Node ID')
        axes[0, 0].set_ylabel('Task ID')
        plt.colorbar(im1, ax=axes[0, 0])
        
        # 2. 最优分配结果
        optimal_assignment = np.argmax(assignment_np[0], axis=1)
        axes[0, 1].scatter(range(len(optimal_assignment)), optimal_assignment, 
                          c=optimal_assignment, cmap='tab10', s=100)
        axes[0, 1].set_title('Optimal Assignment Results')
        axes[0, 1].set_xlabel('Task ID')
        axes[0, 1].set_ylabel('Assigned Node ID')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 节点负载分布
        node_loads = np.sum(assignment_np[0], axis=0)
        axes[1, 0].bar(range(len(node_loads)), node_loads, color=self.colors['nodes'])
        axes[1, 0].set_title('Node Load Distribution')
        axes[1, 0].set_xlabel('Node ID')
        axes[1, 0].set_ylabel('Load Weight')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 任务分配统计
        task_assignment_conf = np.max(assignment_np[0], axis=1)
        axes[1, 1].hist(task_assignment_conf, bins=10, color=self.colors['assignment'], alpha=0.7)
        axes[1, 1].set_title('Task Assignment Confidence Distribution')
        axes[1, 1].set_xlabel('Assignment Confidence')
        axes[1, 1].set_ylabel('Number of Tasks')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if output_path is None:
            output_path = os.path.join(self.output_dir, 'final_assignment.png')
        
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Final assignment visualization saved to: {output_path}")
    
    def visualize_comprehensive_results(self, debug_info: Dict[str, Any], 
                                      workflow_data: Dict, 
                                      assignment_probs: torch.Tensor) -> None:
        """生成综合结果可视化"""
        print("📊 生成综合结果可视化...")
        
        # 创建大型综合图表
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)
        
        # 1. 工作流DAG (左上)
        ax1 = fig.add_subplot(gs[0, :2])
        if 'dag' in workflow_data:
            dag_data = workflow_data['dag']
            dag = nx.node_link_graph(dag_data)
            pos = nx.spring_layout(dag, k=2, iterations=30)
            nx.draw(dag, pos, ax=ax1, 
                    node_color=self.colors['tasks'], 
                    node_size=800,
                    with_labels=True,
                    font_size=8,
                    font_weight='bold')
            ax1.set_title('Workflow DAG Structure', fontweight='bold')
        else:
            ax1.text(0.5, 0.5, 'DAG data not available', ha='center', va='center', transform=ax1.transAxes)
            ax1.set_title('Workflow DAG Structure', fontweight='bold')
        
        # 2. 节点特征 (右上)
        ax2 = fig.add_subplot(gs[0, 2:])
        if 'node_features' in workflow_data:
            node_features = torch.tensor(workflow_data['node_features'])
            node_features_np = node_features.detach().cpu().numpy()
            ax2.bar(range(len(node_features_np)), node_features_np[:, 0], 
                    color=self.colors['nodes'], alpha=0.7)
            ax2.set_title('Node CPU Capacity Distribution', fontweight='bold')
            ax2.set_xlabel('Node ID')
            ax2.set_ylabel('CPU Capacity')
        else:
            ax2.text(0.5, 0.5, 'Node features not available', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('Node CPU Capacity Distribution', fontweight='bold')
        
        # 3. Transformer层输出 (中上)
        ax3 = fig.add_subplot(gs[1, :2])
        if 'transformer' in debug_info['layer_outputs']:
            transformer_output = debug_info['layer_outputs']['transformer'].detach().cpu().numpy()
            im3 = ax3.imshow(transformer_output[0], cmap='viridis', aspect='auto')
            ax3.set_title('DAG Transformer Layer Output', fontweight='bold')
            ax3.set_xlabel('Feature Dimension')
            ax3.set_ylabel('Task ID')
            plt.colorbar(im3, ax=ax3)
        
        # 4. PINN约束层输出 (中下)
        ax4 = fig.add_subplot(gs[1, 2:])
        if 'constraint_enhanced' in debug_info['layer_outputs']:
            constraint_output = debug_info['layer_outputs']['constraint_enhanced'].detach().cpu().numpy()
            im4 = ax4.imshow(constraint_output[0], cmap='plasma', aspect='auto')
            ax4.set_title('PINN Constraint Enhanced Layer Output', fontweight='bold')
            ax4.set_xlabel('Feature Dimension')
            ax4.set_ylabel('Task ID')
            plt.colorbar(im4, ax=ax4)
        
        # 5. 约束损失 (左下)
        ax5 = fig.add_subplot(gs[2, :2])
        if 'constraint_losses' in debug_info['losses']:
            constraint_losses = debug_info['losses']['constraint_losses']
            loss_names = list(constraint_losses.keys())
            loss_values = [constraint_losses[name].item() for name in loss_names if name != 'total']
            colors = [self.colors['pinn'] if 'constraint' in name else self.colors['transformer'] for name in loss_names if name != 'total']
            
            # 确保长度一致，避免广播错误
            n = min(len(loss_names), len(loss_values), len(colors))
            if n > 0:
                loss_names = loss_names[:n]
                loss_values = loss_values[:n]
                colors = colors[:n]
                ax5.bar(loss_names, loss_values, color=colors, alpha=0.7)
            else:
                ax5.text(0.5, 0.5, 'No constraint losses available', ha='center', va='center', transform=ax5.transAxes)
            
            ax5.set_title('Constraint Loss Distribution', fontweight='bold')
            ax5.set_ylabel('Loss Value')
            ax5.tick_params(axis='x', rotation=45)
        
        # 6. 最终分配结果 (右下)
        ax6 = fig.add_subplot(gs[2, 2:])
        assignment_np = assignment_probs.detach().cpu().numpy()
        im6 = ax6.imshow(assignment_np[0], cmap='YlOrRd', aspect='auto')
        ax6.set_title('Final Task Assignment Results', fontweight='bold')
        ax6.set_xlabel('Node ID')
        ax6.set_ylabel('Task ID')
        plt.colorbar(im6, ax=ax6)
        
        # 7. 分配统计 (底部)
        ax7 = fig.add_subplot(gs[3, :])
        optimal_assignment = np.argmax(assignment_np[0], axis=1)
        node_loads = np.sum(assignment_np[0], axis=0)
        
        # 双轴图
        ax7_twin = ax7.twinx()
        
        # 分配结果
        bars1 = ax7.bar(range(len(optimal_assignment)), optimal_assignment, 
                       alpha=0.6, color=self.colors['assignment'], label='Assigned Node')
        
        # 节点负载
        bars2 = ax7_twin.bar(range(len(node_loads)), node_loads, 
                            alpha=0.4, color=self.colors['nodes'], label='Node Load')
        
        ax7.set_title('Assignment Results vs Node Load Comparison', fontweight='bold')
        ax7.set_xlabel('Task/Node ID')
        ax7.set_ylabel('Assigned Node ID', color=self.colors['assignment'])
        ax7_twin.set_ylabel('Load Weight', color=self.colors['nodes'])
        
        # 添加图例
        ax7.legend(loc='upper left')
        ax7_twin.legend(loc='upper right')
        
        plt.tight_layout()
        
        output_path = os.path.join(self.output_dir, 'comprehensive_results.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Comprehensive results visualization saved to: {output_path}")
    
    def _plot_feature_heatmap(self, ax, data: np.ndarray, title: str, xlabel: str, ylabel: str) -> None:
        """绘制特征热力图"""
        im = ax.imshow(data, cmap='viridis', aspect='auto')
        ax.set_title(title, fontweight='bold')
        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        plt.colorbar(im, ax=ax)
    
    def _plot_constraint_losses(self, ax, constraint_losses: Dict[str, torch.Tensor]) -> None:
        """绘制约束损失"""
        loss_names = list(constraint_losses.keys())
        loss_values = [constraint_losses[name].item() for name in loss_names if name != 'total']
        colors = [self.colors['pinn'] if 'constraint' in name else self.colors['transformer'] for name in loss_names if name != 'total']
        
        # 确保长度一致，避免广播错误
        n = min(len(loss_names), len(loss_values), len(colors))
        if n == 0:
            ax.text(0.5, 0.5, 'No constraint losses available', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('Constraint Loss Distribution', fontweight='bold')
            return
            
        loss_names = loss_names[:n]
        loss_values = loss_values[:n]
        colors = colors[:n]
        
        bars = ax.bar(loss_names, loss_values, color=colors, alpha=0.7)
        ax.set_title('Constraint Loss Distribution', fontweight='bold')
        ax.set_ylabel('Loss Value')
        ax.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars, loss_values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=8)
    
    def _plot_assignment_matrix(self, ax, data: np.ndarray, title: str, xlabel: str, ylabel: str) -> None:
        """绘制分配矩阵"""
        im = ax.imshow(data, cmap='YlOrRd', aspect='auto')
        ax.set_title(title, fontweight='bold')
        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        plt.colorbar(im, ax=ax)