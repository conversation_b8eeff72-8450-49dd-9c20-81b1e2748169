import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Dict, Any
import numpy as np


class DAGPositionalEncoding(nn.Module):
    """DAG专用位置编码"""

    def __init__(self, d_model: int, max_len: int = 1000):
        super().__init__()
        self.d_model = d_model

        # 传统位置编码
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                             (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe.unsqueeze(0))

        # DAG结构编码
        self.topo_embedding = nn.Embedding(max_len, d_model // 4)
        self.depth_embedding = nn.Embedding(max_len, d_model // 4)
        self.critical_path_embedding = nn.Embedding(2, d_model // 4)  # 是否在关键路径上
        self.dependency_embedding = nn.Embedding(max_len, d_model // 4)  # 依赖数量

    def forward(self, x: torch.Tensor, dag_info: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Args:
            x: [batch_size, seq_len, d_model]
            dag_info: {
                'topo_order': [batch_size, seq_len],
                'depth': [batch_size, seq_len], 
                'critical_path': [batch_size, seq_len],
                'dependency_count': [batch_size, seq_len]
            }
        """
        batch_size, seq_len, d_model = x.size()

        # 传统位置编码
        pos_encoding = self.pe[:, :seq_len, :]

        # DAG结构编码
        topo_enc = self.topo_embedding(dag_info['topo_order'])
        depth_enc = self.depth_embedding(dag_info['depth'])
        critical_enc = self.critical_path_embedding(dag_info['critical_path'])
        dep_enc = self.dependency_embedding(dag_info['dependency_count'])

        # 拼接所有编码
        dag_encoding = torch.cat([topo_enc, depth_enc, critical_enc, dep_enc], dim=-1)

        # 组合编码
        combined_encoding = pos_encoding + dag_encoding

        return x + combined_encoding


class DAGAwareAttention(nn.Module):
    """DAG感知的注意力机制"""

    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        assert d_model % num_heads == 0

        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads

        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)

        self.dropout = nn.Dropout(dropout)

        # DAG结构感知权重
        self.dependency_weight = nn.Parameter(torch.ones(1))
        self.distance_weight = nn.Parameter(torch.ones(1))

    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None,
                adjacency_matrix: Optional[torch.Tensor] = None,
                distance_matrix: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            x: [batch_size, seq_len, d_model]
            mask: [batch_size, seq_len, seq_len] 
            adjacency_matrix: [batch_size, seq_len, seq_len] DAG邻接矩阵
            distance_matrix: [batch_size, seq_len, seq_len] 任务间距离矩阵
        """
        batch_size, seq_len, d_model = x.size()

        # 计算Q, K, V
        Q = self.w_q(x).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(x).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(x).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)

        # 标准注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)

        # 融合DAG结构信息
        if adjacency_matrix is not None:
            # 依赖关系增强
            dependency_boost = adjacency_matrix.unsqueeze(1) * self.dependency_weight
            scores = scores + dependency_boost

        if distance_matrix is not None:
            # 距离衰减
            distance_decay = -distance_matrix.unsqueeze(1) * self.distance_weight
            scores = scores + distance_decay

        # 应用掩码
        if mask is not None:
            scores.masked_fill_(mask.unsqueeze(1) == 0, -1e9)

        # 注意力权重和输出
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        context = torch.matmul(attention_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)

        output = self.w_o(context)
        return output


class DAGTransformerLayer(nn.Module):
    """DAG Transformer层"""

    def __init__(self, d_model: int, num_heads: int, d_ff: int, dropout: float = 0.1):
        super().__init__()

        self.self_attention = DAGAwareAttention(d_model, num_heads, dropout)
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model)
        )

        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None,
                adjacency_matrix: Optional[torch.Tensor] = None,
                distance_matrix: Optional[torch.Tensor] = None) -> torch.Tensor:
        # 自注意力
        attn_output = self.self_attention(x, mask, adjacency_matrix, distance_matrix)
        x = self.norm1(x + self.dropout(attn_output))

        # 前馈网络
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))

        return x


class DAGTransformer(nn.Module):
    """DAG Transformer基础层"""

    def __init__(self, input_dim: int, d_model: int, num_heads: int,
                 num_layers: int, d_ff: int, max_len: int = 1000, dropout: float = 0.1):
        super().__init__()

        self.d_model = d_model
        self.input_projection = nn.Linear(input_dim, d_model)
        self.positional_encoding = DAGPositionalEncoding(d_model, max_len)

        self.transformer_layers = nn.ModuleList([
            DAGTransformerLayer(d_model, num_heads, d_ff, dropout)
            for _ in range(num_layers)
        ])

        self.output_projection = nn.Linear(d_model, d_model)
        self.dropout = nn.Dropout(dropout)

    def compute_dag_info(self, adjacency_matrix: torch.Tensor) -> Dict[str, torch.Tensor]:
        """计算DAG结构信息"""
        batch_size, seq_len, _ = adjacency_matrix.size()
        device = adjacency_matrix.device

        dag_info = {}

        # 拓扑排序 (简化版本)
        topo_order = torch.arange(seq_len, device=device).unsqueeze(0).repeat(batch_size, 1)
        dag_info['topo_order'] = topo_order

        # 计算深度（从源节点的最长路径）
        depth = torch.zeros(batch_size, seq_len, device=device, dtype=torch.long)
        for i in range(seq_len):
            # 简化深度计算：基于前驱节点数量
            predecessors = adjacency_matrix[:, :, i].sum(dim=1)
            depth[:, i] = predecessors.long()
        dag_info['depth'] = depth.clamp(max=999)  # 限制最大值

        # 关键路径标记（简化版本）
        critical_path = torch.zeros(batch_size, seq_len, device=device, dtype=torch.long)
        # 假设度数最高的节点在关键路径上
        total_degree = adjacency_matrix.sum(dim=1) + adjacency_matrix.sum(dim=2)
        threshold = total_degree.max(dim=1, keepdim=True)[0] * 0.8
        critical_path = (total_degree >= threshold).long()
        dag_info['critical_path'] = critical_path

        # 依赖数量
        dependency_count = adjacency_matrix.sum(dim=1).long().clamp(max=999)
        dag_info['dependency_count'] = dependency_count

        return dag_info

    def compute_distance_matrix(self, adjacency_matrix: torch.Tensor) -> torch.Tensor:
        """计算任务间距离矩阵"""
        batch_size, seq_len, _ = adjacency_matrix.size()
        device = adjacency_matrix.device

        # 使用Floyd-Warshall算法计算最短路径（简化版本）
        distance_matrix = torch.full((batch_size, seq_len, seq_len), float('inf'), device=device)

        # 初始化直接连接的距离
        distance_matrix = torch.where(adjacency_matrix > 0,
                                      adjacency_matrix,
                                      distance_matrix)

        # 对角线设为0
        for i in range(seq_len):
            distance_matrix[:, i, i] = 0

        # Floyd-Warshall
        for k in range(seq_len):
            for i in range(seq_len):
                for j in range(seq_len):
                    distance_matrix[:, i, j] = torch.min(
                        distance_matrix[:, i, j],
                        distance_matrix[:, i, k] + distance_matrix[:, k, j]
                    )

        # 将无穷大设为最大距离
        distance_matrix = torch.where(torch.isinf(distance_matrix),
                                      torch.tensor(seq_len, dtype=distance_matrix.dtype, device=device),
                                      distance_matrix)

        return distance_matrix

    def forward(self, x: torch.Tensor, adjacency_matrix: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            x: [batch_size, seq_len, input_dim] 任务特征
            adjacency_matrix: [batch_size, seq_len, seq_len] DAG邻接矩阵
            mask: [batch_size, seq_len] 序列掩码
        """
        # 输入投影
        x = self.input_projection(x)

        # DAG结构信息
        dag_info = self.compute_dag_info(adjacency_matrix)
        distance_matrix = self.compute_distance_matrix(adjacency_matrix)

        # 位置编码
        x = self.positional_encoding(x, dag_info)
        x = self.dropout(x)

        # 创建注意力掩码
        if mask is not None:
            attention_mask = mask.unsqueeze(1).unsqueeze(2)  # [batch_size, 1, 1, seq_len]
            attention_mask = attention_mask.expand(-1, -1, mask.size(1), -1)
        else:
            attention_mask = None

        # Transformer层
        for layer in self.transformer_layers:
            x = layer(x, attention_mask, adjacency_matrix, distance_matrix)

        # 输出投影
        x = self.output_projection(x)

        return x