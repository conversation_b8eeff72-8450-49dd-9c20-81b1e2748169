import networkx as nx
import numpy as np
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors


class ResourceType(Enum):
    """资源类型枚举"""
    CPU_INTENSIVE = "cpu_intensive"
    MEMORY_INTENSIVE = "memory_intensive"
    IO_INTENSIVE = "io_intensive"
    NETWORK_INTENSIVE = "network_intensive"
    MIXED = "mixed"


@dataclass
class ColoringResult:
    """图着色结果"""
    task_colors: Dict[int, int]  # 任务ID -> 颜色
    task_types: Dict[int, ResourceType]  # 任务ID -> 资源类型
    color_features: Dict[int, np.ndarray]  # 任务ID -> 颜色特征向量
    parallelizable_groups: List[Set[int]]  # 可并行执行的任务组
    critical_path_colors: List[int]  # 关键路径上的颜色序列
    coloring_quality: Dict[str, float]  # 着色质量指标


class ImprovedGraphColoring:
    """改进的图着色算法"""

    def __init__(self, alpha: float = 0.6, beta: float = 0.3, gamma: float = 0.1):
        """
        初始化图着色算法

        Args:
            alpha: 资源主导性权重
            beta: 依赖关系权重  
            gamma: 负载均衡权重
        """
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma

        # 颜色到资源类型的映射
        self.color_to_type = {
            0: ResourceType.CPU_INTENSIVE,  # 红色
            1: ResourceType.MEMORY_INTENSIVE,  # 蓝色
            2: ResourceType.IO_INTENSIVE,  # 绿色
            3: ResourceType.NETWORK_INTENSIVE,  # 黄色
            4: ResourceType.MIXED  # 紫色
        }

        # 预定义颜色特征向量 (5维独热编码 + 3维资源强度)
        self.color_feature_dim = 8

    def analyze_task_resource_dominance(self, task_features: Dict) -> ResourceType:
        """分析任务的资源主导类型"""
        cpu_intensity = task_features.get('cpu_demand', 0)
        memory_intensity = task_features.get('memory_demand', 0)
        io_intensity = task_features.get('io_demand', 0)
        network_intensity = task_features.get('network_demand', 0)

        # 归一化
        total_intensity = cpu_intensity + memory_intensity + io_intensity + network_intensity
        if total_intensity == 0:
            return ResourceType.MIXED

        cpu_ratio = cpu_intensity / total_intensity
        memory_ratio = memory_intensity / total_intensity
        io_ratio = io_intensity / total_intensity
        network_ratio = network_intensity / total_intensity

        # 判断主导资源类型
        dominance_threshold = 0.4

        if cpu_ratio > dominance_threshold:
            return ResourceType.CPU_INTENSIVE
        elif memory_ratio > dominance_threshold:
            return ResourceType.MEMORY_INTENSIVE
        elif io_ratio > dominance_threshold:
            return ResourceType.IO_INTENSIVE
        elif network_ratio > dominance_threshold:
            return ResourceType.NETWORK_INTENSIVE
        else:
            return ResourceType.MIXED

    def compute_task_priority(self, dag: nx.DiGraph, task_id: int) -> float:
        """计算任务优先级（基于关键路径和依赖关系）"""
        # 计算到结束节点的最长路径（向后）
        try:
            # 找到所有出度为0的节点（结束节点）
            end_nodes = [n for n in dag.nodes() if dag.out_degree(n) == 0]
            if not end_nodes:
                return 0.0

            max_path_length = 0
            for end_node in end_nodes:
                try:
                    if nx.has_path(dag, task_id, end_node):
                        path_length = nx.shortest_path_length(dag, task_id, end_node, weight='runtime')
                        max_path_length = max(max_path_length, path_length)
                except:
                    continue

            return max_path_length
        except:
            return 0.0

    def construct_conflict_graph(self, dag: nx.DiGraph) -> nx.Graph:
        """构建冲突图（资源冲突和时间冲突）"""
        conflict_graph = nx.Graph()

        # 添加所有任务节点
        for node in dag.nodes():
            conflict_graph.add_node(node)

        # 添加冲突边
        for task1 in dag.nodes():
            for task2 in dag.nodes():
                if task1 >= task2:  # 避免重复
                    continue

                # 检查是否存在冲突
                if self._has_resource_conflict(dag, task1, task2) or \
                        self._has_temporal_conflict(dag, task1, task2):
                    conflict_graph.add_edge(task1, task2)

        return conflict_graph

    def _has_resource_conflict(self, dag: nx.DiGraph, task1: int, task2: int) -> bool:
        """检查两个任务是否存在资源冲突"""
        task1_features = dag.nodes[task1]
        task2_features = dag.nodes[task2]

        # 同一资源类型的任务可能存在冲突
        task1_type = self.analyze_task_resource_dominance(task1_features)
        task2_type = self.analyze_task_resource_dominance(task2_features)

        # 如果都是相同的资源密集型任务，则可能冲突
        if task1_type == task2_type and task1_type != ResourceType.MIXED:
            return True

        # 检查具体资源需求是否超过阈值
        resource_conflict_threshold = 0.7

        cpu_conflict = (task1_features.get('cpu_demand', 0) + task2_features.get('cpu_demand',
                                                                                 0)) > resource_conflict_threshold
        memory_conflict = (task1_features.get('memory_demand', 0) + task2_features.get('memory_demand',
                                                                                       0)) > resource_conflict_threshold

        return cpu_conflict or memory_conflict

    def _has_temporal_conflict(self, dag: nx.DiGraph, task1: int, task2: int) -> bool:
        """检查两个任务是否存在时间冲突（依赖关系）"""
        # 直接依赖关系
        if dag.has_edge(task1, task2) or dag.has_edge(task2, task1):
            return True

        # 间接依赖关系（通过路径连接）
        try:
            if nx.has_path(dag, task1, task2) or nx.has_path(dag, task2, task1):
                return True
        except:
            pass

        return False

    def improved_graph_coloring(self, dag: nx.DiGraph) -> ColoringResult:
        """改进的图着色算法"""
        # 1. 分析任务资源类型
        task_types = {}
        task_priorities = {}

        for task_id in dag.nodes():
            task_features = dag.nodes[task_id]
            task_types[task_id] = self.analyze_task_resource_dominance(task_features)
            task_priorities[task_id] = self.compute_task_priority(dag, task_id)

        # 2. 构建冲突图
        conflict_graph = self.construct_conflict_graph(dag)

        # 3. 改进的着色算法
        task_colors = self._adaptive_coloring_algorithm(
            dag, conflict_graph, task_types, task_priorities
        )

        # 4. 生成颜色特征向量
        color_features = self._generate_color_features(dag, task_colors, task_types)

        # 5. 识别可并行执行的任务组
        parallelizable_groups = self._identify_parallelizable_groups(dag, task_colors)

        # 6. 分析关键路径颜色
        critical_path_colors = self._analyze_critical_path_colors(dag, task_colors)

        # 7. 计算着色质量
        coloring_quality = self._evaluate_coloring_quality(
            dag, conflict_graph, task_colors, task_types
        )

        return ColoringResult(
            task_colors=task_colors,
            task_types=task_types,
            color_features=color_features,
            parallelizable_groups=parallelizable_groups,
            critical_path_colors=critical_path_colors,
            coloring_quality=coloring_quality
        )

    def _adaptive_coloring_algorithm(self, dag: nx.DiGraph, conflict_graph: nx.Graph,
                                     task_types: Dict[int, ResourceType],
                                     task_priorities: Dict[int, float]) -> Dict[int, int]:
        """自适应图着色算法"""
        task_colors = {}
        available_colors = set(range(len(self.color_to_type)))

        # 按优先级排序任务
        sorted_tasks = sorted(dag.nodes(),
                              key=lambda x: (task_priorities[x], len(list(conflict_graph.neighbors(x)))),
                              reverse=True)

        for task_id in sorted_tasks:
            # 获取邻居使用的颜色
            neighbor_colors = set()
            for neighbor in conflict_graph.neighbors(task_id):
                if neighbor in task_colors:
                    neighbor_colors.add(task_colors[neighbor])

            # 优先选择与资源类型匹配的颜色
            preferred_color = self._get_preferred_color(task_types[task_id])

            if preferred_color not in neighbor_colors:
                task_colors[task_id] = preferred_color
            else:
                # 选择可用的最小颜色
                available = available_colors - neighbor_colors
                if available:
                    task_colors[task_id] = min(available)
                else:
                    # 需要新颜色
                    new_color = max(available_colors) + 1
                    available_colors.add(new_color)
                    task_colors[task_id] = new_color

        return task_colors

    def _get_preferred_color(self, resource_type: ResourceType) -> int:
        """获取资源类型的首选颜色"""
        type_to_color = {v: k for k, v in self.color_to_type.items()}
        return type_to_color.get(resource_type, 4)  # 默认为混合类型

    def _generate_color_features(self, dag: nx.DiGraph, task_colors: Dict[int, int],
                                 task_types: Dict[int, ResourceType]) -> Dict[int, np.ndarray]:
        """生成颜色特征向量"""
        color_features = {}

        for task_id in dag.nodes():
            task_features = dag.nodes[task_id]
            color = task_colors[task_id]

            # 8维特征向量：[5维颜色独热编码] + [3维资源强度]
            feature_vector = np.zeros(self.color_feature_dim)

            # 颜色独热编码 (前5维)
            if color < 5:
                feature_vector[color] = 1.0
            else:
                feature_vector[4] = 1.0  # 其他颜色归为混合类型

            # 资源强度特征 (后3维)
            total_demand = (task_features.get('cpu_demand', 0) +
                            task_features.get('memory_demand', 0) +
                            task_features.get('io_demand', 0) +
                            task_features.get('network_demand', 0))

            if total_demand > 0:
                # 归一化资源强度
                feature_vector[5] = min(task_features.get('cpu_demand', 0) / total_demand, 1.0)
                feature_vector[6] = min(task_features.get('memory_demand', 0) / total_demand, 1.0)
                feature_vector[7] = min((task_features.get('io_demand', 0) +
                                         task_features.get('network_demand', 0)) / total_demand, 1.0)

            color_features[task_id] = feature_vector

        return color_features

    def _identify_parallelizable_groups(self, dag: nx.DiGraph,
                                        task_colors: Dict[int, int]) -> List[Set[int]]:
        """识别可并行执行的任务组"""
        # 按颜色分组
        color_groups = {}
        for task_id, color in task_colors.items():
            if color not in color_groups:
                color_groups[color] = set()
            color_groups[color].add(task_id)

        parallelizable_groups = []

        for color, tasks in color_groups.items():
            if len(tasks) > 1:
                # 验证同颜色任务之间确实可以并行执行
                can_parallel = True
                task_list = list(tasks)

                for i in range(len(task_list)):
                    for j in range(i + 1, len(task_list)):
                        task1, task2 = task_list[i], task_list[j]
                        # 检查是否存在依赖关系
                        if (nx.has_path(dag, task1, task2) or
                                nx.has_path(dag, task2, task1)):
                            can_parallel = False
                            break
                    if not can_parallel:
                        break

                if can_parallel:
                    parallelizable_groups.append(tasks)

        return parallelizable_groups

    def _analyze_critical_path_colors(self, dag: nx.DiGraph,
                                      task_colors: Dict[int, int]) -> List[int]:
        """分析关键路径上的颜色序列"""
        try:
            # 计算关键路径
            critical_path = nx.dag_longest_path(dag, weight='runtime')
            critical_path_colors = [task_colors[task_id] for task_id in critical_path]
            return critical_path_colors
        except:
            return []

    def _evaluate_coloring_quality(self, dag: nx.DiGraph, conflict_graph: nx.Graph,
                                   task_colors: Dict[int, int],
                                   task_types: Dict[int, ResourceType]) -> Dict[str, float]:
        """评估着色质量"""
        quality_metrics = {}

        # 1. 颜色数量（越少越好）
        num_colors = len(set(task_colors.values()))
        quality_metrics['num_colors'] = num_colors

        # 2. 冲突率（应该为0）
        conflicts = 0
        total_edges = conflict_graph.number_of_edges()

        for edge in conflict_graph.edges():
            task1, task2 = edge
            if task_colors[task1] == task_colors[task2]:
                conflicts += 1

        quality_metrics['conflict_rate'] = conflicts / max(total_edges, 1)

        # 3. 负载均衡度
        color_counts = {}
        for color in task_colors.values():
            color_counts[color] = color_counts.get(color, 0) + 1

        if color_counts:
            avg_tasks_per_color = len(task_colors) / num_colors
            balance_variance = np.var(list(color_counts.values())) / (avg_tasks_per_color ** 2)
            quality_metrics['load_balance'] = 1.0 / (1.0 + balance_variance)
        else:
            quality_metrics['load_balance'] = 1.0

        # 4. 资源类型一致性
        type_consistency = 0
        for task_id, color in task_colors.items():
            preferred_color = self._get_preferred_color(task_types[task_id])
            if color == preferred_color:
                type_consistency += 1

        quality_metrics['type_consistency'] = type_consistency / len(task_colors)

        return quality_metrics

    def visualize_coloring(self, dag: nx.DiGraph, coloring_result: ColoringResult,
                           save_path: Optional[str] = None) -> None:
        """可视化图着色结果"""
        plt.figure(figsize=(15, 10))

        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

        # 1. 原始DAG结构
        pos = nx.spring_layout(dag, k=2, iterations=50)
        nx.draw_networkx_nodes(dag, pos, ax=ax1, node_color='lightblue',
                               node_size=500, alpha=0.7)
        nx.draw_networkx_edges(dag, pos, ax=ax1, alpha=0.5, arrows=True)
        nx.draw_networkx_labels(dag, pos, ax=ax1, font_size=8)
        ax1.set_title('Original DAG Structure')
        ax1.axis('off')

        # 2. 着色后的DAG
        colors = list(mcolors.TABLEAU_COLORS.keys())
        task_colors = coloring_result.task_colors
        node_colors = [colors[task_colors[node] % len(colors)] for node in dag.nodes()]

        nx.draw_networkx_nodes(dag, pos, ax=ax2, node_color=node_colors,
                               node_size=500, alpha=0.8)
        nx.draw_networkx_edges(dag, pos, ax=ax2, alpha=0.5, arrows=True)
        nx.draw_networkx_labels(dag, pos, ax=ax2, font_size=8)
        ax2.set_title(f'Colored DAG ({coloring_result.coloring_quality["num_colors"]} colors)')
        ax2.axis('off')

        # 3. 资源类型分布
        type_counts = {}
        for task_type in coloring_result.task_types.values():
            type_counts[task_type.value] = type_counts.get(task_type.value, 0) + 1

        ax3.pie(type_counts.values(), labels=type_counts.keys(), autopct='%1.1f%%')
        ax3.set_title('Resource Type Distribution')

        # 4. 着色质量指标
        quality_metrics = coloring_result.coloring_quality
        metrics_names = list(quality_metrics.keys())
        metrics_values = list(quality_metrics.values())

        bars = ax4.bar(metrics_names, metrics_values)
        ax4.set_title('Coloring Quality Metrics')
        ax4.set_ylabel('Score')
        ax4.set_ylim(0, 1.1)

        # 添加数值标签
        for bar, value in zip(bars, metrics_values):
            ax4.text(bar.get_x() + bar.get_width() / 2, bar.get_height() + 0.01,
                     f'{value:.3f}', ha='center', va='bottom')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()