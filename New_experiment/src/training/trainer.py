import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import time
import os
import json
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns

from ..models.three_layer_gnn import ThreeLayerGNNScheduler
from ..evaluation.metrics import SchedulingMetrics
from ..utils.logger import get_logger


class GNNTrainer:
    """GNN调度器训练器"""

    def __init__(self, model: ThreeLayerGNNScheduler, config,
                 device: torch.device):
        self.model = model.to(device)
        self.config = config
        self.device = device
        self.logger = get_logger(__name__)

        # 训练配置 - 支持两种配置格式
        if hasattr(config, 'learning_rate'):
            # 直接配置对象
            self.learning_rate = config.learning_rate
            self.num_epochs = config.num_epochs
            self.batch_size = config.batch_size
            self.weight_decay = config.weight_decay
            self.patience = config.patience
        else:
            # 字典格式配置
            training_config = config.get('training', config)
            self.learning_rate = training_config.get('learning_rate', 1e-3)
            self.num_epochs = training_config.get('num_epochs', 100)
            self.batch_size = training_config.get('batch_size', 32)
            self.weight_decay = training_config.get('weight_decay', 1e-4)
            self.patience = training_config.get('patience', 10)

        # 优化器和调度器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay
        )

        self.lr_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )

        # 损失函数
        self.criterion = nn.CrossEntropyLoss()

        # 评估指标
        self.metrics = SchedulingMetrics()

        # 训练历史
        self.train_history = {
            'losses': [], 'val_losses': [], 'metrics': [], 'lr': []
        }

        # 最佳模型
        self.best_val_loss = float('inf')
        self.best_model_state = None
        self.epochs_without_improvement = 0

    def train_epoch(self, train_loader: DataLoader, debug_mode: bool = False) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()

        total_loss = 0.0
        total_assignment_loss = 0.0
        total_constraint_loss = 0.0
        num_batches = len(train_loader)

        with tqdm(train_loader, desc="Training", leave=False) as pbar:
            for batch_idx, batch_data in enumerate(pbar):
                # 移动数据到设备
                batch_data = self._move_batch_to_device(batch_data)

                # 前向传播
                assignment_probs, debug_info = self.model(batch_data, debug_mode=debug_mode)

                # 计算损失
                ground_truth = batch_data['ground_truth_assignments']
                constraint_losses = debug_info['losses']['final_constraint_losses']

                losses = self.model.compute_total_loss(
                    assignment_probs, ground_truth, constraint_losses
                )

                total_loss_value = losses['total']
                assignment_loss = losses['assignment']
                constraint_loss = losses['constraint_total']

                # 反向传播
                self.optimizer.zero_grad()
                total_loss_value.backward()

                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                self.optimizer.step()

                # 统计
                total_loss += total_loss_value.item()
                total_assignment_loss += assignment_loss.item()
                total_constraint_loss += constraint_loss.item()

                # 更新进度条
                pbar.set_postfix({
                    'Loss': f'{total_loss_value.item():.4f}',
                    'Assign': f'{assignment_loss.item():.4f}',
                    'Const': f'{constraint_loss.item():.4f}'
                })

                # 调试信息输出
                if debug_mode and batch_idx == 0:
                    self._log_debug_info(debug_info, batch_idx)

        avg_losses = {
            'total': total_loss / num_batches,
            'assignment': total_assignment_loss / num_batches,
            'constraint': total_constraint_loss / num_batches
        }

        return avg_losses

    def validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """验证一个epoch"""
        self.model.eval()

        total_loss = 0.0
        total_assignment_loss = 0.0
        total_constraint_loss = 0.0
        num_batches = len(val_loader)

        all_predictions = []
        all_ground_truths = []
        all_scheduling_metrics = []

        with torch.no_grad():
            with tqdm(val_loader, desc="Validation", leave=False) as pbar:
                for batch_data in pbar:
                    # 移动数据到设备
                    batch_data = self._move_batch_to_device(batch_data)

                    # 前向传播
                    assignment_probs, debug_info = self.model(batch_data, debug_mode=False)

                    # 计算损失
                    ground_truth = batch_data['ground_truth_assignments']
                    constraint_losses = debug_info['losses']['final_constraint_losses']

                    losses = self.model.compute_total_loss(
                        assignment_probs, ground_truth, constraint_losses
                    )

                    total_loss += losses['total'].item()
                    total_assignment_loss += losses['assignment'].item()
                    total_constraint_loss += losses['constraint_total'].item()

                    # 收集预测结果
                    predictions = torch.argmax(assignment_probs, dim=-1)
                    all_predictions.append(predictions.cpu())
                    all_ground_truths.append(ground_truth.cpu())

                    # 计算调度指标
                    batch_metrics = self.metrics.compute_batch_metrics(
                        predictions, batch_data
                    )
                    all_scheduling_metrics.append(batch_metrics)

                    pbar.set_postfix({
                        'Val Loss': f'{losses["total"].item():.4f}'
                    })

        # 汇总指标
        avg_losses = {
            'total': total_loss / num_batches,
            'assignment': total_assignment_loss / num_batches,
            'constraint': total_constraint_loss / num_batches
        }

        # 计算整体调度指标
        overall_metrics = self.metrics.aggregate_metrics(all_scheduling_metrics)

        return {**avg_losses, **overall_metrics}

    def train(self, train_loader: DataLoader, val_loader: DataLoader,
              save_dir: str, debug_mode: bool = False) -> Dict[str, List]:
        """完整训练流程"""
        self.logger.info(f"开始训练，共 {self.num_epochs} 个epoch")
        self.logger.info(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")

        os.makedirs(save_dir, exist_ok=True)

        for epoch in range(self.num_epochs):
            epoch_start_time = time.time()

            # 训练
            train_losses = self.train_epoch(train_loader, debug_mode=(debug_mode and epoch == 0))

            # 验证
            val_metrics = self.validate_epoch(val_loader)

            # 学习率调整
            self.lr_scheduler.step(val_metrics['total'])
            current_lr = self.optimizer.param_groups[0]['lr']

            # 记录历史
            self.train_history['losses'].append(train_losses)
            self.train_history['val_losses'].append(val_metrics)
            self.train_history['lr'].append(current_lr)

            # 检查是否为最佳模型
            if val_metrics['total'] < self.best_val_loss:
                self.best_val_loss = val_metrics['total']
                self.best_model_state = self.model.state_dict().copy()
                self.epochs_without_improvement = 0

                # 保存最佳模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.best_model_state,
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': self.best_val_loss,
                    'config': self.config
                }, os.path.join(save_dir, 'best_model.pth'))

            else:
                self.epochs_without_improvement += 1

            # 计算epoch时间
            epoch_time = time.time() - epoch_start_time

            # 日志输出
            self.logger.info(
                f"Epoch {epoch + 1}/{self.num_epochs} - "
                f"Train Loss: {train_losses['total']:.4f} - "
                f"Val Loss: {val_metrics['total']:.4f} - "
                f"Makespan: {val_metrics.get('makespan', 0):.2f}s - "
                f"Load Balance: {val_metrics.get('load_balance_degree', 0):.3f} - "
                f"LR: {current_lr:.2e} - "
                f"Time: {epoch_time:.2f}s"
            )

            # 早停检查
            if self.epochs_without_improvement >= self.patience:
                self.logger.info(f"Early stopping triggered after {epoch + 1} epochs")
                break

            # 定期保存训练历史
            if (epoch + 1) % 10 == 0:
                self._save_training_history(save_dir)
                self._plot_training_curves(save_dir)

        # 加载最佳模型
        if self.best_model_state is not None:
            self.model.load_state_dict(self.best_model_state)

        # 保存最终结果
        self._save_training_history(save_dir)
        self._plot_training_curves(save_dir)

        return self.train_history

    def _move_batch_to_device(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """将batch数据移动到指定设备"""
        moved_batch = {}
        for key, value in batch_data.items():
            if isinstance(value, torch.Tensor):
                moved_batch[key] = value.to(self.device)
            elif isinstance(value, dict):
                moved_batch[key] = {}
                for k, v in value.items():
                    if isinstance(v, torch.Tensor):
                        moved_batch[key][k] = v.to(self.device)
                    else:
                        moved_batch[key][k] = v
            else:
                moved_batch[key] = value
        return moved_batch

    def _log_debug_info(self, debug_info: Dict[str, Any], batch_idx: int):
        """记录调试信息"""
        self.logger.info(f"=== Debug Info for Batch {batch_idx} ===")

        # 层输出统计
        layer_outputs = debug_info.get('layer_outputs', {})
        for layer_name, output in layer_outputs.items():
            if isinstance(output, torch.Tensor):
                self.logger.info(f"{layer_name}: shape={output.shape}, "
                                 f"mean={output.mean().item():.4f}, "
                                 f"std={output.std().item():.4f}")

        # 损失信息
        losses = debug_info.get('losses', {})
        for loss_name, loss_value in losses.items():
            if isinstance(loss_value, dict):
                for sub_loss, sub_value in loss_value.items():
                    if isinstance(sub_value, torch.Tensor):
                        self.logger.info(f"{loss_name}.{sub_loss}: {sub_value.item():.4f}")
            elif isinstance(loss_value, torch.Tensor):
                self.logger.info(f"{loss_name}: {loss_value.item():.4f}")

    def _save_training_history(self, save_dir: str):
        """保存训练历史"""
        # 转换为可序列化的格式
        serializable_history = {}
        for key, values in self.train_history.items():
            if key in ['losses', 'val_losses']:
                serializable_history[key] = [
                    {k: float(v) if isinstance(v, torch.Tensor) else v for k, v in item.items()}
                    for item in values
                ]
            else:
                serializable_history[key] = [
                    float(item) if isinstance(item, torch.Tensor) else item
                    for item in values
                ]

        with open(os.path.join(save_dir, 'training_history.json'), 'w') as f:
            json.dump(serializable_history, f, indent=2)

    def _plot_training_curves(self, save_dir: str):
        """绘制训练曲线"""
        epochs = range(1, len(self.train_history['losses']) + 1)

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # 损失曲线
        train_total_losses = [item['total'] for item in self.train_history['losses']]
        val_total_losses = [item['total'] for item in self.train_history['val_losses']]

        ax1.plot(epochs, train_total_losses, label='Train Loss', alpha=0.8)
        ax1.plot(epochs, val_total_losses, label='Val Loss', alpha=0.8)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Total Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 分解损失
        train_assign_losses = [item['assignment'] for item in self.train_history['losses']]
        train_const_losses = [item['constraint'] for item in self.train_history['losses']]

        ax2.plot(epochs, train_assign_losses, label='Assignment Loss', alpha=0.8)
        ax2.plot(epochs, train_const_losses, label='Constraint Loss', alpha=0.8)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.set_title('Training Loss Components')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 学习率
        ax3.plot(epochs, self.train_history['lr'], alpha=0.8, color='orange')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_title('Learning Rate Schedule')
        ax3.set_yscale('log')
        ax3.grid(True, alpha=0.3)

        # 验证指标（如果存在）
        if self.train_history['val_losses'] and 'makespan' in self.train_history['val_losses'][0]:
            makespans = [item.get('makespan', 0) for item in self.train_history['val_losses']]
            load_balances = [item.get('load_balance_degree', 0) for item in self.train_history['val_losses']]

            ax4_twin = ax4.twinx()

            line1 = ax4.plot(epochs, makespans, label='Makespan', alpha=0.8, color='blue')
            line2 = ax4_twin.plot(epochs, load_balances, label='Load Balance', alpha=0.8, color='red')

            ax4.set_xlabel('Epoch')
            ax4.set_ylabel('Makespan (s)', color='blue')
            ax4_twin.set_ylabel('Load Balance Degree', color='red')
            ax4.set_title('Validation Metrics')

            lines = line1 + line2
            labels = [l.get_label() for l in lines]
            ax4.legend(lines, labels, loc='best')

            ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
        plt.close()

        self.logger.info(f"训练曲线已保存到 {os.path.join(save_dir, 'training_curves.png')}")