import torch
import psutil
#import GPUtil


def get_device_info():
    """获取设备信息"""

    info = {
        'cpu_count': psutil.cpu_count(),
        'memory_total_gb': psutil.virtual_memory().total / (1024 ** 3),
        'cuda_available': torch.cuda.is_available(),
    }

    if torch.cuda.is_available():
        info['cuda_device_count'] = torch.cuda.device_count()
        info['cuda_devices'] = []

        for i in range(torch.cuda.device_count()):
            device_props = torch.cuda.get_device_properties(i)
            device_info = {
                'name': device_props.name,
                'memory_total_mb': device_props.total_memory / (1024 ** 2),
                'major': device_props.major,
                'minor': device_props.minor,
                'multi_processor_count': device_props.multi_processor_count
            }
            info['cuda_devices'].append(device_info)

    return info


def check_memory_usage():
    """检查内存使用情况"""

    # CPU内存
    memory = psutil.virtual_memory()
    cpu_memory = {
        'total_gb': memory.total / (1024 ** 3),
        'available_gb': memory.available / (1024 ** 3),
        'used_percent': memory.percent
    }

    # GPU内存
    gpu_memory = []
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            memory_allocated = torch.cuda.memory_allocated(i) / (1024 ** 3)
            memory_reserved = torch.cuda.memory_reserved(i) / (1024 ** 3)
            memory_total = torch.cuda.get_device_properties(i).total_memory / (1024 ** 3)

            gpu_memory.append({
                'device': i,
                'allocated_gb': memory_allocated,
                'reserved_gb': memory_reserved,
                'total_gb': memory_total,
                'utilization_percent': (memory_allocated / memory_total) * 100
            })

    return {
        'cpu_memory': cpu_memory,
        'gpu_memory': gpu_memory
    }