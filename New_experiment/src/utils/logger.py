import logging
import os
import sys
from datetime import datetime
from pathlib import Path
import colorama
from colorama import Fore, Back, Style

# 初始化colorama
colorama.init()


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""

    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.RED + Back.WHITE
    }

    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = (self.COLORS[record.levelname] +
                                record.levelname + Style.RESET_ALL)

        return super().format(record)


def setup_logging(level: str = 'INFO', log_dir: str = './logs'):
    """设置日志系统"""

    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)

    # 设置日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)

    # 创建根logger
    logger = logging.getLogger()
    logger.setLevel(log_level)

    # 清除已有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = ColoredFormatter(
        '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(log_level)
    logger.addHandler(console_handler)

    # 文件handler
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    file_handler = logging.FileHandler(
        os.path.join(log_dir, f'gnn_scheduler_{timestamp}.log'),
        encoding='utf-8'
    )
    file_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(logging.DEBUG)  # 文件记录所有级别
    logger.addHandler(file_handler)

    # 设置第三方库日志级别
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

    return logger


def get_logger(name: str):
    """获取指定名称的logger"""
    return logging.getLogger(name)