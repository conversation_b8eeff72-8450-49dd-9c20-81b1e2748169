# ABC算法Python实现 - 负载均衡优化

## 项目简介

本项目将原有的C++版本ABC（人工蜂群）算法完全替换为Python实现，用于解决云计算环境中的任务调度和负载均衡问题。新的Python实现提供了更好的集成性、可维护性和扩展性。

## 🎯 主要改进

### ✅ 完全Python化
- 移除了对C++可执行文件的依赖
- 纯Python实现，便于调试和修改
- 更好的跨平台兼容性

### ✅ 算法增强
- 智能初始化策略，提高可行解生成率
- 改进的约束处理机制
- 多目标优化适应度函数
- 自适应搜索策略

### ✅ 集成优化
- 无缝集成到现有数据生成流程
- 统一的配置管理
- 完整的测试覆盖

## 🚀 快速开始

### 1. 环境要求
```bash
pip install numpy networkx
```

### 2. 基本使用
```python
from abc_algorithm_wrapper import ABCAlgorithmWrapper
import networkx as nx

# 创建ABC算法实例
abc = ABCAlgorithmWrapper(colony_size=50, max_iterations=100, limit=10)

# 定义任务和节点（示例）
tasks = [
    {'id': 0, 'cpu_req': 2.0, 'memory_req': 4096, 'io_req': 100, 'network_req': 200, 'type': 'cpu_intensive'}
]
nodes = [
    {'id': 0, 'cpu_cap': 8.0, 'memory_cap': 16384, 'io_cap': 1000, 'network_cap': 1000, 'type': 'general', 'current_load': 0.3}
]

# 创建任务依赖图
dag = nx.DiGraph()
dag.add_node(0)

# 执行分配
result = abc.allocate_tasks(tasks, nodes, dag)
print(f"分配结果: {result['allocation']}")
print(f"目标函数值: {result['objective_value']:.4f}")
```

### 3. 测试验证
```bash
# 测试ABC算法功能
python New_experiment/src/data_generation/test_abc_integration.py

# 测试数据生成
python New_experiment/src/data_generation/test_data_generation.py
```

## 📊 性能表现

根据测试结果，新的Python实现能够：

- **资源利用率**: 平均 70%+
- **负载均衡度**: 平均 63%+  
- **约束满足**: 100% (在合理资源配置下)
- **算法收敛**: 50-100迭代内收敛

## 🔧 算法参数

### 核心参数
- `colony_size`: 蜂群大小 (建议: 20-100)
- `max_iterations`: 最大迭代次数 (建议: 50-200)
- `limit`: 放弃阈值 (建议: 5-20)

### 推荐配置
```python
# 小规模问题 (< 20任务)
ABCAlgorithmWrapper(colony_size=20, max_iterations=50, limit=5)

# 中等规模问题 (20-100任务)  
ABCAlgorithmWrapper(colony_size=50, max_iterations=100, limit=10)

# 大规模问题 (> 100任务)
ABCAlgorithmWrapper(colony_size=100, max_iterations=200, limit=20)
```

## 📁 文件结构

```
New_experiment/src/data_generation/
├── abc_algorithm_wrapper.py          # ABC算法核心实现
├── gnn_training_data_generator.py    # 数据生成器 (已更新)
├── test_abc_integration.py           # ABC算法测试
├── test_data_generation.py           # 数据生成测试
└── README.md                         # 详细文档
```

## 🎯 核心特性

### 1. 智能初始化
- 优先选择能满足任务资源需求的节点
- 避免生成大量不可行解，提高算法效率

### 2. 约束处理
- 软约束处理：对违反约束的解进行惩罚而非直接丢弃
- 约束违反程度计算，指导搜索方向

### 3. 多目标优化
```python
fitness = 0.4 * resource_utilization + 
          0.4 * load_balance + 
          0.2 * (1.0 / (1.0 + makespan / 100))
```

### 4. 自适应搜索
- 雇佣蜂阶段：局部邻域搜索
- 观察蜂阶段：基于概率的全局搜索
- 侦察蜂阶段：随机重启机制

## 🔍 测试结果示例

### 基本功能测试
```
=== ABC算法分配结果 ===
分配方案: {0: 2, 1: 1, 2: 0, 3: 1}
目标函数值: 0.7384
资源利用率: 0.4996
负载均衡度: 0.9366
完成时间: 22.00

=== 分配结果验证 ===
所有任务都被分配: True
分配的节点都有效: True
资源约束违反数量: 0
✅ ABC算法测试通过!
```

### 批量生成测试
```
=== 批量生成统计 ===
成功生成样本数: 10/10
平均目标函数值: 0.2031
平均资源利用率: 0.6982
平均负载均衡度: 0.6322
✅ 批量生成测试通过!
```

## 🚨 注意事项

1. **资源配置**: 确保节点总容量大于任务总需求
2. **参数调优**: 根据问题规模调整ABC算法参数
3. **依赖管理**: 确保安装了必要的Python包

## 📚 详细文档

更多详细信息请参考：
- [完整API文档](New_experiment/src/data_generation/README.md)
- [算法实现细节](New_experiment/src/data_generation/abc_algorithm_wrapper.py)
- [测试用例](New_experiment/src/data_generation/test_abc_integration.py)

## 🎉 总结

ABC算法的Python实现已经完全替换了原有的C++版本，提供了：

- ✅ **更好的集成性**: 无需外部可执行文件
- ✅ **更强的可维护性**: 纯Python代码，易于调试
- ✅ **更高的可扩展性**: 便于添加新功能和优化
- ✅ **更好的性能**: 改进的算法策略和约束处理

现在您可以直接运行数据生成程序，ABC算法会自动处理任务分配优化，生成高质量的训练数据用于负载均衡研究。

---

**更新时间**: 2025-08-03  
**版本**: v2.0.0 (Python实现)  
**状态**: ✅ 已完成并测试通过
