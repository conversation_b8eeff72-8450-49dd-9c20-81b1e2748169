{"total_samples": 1000, "train_samples": 700, "val_samples": 150, "test_samples": 150, "task_feature_dim": 128, "node_feature_dim": 32, "resource_dim": 2, "enable_coloring": true, "max_colors": 8, "algorithm": "Artificial Bee Colony", "generated_at": "2025-08-03T11:37:38.217982", "config": {"total_samples": 1000, "train_ratio": 0.7, "val_ratio": 0.15, "test_ratio": 0.15, "min_tasks": 10, "max_tasks": 50, "min_nodes": 5, "max_nodes": 20, "task_feature_dim": 128, "node_feature_dim": 32, "resource_dim": 2, "max_dag_depth": 6, "max_dag_width": 10, "dependency_probability": 0.3, "population_size": 30, "max_iterations": 50, "limit": 10, "elite_ratio": 0.1, "scout_ratio": 0.2, "enable_coloring": true, "max_colors": 8, "normalize_features": true, "add_noise": true, "noise_level": 0.05, "random_seed": 42}}